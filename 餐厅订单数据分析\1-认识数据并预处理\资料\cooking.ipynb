{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### 1.订单表的长度，shape，columns\n", "#### 2.统计菜名的平均价格（amounts）\n", "#### 3.什么菜最受欢迎\n", "#### 4.哪个订单ID点的菜最多\n", "#### ..."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 10037 entries, 0 to 3610\n", "Data columns (total 11 columns):\n", " #   Column            Non-Null Count  Dtype         \n", "---  ------            --------------  -----         \n", " 0   detail_id         10037 non-null  int64         \n", " 1   order_id          10037 non-null  int64         \n", " 2   dishes_id         10037 non-null  int64         \n", " 3   dishes_name       10037 non-null  object        \n", " 4   itemis_add        10037 non-null  int64         \n", " 5   counts            10037 non-null  int64         \n", " 6   amounts           10037 non-null  int64         \n", " 7   place_order_time  10037 non-null  datetime64[ns]\n", " 8   add_inprice       10037 non-null  int64         \n", " 9   picture_file      10037 non-null  object        \n", " 10  emp_id            10037 non-null  int64         \n", "dtypes: datetime64[ns](1), int64(8), object(2)\n", "memory usage: 941.0+ KB\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "plt.rcParams['font.sans-serif'] = 'SimHei' ## 设置中文显示\n", "%matplotlib inline\n", "# 1.加载数据\n", "data1 = pd.read_excel('meal_order_detail.xlsx',sheet_name='meal_order_detail1')\n", "data2 = pd.read_excel('meal_order_detail.xlsx',sheet_name='meal_order_detail2')\n", "data3 = pd.read_excel('meal_order_detail.xlsx',sheet_name='meal_order_detail3')\n", "# 2.数据预处理（合并数据,NA等处理），分析数据\n", "data = pd.concat([data1,data2,data3],axis=0)  #按照行进行拼接数据\n", "# data.head(5)\n", "data.dropna(axis=1,inplace=True) #按照列删除na列，并且修改源数据\n", "data.info()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["44.82"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["#统计卖出菜品的平均价格\n", "round(data['amounts'].mean(),2)  #方法一：pandas自带函数\n", "round(np.mean(data['amounts']),2)  #方法二：numpy函数处理"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 323\n", "1 269\n", "2 239\n", "3 216\n", "4 189\n", "5 188\n", "6 187\n", "7 186\n", "8 178\n", "9 173\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXwAAAFvCAYAAABJpXD/AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjMsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+AADFEAAAgAElEQVR4nOydd5xU1fXAv4ddpCzSu3REUVEEFuwCliCooKggRQULqEFjCQkqiV2wJZZgBIktAUXBiiX+VCwBjYBgCQIioiCL0qXD7p7fH+cNDMOWWffNzLpzvp/P+8zMvW/mnHnl3PvOPfdcUVUcx3Gc8k+FVCvgOI7jJAc3+I7jOGmCG3zHcZw0wQ2+4zhOmuAG33EcJ01wg+84jpMmZKZagcKoW7eutmjRotj9tmzZQk5ODqpK/fr1qVGjBqtWrSInJwcRoVGjRjRo0KDQfR3HccoTc+fOXaOq9QqqK7MGv0WLFsyZM6fIfTZu3Ejnzp158cUXERH69evH008/zeWXX84HH3zA9u3bOfroo5k1axbVq1ffZ99PPvmEWrVqJekfOY7jJB4R+a6wujJr8OMhJyeHO+64g+7duwPQtGlTqlatyuTJk2nVqhVgDcfKlSvZsmXLPvuuWLHCDb7jOGnDr9rgt23blrZt25KXl8dLL73Erl27aNeuHRkZGQCsXLmS5cuX065dO7KysvbZ99BDD03xP3Acx0ke5WLQ9sEHH2Tw4MEMHz58t7EHuOmmmxg+fDhZWVnF7us4jlPekbKaSyc7O1uL8+FHs2LFCo466ijef/99DjzwQN544w2uvvpq5s+fv5fBL2hfx3Gc8oKIzFXV7ILqftU9/CVLlvD5558D0KRJEzp37syiRYtYvnw5l156KZMmTdpt7Avb13EcJ134VRv8lStX0r9/fzZs2EBOTg6zZ8+mXbt29O7dm5EjR9KlS5ci9+3QoUMKtXccx0kuv+pB2xNPPJELL7yQQw45hKysLB544AHmz5/P/PnzycnJYezYsQCMGzeOc845Z599GzdunOJ/4DiOkzzKjQ9/H376CebNgx49wlPKcRynjFOUD/9X3cNvMeq1Quvufe0B+ix4j+F9b2JG686F7rds7OmJUM1xHKfM8av24RfFbadcxsL6LXj0xbs4/tt5qVbHcRwn5ZRbg7+pUhYX9ruNpbUP4LEX7uCo779ItUqO4zgppdwafIANVaozuP8drKhRn39Mu42OP3yVapUcx3FSRrk2+ABrs2oyqP8drM6qyZPP3Uy7VUtSrZLjOE5KKPcGH+Cn/esw8Py72Fhlf/455U+0/enbVKvkOI6TdNLC4APkVK/HgPPvZHvmfvxrymhar1meapUcx3GSStoYfIAVNRsycMBdqAiTp9xE8/UrU62S4zhO0kgrgw/wbe0DGNj/TjLzcpn8zE2wbFmqVXIcx0kKcRt8ETlLRJ4OtjOjyiuJyCMisk5EFotIz5jvFVmfCr6u15wL+t9BtZ1b4aSTYMWKVKvkOI6TcOIy+CJyJfAE8COwE3hZRC4Mqh8CBgBXAQ8AU0Xk4KivF1efEhY0aMUF/W+HNWvg5JNh1apUq+Q4jpNQik2tICL7A2OBc1T1/4KyfOAyEXkXuBS4UFUnBXUdgeuBYSLSpKj6BPyfEvF5o4PgjTcs384pp8B770HduqlWy3EcJyHE08OvBIyIGPuAnKC8K5AHvBBVNx04OXhfXH3qOe44ePVV+OYbOPVUWL8+1Ro5juMkhGINvqquUdWnI59FpC4wGHgZaAwsVdVtUV9ZDjQXkYw46ssG3bvDiy/CggVw2mnw88+p1shxHCd0ShSlIyKPAIuAL4B7gMrAhpjdtgEZQM046ssOp50Gzz8Pn34KvXrB5s2p1shxHCdUShqWOROYC3QDjgZ2YC6baHYGr1XiqN8LERkmInNEZM7q1atLqFoI9O4NkyfDRx/Z+23biv+O4zjOr4QSGXxVnaSqv8H88A8DP2Fum2hqBa9b46iP/f0Jqpqtqtn16tUriWrhcd558NRTNoDbty/s2JEaPRzHcUKmWIMvIvuJSNOY4teAtsAnQDMRiTbqHYDtwPo46ssmgwfDY4/Bm29Cv36wa1eqNXIcxyk18fTwuwNfiEjtqLI2wHequgD4BrgWQEQygcuAd9Uosj68v5EALrkE/vY3eOUVGDQIcnNTrZHjOE6piGeJw3eBFcBrInIr0BD4PXBDUD8aeEZEDgIaAR2BE6K+X1x92eW3v4Xt2+H3v4dKleDJJyGj7AQXOY7jlIRiDb6q7hKR0zGf/XPAauBGVR0X1D8nIpuBP2J++d+o6qyo7xdZX+a5/noz+qNHQ+XKMH48VEi7FESO45QD4lrEXFW/A3oXUf868PovrS/z3HSTRezceacZ/YceApFUa+U4jlMi4jL4DnD77dbTv/9+M/r33ONG33GcXxVu8ONFBO6914z+ffdBlSpw222p1spxHCdu3OCXBBFz5+zYYT3+SpXM3eM4jvMrwEcfS0qFCvDooxarP3o0/OUvjB07lho1alCzZk3uv//+3btu2LCBZs2asXTp0t1lo0ePpnnz5rRp04YXXnihIAmO4zgJwXv4v4SMDHjiCdixg4+uv55nDjiAefPmsX37do4++mj69etH06ZNGT16NAMGDKBVq1YATJ8+nVmzZrFw4UJWrVrFcccdR8eOHWnRokVq/4/jOGmBG/xfSmYmTJpE1Z9+YvL779Nqxgy45BJatGjBypUrWbNmDS+88AILFy7c/ZUpU6ZwySWXUKVKFVq2bEmnTp2YM2eOG3zHcZKCG/zSULEi7f/9b+jTBy67jJXbtrF8+XLatWtHz549qV69OoMHD6Znz55cfvnlAKxZswaAvLw8vvrqK+r6giuO4yQJN/ilpVIleOEFOOMMbrrqKob36cNnn33GrFmzuPfee2natCmjRo1CRBgwYAAXXHABmzdv5sMPP2Tr1q0cf/zxqf4HjuOkCW7ww6BqVd4YMYL/fPQR86dPZ0Ldupx55plce+21AGzZsoXnnnuO1157jalTp/Lmm28ye/ZsbrzxRjIz/RQ4jpMcPEonBJYvX86lV13FpNdeI6tjR6o/+STNonLDVa5cmZo1bb2X7t27M3jwYCpXrswVV1yRKpUdx0lD3OCXkh07dtC7d29GjhxJl5NOgn//m+PbtOHNV15h9UsvsW3bNiZOnEjXrl13f2fMmDGMHj2aqlWrplBzx3HSDTf4peTNN99k/vz5jB07loYNG9LwkEP4cuRIbmrUiOP69qVJw4YccMABXHLJJQB8+eWXzJ07l0svvTTFmjuOk264A7mU9OnThwJT+/fqxYVdu0JODpx11u4Mm+3atWPRokVJ1tJxHMcNfqlpMeq1QusannwjTz5/M23PPpsPmx/J7SdfyuJ6LfbZb9nY0xOooeM4juEunQSyqnpdzhjyIDefMpzDf1zCG09czW1v/Z1aWzemWjXHcdIQN/gJJjcjk6c6nUm3YRP4Z4deDJz/Bu9NGMbQOS+TmefLJjqOkzzc4CeJDVWqc8upl9Nz6MN81uggbn7nMd58fATdvpmTatUcx0kT3OAnma/rNefCfrdx8Tl/poLm8+TUW6BXL4jKueM4jpMI3OCnAhHePbALPS4Zxx3dL4aZM+Hww+Gaa2D9+lRr5zhOOSUugy8iDUTkRRHZIiLbROQZEckK6o4REY3ZlsR8/0oR+V5EfhSRGxLxR36N7MqoyMQufeHrr+Hii+Hhh6FNG/j73yHX/fuO44RLvD38qUB7YBQwBjgXuCuoaw/MATpHbWdFvigiA4C/AROBy4CrRWRICLqXH+rXh/Hj4dNPrad/5ZXQoQO8806qNXMcpxxRrMEXkZOBdsBxqvqwqt4GPAacHexyJDBbVedEbV9G/cStwD9U9TZVfQUYCdwY7t8oJ7RvD+++C9OmwZYtcMopNmlryZLiv+s4jlMM8fTw5wDHqGpOVNlaoGLwvgMwt6AvisgBQBtgSlTxdKCNiDQtubppgAj07QsLFsCYMdbLP/RQ+MMf4Oefi/xqQUstzp49mz59+tCjRw/eeOMNAHbu3Mnw4cNp0qQJhx9+OB9++GHC/5bjOKmnWIOvqhtVNTaE5FTgYxHJBA4HrhCRn0VkjYg8IiLVgv0aB69fRv3eBmAz0Lr06pdjKleGUaNg8WJbP/e++8y/P3Ei5OXts/tHH33EM888w7x585g1axa33nor3333HYMGDeKaa67hhhtu4KKLLmL9+vVMmDCBLVu2sGzZMp544gn69evHpk2bUvAnHcdJJiWO0hGR3wBHAQ8BhwBVgE+Bc4AbgAHAX4PdKwevG2J+ZhtQ5xfom340agSPPw6zZ5vBv+wy6NwZPvhgr92qVq3K5MmTadWqFYceeigtWrRg2bJl3HHHHXTv3p1u3brRtGlTVqxYwZQpU7jiiivIzMwkOzubunXren4fx0kDSpRLR0SqAOOAN1R1RhCp01FV50XtkwuME5FrgB1BcWyXdCfWUMT+/jBgGECzZs1Kolr5p1Mn+PBDeO45GDkSunaFc8+Fe++FFi1o37797l1XrlzJ8uXLyc7OJisri7y8PF566SV27drFoYceCuxZanHTpk189913vtSi46QBJe3h3wPUJTDKqrol2tgHfIQZ89bAT0FZ45h9agJbY39cVSeoaraqZterV6+EqqUBItC/v03SuvVWeP11aNsWRo+GzZt373bTTTcxfPhwsrKyAHjwwQcZPHgww4cPJyMjg0GDBjFixAjuvvtuTj31VNq2besLqTtOGhB3D19EzgZGAOep6oqgrDFQT1U/i9q1VvCqwPfAj8AxwHfBdw4CsoCVpdY+XalaFf78Z4vdHzUK7rzT3D5jxvBG3br85z//Yf78+bt3v+666+jXrx9HHXUUPXr04PLLL6dBgwbMnDmTTz/9lGeeeSaFf8ZxnGQR78SrI4Gngb+p6tSoqvOBJ0REosoGYj76xaqaD7wC/C4Y4AW4EthIIZE9Tglo0gT+9S/46CNo2pTlQ4Zw6dlnM2nUKLKysliyZAmff/55sGsTOnfuvNtXf/bZZ9O1a1cOO+ww+vbtm8p/4ThOkognDr8iFla5HnhGRLIjGzANc938S0QuEJFHsaeA+1U14r+/BzgUmCkiLwG/A8aq6q4E/J/05Oij2fHee/Ru1oyRVarQ5dJLYdAgVs6fT//+/dmwYQM5OTnMnj2bDh067P7amDFjuOuuu9i7vXYcp7wSj0vncOCg4P3MmLqWQG/gQSxK51tgqKo+GdlBVZeISBdgLNAEuAob+HVC5M233mL+99+TU78+Y/PyYPJkxk2dyoUnnMAhbduSVa0aDzzwAI0b23DK66+/zn777UfPnj1TrLnjOMmiWIOvqp8CRXUBl2GzbYv6jUXsmZnrJIB9llr87jubrPXcc9zQtCncfLPN2g3o1asXvXr1SoGmjuOkCl/isJxQ4FKLLS+k88CO/Pmdxzj8wgvJufJanu50BpPbn8bGKvvvs7svteg45RtPj1zOmd20Hb0v+itDz72Zb+o04Y/vP8VHfx/C7W89Qqu1K1KtnuM4ScR7+GmASgVmtO7MjNadOXj1Mi6e/TL9Pv8/Lpj3Ou+2yuYfnc9iZvP2xf+Q4zi/atzgpxmL6rXgj71+xz1dL2LQ/De44NPXmDRlNAvrNoeDfoRBgyyPj+M45Q536aQpa7Nq8tBxAzjuiif4fa9rUBG49FJo1swGeH/8MdUqOo4TMm7w05ydmRWZevgp9Bz6sKViPvpouP12M/xDhsBnnxX7G9EUlKIZYNKkSQwdOnSf/XNzc2nXrh0zZ8ZG/DqOEzZu8B1DBE46CV55BRYtsqyczz8PRx65pzw/v8ifKChF8/Lly5k+fTq//e1v9w4bDXjggQc47LDDOO644xL1zxzHCXCD7+xLmzbwt7/BihVw99225m6fPnDwwVYelagtmoJSNK9cuZInnniCP/3pT/vsv3LlSu655x7uvvvuRP8jx3Fwg+8URa1aNnlr6VJ49lmoUweuugqaNrXy77/fa/f27dtz2GGHAXtSNLdr146pU6dSp86+yx+MGjWKSpUqcdVVV3HXXXexa5dn23CcROIG3ymeihUtLfPHH8OsWXDqqXD//dCq1Z7yGKJTNBeUq2f58uVMmjSJCy64gGHDhvHiiy8W+BTgOE54eFimUzKOOca2774z985jj9miLEcdBddeC+ecwxv/93/7pGiOZc6cORxxxBHcddddAFSvXp0rr7ySsWPHJuufOE7a4T1855fRvLmttrViBTz8MKxdC+efz/Jmzbj0/POZ9OijuxdgKYjq1avvtapZ5cqVqVmzZjI0d5y0xQ2+UzqqVYMRI2DhQnZMnUrvTZsY+fPPdOnTx8q//rrAr3Xs2JF58+axdOlS8vLyeOSRR+jatWuSlXec9MINvhMOGRm8mZnJ/M2bGVunDg3z8mg4bhzTDjoIeveGr77aa/datWoxfvx4+vTpQ4MGDVi7di033nhjipR3nPTAffhOaOyTonnVKnjkEfj73xny6qsMad/eYvvPOQcqVKBnz56ej99xkogbfCc0CkzRzFFUuvBI+ix4n2GfvMCB/frxv/qtuP+EwbzburNN+IrCUzQ7TuJwl46TcHZUrMRz7X/Dby4ZxzVnXE+1nVt5fNptvPCv33PssvlQwAxcx3HCxw2+kzTyK2Tw0mHdOfnSRxnVYwQNN61l8pTRPPPsjXRasSDV6jlOucddOk7Syc3I5NkjT+PFdicxYP6b/Pbj55g26Q+817ITnNcQOnVKtYqOUy7xHr6TMnZk7seT2b05cdhExnQbQvucxZCdDX37wpdfplo9xyl3xGXwRaSBiLwoIltEZJuIPCMiWUGdiMitIrJKRL4XkaEx3y2y3nG27VeZ8Uedy4mXT4RbboG334YjjrDFWAqJ43ccp+TE28OfCrQHRgFjgHOBu4K6UcF2O/BH4CER6R713eLqHQeATZWybPGVb7+15GwvvgiHHGILs3z3XarVc5xfPcUafBE5GWgHHKeqD6vqbcBjwNkiUgm4AbhNVcep6jPA3UEZxdU7ToHUqQNjx1qWzt/+Fv75T0vZPGIE5OTE9ROxC65MnDiR5s2b06BBg935ewBGjx5N8+bNadOmDS+88ELof8VxyhLx9PDnAMeoavSdthaoCGQD+wNTouqmAyeKSGYc9Y5TOA0bwoMPwpIlMHQojB9vGTpHjoQ1awr9WuyCK2vXrmXcuHF89dVXLFy4kKeeeoo5c+Ywffp0Zs2axcKFC3nrrbcYMWIEy5YtC/UvxNvwFFaeLB0mT55Mw4YNd281atTg4osvDlUPJ/UUa/BVdaOqLowpPhX4GGgMbAe+iapbDlQCmsRR7zjF07SpGfuFC+G88yw1c8uW8Oc/w4YN++weu+DK0qVLOeigg6hatSq1atXiiCOO4IcffmDKlClccsklVKlShZYtW9KpUyfmzJkTmtrxNjyFlSdTh4EDB7Jq1ard2+mnn85ZZ50Vig4R4m38wJe+TBQljtIRkd8ARwEPAZWBjbr32nXbgtc6cdTH/vYwEZkjInNWr15dUtWc8k7r1vD00xbBc9pptvZuq1YwZsxeq3DFLrjSunVrZs2axeLFi1mwYAEff/wxxx57LABrgieFvLw8vvrqK+rWrRuauvE2PIWVJ1OHaL7++msWL15M7969Q9EB4m94IvjSl4mhRAZfRKoA44A3VHUGsAPIi9ltZ/BaJY76vVDVCaqararZ9erVK4lqTjpx6KGWk+fTT+HYY+HGG83w//WvsG3bPguu1K5dmx49etCnTx/69+/P+eefT7169RgwYAC33XYbd955J6effjpbt27l+OOPD03NeBueohqkZOkQzcMPP8xVV10VivwIJWl4fOnLxFHSHv49QF1gWPD5J6C+iGRE7VMreN0aR73j/HI6dIDp020VriOOgOuus8HdRx+F3Nzdu82YMYMffviBBQsW8MUXX7BmzRqmTp1Kr169mDp1Kj///DOzZ8/m+uuvJzMzvKGleBuewsqTqUOEbdu2MX36dPr16xeK/AglaXh86cvEEbfBF5GzgRHAZaq6IiieByjQOWrXDsFrThz1jlN6jjnGYvfffdcWZrniCrjhBhvszc3l448/pm3btruNX3Z2NrNnzwage/fuDB48mMqVK3PFFVckVM3CGp7CypOpQ4RXX32Vbt26UaXKPg/gpSLehseXvkws8U68OhJ4Gvibqu6+OlR1I/AecH3U7lcAC1Q1p7j60qnuODF07w7/+Q+8/jpkZcHMmdCuHS1XrWL69Ol88skn/Pe//2X8+PG0a9du99fGjBnD6NGjqVq1akLVK6zhKapBSpYOEaZNmxb6YG1BFNbwRC99eeaZZ3Lffffx6quvhiY3euC4qMikREZNpZJ44vArYmGV64FnRCQ7atsPuBXoIyJvi8g7wFnYJKsIxdU7TniIQM+eNoGrWzfIyOC8hx7ipHXrOO3kk+nRowddunRh0KBBAHz55ZfMnTuXSy+9NOGqtWzZssCGp7DyZOoAkJ+fz9tvv023bt0SIjuawhqeRC59GTtwXFhkUiKjplJNPA7Lw4GDgvexMVItVXWmiJyAGfbqwPmqujvuvrh6x0kEQ4YOZcjQoZCXR8azzzL+5psZ/8030KULHH00PPssVKxIu4oVWfSXv5g7qGLF4rfMzH3LKsTnGT3vvPOYMWMGp512Gvn5+Zx77rkMGjQIVS2wPBEUpgPAZ599RqNGjahevXpCZEfTsmVLHn/8cQYMGICqMn78eEaOHLnX0pfNmzcPdenLyMDxF198sU9ddGTS7Nmzdw8oV61adfeAcnZ2dih6pBLRMpqLPDs7W4trVQtecKNklHbBjbKgQ1nRoyzoUJgemXm5nPPlO1w981kO2BRyyG+FCsU3CvE2HmXhe3E2YCXlySef5L333uPJJ58kLy+PK6+8kueff353wzNhwgQqVKjAG2+8wR/+8AdycnI4+uijefbZZ6lWrVqp5asqTz311G4dorn66qvp1KkTF110EevWraN9+/a888475Obm0qNHDz799NPQBtLBXEtvv/02TzzxBJMnT+a6667bXbdt2zbOOeccHn/8cZYsWcJNN93EmjVrGDJkCBdccEGxvy0ic1W1wNbJZ7s6aUFuRiZT2vdgWruTabRpDRXzcsnMz6Vifh6Zeblk5OdTMT+XzPw8Jl3UEXbtKnjLzS28Lmp79qNv7ffy8kzOtjwyN+eSmb+DivlbgnKry8zPC/TJ20unOvvJ3jKT1TmrUCH8RiszkyEVKzKkTh0YNYqMihUZf8ABjB85EipXhhYtbGLdgQcmbOnL2IHjCJHIpEgYaPSAcmZmZqhRU7DHtRQZKxk4cCADBw7cXT9w4EDOOuss8vLy6Nu3L7fccgstWrSgT58+dO7cmbZt2/5i2W7wnbQiNyOT5TUbFr1TCMZmVCKedvLyim5oCmiMBj7yoTUgQeNTMT+XjPy83Y1KZn7e7oYp0uBl5lmjk5Gfx7BjmsbVwJGbC9u2FVi3au2mmEbNtgzN3+c/50kFvq/ZgKW1m7C09gEsrd2Eb+o04bm/DIH69fdZEjMMYiOTogeURYShQ4cydepUzj333FDkxeta+vHHHxkxYgR9+/YFoEuXLnz99ddu8B0nLcjIsK1y5bi/Muv1TaUSOSwEF9vRhTR+ovlUzMujcu4Omq/PodW6FbRa9wOt1/1A67XLOe67z6icG8zTnDwKatSAgw+2rW3bPe8PPLBExySWadOm7TVmUtiAclgGf+rUqTz11FMF1kVPemvQoAHDhg1DVfnggw/47LPPOPHEE0sl2w2+4zgpQaUCOzMrsDOzIl80asMXjdrsVS+aT+Of19Bq3Qr+eVwNWLTIthkzLIPq7h3FXELRjUBka9SoyKeCSGTSY489trussAHlsIjXtRThueee47LLLmP48OHUqFGjVLLd4DuOUyZRqcAPNerzQ436cHXMk8bmzbB48Z5GYNEiGwN4/33YGjWJf//9924AVq2C9evN/VSlSoGRSUVFMiWSwia99e/fnx49enD88cdz+umnlyps1g2+4zi/PqpVg44dbYsmPx9++GHvRmDRIvjwQ5g0iSHAELCJec2a0eHgg/ny5JPhkUcsA2ujRmQ0asT4Rx5h/PjxSf1Lsa6lnJwcli1bxjHHHEPNmjU55ZRT+N///ucG33EcB7AIo6ZNbTvllL2qDrluGi3Xr9w9VtBq3Qpafb6UVu99SLWd2/baN1cqsDarJquzavFTVi1WZ9Wif+8utkZDo0Z7v4YwQ7sg19L27dvp27fv7glpb731VqmfNNzgO46TFmzbrzILGrRiQYNWe1eo0mDzWg7YuJp6W9ZTf8s66m1eT/0t6+3z5nUc+tNSGPuuRUrFUr36ngYgtjGIfq1du9A5DgW5llq2bMmYMWM45phjqFChAiNHjqRz584Ffj9e3OA7jpPeiPDj/nX5cf+i10JYdudpsHatLbO5atXer5H3c+bYa9T6DLvJzDTj37AhQxo1YkjDhpYCpGFDOjRqxJcTJ9razY0b29wFYMiQIQwZMiS0v+oG33EcJx4yMmwuQP360L590ftu3rxvoxD9+v338Mkn8NNP+06oe/NN6NEjIX/BDb7jOE7YVKtm8wMOPHCv4hajXoMGQNBeZOTnUXvrRupvXhe4j9Yz49XVrJ5R+MS90qQfcYPvOI6TIvIqZLC6Wm1WV6udFHmJyZLkOI7jlDnc4DuO46QJbvAdx3HSBDf4juM4aYIbfMdxnDTBDb7jOE6a4AbfcRwnTSiRwReRoSLyXkzZABHRmO3tqHoRkVtFZJWIfC8iQ0PS3XEcxykBcU+8EpGjgHHAJzFV7YFXgNujyn6Oej8q2K4D1gETRGSZqs74RRo7juM4v4i4DL6IdAdeAhYVUH0k8K6qzinge5WAG4DbVHVcUNY6KHOD7ziOk0TidemcCFwIvFxAXQdgbiHfywb2B6ZElU0HThQRT+vgOI6TROI1+Ler6j7GXkQaA/WBO0Vks4isFJE7o4x5Y2A78E3U15YDlYAmpdDbcRzHKSFxGXxVzS+kqhOQj7ln+gD3Ab8H/hjUV/bR5ywAACAASURBVAY2qu6V/zOytEyd2B8TkWEiMkdE5qxevToe1RzHcZw4Ka1b5UOgvap+GXx+R0RqAMOBO4EdQOwSMTuD1yox5ajqBGACQHZ2tsbWO47jOL+cUsXhq+qGKGMf4SOgqYhUB34C6otIRlR9reB1K47jOE7SKJXBF5HWInJQTHGtqPfzAAWiF2LsELzmlEa24ziOUzJKO9P2d8DdMWUDgaWq+rOqbgTeA66Pqr8CWKCqbvAdx3GSSGl9+E8A/xWRh4A5wJnAGcBlUfvcCswIZt8KcBIwoJRyHcdxnBJSWh/+PMx4n4YNtrYGTlfViVH7zAROAHKxgdrzVfXZ0sh1HMdxSk6JeviqeksBZdOAacV8779Yo+A4juOkCM+W6TiOkya4wXccx0kT3OA7juOkCW7wHcdx0gQ3+I7jOGmCG3zHcZw0wQ2+4zhOmuAG33EcJ01wg+84jpMmuMF3HMdJE9zgO47jpAlu8B3HcdIEN/iO4zhpght8x3GcNMENvuM4TprgBt9xHCdNcIPvOI6TJrjBdxzHSRPc4DuO46QJJTL4IjJURN6LKaskIo+IyDoRWSwiPUtS7ziO4ySHuBcxF5GjgHHAJzFVDwH9gBFADWCqiHRU1UVx1juO4zhJIC6DLyLdgZeARTHlTYBLgQtVdVJQ1hG4HhhWXH1Yf8JxHMcpnnhdOicCFwIvx5R3BfKAF6LKpgMnx1nvOI7jJIl4XTq3q2q+iHSIKW8MLFXVbVFly4HmIpJRXL2q5v1izR3HcZwSEVcPX1XzC6mqDGyIKdsGZAA146jfCxEZJiJzRGTO6tWr41HNcRzHiZPShmXuwFw20ewMXqvEUb8XqjpBVbNVNbtevXqlVM1xHMeJprQG/yfMbRNNreB1axz1juM4TpIorcH/BGgmItFGvQOwHVgfR73jOI6TJEpl8FV1AfANcC2AiGQClwHvqlFkfWlkO47jOCUj7olXRTAaeEZEDgIaAR2BE0pQ7ziO4ySBUufSUdXngDOxqJutwG9UdVa89Y7jOE5yKFEPX1VvKaT8deD1Ir5XZL3jOI6TeDxbpuM4TprgBt9xHCdNcIPvOI6TJrjBdxzHSRPc4DuO46QJbvAdx3HSBDf4juM4aYIbfMdxnDTBDb7jOE6a4AbfcRwnTXCD7ziOkya4wXccx0kT3OA7juOkCW7wHcdx0gQ3+I7jOGmCG3zHcZw0wQ2+4zhOmuAG33EcJ01wg+84jpMmhGLwRWSAiGjM9nZQJyJyq4isEpHvRWRoGDIdx3GcklGiRcyLoD3wCnB7VNnPweuoYLsOWAdMEJFlqjojJNmO4zhOHIRl8I8E3lXVOdGFIlIJuAG4TVXHBWWtgzI3+I7jOEkkLB9+B2BuAeXZwP7AlKiy6cCJIhJWY+M4juPEQakNvog0BuoDd4rIZhFZKSJ3Bga9MbAd+CbqK8uBSkCT0sp2HMdx4ieMXnYnIB9z0dyE+fPHAFuB74GNqqpR+28LXusAy6J/SESGAcMAmjVrFoJqjuM4ToQwDP6HQHtV/TL4/I6I1ACGA78H8mL23xm8Von9IVWdAEwAyM7O1th6x3Ec55dTapeOqm6IMvYRPgKaYu6c+iKSEVVXK3jdWlrZjuM4TvyE4cNvLSIHxRRHjPr7gAKdo+o6BK85pZXtOI7jxE8YUTq/A+6OKRsILFXVjcB7wPVRdVcAC1TVDb7jOE4SCcOH/wTwXxF5CJgDnAmcAVwW1N8KzAhm3gpwEjAgBLmO4zhOCQjDhz8PM+CnYQOurYHTVXViUD8TOAHIxQZqz1fVZ0sr13EcxykZoUx+UtVpwLQi6v+LNQiO4zhOivBsmY7jOGmCG3zHcZw0wQ2+4zhOmuAG33EcJ01wg+84jpMmuMF3HMdJE9zgO47jpAlu8B3HcdIEN/iO4zhpght8x3GcNMENvuM4TprgBt9xHCdNcIPvOI6TJrjBdxzHSRPc4DuO46QJbvAdx3HSBDf4juM4aYIbfMdxnDTBDb7jOE6akBSDLyKVROQREVknIotFpGcy5DqO4zh7CGUR8zh4COgHjABqAFNFpKOqLkqSfMdxnLQn4QZfRJoAlwIXquqkoKwjcD0wLNHyHcdxHCMZLp2uQB7wQlTZdODkJMh2HMdxApJh8BsDS1V1W1TZcqC5iGQkQb7jOI5Dcgx+ZWBDTNk2IAOomQT5juM4DiCqmlgBIn8A+qjqcVFlBwJfA01VdUVU+TD2+PUPBko7qFsXWFPK3wiDsqBHWdAByoYeZUEHKBt6lAUdoGzoURZ0gNLr0VxV6xVUkYwonZ8wt040tYLXrdGFqjoBmBCWYBGZo6rZYf3er1mPsqBDWdGjLOhQVvQoCzqUFT3Kgg6J1iMZLp1PgGYiEm30OwDbgfVJkO84juOQBIOvqguAb4BrAUQkE7gMeFcT7U9yHMdxdpOsiVejgWdE5CCgEdAROCEJckNzD5WSsqBHWdAByoYeZUEHKBt6lAUdoGzoURZ0gATqkfBB292CRHoBfwQUuF1V30mKYMdxHAdIosF3HMdxUotny3Qcx0kT3OA7juOkCW7wQyJIAX14PHUiUkFEqidPu7KFiByWah1iEZFmKZJb5o5FupPKcxLYhhNFpGIift8Nfng0Bz4upK4VMCfq84nAiyJS4Gy4Xzsi8riIDCyg/FgR+TfwuYgkI0orLkSkKrAkQb/9qzoW6UAZPyf7ATOAOon48bQz+GIk4mDuAHaIyH4icn1Mj3F7UB/hcqBTAnQoK+yVFE9EDhORV4EPgS1Ad1X9MJkKiUgDEakhItWjttoi0pB9z0+YlLlj8WsggfcplIFzIiK9I42KiLQWkdkAqrodEOyaDJ1kxeEnBRH5CtgJ/AvLyLkdyMdCQdeo6kdAPWC5iNyhqrcnQI3mWOrn80VkDWbc84HcQMcOwLnA9aq6OgHyywL5WErsCA9hs6qPUNX/pUYlcrDrIBoB1qtqHRHJTZDcsngsUkoZuE9Tek4Cd82/gFFYIyPAIVG7aKBj6JQrgw80AU7HemtvAvOD8spAZxG5WlUfCeYETAMSYfArAFWw2cT3Ap8BxwGIyH7AU8AbqvpgAmSXJQ4RkROD9/dieZPqRJVFEOwxdj9VfS1MBUSkmqpuDj5uxib7CfAplt5DgHfDlFkIKT8WewkRqYUZmIaYYVkFfBl1rBJNWbhPU3ZOVHWXiPwXaBAU7cIawGiZCaG8GfxdqvqBiDQA5qnqSZEKERkDLAw+fg9UDUOgiGQB72Ct8v7Aa8BaVZ0PnCoix2PGJgN4DtgEDAhDdhnn98Dv4thPgErYtRj2QNXXInKjqj4B5KnqZwAiolHvQxZZIGXhWEQmP14PHAl8CfyIXZcNMQM4CxijqjPDlh1D0u/TAkj1OfmSPQY/aZQLgy8ihwKnxBR3E5HoxzYB/igiGryfHZL4XOBlrFd/BDAJOC9Sqar/EZHmWGOQD/xGVbeEJLssc4mqTkmxDg2Av4jIcPZ15ySTlB6LwBf+T+AA4M/YE+bOmH2qAmcDT4nIf4DLA39ymHqk8j6NJdXX57fASUXUJ+R6LRcGH8s9Ed1a/gRcgfUQlgSfo8kL6/FVVXcAYwKjPgJ7PLxERLoAfwCWYVE6u4CNwMNBrzIDqKyq/cPQo4xRgTgDAkQkQ1Xzit/zF6HAQZhL4EgReTxK7uOFfitcUnosRKQN8G/gCeBOVS3QN6yqW4FJIjIVeBj4UEROUdWNIaqTsvs0hrJwfeYAvxGRpZgdrh68j/CFiETO1Q5goqreX1qh5SVK5wxVbUPg+wqycP4deBJ7PFwMTAROBX5OpK8y+O39gduwE7kaWIEZn++B72K20BCRZiLybnQMr4hkFBRjLiKdRGRnbHlIVKaYx18RaS8iDwM5IlJUT6dUqOpqVb0cuzYiGzHvE0mqj8V3WG/29sKMfTSqukNVhwE3hWzsoezcp6k+J2CN2w7gQez/R94/FNQ/Gnx+EHsauE1EKpdaqqqWmw1YF7wOxnoHADWCz7mY4f0C6BCy3Ouw3vxm4EJsSceqUfXNMXfOG0CNBP7/A7Hog8yoskOCsiox+7YDNidIj+bB9gkwE3gPiy2eGZQdHhyP9UF5k5Dl3w60j1wDQdn6qPfR5eti68vTsSiLW6ru07J0TgIZa4P3LSLHJPicD1SP+tw1KGtXarmpPvkhH8R8YCkwGfg/4H3gDOBS4BGsVe+NuVnODlHuYuArzG3zFfa4ViOoqxFcXJuwAd1PgVoJ+v/Noo1ZUNYSyC9g34OjL7IE6FIjOB+9gT7Btjn4nIG5uU4HPkqA7OlYPPU+hj14nzSDn+pj8Qt0bQi8lciGJ1X3aVk6J4FN2Bn1viiD3zAMY6+q5caHH+F0LLzpe1X9OvCjPwWcq6oTg31eEZGvgQ9E5EtV/bq0QlX1IBFpAXyqqoeIyDKgtoj8Hrga6IU1BmdikTqvi0hXjRk4C4E89o4vhj3xzUlBREZhj6eVArktIlXB1gL4bVC2P9BJRLI0xIFsVT0jmEn5NxF5FPMT7yciZwY6EP0+UZSFY1GEbmdjY0p5mIHbH+vpVsRCFB8DeiZIfEruUyhT52QLkBG4aYocU1DVVVjobKkpNwZfRK4Euqlqv+DzWGxCx4vAOSJyDnZh56vqrSJyRlgXUUC0Uc0FhgMXYYuyrwBQ1XwRuQCYhfnoLg5R/m4CH/7+2A2dbI7EbqjItdUhqi4j5jPYY3NX4PUwlVDVySIyF3gFeAE7P08H1T9jUSuZxKyrHDJl4lgUwjT2XB/Vg/cnqOr/RGQkNiEodMrAfVpWzskOrIGphsX5l94/HwflJh++iFwCnKqq5wef84EH2LcXN0xVsxIgvyUwX1VriMgXmC9/larmiEhrLN64erBvJyzvztmqOj0k+R2AgVhs8SlYby1ycoWCe/kbVbV2GPIL0KcmsBZzKUV0+AwLXV0b6TGJyGRsLGFYgvSohbkNtgE91KJRYvfJwFw6CUloV4aOxTjgGrWJP/mqWiEoX6+qtaL2a45dr6FfG6m+T6P0SOk5EZFqWOTURZjRv1xVbxORCpg3oL6qrg1TJpSjHj7Wq+4XPB4+hhm4J2P2EeBqscyVP6tqmFEyldkzSeR43TvCYa8WXFXnisgr7Ot++UWISBVgLua+yccGvM7EjFx9bG5AbKRBc+xGSxSRBmYhdgEr1oNajM1C/mdQvxh7GkqMEqrrRaQnMAV7jC+oR78ficulA2XkWGCura4icjF7dwCS2etL9X0aIaXnRC0CKTJfZysW1QdmJyKTvUKnPPXwh2I5bO4H+gFXYi1nDuYvjPQgKmKTpN6JPFaGJD8TOKCgi1NEKgEHq+rnUWXR0/7DkH8MNnNyoaruF1XeHFiqqrEJow7GBqQS0sOPFxHJBrar6pcJliNaxi/2RB+LoDd9N3ANliogIyhfF30dJLiHn9L7tKQk6/qMkVkDa+hCv17LSxw+WM9hrarOU9UbgLbAR9jIe09VbRlsTVS1TgIuohrYCP8+qMU2fx5T3DBM4WoJpwrqpUZu6owC6lJK0EjWSMbNVNTNI5YWN6VPu0k6FhrcG91MpCwVkW8xH36ySPV9WiJUdU4SOiNVJSods6puTFjnJIxQn7K6YS6Wc5Ikqy2wI3hfBbgBOBrrScXuWwN7jDw1ZB0OIAj1iio7FHPzVC1A358TfEyOAGYXUT8k0O2fCZJ/MNawvl3EPk2wCIj3y+uxABoFr/lRZflYKOJZwKaY/fcKE0z0lsz7tADZnYs5L5WwmcptQ5Z7DFAneH8osDV43xaLFGoWtbUIS37SD3B53bC43Z+D95EY313Y1P4lwEggK+pk52EDM2HqsI/Bj+hTQNkRkQYqgcekHbAleN8E6BVTXwEba9hH51LKleB4rw0a3ffi+M6c8ngsgt9+HBjH3vMPCpyjEHxOqsFP5Rbci1uLqL8luFdPCVFmJGPrN0BHoDXBPBD2pG6O3vKB3DBkl6dB292ISGfgEVXtXEh9JSxc73equrCgfX4BeQQ576Oogz2qtsIGfq4OwjJPBz5U1djcIWGQKSJLsEGoPKzR2SoiG7Hw0MXYbMKF2OBU6ASRBhdiE2oibqbzgLEi8idVvQd2h6m+RsyCFCFwGDAIC6f7FjhARK4uYv+KJMitUQaOBVjE1u2BPs1U9fti9q9GCpLNiS1GcxnwN1VdnySxWylkwF4sVfJNgT5vhyVQVTX47RuBD7A5CNHHuyVQE0sb3QLrDCwlBMqlwcf+V1HrUt6AhS42YU8q1rBRVf0ZLB0v0AW78d/EBqcGJ0DmBuDamDLBXEzVsYids7Cbfyvwgoh8pkGq4DAQy7r4Mrai1wVRVQ9iPe57g4t9kJqvMl/CT1H8jaoeGaVPNSz+GgqebKXA6LCVKCPHAlV9WkRewOZ+zBSR35h68ieC60NE/hz1ldMC/ZKCiDTBjv9F2L0xB0tDkgy2U0DjJiLtsbkBr2OpU8JmF/BH4DfAeOwcHIDZje+DDppGGufAhpSa8mrwk9pqi8jNWM+skogMw6IQIhkZI3fwLuzR+gLM+ITSYkejFjtc7MIqQRRAT6yHcQuWFjcsmgK1gaOIWtRBLXHX0yLyJvA88L6IdMMmQYXNssBwbsNu5hqYn3Qp8D9s4tsstUyniaQsHIuIzM3AYBG5H3vaWMyeUN2Pge5Ru6/F0ignFBGpDfwJWxXue8wN95Sqbkq07GL0GoSleHgBmw8QdgbT4eyZeNUE+AdwJ3tCQRNGeTX4yW61I1PQK2IREJGlC6Nj8S8BrsJi5BcAtwLnhKhD3KjNEXhWRKZhS8mF+duLCNbrDcL7YnuPYLM4r8RcDfMJPwa+GzZ1fTtQC5thOxxojCVVuwVLlzwVeEBVvwhZPlBmjkWsTteLSDtsvKeXqm5LpLzCCKLG5mCDor8D/hG2Yf0FOrUB/oKtjHadqv4jQaJGsqczcg02D2QDsDJB8nZTbuLwoykoxryAVntXyDJbYLl0akdm8emeOOd8LKnaXao6Kbjh5gPNVfWHMPUoSwRG7mssmVlBvoou2IDUg6p6X4J0qA28oqrHx5Q3xtxq12Dug2sjLrgE6ZHyYxGlS1Ms531fjSNlcgL16IDNG0laoyOWumEINp61GYsQuhjLlHkcltDtz4m+L8UeQXNVNSNiO7AInVXAzZgbdhTWORHsiauKljL/Vnnt4e8mia12UTMXFTg26Fmjql+KyCosFcK9CdJnL5I1ICYi72D5R9Zig9g7sUGpT1Q1J2bfY4G3saeuRLEDeE1EKkW7cFR1pYj8FTP2jwFzReTkOAY046YMHgsAVHU5NpaTUlR1XgrEVgeysHDHCpi7ryLmZs3EnggPABLdEauE2f3IjNoKqvqTiPwflnCxNnY9dMDcxW8H+pXK4JeLHn6qW20ROQJrkd/ELqRK2AIHqzG3QlNsqba5WITMy1gGzRcS3bspYEDsfFVNyIBYcPE+iY2h5GOrG52OjV9UxNbxfBR4MvK/ReQZoJKq9k2APp1VdbZYbqPIIL6yp4f9BXAP1rOqANwS1pNfWTsWMbpVxEJ11xRQVxPLHRMbcRaG3DewBjjep4odwL9V9cmwdYnSqS02nlM7iO4bgXXExmMdxIQsEhRcH5VVdaOIHAR8oaqVgrrDgHex6/O0MM9FeZlpG91qt8NaxYJa7UTxPjYQWAPzxXXF/PrnYw1RL2wlm/WYHz8HGyRL2CLGIlI76MF+HcgaieU4T1j0g9qM4gGqeomqXob1VDZiIWanYKGwo4HvxZJoAfyVxPmtPxaR6UBtVa2glizs+6j3FbBY6FaqelOYbr4yeCyieRdbxLsg7gJWisiQBMj9IdhiV30rbKsE/COIXkkUu3u8qjpbVS/CvAE9gVli6wGHShAl9QV7cm/9CAwTkTpBA1QbSz0xh7BtdBjB/GVtw3pskYUtOmOP0buAv1HAzNcQ5LXBLpIN2ESrFsBvi9j/JMwQX52g/5+BRaT8gKVnzkjRedhnAg92E98SnI9JWMMsYZ//4HU9FmK4JjgeS7GnnMj7z4G62NhLuTwWhejyJwqZfYwFF3wErEnFNROjSz3saeD0BMoocCEgoBEWsj0TqBiyzEqYm3kxMBQb19mAzZv5EZs78h17PATPAw1CkZ3qk5qsk4jNuPwGazXrJEBmc2Ad5i54C/gv1jqfEdzI2dhyaQMw90+tROgRpU8HYpY1TMF5aEEhK0kFx2VTcLFnhix3MfbEFWn0v8Ce/ppj4X+R958H9WsSbWhTdSwKkXd0IK/AjgDWgclLgh5ZxdRXidwvCdTh0CLOy2FYtNc9CZBbH1vRKw97+u+CRZe9T1TKFewp4Hzg9VDkJvqkpmJLUat9IDZ+sH9w0g4ILphdwYnsgPls12Hx1hMLu+HKy4Y97RQ1bb1LcEM9GbLcxlgqgV3YJLMvMDfGu1g4XOR9xOB/BRxUHo9FIbIqBsehcyH1zZNk8D8K7sXhQM1EyytEh84UkVMKC5/eBXQMUWa9wA69DozBOqINgro6WGflbmwgN/KdC8PQobxG6WRQQOib2mIk52ADp3diC4+HRUXMXbQJ8+EjItcDr6rqe8E+l4vINZibpbuGGHdcFgfEMAP2TGGVqvqJiFzLnlmwoaCqK4Hfisg/gH9h//XWmN0y2XONLMIaicVh6hFDSo5FIbJ2icinwPFYMEGqeBwb3/or8ICIvAyMV9UZSdRhE7bsaGHch4XutsdCJ8NgA7YWxeOqulMs7/8jWAK5tSLSC5sMl49lBQB4CetQlopyEaUTSzDa/o4WsoKRiNyKzTI9SlVDOYnB4M4gVX0oqqwCsL/uvRhKQhCRiZhhizeqoDmWLbGZpnguQGzIZMi/nQUcqqqpNGxxk8hjESPnPmywep+IIClkDYUE6lINi4wZjjV4b6jqGcmQHQ8i0kbDXWYx9vcbYTn310eVHYidg1DnSZRXg98W+L2qXlpI/f5YCOc1qvpEEvQRLDRzRdgn8JciIvWwAaIzVfW1VOuTaESktap+k2o9ikMkOQu1BE+641R1n3UZxJbkXJwsgx8juzcWmvp8smUXoEumJiA8NfjtHVgHLd7fXw/cqaqPl0pueTT48RB2qy22ZFtv4LbgMW1/bAT+b9io/Gagsar+GJbMYvTJ0mBdzkLqq2CTb17S5M50THpGRBH5HeYrvQabK3G2qhabcygk2SuBFsE10Zc9WUwjZGDXx/PYYPLrwFlqaRnC1GMae1aVAvMVD8YmncUu+5gNHK5R69yWN4JQz9tU9ZIi9rkV851foapvhix/I5aiPJbW2BoOM7F5G29j56wnMEpVm5dKcCoGSsLegMOxmyqVOvTHRt2XYY+ndbEbO9Ko5lNAXvoE6pPyAbEYfZpgg9bbsHj0nkmQWReLfMnFIiGqY+Fw+Zjhr50EHZZFvV+PhdzFbkuxORmfYfH5oQ/mYwOD/wPmRW0/Y6G782K2j4AhCdAh5fdplC4HA9uC91mYYd8vZp8jsOCKrxIgf13U+8zgdTLm338IG+T/HzYrG8wF+7tSy031gQ/xQnoFi8xolGJdegH/IVgEJao8D6ieRD0uw6bpbw2M7LPYQHGyj0dtbFBuGzY4OgIb10iG7G7YQOxxMeV9geXYXIhmCdZhadT7AsP/groJWMhw6PNEipD5NPaklSx5Zek+bQlsCN43D+7PPxewX10sL1bY8tdHvb8NOBUL1Zao8gxCjpYqVy4dETkem1TyOTBGVdelUJcaWCsenUCthiY59WsqB8SCjIhfYy6LW0lSRsTAXbVDLb98BY0ZNxGR/bDQuGlYGO0xmqDEaWJrxv4Fy4o5Q1VryZ6FTnKC7VMsg+oWVf0uEXoUotv1QH9V7ZIsmYHclN+nErVQe/B+EeZ2vVJVn4varwbwrYa4oHuQVmGzqlYMXJxvYu7gK7GJVpHrtRYwXFWrFvxLJadchWWq6n+AHiJyGjBNRN4H7k+0kRWRyIy46Kn5FYK66Lz3XwYDuBnBlqmqdROpm1oe9AnAhMiAWCLlxcjOCwYHk5oREQuBVAkWE5GCFxWJ5DOai+XTuTyB+vTEsh1GosamYylxMzC//S1YyoWh2AzLZDEfuENEKmrI2WOLIlX3aTFsxYzu6yKyQ1VfTqCsXVh6DbCcW4OCsug0CoK5dwaFKbhc9fBjCYzNNdhj5MOquj1Bcgawd8sMtsrS05j7QLC0zBcG+0UMfoaqJnzRg3RDRM5g3wY4QgWs0VujqnNEZCB2ng7TkAdKA12+VdWWQUO/DrsGCmqBsjBXx2BVfT1sPQrRLRKpdbSqfpIMmYXokZT7NJB1GLYOxXPsSZrWApivqjWDPDfPYynUpySohy/Y/x0KnKyqq4PyNsD/YXMjPsfWagi1ESzXBh92x8IPxlwak4AJmqBQq0BeN8wfdy/m0on09POBWpqcmPy9InREpDLm4tCosgws/C02QqO0ssvUBDARuRPrWb+N5Y/ZElMvWObKGao6IgHyc7B1D3aKyFysZ5/HnqydGdis77YiciaWYfMITdLcCBH5EbhbVf+SDHlF6JGU+zRoXB7BJkpmYhPuGgOfqWqVYJ+TgKnA37FxlXGq2jhEHf6CpdO4GQusOIQ990wtbLC4C7ZmQaTxq4jdr6XqDJR7gx9BRDKxgcxBWCja0xrinxeRY7AMd+2wEMAJWPKjigSrzgN1NUGhiCIyGLs4PsF6CPcBj6rqpqCxKfC/asix1mVtApiI3A4ci02hz8RSKjwd46cdCYxU1foJkP8H7Jx8jfmI67LnySMTuwe/idp/IpZe4T9h61KIfgepaiJnGJeIRN+nUTL6YQY3Cwsq6KWqJ0ftcxBm8Ltji9LErhVdGvnVsTQbuUGo7u2YfYjtJEX+d6RjUElVDy6V8DBHgH8NG1AZSw3bKeTfPRRbT7Z+8Lk+FnJXCevV5QP1Evi/vsJWyMnCHhVnYj7Ag7FJZs2xQEeEpQAAH99JREFUMLzmWCKvjVjPM9XnI2EZEbGe0lwsAVUGttbwROAJLDHXa9h6CR0CHQrMLROCHq8B52HunK2Ya2c91tOfH+zzD6zRCVv2fkDXX/C9U1J8XSTkPo2RUSGQsRmbm1HQPglNQIilWRlWQHkGcC3WaQtNXtr08GMRkaoasjsj+N2m2M3yRDAZayjmr5sIXKWqG8KWGch9BOstXhFVdhz2SLpUVQ8Qke1AVbXolXUaol+yCL1SNgEsSKswGVtK7pyo8uqYEa6NLZQzD0tW9Se1Qe5QEZGXsF77S1FlVbBwv6rB507AO5g7J8xVt9pg2Vv/gc3ULPKGDyKYHsbW4j1Zk+CCLEafRN2nl2Duk51YksPF7D3msx+Qo6qTEiD7DGwVtK1Yp2QskK3BKmjBTOdJ2ASsJzGXWyj3Rrkx+CJynKrOjPrcAfPLFehLFpG3gHdVdWwIsrthLXIu1sM+AZsxmYElzBrAvo9ra1X1f6WVHaXD5cCZwXYRlhjr/1T1WRHZX821015VPwv2T5bB/wj7708DUxLV4EXJy8DcWT9iN1QlbKGRlzHDvglblGYttvpXwqKHRGQSFoVzMLZWaewKU12xzKoROmOZPU/QcBPr1ccMR2MsHPJNjYnKCRqgPsAd2NPhFQkytCm7T2N+dx6WKrugkNAB2H37lareE7LcOpirN+JmzcdcNplYg7MeCxVeA/wReE1DHLgtFwY/GGR5XVUrR5XlYzf8R8C/gWdVdUVQdzHW4+6jqq+GIH8JdjNF/HD7Y+4TMIOj7O3TzsTcDL9X1b+WVn6gQ3dsVL+92LqYC7GBp6+wCVdTRORPWNxzbhIN/mVY+GMP7MJOaEZEsSX6pmI3zy7s2CvWY6uCuZBaYo/zr2I974RExYhIf2wC3gVYrH1sor6HgZuwa6VCoNexWGz82gTocybmJmiPNSyrsE5JQ6yXOwvrTX4YtuxAfkrv0xhd5gEXqernBdSt1ySllQiCBk7HGp812PXSFBsLPArrFHwC/BN7Ci5d+Gwi/VPJ2jBjm0/UqjDYTXQW1tv7DDPGU7EVfXZgj7eJ0KUWwQy+4HMPYFoB+70DLAlR7sEUMCMQ6628HbzfxJ5Gfp/1AhJ8jqph/sq5mO96ehJkVgxumFZEzXLGjGt7bDm/9VgIXMJ81ljv+uzgHA3C4r37YDHYbZJ5HgJ9amNPgOdiYcPHkoTZz2XsPp2HLTvZu4BtE/ak3Bs4MsHHZP/g+tsQ/Pc/xtRnYmM/i4G5pZVXLnr4ACLyE7bg76fB5716sEGs/ONYT+9VVT0rQXrUB77TPSFeFTH3QntVXR6133Bgl5Yy+13U79UN5GRhA7NXYTfyUcB72KLMf8cmGFXAQtOuAFDVp8PQoQS6JiUjolja2R/YEwKZCxyrqnOi9qmDRYNMUdUpCdJjMhZjvhnLb54b6FQRG7ALzbVX1ilD9+mrWKKynRQcwSbY0/nLqjoqEToUoFNzQLWAMZzA5XaU7llb45fJKEcG/2Ms+93rwedN2IDpkZhP/TjsMfZ9LNb3XE1AWuBg0OtoVf0gquzEmM+hp8ANXBkfAidij4gnAxNVdaaIjMYMf8QvHBsDflqYupQFgrjuKlioamWsJ9UA+FpjfNMicqiqLkigLs2BjZrg8YtfA2XlPk1XypPBfx5zEzwVfM4DlmA+7P9iOWTmB3VXYBOjOmpIMchBRMjPFBNPLiItMT/2/7d35lF2VNUa/33dmQiBDIAgqEAAEyIiKCIgQxQRUZ6Kw1J4DCI+huXzKfKcEAF9oCCICrJwQHEAFRBEEEFkCiIQBAmDMqhEmWUIJEyZOvv98Z2iq29uhwhV93aqz2+tu/pW3ep7dt++tevUPnt/+0NRUfOV9L5jgbUi4u7S9hbFhSYVnNwTHWgEUjjbWEZ2TunYWpytpJ2A4yPiNWn7yzhEAJ5FfSntn4LDTJ+JiJOrtqONXcLhk1q0e4Y63T5PhztN0tKZBxwgaQc8e12EQxkL8O3h5pIWR8RtEXGKpK1wcdT0isZfmMZdACDpzvQ8cPhiqqTXABfhxbKHKxq34Je44OqwtL02cEUKKb0B3yb/N51pabcjjskuj7O9XlIdznYR1tQpOASnaILj51+SG+VciNvJfa/i8QdjM7wIN7JD470g6kqHpMvnqVx1/hdgarj6eWd8FzjYzLfSbLpu06QZ/rtx44ancYy0F3+BVsfNyzfHi3f3YF32c/FC3g1t3/CF2bAEa8/PS88Pxtk4R0VEr6RbcUz5vcsz+/03x/487m5/XNq1Rnq+D47pT8X52GV6sBzvdyu2ZTrw5YjYJm0voORsw/olhbOdjascl7cyd3lt2AE4MiLelLafy7yQNA9XQ38G+BHOluqIeJikacAN0UYBMV0AT46Ityz9my963KGSDtnV8zTdYfVhHatI52lrrUER8iyy6Q6NiGOrGL/bNMbhF0j6LZYbvSgi7mh5bROsYXEQdna7R4Ul7C0Ovy85+Ql4ltArl2vfHfVohEzEUrt/wV/gMXhmD/4C34Oda1m4q3D4W1dsS1edraRjcKrh9jhDBlyF/BX89x+JP5OLcRgB+rVKqmxs3862KcB10SbtT9Km6bXK5HDT+w6ZdMjS+N08T/tioGz5hCLElpIf9oqULi3r3mwXEa+vavxu0qSQTsF0nKlyrKRNithfujX8HU5xmka/gNKLRtKOWK6g3dUzfIhOKB0fOOf2xKpm+hHxeFoQOzsiTpY0Geu3TMN6+AfiWdVRETFzGW/1oig523UlHZ52j5ZrAITTM4/CJ/tjwOdS2KlKZ7sBFkxbCee2g2eSk0vH3IqzmtZPdo1Kj04w2CxrIf2hryq5Axglac3ob7H5FD4XtsV1AsfIFcEX4XPjK3U5+8R0OnyeDkJRp1EU7Z0DrCzpe+Gq67uTLY2gEQ5flnl9PM2cn4mIbWVZ05HpZ+CZ74F4VnlEROxT0dhb41DJic9zaHlGNwbnAN+PK1Cr4nr6ndYo7Mj+GRFHSPoG7q97Tcoa+sNgb/Ii6bqzjYj3l+4y9gWQ9K7S8z3T2IuAT0ZNhUYvgOecT6VvGvGApEeBdfDnDpabOA84DwakQ+6G0yE/X7Ud3TxPlzZFP8e57wDvTZOlj+HCpx2iX2LjUlz01Ayiw0UfdTzwP+QeLIRU7hU5G8friseS9OijwoIOnA1Deu9V0/O+9HM8bdqU4YXbr1b8OfSWno+gjTgaL0BI6wXYsQOWGy62y/+TRTjk9Ed8q1yXDW8GZpS252M10xNwOf14HNp5Fqsljqz7c0l2TGGQordlvVbBuNfhtZJi+0lceHUUToFcjIuRvpE+kzrE7Lp6npbGW4KFDr+bnt+O766WYAe/dSe+C914NGKGDxyNq/Q+AKySskJOALbCX+RyzLxQTbyuqsGj/YKSJO2FQxjFjkNxgc/fsQTs76qyIdnRV3q+mNQ9SdLkSOmaETFjkF+vkl4Gdu8ZK+lreDb/JJ5dHwxcIunbwKej+kXTUQzs7nUesFqy4ZdhUbAjUxjjAuB16S6gMvnqtPC5gIGiXONwyODcNr8yrs2+qrgXL+QXjMXnze04vPbx6E+HvBM4U1LV6ZBdPU/LRMTHASR9BK91LcATlX2Bq+S+Dh+LDrac7AQ9z3/I0Cci7oiI48MLK1vh2N9sYKeIeCwi5pYecyLi7ChVvVZBWv0vNFvA8fNDcJ/K+1Nu+q7AjZLeFxEXRA2dfSRt1mb3CZKuarO/LgZztquRnG1EHAlsjWeZl6VF5yq5FjgsrQ8QER/E/4tLI2I/SSMk7Y3DS9PxzLPSzCn6L3xqedBmX/m1OijSIX8g6TT60yHvoT8dchOAiDgFx7Irzd4aCufp4KZFkRa6Ca5QXw2HJxtF47J0CiS9Bbg2Kk5/XMZ4Y3DWw8tj2YVXB+EQwqlRcYclSetgB3ZERJyU9o3GOf+/iIj9qhxvGXaMx8qPM4qZu1wI9t6I+IncgGIPLAG7Hm5w/uGoMDVTrnh+Fqf5bYWd6WVYj2R8+n89jUM5y9udqwq7ppJa6/07r1UwbtfTlgexq6PnaRpzCQ7hPIHves7Cejafwnc6P5HU08nvRadorMPvBsnRzaNUSSkrJk5Ks6biuLdi0axKi41SCGFdYPNIRTOSPkq/KuN1OC5a7qBzUZU2pDG76mxTKuyr8Qx2TVyi/zZcCDYfi2I9jT+PcdHBkyClZV47iMMf9LUKx+9aOuRQITn8bXDj+N/gTKBt8Oy+WCx+oHsW1kejHL4sb3AZjsctS098BDAmIrasePyxOOtmY2DLiHha0seAw4G/AwdE0qOvGkkH40YKW0XETWnfSjgl7z76HW9BQC0tDrvubOUCt7uwCuPE0sV3PZxmVxB4lncBbmN3U9W2tLHt+Rx+2xz9CsdfgBfMtwQGS4e8BDvBH0YNMtZD4DxdEgN7TRe1M5vhRjhb4Itda6HiCk8jYvglRuAv8uU482AmXoiZmbZn4EWjbYFfVzmwrJJ5dXrvzxW3qCm0MhlrjV8n6euSKl2ck7Q2Lir6fIvTOhEvBO6Nnf3EiOhJX3YxMFW0Kn6PO3wBLIyIoyNiO9zZZ3X8OdyMU1MflfRDueqzSt6XHgC9kvaWexYcgi9+vThL5xncenAklniotKr0BdDLwLWPSpC0RgqjQUqHxKGMOZLGyx3AyumQ+0fEPnU4+0TXztNEtDwvJj+zImJnnLl0R7tfXOGpI/WnWw9aUiDxLVsfA7XQ16NNmmQFY78Tf4FfsoxjtsEx0qNqGL/QJpmYtg/CDm2HtL2k5XMY8LlUaMeUZEcfvqDsjcWxTkp/u7By5ZM4dfIMvIB4TMV2jE5/8zTciPp/8YX3sfT6WCxPXRz/buCwDnxHN8bKme1em4plF6oec0ikQ5bG7eZ5KmBJaXsJ8A985zfoo+7vRaceTUnLHIzBKl+rHyjifKx5vqxjrpH73LZrq/Zix58BIOn7ssb7VJxLPVgaZi0ZIRFxZ1ooFl4MvJf+RcA9IiJkhcQxEXE5cLmsoLhJxaYswo7+3rAa5xVp7eBDyc5nJG1Ysvu5IqSaGYOL0pYiHFPfooYxh0w65CB07DzFC9UzJI0MJxTsideb2q0lFXo6ld91dYumxfDH4xlMb3mbgXHcdfEVu9LY9VBB0ik4x/9W3MXpsbS/VTNkwHbFNvRgbfM/RerHmZztTpHK9dP/oagEbs6XcIgjN0v/AnbqB0XE6V2wYdifp92iaTH8YYvc6Z6IOAgXkozDM5k1u2DOKliAqnD2PcDrY6A2yxNYLbPSXO8VnXR3VhsRcWO4i9RuWFJ72JPWMFr3rSkLqTWKRjh8SdvJ8sBDWmO8LtIX9ipJ24NParzgtRA4P6VJwiCLVTWwCEs1F4zGJeuFvRvgxd0e3Fe20Ug6RNJWRRHYMo5bDfhjOdRUI1cCG8iCYWUb1qtrwKFynpaTJlIm2yMtr48FfoZDkY2iEQ4feC3wSawFMhxZAlwF/FoWcyOsirgzzsv/Vjruakl/kvQnHE65Oj2vlHANQDkuvBBYKGkDSV8FbsAOZ8uImF31+EOQ44A/4E5nSPq5pD2Tsynzdtxg/MEO2PQSfL60poeeI2nm812cXiBdP09TmvRXSrsWUFIolTulXY+zmA7prHX105gYfvqC7g6cNlxjg5KOBz6CY/c3pH274sXki4Er6M/G6EmP0RFxdA22zMEXnNOwWNrW9N9R3IGrf4vtXryI+4bW92kCab1kE1xodnPafgwvCJ6MRfTmSboamBkRlTsaWeP+nLCGUHE38QiwckQ8m/Zth1Mij4mIQ6u2IY3R1fNU0gHALlFqji7paZxF9h94Qf0U4KSoQfqk2zTG4cMyF4MmlOLJjXX4AJIuxDOpjSM1zZZ0GfCKtK/y5iuD2DEHp2juhGf4ZwB3Ysd3L1auLISpigKbMzphW6dJDn586Tu4BM+wdwSOwGseP8YtKKdExEMVjz8J1x88DOwXEZe1OVdG4Zl3L/C6qFHqoJvnqdyN7QIGtvqcjicf/8LfURgY7mzMhKSJaZlSvxLhSBy6+KmkQrFwbDrmHFz5+mg3jKyRvYBDCmefOBGn4a2FT/yOEBGP4M9+jDdjU7l8/1M4VfAHwOHFrLNpSLoWVxcDXChpMf4+Bi5KO1PSL/Gs+rPAl6p29gARMScVt30ZK5QeSik+nWbdZ2K9/K3qdPYlunWe3osbr/wEX2SEM8rm4srwv+PvZTl1uhen067wNG2GvxLWuS5Kttv9cYWOzBjgMxFReU78UEOSgNWTA657rKOwnEJvJPmA5FAOjIiTJL0fX3wOxnr0cyJi+7rt6gaSDsR3N6cCH8WFcAK+j2Pnrwc+h6uQL8UtGafVeQGUtAd2andgp7YBvrt4BbBbdEZeomvnaVqQfQpr3s9M+57AVeC7AB/HVcBHAl9vWspwoxx+pvskhyIcA51UzO5xS8dVcWjn7Ij4ZcoumhgR/5Q0pokxUxi0BuJhXG18OnYs8yRdANxVRwy/jU3jgcfxzPYs4FN11GQMRSTdCOwbEbekUNbciFip9Pp2OKa/ANg+LJ3cCLLDz9RCiuFPB2aR+vqy7DTQuVGjSmQ3kSuLN8UOHixpsEO0KFHKTe5vBNYrCuYqGv8i7LzK1aQjgXfgvg1/bvNrPcBKYW2ZxpDuNj8bEf+Xtj+E76yubDl0NF7EPR+4PeprCdpRssPP1EJy+Gth/ZrRuFT/dOxkRuHsndOwcmYvzmC5s/27rdikGX1x0Svow4uEt+BmI2dFxJOSfgFcFiU57QrG/wVuqVnuvDUai6VdjMM7rVIbxULl/lXZMRRIDn9eMaOX9Az+DIQ/ky1wCu1OWD307TgceVp3LK6W7PAztSBpTimGPx54KCJWSusJH8CS0ROwsuiPumhq7chKqk/h2oSiafsqwMtx6up+9LccvAW4KSpstTiITUVIpw/LIX+5KbPY5yM5+QNJoUecHSW8aLtTROwkaXZErC/p8ahRrrrTNKXwKjP0kKR1JL0U5zZvkyocR0TEz4HNcGbI67ppZN3IOvOnYIXGhbg5zpN4TeMAHEpYHzeo+SKwYZ3OXtKBkqalzcAz2oVYhuPbsvBd0+nF4cbpOFNxOlabfSX9RVmNnAlnh5+pi/FYkvd+4AFcXTsXmC/pQdwAYxUsJdDI2H3ieFzcNj/JGFwo6TisddSHs3OOxTr02+KLYOVI2kjSlbjQ673F/oi4OSJ2wyqie+Fq7Sama5fpwfLL66XnL8N3m3OBiWqRm2gS2eFn6mId7PSLph6r4lvmacB7cGriU1i58X65t2mjkPQG3OmriIMfDGwEnBhu0v1fwFtws5ZZWA++rpTM83Gx1/RiwbJMUs18D5aU/lpNNgwVFuC0yy/iu5vf4gYwr8Jd414FjJK0KdAj6dVdsrNycgw/03Uk7RI19NYdCkh6VUT8OT2/EjeT/1bLMZOAs3GYa5OIqFxLR9LGuHJ1QdqeQJJ3KOeay1pHhwBvjIhOauJ3hJSG+XhErJy25+PspcXpZ3lxvQeHfEZGRCOEGbPDz2Q6iCS1K+ZJsfPJEXF7h+xYE4u0TSpXZacF5kuBDxd6TE0ihWveEW5YNOzIDj+T6RCSJgJPhTstdduWUVjQ7ppWeyT1RES7DlCNQtJhEXFUaXtPXPn9my6aVSvZ4WcyHULSF4A3RMSuaUb/GeCEiHiqdMw+wL8i4uIaxn8ZDlPMKcZM6wz/rEPDZ6jTmnIpaRawOCLqaDM5JMiLtplMB0gyEh8DZpZ2H4Fz8pE0Namafgcv7FY59sqSzsM5/rOA/yy9vCewsaQHJc2R9Hh6PCCpUXrwknolHVzaNa/02n4kTSNJr5S0nqR1Ja2f1j8aQXb4mUxnOAmnqE6RNCktngpYnNQrb8YCXv+IiJMqHnsEFgQD2Bj4gMwMXH27EFfVToqIiWnWey+wu5Zu0rIiszvOCivoA5C0Gc5M+gVWcr0DC8zNTj9v7ayZ9dH0fNtMputI+hruM/wW3APg4+mlIp56G86BvwRr29TBQqw8OQrYPO0bUEEq6fDS5lcj4pyabOkWvwfGSxpdylbaBjgP+A3+HzySRP9mR8T66Zhaq547SZ7hZzI1kcICt+JQwRsj4j48qy8WSQVMBa6OiLNStsx41dNecCSuiZiAK0tfjaucy6J2Kj2eXPotVmwi4p9YEnrdtGsVrJfzrYjYIyL6BvvVTtjXCfIMP5Opj5dh3fsRwHpJUA7seAu+D0yQtG9EXIr1bdYF/laxLS/BjvzX2PmPpH+mfwGu/P166fiFFY8/VLgDh7RuxgWBJwOzJL2TpQXkGkd2+JlMTUTE1ZIm425WV+FFWnDHpSJM8D5gb+BsuQPUs1hbp2qHfx+ufN4E59h/U9Kn8ez1YNzo/h/0z/h/ikXFmsZsHLp5M9Z42hY3ohkW5JBOJlMjEbEgIr6IZQsOxbHx+0p6NXMj4vO489RaWEFzcsVmiP5zfRJwgqQe3G0LvDC5uLRoOykimujswcVmP42IN2Fpjz7gPRHxprSv0eQZfiZTM0mLZS/cQu9KSdcDF+HMnEjO90CcRfJZHFeuktXxbBasZ/SBiFiSCo12whcDqb/H7HNExHsqtqXbPIDXTcBpmfdgpdCtO9TLt6tkh5/J1M/DWIZ4MvBO4IqIWAxsnmb6Z2F53ksj4tAaxn8E9yA4BjcvPw4gIi6U9GacuXMMaaZPfz/ZJkolzwPWSM/7cB3CBcDP8P9mpZStNCH9FDBG0qgkb71Ckx1+JlMTqdnLJ3DGy7l4UfR/gDelVL+52Pm+HIum/asOOyJirk1hQ3xxOVfSObjxyilYuO0PEXFsHeMPMebT7/AVESH3Yf6zpI/irmxr4MbuRdrqd2jIgm6WVshkaiJp5/ypdTeeOa+MM2MAbsJ6+GdU2cu2ZMeWWBJ4GrAr1t//Jg4fbYovBGe0/FrR4vDaqu3pJklK4qURcZ6kRyJijbT//cCpwJQmy0xkh5/JdICUW78K8EQhTCZpLDAFF2W9D9gGO51jIuKBCsf+G/AMqbI0sX6yh/TaX+mfxQrH/EdExAZV2THUkHR9RGyZngu4FvhEE2WhC7LDz2Q6QGp1eA3WXH8CyyxsX254Imlr4CfAARFxWQ02bA58FMsH/BiHL3bE8f0bgMOHS1/bdkgaORSUTOskp2VmMp3hJtz96q04rn8WJfEugBQ+2awmZ/9K4FfAv+hfkL0kIvbH3cmuAC6QdHzVY68oNN3ZQ57hZzIdJYUOVomIec97cPVj95TCSS+JiIdbXl8HWD0ibu60bZnOkB1+JtNBUljl+qa0zMusWOSQTibTWRbQL542AElTJF3aYXtabZgg6buSxnfTjkw95Dz8TKaz9GGn347ROFOndtKdxtxkTy/O2LkL56C/Gueef7ATtmQ6R3b4mUznGSyOupDBLwZVc2OyoyyPvHlE3JK6P3X1TiNTDzmkk8kMHYIatdclfapl10QspvZk+nlb2v80bpaSaRjZ4Wcyw4djJf1O0rpARMS81HRlSUTMLTJ4Ms0lh3QymZqQdAlLL9KOA1Zup0xJv9RCnTxKg3q0Zv498gw/k6mPXpL0cMuDNvvKr9VFRMTuwL64JOBySVfQmQtNZgiQZ/iZTE1ExI6t+yRNBa6JiN0Ge61qOySNi4inSBeUiDgnqWf+Ku3bsuoxM0OT7PAzmc6yrEXZuhZsj5Z0X8v7R0R8EyDpvmeGATmkk8kMLeoI6zwAHAYurFqO45vY+CRDnuFnMkOJWrpMRcSxkn6KlThnSNoZx/D3wheYUaXn4P67T1ZtR6b7ZC2dTKaDSNoYuC4ilpIuSDH80yNii5rGHoElkbcAVsX59u14Cjg2Ik6vw45M98gOP5PpIEnSYGZEjOrS+CPxwvB8YHpE9D3Pr2QaRI7hZzI1ImmEpE2L7Yi4aTBnL+mlySHXRtJ83xeHjrJi5zAjO/xMpl5WB26StDxSBVOAB1Prw9qIiNuAN0bE/DrHyQw9ssPPZOplPg6dPudcJa0m6QeSXtFy7CNY36by87J1rMG6O0kaI6kvhZ4yDSM7/EymXtoJoo0DPgTs3LK/UMpcXKUB6e5itqSVl+PwBThbJ8/+G0hOy8xkakBSL24QPjJtTwcWtTQJ3xv4Xmm7N/1cWKUtETFfkiJiQFaOpHfivrrz02NB+hlYJz/TMLLDz2TqYRxwMf2a85cDs4DXptcXAK+RtEVE3JD2TQCerUm1sl063nnAM1gKued5js00gBzSyWTqYR6wNrABdqATge1Lr88HTgUOKe3bEHioUwYCRMS4iBgBrASsBryc+kXcMl0iO/xMpgbCPAQ8nrbnArtKuh1n4wRwAvAuSeunX9sIuKWTZpbsXRARj0fE/R0cP9NhssPPZDqApNWAbwFnAXMAIuK+tP3pdNibgcu6YmBmWJBj+JlMB4iIxyRNjoh5qeNUwfHAHyWdCbwe69jUgSTdjENGxQNJuwB/Be7OHa+aT3b4mUxNSLqe1O1K0uXA34D9y8dExG2SZgDnAqdGxKM1mnQcsFZ6rI1j9WfiBeZFku4G7iIv2jaW7PAzmfqYgZ3nVli/5rHSa+WF0a8AVwDfr9GWaBVDk7RHRKwqaW28fjAlPTINJYunZTI1kvTnH4uI3tK+jYBZEbFyad/5WJJ4n4iotPAqvX9f2YbB9qX9S4CpEXFX1XZkuktetM1kOs8olta93x8Xas2SVGmPWUk95Z/p+Wj/UL7LH0Zkh5/J1MtY7FifE0+LiD+n3HdK+x4CdgF+nfrPVskYHEIqSyssAjZvvZtQanZLVtJsJDmkk8nUSJI7nhYRN3fbluUhXZgeBN4WETO7bU+mWrLDz2QymWFCDulkMpnMMCE7/EwmkxkmZIefyWQyw4Ts8DOZTGaYkB1+JpPJDBP+H0Um9jfrg0RNAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# 频数统计，什么菜最受欢迎  （对菜名进行频数统计，取最大前10名）\n", "dishes_count = data['dishes_name'].value_counts()[:10]\n", "# print(dishes_count)\n", "# 3.数据可视化matplotlib\n", "dishes_count.plot(kind='line',color=['r'])\n", "dishes_count.plot(kind='bar',fontsize=16)\n", "for x,y in enumerate(dishes_count):\n", "    print(x,y)\n", "    plt.text(x,y+2,y,ha='center',fontsize=12)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, '点菜种类')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#订单点菜的种类最多（1，1，1    1，2，3）\n", "data_group = data['order_id'].value_counts()[:]\n", "data_group.plot(kind='bar',fontsize=16,color=['r','m','b','y','g'])\n", "plt.title('订单点菜的种类Top10')\n", "plt.xlabel('订单ID',fontsize=16)\n", "plt.ylabel('点菜种类',fontsize=16)\n", "# 8月份餐厅订单点菜种类前10名，平均点菜25个菜品"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, '订单ID点菜数量Top10')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#订单ID点菜数量Top10（分组order_id,counts求和，排序，前十）\n", "data['total_amounts'] =data['counts']*data['amounts']  #统计单道菜消费总额\n", "dataGroup = data[['order_id','counts','amounts','total_amounts']].groupby(by='order_id')\n", "Group_sum = dataGroup.sum()  #分组求和\n", "sort_counts = Group_sum.sort_values(by='counts',ascending=False)\n", "sort_counts['counts'][:10].plot(kind='bar',fontsize=16)\n", "plt.xlabel('订单ID')\n", "plt.ylabel('点菜数量')\n", "plt.title('订单ID点菜数量Top10')\n", "#8月份订单点菜数量前10名"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, '消费金额前10')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#哪个订单ID吃的钱做多（排序）\n", "sort_total_amounts = Group_sum.sort_values(by='total_amounts',ascending=False)\n", "sort_total_amounts['total_amounts'][:10].plot(kind='bar')\n", "plt.xlabel('订单ID')\n", "plt.ylabel('消费金额')\n", "plt.title('消费金额前10')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, '订单消费单价前10')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYQAAAEfCAYAAABBHLFTAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjMsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+AADFEAAAgAElEQVR4nO3deZhdVZX38e9KZZ5IiCHIGIaAIhDUckCCBAUVR1pRcFZa0W4B+1EUbKMgra3iLN02RhF5W1tFbAca0NiNNAESMQnoK4qCCshcZJ6Tqlr9x16be+rkVt2qVN2hqn6f56mnzj3jOuO6Z++zzzV3R0REZEyzAxARkdaghCAiIoASgoiIBCUEEREBlBBEajIza3YMIo2ghCAtw8z2MrOXDHCaD5nZKwqfP2Fm51YZ7yQzmxHdzzKzCdF9j5kdF93Hm9nEKov5TzN7oZm1m9l/DEU8MWwPM7vBzMbH59lm9m0zG9vH/M80s48UPk8ys+VmNquvuET6QwlBWsk+wH+Y2avM7DQz+62ZrYi/B8xspZn9fzP7YGGaq4ALAOIi/zbgp1Xm/TbgxkgKnwTOj/47gZ3R/0fAgpjXy2O5twDHAZ8HFgMvNLObzexWM7u4ynL6Gw/AqcDD7r4jPp8LnAI8p49t9GOg3czGxeezgFvdfXV5RDN7o5ldUep3qpndb2a/N7Nn9LEcGYVM7RCklZjZSaQL6sleODjN7AF336807jXA/qQvNt3AJGA/4G5go7sfXxh3PPBDUjLoAJbGtKuAt8Yf7n5OjD8BOComvxD4QXS/BvgYcCzwdHc/c3fiifFXAOe6+61m9jTg34HTgKuB17n7PTHeecA7gU1A+YSdCOyI5U0BrnP395vZy4FvAT9y97fFfPYCfgecAGwDvufu7YiEXm9NRZrB3f8b+O9+jn4ocKy7rzOzi4AH3P3rAGa2Lo9kZmOAne7+skK/o919u5nlC+zHga2FeR8MfBe4DDgC+CMwDtgbWBjjXL078cTn04FnAg+Z2YGxrL919z+b2TuBn5nZ+e5+tbt/1sy+ChSLhdzd7yvM7yhga04iwNuBf6KS1ABeDPzc3e+Mae43s8Pc/Y+IoIQgLcLM3gQsAgz4f+7+iX5M1gX8wsy6SMVNO8zs3VXGezVwnpmd4+6/iiKn95nZI0An8PUYb76ZjXX3LtKFfxapWGdv0rfqnBBOjfFPNbO17r5sIPGY2T7Ap4E/APOArwIXAQ/HKI8DHwA+Zmbr3f3nwBtJdwm/inHeAEw3s8XAecDrgAeAnBBOI+56CvYDflP4fB8piSkhCKA6BGkR7v4td38K8BlgfD8nGwOcGMUei4GPu3t7uRjE3a8GvkAqo4dUb3CZux9T/AO6Ixng7r8AjibdNVwW83wXcL27LwDOIBXftA00HmAO8FngXtIF/GRgJumiD6keoR2YH8kAYDuwPKb7LKn4CFKiclIy6iqsc7Wy4DZgQ+HzZmBGlfFklNIdgrSi/lZsjQP+y8w6gbnA9rjT2HWG7t8zs1wU5cDZZnZGteWa2UHA50gX2/HAJDO7GZgATIuy/27gNmDlQONx99uB26Oc3939T2Y2CXgoRukEdrh7dym2duDs+Dy20L+/22stPRPApFgPEUAJQVpUPHo53d3X9DaOu88rjH8RhTL7KvNrA+4ws2Oi17+4+0WlcTqj8z7gm8AVwKvc/R1m9gLgSHf/spmdCpzq7mcXpx9IPFXMAu6oMc7dwH9F9+n9nG/RCuAjhc/tpIp2EUAJQVrTNNKF7wfA18oDzezIGL6t0PtJpMdHz8ujAZNJFbVLgFcAq9x9dbQze3dc2HcR38x/ktsTRMO0DwI/NbP5pG/VxSegdieesoXAl6pujYq9gCOjOxf3Toi//rgNmGdm74jpD41+IoASgrSePYFzgA+6+9fMbD9SBe2UPIK7/5ZUJPMEM/sn4H53r5ZA2oCPAp+KXmNI9QIXFcYxUt1C0a9iud+JuK6I7qcAl5nZDHdfN9B4CsbFot8HPOjuD+TJq4w7FrjN3b8Y8/8AgLsfVFjHPouO3N3N7FXAl4HpwGsKbSBEVKksLacNOD1f+EjFGleTKmn7Mpl0ga3mqaSL5bXxuUeltZlNAX4JXF/odympwvdsUpuFBe6+3t1fSrrbeCYpce1OPNk44BBSBfW7Cv2rfeufFnGZmf2UQmO3SA4nUXkCCQB3/2Zug1Dod5e7v8jdn+vuy2vEJ6OMGqaJAGY2zt13Fj5PIFXs6gSRUUMJQUREABUZiYhIUEIQERFACUFERMKweez0SU96ks+dO7fZYYiIDCsrV6583N1n92fcYZMQ5s6dy4oVK5odhojIsGJm99UeK1GRkYiIAEoIIiISlBBERARQQhARkaCEICIigBKCiIgEJQQREQGUEEREJCghiIgIoIQgIiKhbgnBzOaY2dLoPsDMbjSzG8xscfzq0zgzu8bMbjGzM+sVh4iI9E9dEoKZzQSupPI7uO8C/s7dXwDsDxxF+vnBle5+HHCamU2rRywiItI/9bpD6AJOBzYAuPuH3f33MWwW8DiwELgq+t1E+u1cERFpkrokBHff4O7ry/3N7HTgTnd/iHT38GAMWgPMqTL+WWa2wsxWdHR01CNUEREJDatUNrODgfOAf4hem4BJ0T21Wizuvtjd2929ffbsfr3OW0REdlNDEkLUKXwHOLNw57ASWBDd84F7GxGLiIhU16gfyLkAOAC41MwALiRVOl9nZscDRwC/bFAsIiJSRV0TgrsvjP/nA+eXh5vZyaS7hI+6e1c9YxERkb419Sc0o3L5qpojiohI3amlsoiIAEoIIiISlBBERARQQhARkaCEICIigBKCiIgEJQQREQGUEEREJCghiIgIoIQgIiJBCUFERAAlBBERCUoIIiICKCGIiEhQQhAREUAJQUREghKCiIgASggiIhKUEEREBFBCEBGRoIQgIiKAEoKIiAQlBBERAZQQREQkKCGIiAhQx4RgZnPMbGl0jzOza8zsFjM7s7d+IiLSPHVJCGY2E7gSmBK9zgFWuvtxwGlmNq2XfiIi0iT1ukPoAk4HNsTnhcBV0X0T0N5LPxERaZK6JAR33+Du6wu9pgAPRvcaYE4v/Xows7PMbIWZrejo6KhHqCIiEhpVqbwJmBTdU2O51fr14O6L3b3d3dtnz57dkEBFREarRiWElcCC6J4P3NtLPxERaZKxDVrOlcB1ZnY8cATwS1JxUbmfiIg0SV3vENx9Yfy/DzgZuAU4yd27qvXr73znXnBtHaIVERndGnWHgLs/ROWpol77iYhIc6ilsoiIAEoIIiISlBBERARQQhARkTBsE4KeNBIRGVrDNiGIiMjQUkIQERFACUFERIISgoiIAMM8IahiWURk6AzrhCAiIkNHCUFERAAlBBERCUoIIiICKCGIiEhQQhAREUAJQUREghKCiIgASggiIhKUEEREBFBCEBGRoIQgIiKAEoKIiAQlBBERAUZQQtCrsEVEBqdhCcHMZprZdWa2wsy+Gv0uN7NlZraoUXGIiEh1jbxDeDPwbXdvB6aZ2QeBNnc/FjjYzOY1MBYRESlpZEJYDRxpZjOA/YGDgKti2BJgQXkCMzsr7ihWdHR01FyAio1ERHZfIxPCzcCBwLnA74HxwIMxbA0wpzyBuy9293Z3b589e3bDAhURGY0amRAuBN7t7hcDdwFvACbFsKkNjkVEREoaeRGeCRxlZm3Ac4BPUSkmmg/cOxQLUbGRiMjuGdvAZX0SuIJUbLQM+AKw1Mz2AU4BntvAWEREpKRhCcHdbwOeVuxnZguBk4FL3H19o2IREZFdNfIOYRfuvpbKk0YiItJEqsgVERFACUFEREK/E4KZnVHPQEREpLn6lRDM7DTgVXWORUREmqhmpXI8CfR24FNmdguwOQ8Cprm7HhcVERkB+kwIZvZlYBZwqrvvBI5rSFQiItJwtYqMlgJ7Ay83sz3N7K1m9mIzO6IBsQ0JtVwWEemfPhOCu38feDHwQuAcoBM4AHiTmS01s+fVP0QREWmEWkVGz3f3m4CzzeybwGXu/mgM2wu4BLi17lGKiEjd9ZoQ4iV0rzezS4DfAd3AJ82sONqwaMcw94JrufdTL2t2GCIiLa3XhODuXcDfmdkkUnHRG4DzgRUxyhhgQt0jFBGRhqj52Km7bwUuMbMfAjPdfXX9wxIRkUbrd5GPu98dbyzFzNRITURkhOlPw7R9gEMLve4GPgj8uF5BiYhI4/V5h2BmewCnAe8EvgmcDrwS2F73yOpIbRNERHbVa0KIx0pXkRLAZcCfgE83KK66U1IQEemp14Tg7o8BRwF/Bo4BZpB+5vIwYIaZnWBmJzUkyjpRUhARqajVUnkL8AfgaGAP4FnAIdF9AnBivQMUEZHG6Kth2jTgu6T6gs8BBwGXAqcA09394oZEKCIiDdFXkdFG4B3A/wLvBuaRGqaNWCpCEpHRrFaR0cPA90l3Bq8FriQ9blrzcdXhSklBREar/rRUfgh4qNjPzD5et4hERKQp+v1N38xeCEwEbnD3JfULqbXkOwa9HE9ERrpaDdMWm9lyM3sTsIjUSO38GDbOzL7egBhbgoqSRGSkq3WHMA94F3AS4MC5wF1m9hLg14BedCciMkLUerndbNKvpR0V3ScDf3D35wL7k34gZ0DM7Ctm9orovtzMlpnZooHOp5nmXnCt7hhEZMSplRAMmEz63YM2YBpgZvYPwKKBvgrbzI4H9nb3a8zs1UCbux8LHGxm8wYevoiIDJVaCaGL9ITRY8BGd78y+m8ArhjIRdzMxgFfA+6N12cvBK6KwUuABVWmOcvMVpjZio6Ojv4uqmF0lyAiI0lfL7fLdwdHkOoSnmxm3wXc3b8B/D3wjwNY1ltIP8V5CfBs4D3AgzFsDTCnPIG7L3b3dndvnz179gAWJSIiA9VXS2UH/gp8m/Sm00dIFclHm9kNpBfeHTWAZT0dWOzujwDfAm4CJsWwqX3FIiIi9VfrIrwM6AA2AiuBz5CKkF7i7v8KPGpm/W3LcA9wcHS3A3OpFBPNB+7td9QtSkVIIjKc1bqY/xDYBPwzsBewD+lFdweY2Q7gNHfv7OeyLge+YWZnAONIdQg/iV9kO4X0au1hb+4F16oRm4gMS7USwmzgS6RfS3sr6bcQbibdKdxK+pZ/TH8WFC/Le22xn5ktJD3Keom7r+9/2CIiMtT6TAjufp2ZrQKOAy4GPuzuf2Nmv3D3U83sDDOzqG8YMHdfS+VJoxGpWIykOwcRaWW1Xl3xIOmO4NJqw939u7ubDEYz1TWISCuqdYewr5mNB15RHlS/kEYHvTRPRFpNrTuEBcBy0mOh7wcOM7NvAE81s29EtwwR3TmISDPVqlTuBl4GbAZui88Anya91mLE/lBOs+gpJRFpllrtEO4kPQG0AdiX1EDtLcCRwH3u/ts6xzfq6a5BRBqlVkJ4P3C1mc0BLiTdIfwAOBr4rZld2dfEMjTKb1dVkhCReqhV5HORu3eb2f7Aue7eDawCVpnZPwPPrHuEUpUqpUVkqNV6yqg7/v+V9F6j4rDtpMZpIiIyAuiFciOIfrhHRAZDCWEEKtc3KFGISH8oIYxSShAiUqaEMIopKYhIkRKCPEFFSyKjmxKC7EJJQWR00qsnpE96fbfI6KE7BBERAZQQZDepvkFk5FFCkCGjJCEyvCkhyJCr1jBORFqfEoI0RDkpKFGItB4lBGkZShIizaWEIC1HRU4izaGEIMNCtSInERlaapgmw1b5R4LUiE5kcBp+h2Bmc8zs9ui+3MyWmdmiRscho4fuJkT6pxlFRp8FJpnZq4E2dz8WONjM5jUhFhkl9JSTSG0NTQhm9gJgM/AIsBC4KgYtARY0MhYR0I8JiRQ1LCGY2XjgI8AF0WsK8GB0rwHmVJnmLDNbYWYrOjo6GhOoSImShIwWjaxUvgD4iruvMzOATcCkGDaVKsnJ3RcDiwHa29u9QXGK1FStArtcyS0y3DSyyOgk4D1mdiNwDPAKKsVE84F7GxiLSN2ovkKGq4bdIbj783N3JIVXAkvNbB/gFOC5jYpFpNn0iKy0oqa0Q3D3hQBmthA4GbjE3dc3IxaRVqEkIc3W1IZp7r6WypNGIlJFX/UVxX4ig6VXV4iMIKqvkMHQqytERjjdTUh/6Q5BZBTSnYRUo4QgMorpEVkpUpGRiPRJjfBGD90hiMhu0x3GyKKEICJ11dtLA5U4Wo8Sgog0TW93GEoWzaGEICItTQmicZQQRGRYUH1F/ekpIxEZMdQIb3CUEERkRFOS6D8VGYnIqKQip13pDkFEJIz2uwndIYiICKA7BBGRPo2muwbdIYiI7IaRWAehhCAiMkSGe4JQQhARGULD+c5BCUFERAAlBBGRuhsudw1KCCIiTdCKSUIJQUSkiVopKagdgohIi2h2mwfdIYiICNDAhGBme5jZ9Wa2xMx+aGbjzexyM1tmZosaFYeIyHDTqPqGRt4hvBH4vLu/CHgEOANoc/djgYPNbF4DYxERkZKG1SG4+1cKH2cDbwK+GJ+XAAuAuxsVj4iI9NTwOgQzOxaYCfwVeDB6rwHmVBn3LDNbYWYrOjo6GhiliEhrq0cxUkMTgpntCVwKnAlsAibFoKnVYnH3xe7e7u7ts2fPblygIiLDxFAmhUZWKo8Hvg98yN3vA1aSiokA5gP3NioWERHZVSPvEP4WeAbwYTO7ETDgzWb2eeB1QOu0zhARGYYGe7fQyErlfwP+rdjPzH4CnAxc4u7rGxWLiIjsqqkN09x9rbtf5e6PNDMOEZGRYjB3CWqpLCIywuxuUlBCEBERQAlBRESCEoKIiABKCCIiEpQQREQEUEIQEZGghCAiIoASgoiIBCUEEREBlBBERCQoIYiICKCEICIiQQlBREQAJQQREQlKCCIiAighiIhIUEIQERFACUFERIISgoiIAEoIIiISlBBERARQQhARkaCEICIigBKCiIiEpicEM7vczJaZ2aJmxyIiMpo1NSGY2auBNnc/FjjYzOY1Mx4RkdHM3L15Czf7MvBTd7/OzM4AJrn7FYXhZwFnxcfDAStMvh2YUGW21foPdtzhPv1wilXr2prLavb0inX3px/r7hOrjLOLsf0ZqY6mAA9G9xrgGcWB7r4YWJw/m1lXYbAB1VayWv/Bjjvcpx9OsWpdW3NZzZ5ese7+9NuqDK+q2XUIm4BJ0T2V5scjIjJqNfsCvBJYEN3zgXubF4qIyOjW7CKjHwFLzWwf4BTguTXG/1Wh+26gWiV0tf6DHXe4Tz+cYtW6tuaymj29Yt396e+uMryqplYqA5jZTOBk4CZ3f6SpwYiIjGJNTwgiItIaml2HICIiLUIJQUREgGGYEMzsOTWGTzGzag01hgUzm9zsGGRkMrM2M9vLzPY2s7Zmx9MoZjat2TEMFy1dh2BmhwOvAV4EjAOeRXoyyqm0Wrb47EA3sJXUpqGT9BjrHjHeRmBujNMZ87PC500xj52kFn57xPAxwCPAjOjuJjX42BnDDdgQ89sOdMW0O2KZ+wAdpCekuoETYtqZwBZgMnBXxHAgMA24B/g58DxSO419Y5mdVNptbIj5jQHGR+wAf4hhhwJPjjjGR1wW887zzF8IdgL3xfhbI9Y5pEeBt8T23Bqx3h/r9dTYZttj2dOBttgOO2JZ4yLGthhncyyvrbCdxxa2g0d8B0a8y4EDgP1iHbYBj0Wc3bHsTmB2jL+TSgvN8RHHJHru57HR/UhMtzP+xsU8JgJrSQ0l58a8NgD/E8NmxnaZFHFvjflPpnJsPExlf3psr/Hx/2FgL2BWzHtT9J8R43YVYumMbe8xXiewZ6xj3oadMe6dwLNj+z5K2r/LgVXAG0iNQCcWtkM+ZvL+2xLbvh1YHZ/3i3G3RUwTonsy8A3SuTmJ1Lh0j5jnLaRjb/+Y7+PA3tHdFvPeRjpepsXwabEOxP79fsR6TuyLKbGNu2J5G0nH6xGF9Z9A5fzIx8Ja4DfA8bFsIx2DU0nHRlth3O6IvyOGLyc97NIWw6ByXLfFsnaSjo05sX3GxLb4OvDeWIf7IraJsd3Wxbh7xzxy/FC5vkyN5eyI7TU+xoPK9W9LbPPJMd590f/QGLYllpOvC69x91upoWUTgpktAV4YH4fdnYyISAvpBC5y90/0NVIrJ4Sc4TaQvhXlb8My+uRv9rureEfZjOlFmiEftw4sBXD3E/qaoJUvsFtJt5a5rLOesTYzK7ZaRu6uPcqQ6e+6D7YB5WAv5koGFb0dHzsaGoX0R7FYvYt+XENbOSGcSioXM1L5aRfpArKV9I0xl/dDpfyfwrDOQr8u0p1GlstR15HKI7tIB3Qu0ysf9NUuXN2F/t1V/oov4sux9HYB3EEq88vlhB7xeuGzA3eU1rOzyjibCvEXh22g7xjy+hgpEecTvNr42+k577JcJ5OH521CqV9fF9o8Tnk7dpeGe2mavuZXXn8nrWuOL5fh57L58vycVD6fdRfG98L/8n4pyvVMxf47Sp9zvUa16cvrkI+Z8j4nlrOqyjy66Ft3LL+r8D8vZ0xhnLy8O6nUe2yjsn7Vtnc5xmrn2l9J52Y57p3sqnhu5W2f90OeX3F9y/MsnrPlZRXrJvN8dxT6FafL/7eQ6im66Lmfu6vM78/0PIag57rka9LOmO8fCvPaWFp2d2n6vHxi3HuAN1NDyxYZZWZ2MfABUmXPHaSK5Zmkipq8YdeQKpN+BRxDqpTpjnEeonKy7kX6trmZVBmT57EcOCjmO4nKznucdHAeQ6Wybx2pCGsLqcJnGanyaRrpZNkMrI/5TyFVbC0nVRA/KcbJO3sClRPqcCqVv7mIZCeVirjJ8UfEdEAMHx8xTSRdrPaj50G2hlS5mS++22Iao+eJsyNi94h7avTfHOu9lVQR2EW62E+nUrE7DvgdqZJvLKnidP+YZmysw+b4vJlUobozljOmsE+mRXw5tt/H/jiUSqX6n0iVylOonES5gnVdrO8eVCrNixey/BBAd4w3k1S5nCs1x8T/x2N7bCUdF1OB24AjYznTI+bHSMdUrmTspFJRnJe9vbCt7oruQ6hcOMfFuv2ZdNIeE3Hl5JwrPHfQ80EHYtg20jG2PrbT+IgtV0rmSu09Ipbt0d1FOob/AjyFyoMSUHnpZK5kz9ssx/M48C+kY/pF9HzI4mFShekmKpXBmyOGVcBLC/uyO8b/I+m8nhLxbSSdK53RPSm209jSftwOfA04mPROtL+QXtewjnTeT475Q3qAANL5sTPmk+eV499G5ZztJj0Qks/XfK7l42JsjL+cVNc5jp4Pq4yN+Iox74xpx5OOk5wwNpIe1ngalYrkvWJ+eV9PiLg2x7rNI12H8jhdMV9IX/6WkI7byaRXBC0Hfuk1LvgtmxDM7GjgWtLJP2oekRMRGUI5ma0iJY6T3H1jbyO3cpHRL6g89gatV9Yug6P9ObI0Y3/qGKot31HuTSrNeHFfI7dyQjiCtMPHxedq5c0DOSB6G7e/8xhpB9/2Ji9fFbUjSzP2p46h3uX6lNwOaBZwEnBzXxO1bEJw90dJr8TeEr3KFYiw6wHRV4Vpb/JjWb1VIva2rL6UKw2ryRWag1WrgrC3iuQJheFQff0Ha7DJtlzZOtjlVRtervTsq+K9L8V93ttyeqtkLX7uaz/0Nm2erpZaFdT9levABjIPp/cvIb2VAtT6DLWfivMq4wx0nXf3vKhW0T/Q+Rbjr1ZR7uy6jnm8NtI1fhvwA+Cltd4o3cp1CKtIFWz6FiDNlL+ItFFp9S31obZGQys/6DAGuMvd59eaoJU3fv420Z9vPiLZUH/DGUPloQYlg/pq5etRK+vtmN9KKgkw4Cgz+3CtGbXyDjiVyqOERa15SyMDVa/9qDtKGW16O+anUXkEdgvpEeE+tWxCiDqEF5CeE8/PYefn6zdQuXMolj/uID1fXm7k85tqi6Dy3PV6Kg2pqjV2qlZ/4VTqN8r9y3c11cqOPZabVWu8VVx+uX/u3lgltuIycsOrovvpveyxXIbdV7llsX9u07C1MG1+2dwmevLot6HUv7fy4HLZfLnhTXG84nqVt8vWUuxbCtMV/5cbG1WLBSov6xuoanFXKycuLy9/LvfrJu3nzey67/J5U21dthamLbZJqdbocmfhczHmYiM0SuOUYyzGlZXPla4q43qVcaudY9299F9eiqPa+ZLr83I7m2oNvcp1Wk5qG1BWPE/K22I9qb3JFnoep3lfFef/GOkFneXtW3whXrVGorm+ZnvMfz2Vthd9atk6hKJ4nfX3gOdQaRxyJ7DI3Zeb2fHAe939tBh/LKkh0SGkxmxrqey4bcAVpIZTHwM+CjyD1MjjKlIDlwXA86k0FFtPSiqfjr/XAq8EFpIaf+xLakiSG1ZBpaHTJCqv4MgN14z0O6czSG0tlgGXkBpiHRHj7x/j5wt+biC2KpZzeKH/LbF+k6k0PFtA+lnSc8xs/1jP2aQD8dyI7wLSAfOGWI8JsYxcXp4b7+S3Qu6IbfFH0sVkXMSxJ6lR3D2kpxiWAZ8Efubui2J//CPwIVIjIKgkn7yc22Lar0aszwZ+RrpTzI/NEdPnhj45qefkuox0Ek0DFgHvBP6V9ObJbuAw0jE0g0pjuztjm70lYr6T1NDwECpFRF2kxmJjYhvlt5cuB74EfAW4AXgV6S2gjwHnUTkenJ4NxvJFdEf8fTHm/eyIL2+jLTF+fivsOFIDtseBL5COuUXANcAWd39v/CTtZ0jfBvOxOINKMnwMWExqzPW82G5TYtssiXiuJtXfzY11BXhHTJ/f5DuR9FbPacDHgUuBE0nH41hSQ8WJEdvrY15dMayTyhsHHot1nxvrmr+QFBuL/Qa4GPj32E+fI70NeAnpfNxCevvu+th/c0iPrE8iNfKaFsvbM5b/9Vjm4thP44AHgOuBM2N77EFq/LkPlbco7yBdoPciNZx7hNRIdFJsz7VU3nw6mUrjxC2kN7j+OLZrboSXz+lpsYy1sT2mR/frgNtjuYeTGqQ9K7bLY7FfumId941+3yFdz/6TdEyeH9vof4BXu/u99GFYJIQiM7uJtLE3kFY8t6jN35bz7dPt7v6CJsT0RG/AhyqGei2jEbE300hYv8Guw0B4AxkAAAVwSURBVFBvg17mB2mGu7VNhyLGVtrXEcvTSdel/Ar2fLHdCPx6MHGV5p/vigZ97RsWCcHM8jvEt5NWOmdXSN+Yn0/KorkiehqVF6IV3yOeFVe6+I1tbHT31TI63z7+mnTgPb/G+LU4lR1Z7K4WK7HMPegfJ9XBzKgy391VLEpoq9I/b79uKu9nKu6PWvPurb1Jf+Ivb8ss96v1FEt/l9PKah1P/Zm+vG8HopPKbwaMJPl4GuzxUXw8Of9GQy4G7+95UusaVZavA3fUTBTu3vJ/pHd8dAD7xucZwNRC94mk2+g8fA7pda/Ffk+LzzeQigOuJ92azYnuQ0i3VUtIt+6rSbdtq/M8CvHMKUyf57uCVH9xQ2FeNxb+lpSWUV5eB+m2r0fspWXNIL1bZ2nEtTTmfUuV9c/zzjGuLsQ3vbSd1gC3luItx319lf/PLmzT6aXtU1x+Xs460sVmXZ6mMG5eVt4W86ssa3VMm2+hn1Zl3PJ+n0MqXjghxs3HytLSuhXjXA2spFJEtoNUHHVbdL8x/t9U2I95H8wvfT6RnsdSHt7b9s3T5v2bu1cUpl9aGv7E+paO9ZsLy9ifdOzk+a+hUtRQPMaK+6P4l+O4AXhJrP8x5fOzMK+8v9YUtuUyqh9by2P4WtL7yNbEX95H00vzzfuvHFc+R55V2OZrC/O6sTS/8nqWz6P8RfMmep4zy9l1/y8pbJc74n97bJODgP8trk+N82R1LKN8DShfMx4nlZLka8Iu222g19phcYcAYGYHAtuisrnmcDObQXoP0ppCvwNJZbkPE2WZ7r4pxs0vlJtCugjMij+AB8vLzdPE9Hm+uXz04cK8itaXllFe3jZ3f7Qce3FZpXXL5ZOQDqg1pfWfAqwvxDiL+NWuGvMqy7F2Vvk/K2/TPM9elp+XcxDpTu7+PE1h3LysWaSy4+1VljWLVEz4V3f/S97vpXHL+31GYfptpPLp8bG+T6xbKc5OUr1BrlNZR+UCmn9Vy0hl+nk/TiedkNtLnydSOJYKw3vbvnnavE+mF4bl+T+5NHxN8RgtHJN5n64vbevpxK+dlY+x0v4oynE8TNqPm9z9L4XlFY/f4v7aGNtyW2zDorzuh0W8j8bn/MtoW0qxF4+54rHfSc/jOh/v2UZ63xbldSweO0cR+7l0zhwW61Pc/+tju+QX5nUUtk+PY7w0r2rnyRP7u7RNa10zdtluDMCwSQgiIlJfLfvYqYiINJYSgkiBme1vZv2qODSz6YXuOfWLSqQxBvvThCIjzXuAaWb2eVJ7lS2kOoQ20tMaN7j7J8xsH9Lz7C81s8nAMjN7prtXa6gkMiyoDkGkIBrRXQP8faFCcCGw0N0vis/7khonLiQ9yXM46QmQnwBXu/sDhfnd4+6HRvc3SQ2TOoGvuvvXGrJSIv2kIiORAnfvdPdTcjLoxX6kRwo/S3ra5Dek1tXtpJ+x7MvZpB8puTB+FVCkZajISCSY2fuAvwFmu/tT+hi1m/RqkL1Jr1x4Jum1FYeSipH65O6rzexa0nPu1d6zJdIUukMQCe7+eXc/nvRem76MITUUegfpB8w/E93XD2Bxq0mNqERahu4QRAbuftKrS14CzCMliBnR74/9nMee1E48Ig2lhCBShZk9HTjC3b9d6v9i0htG8+uHD4y/Z8Tnt5rZJe7+wz7mPYP087BfHPLARQZBCUFkV/uSXi/9RjMbR3qdgQO4+89Ir+UGwMwuJL1m/Bf9nPelpFdPnO/udw1p1CKDpIQgUhCNzW4H3unum+OC/3rg7b1MMpH0vqOq8iOn0f22IQxVZMipHYKIiAB6ykhERIISgoiIAEoIIiISlBBERARQQhARkaCEICIiAPwfZ7dRbMH/m6YAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#哪个订单ID平均消费最贵\n", "Group_sum['average'] = Group_sum['total_amounts']/Group_sum['counts']\n", "sort_average = Group_sum.sort_values(by='average',ascending=False)\n", "sort_average['average'][:].plot(kind='bar')\n", "plt.xlabel('订单ID')\n", "plt.ylabel('消费单价')\n", "plt.title('订单消费单价前10')"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, '点菜数与小时的关系图')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#一天当中什么时间段，点菜量比较集中（hour）\n", "data['hourcount'] = 1 # 新列，用作计数器\n", "data['time'] = pd.to_datetime(data['place_order_time']) #将时间转换成日期类型存储\n", "data['hour'] = data['time'].map(lambda x:x.hour)\n", "gp_by_hour = data.groupby(by='hour').count()['hourcount']\n", "gp_by_hour.plot(kind='bar')\n", "plt.xlabel('小时')\n", "plt.ylabel('点菜数量')\n", "plt.title('点菜数与小时的关系图')"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, '点菜数量与日期的关系图')"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#哪一天订餐数量最多\n", "data['daycount'] = 1  \n", "data['day'] = data['time'].map(lambda x:x.day)  #解析出天\n", "gp_by_day  = data.groupby(by='day').count()['daycount']\n", "gp_by_day.plot(kind='bar')\n", "plt.xlabel('8月份日期')\n", "plt.ylabel('点菜数量')\n", "plt.title('点菜数量与日期的关系图')\n", "#拓展：排序，取点菜量最大的前5天\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, '点菜数量与星期关系图')"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#查看星期几人数最多，订餐数最多，映射数据到星期\n", "data['weekcount'] = 1\n", "data['weekday'] = data['time'].map(lambda x:x.weekday())\n", "gp_by_weekday = data.groupby(by='weekday').count()['weekcount']\n", "gp_by_weekday.plot(kind='bar')\n", "plt.xlabel('星期')\n", "plt.ylabel('点菜数量')\n", "plt.title('点菜数量与星期关系图')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 4}
4.3.3 用卡方检验来合并箱体画出IV曲线

因为有每个箱子中属于0的个数/count_0和属于1的个数/count_1，就有了后面6个指标，换句话说，后面的6个指标跟前面2个count_0和count_1有线性关系


------------------------------------------------

num_bins_ = num_bins.copy() # 为了不影响num_bins，要copy()
  
import matplotlib.pyplot as plt 
import scipy 

num_bins_ 

------------------------------------------------
每个箱子中年龄的下界、上界、属于0的个数、属于1的个数
(21.0, 28.0, 4232, 7616),
 (28.0, 31.0, 3534, 5850),
 (31.0, 34.0, 4045, 6796),
 (34.0, 36.0, 2925, 4766),
 (36.0, 39.0, 5269, 7477),
 (39.0, 41.0, 3977, 5670),
 (41.0, 43.0, 4044, 5650),
 (43.0, 45.0, 4404, 6005),
 (45.0, 46.0, 2345, 3247),
 (46.0, 48.0, 4851, 6360),
 (48.0, 50.0, 4882, 6138),
 (50.0, 52.0, 4658, 5815),
 (52.0, 54.0, 4661, 4892),
 (54.0, 56.0, 4507, 4057),
 (56.0, 58.0, 4525, 3467),
 (58.0, 61.0, 6662, 4884),
 (61.0, 64.0, 6959, 3136),
 (64.0, 68.0, 6608, 2303),
 (68.0, 74.0, 6776, 1936),
 (74.0, 99.0, 7703, 1360)]

------------------------------------------------

# 进行切片，提取每个箱子的count_0和count_1

x1 = num_bins_[0][2:] # 第一组的后面那个数 ，即4232, 7621
x1 # 返回的是第一组属于“0”的有4232个 ， 属于“1”的有7621个

------------------------------------------------
(4232, 7616)

x2 = num_bins_[1][2:] # 第一组的后面那个数 ，即3534, 5825
x2

------------------------------------------------

(3534, 5850)

------------------------------------------------


# 把x1和x2带入卡方的目的是为了相近的2个箱子，合并成一个箱子
scipy.stats.chi2_contingency([x1,x2])

------------------------------------------------
(8.418558526369752,
 0.0037141013800784588,
 1,
 array([[4333.62697815, 7514.37302185],
        [3432.37302185, 5951.62697815]]))

------------------------------------------------
scipy.stats.chi2_contingency([x1,x2])[1] # 返回的是p value
------------------------------------------------
返回的是一个数，咱们的目的是找出箱子中置信度最高/相似性最高的两组箱子合并在一块儿
0.0037141013800784588



------------------------------------------------

pvs = [] # 是为了存放卡方检验的变量明称的列表
# 因为箱子有20个，两两比较的话就是19pv，故len(num_bins_) - 1
for i in range(len(num_bins_) - 1):
    x1 = num_bins_[i][2:] # 第一次是第一组和第二组提取卡方
    x2 = num_bins_[i + 1][2:] # 第二次是第二组和第三组提取卡方
    # 1 返回 卡方检验的变量明称。 
    pv = scipy.stats.chi2_contingency([x1, x2])[1]
    pvs.append(pv)
------------------------------------------------

pvs # 返回第一组箱子和第二组箱子的pv、第二组箱子和第三组箱子的pv
------------------------------------------------
0.0037141013800784588,
 0.6205926544542001,
 0.32670395584075784,
 3.1833573861938186e-06,
 0.8754986303246977,
 0.49727166997825756,

------------------------------------------------

 max(pvs)
 0.8754986303246977
 ------------------------------------------------
pvs.index(max(pvs)) # 既4044, 5614和4404, 6016进行合并了
4





- 如何合并呢？

- 比如说，第一组和第二组相邻的两个箱子pv值最大，也就是说第一组年龄的下限到第二组年龄的上限进行合并，对应的count_0和count_1相加
num_bins_[3:5]
[(34.0, 36.0, 2925, 4766), (36.0, 39.0, 5269, 7477)]

num_bins_[3:5] = [(num_bins_[3][0],
                   num_bins_[4][1],
                   num_bins_[3][2]+num_bins_[4][2],
                   num_bins_[3][3]+num_bins_[4][3])]

num_bins_
------------------------------------------------
(21.0, 28.0, 4232, 7616),
 (28.0, 31.0, 3534, 5850),
 (31.0, 34.0, 4045, 6796),
 (34.0, 39.0, 8194, 12243),
 (39.0, 41.0, 3977, 5670),
 (41.0, 43.0, 4044, 5650),
 (43.0, 45.0, 4404, 6005),

------------------------------------------------
 num_bins_ = num_bins.copy()

import matplotlib.pyplot as plt
import scipy

IV = []
axisx = []
# while不知道要循环几次的情况下使用
while len(num_bins_) > 2:
    pvs = []
    # 获取 num_bins_两两之间的卡方检验的置信度（或卡方值）
    for i in range(len(num_bins_) - 1):
        x1 = num_bins_[i][2:]
        x2 = num_bins_[i + 1][2:]
        # 0 返回 chi2 值，1 返回 p 值。 
        pv = scipy.stats.chi2_contingency([x1, x2])[1]
        # chi2 = scipy.stats.chi2_contingency([x1,x2])[0]       
        pvs.append(pv)

    # 通过 p 值进行处理。合并 p 值最大的两组
    i = pvs.index(max(pvs))
    num_bins_[i:i + 2] = [(num_bins_[i][0],num_bins_[i+1][1],num_bins_[i][2]+num_bins_[i+1][2],num_bins_[i][3]+num_bins_[i+1][3])]

    bins_df = get_woe(num_bins_)
    axisx.append(len(num_bins_))
    IV.append(get_iv(bins_df))

plt.figure()
plt.plot(axisx, IV)
plt.xticks(axisx)
plt.xlabel("number of box") # 特征中的箱子个数
plt.ylabel("IV") # 年龄的IV值
plt.show() # 取箱子为6时比较好，6为折点 ，age选取为6为最佳的
 
# 随着箱子的越多，IV值就越大，该特征就不能用
# 从19到12的时候下降并没有特别的明显，在7的时候出现转折点，到6的时候，IV值就下降了












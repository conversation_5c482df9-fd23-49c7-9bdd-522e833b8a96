<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>大屏展示</title>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/jquery.min.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/jquery-ui.min.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/ResizeSensor.js"></script>

            <link rel="stylesheet"  href="https://assets.pyecharts.org/assets/v5/jquery-ui.css">

</head>
<body >
    <style>.box {  } </style>
        
    <div class="box">
                <div id="********************************" class="chart-container" style="position: absolute; width: 995px; height: 602px; top: 25.666667938232422px; left: 165px;"></div>
    <script>
        var chart_******************************** = echarts.init(
            document.getElementById('********************************'), 'light', {renderer: 'canvas'});
        var option_******************************** = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u7968\u623f/\u4ebf",
            "legendHoverLink": true,
            "data": [
                57.74,
                56.89,
                54.13,
                50.35,
                46.87,
                45.22,
                42.5,
                36.52,
                33.98,
                33.97,
                31.76,
                31.02,
                31.0,
                29.13,
                28.29,
                26.71,
                25.48,
                24.41,
                24.27,
                23.91,
                22.37,
                22.14,
                22.02,
                20.13,
                19.78,
                19.42,
                18.71,
                17.53,
                17.29,
                17.07,
                16.96,
                16.83,
                16.56,
                16.14,
                16.03,
                15.59,
                15.51,
                15.35,
                14.77,
                14.72,
                14.64,
                14.48,
                14.34,
                14.33,
                14.23,
                14.21,
                14.18,
                13.97,
                13.92,
                13.62
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c}/\u4ebf"
            },
            "itemStyle": {
                "normal": {
                    "color": new echarts.graphic.LinearGradient(0, 0, 0, 1, [{                    offset: 0,                    color: '#ee3f4d'                }, {                    offset: 1,                    color: '#eea2a4'                }], false),
                    "opacity": 0.8,
                    "shadowBlur": 8,
                    "shadowColor": "rgba(0, 0, 0, 0.4)",
                    "shadowOffsetX": 10,
                    "shadowOffsetY": 10,
                    "borderColor": "rgb(220,220,220)",
                    "borderWidth": 1
                }
            }
        },
        {
            "type": "line",
            "name": "\u573a\u5747\u4eba\u6b21",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "data": [
                [
                    "\u957f\u6d25\u6e56",
                    22
                ],
                [
                    "\u6218\u72fc2",
                    37
                ],
                [
                    "\u4f60\u597d\uff0c\u674e\u7115\u82f1",
                    24
                ],
                [
                    "\u54ea\u5412\u4e4b\u9b54\u7ae5\u964d\u4e16",
                    23
                ],
                [
                    "\u6d41\u6d6a\u5730\u7403",
                    29
                ],
                [
                    "\u5510\u4eba\u8857\u63a2\u68483",
                    29
                ],
                [
                    "\u590d\u4ec7\u8005\u8054\u76df4\uff1a\u7ec8\u5c40\u4e4b\u6218",
                    23
                ],
                [
                    "\u7ea2\u6d77\u884c\u52a8",
                    33
                ],
                [
                    "\u5510\u4eba\u8857\u63a2\u68482",
                    39
                ],
                [
                    "\u7f8e\u4eba\u9c7c",
                    43
                ],
                [
                    "\u6211\u548c\u6211\u7684\u7956\u56fd",
                    35
                ],
                [
                    "\u516b\u4f70",
                    20
                ],
                [
                    "\u6211\u4e0d\u662f\u836f\u795e",
                    27
                ],
                [
                    "\u4e2d\u56fd\u673a\u957f",
                    26
                ],
                [
                    "\u6211\u548c\u6211\u7684\u5bb6\u4e61",
                    19
                ],
                [
                    "\u901f\u5ea6\u4e0e\u6fc0\u60c58",
                    30
                ],
                [
                    "\u897f\u8679\u5e02\u9996\u5bcc",
                    28
                ],
                [
                    "\u6349\u5996\u8bb0",
                    41
                ],
                [
                    "\u901f\u5ea6\u4e0e\u6fc0\u60c57",
                    42
                ],
                [
                    "\u590d\u4ec7\u8005\u8054\u76df3\uff1a\u65e0\u9650\u6218\u4e89",
                    19
                ],
                [
                    "\u6349\u5996\u8bb02",
                    44
                ],
                [
                    "\u75af\u72c2\u7684\u5916\u661f\u4eba",
                    30
                ],
                [
                    "\u7f9e\u7f9e\u7684\u94c1\u62f3",
                    25
                ],
                [
                    "\u6d77\u738b",
                    18
                ],
                [
                    "\u53d8\u5f62\u91d1\u521a4\uff1a\u7edd\u8ff9\u91cd\u751f",
                    50
                ],
                [
                    "\u524d\u4efb3\uff1a\u518d\u89c1\u524d\u4efb",
                    29
                ],
                [
                    "\u6bd2\u6db2\uff1a\u81f4\u547d\u5b88\u62a4\u8005",
                    17
                ],
                [
                    "\u529f\u592b\u745c\u4f3d",
                    33
                ],
                [
                    "\u98de\u9a70\u4eba\u751f",
                    25
                ],
                [
                    "\u70c8\u706b\u82f1\u96c4",
                    19
                ],
                [
                    "\u4f8f\u7f57\u7eaa\u4e16\u754c2",
                    19
                ],
                [
                    "\u5bfb\u9f99\u8bc0",
                    40
                ],
                [
                    "\u897f\u6e38\u4f0f\u5996\u7bc7",
                    36
                ],
                [
                    "\u6e2f\u56e7",
                    40
                ],
                [
                    "\u59dc\u5b50\u7259",
                    19
                ],
                [
                    "\u5c11\u5e74\u7684\u4f60",
                    16
                ],
                [
                    "\u53d8\u5f62\u91d1\u521a5\uff1a\u6700\u540e\u7684\u9a91\u58eb",
                    23
                ],
                [
                    "\u75af\u72c2\u52a8\u7269\u57ce",
                    28
                ],
                [
                    "\u6211\u548c\u6211\u7684\u7236\u8f88",
                    16
                ],
                [
                    "\u9b54\u517d",
                    25
                ],
                [
                    "\u590d\u4ec7\u8005\u8054\u76df2\uff1a\u5965\u521b\u7eaa\u5143",
                    29
                ],
                [
                    "\u590f\u6d1b\u7279\u70e6\u607c",
                    33
                ],
                [
                    "\u901f\u5ea6\u4e0e\u6fc0\u60c5\uff1a\u7279\u522b\u884c\u52a8",
                    15
                ],
                [
                    "\u9001\u4f60\u4e00\u6735\u5c0f\u7ea2\u82b1",
                    12
                ],
                [
                    "\u82b3\u534e",
                    25
                ],
                [
                    "\u4f8f\u7f57\u7eaa\u4e16\u754c",
                    33
                ],
                [
                    "\u8718\u86db\u4fa0\uff1a\u82f1\u96c4\u8fdc\u5f81",
                    17
                ],
                [
                    "\u5934\u53f7\u73a9\u5bb6",
                    18
                ],
                [
                    "\u901f\u5ea6\u4e0e\u6fc0\u60c59",
                    13
                ],
                [
                    "\u540e\u6765\u7684\u6211\u4eec",
                    21
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8
            },
            "logBase": 10,
            "lineStyle": {
                "normal": {
                    "width": 3,
                    "shadowColor": "rgba(0, 0, 0, 0.5)",
                    "shadowBlur": 5,
                    "shadowOffsetY": 10,
                    "shadowOffsetX": 10,
                    "curve": 0.5,
                    "color": "#2486b9"
                }
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "normal": {
                    "color": new echarts.graphic.LinearGradient(0, 0, 0, 1, [{                                        offset: 0,                                        color: '#2486b9'                                    }, {                                        offset: 1,                                        color: '#FF00FF'                                    }], false),
                    "opacity": 0.7,
                    "barBorderRadius": [
                        45,
                        45,
                        45,
                        45
                    ],
                    "shadowColor": "rgb(0, 160, 221)"
                }
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u5e73\u5747\u7968\u4ef7",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 1,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": true,
            "clip": true,
            "step": false,
            "data": [
                [
                    "\u957f\u6d25\u6e56",
                    46
                ],
                [
                    "\u6218\u72fc2",
                    36
                ],
                [
                    "\u4f60\u597d\uff0c\u674e\u7115\u82f1",
                    45
                ],
                [
                    "\u54ea\u5412\u4e4b\u9b54\u7ae5\u964d\u4e16",
                    36
                ],
                [
                    "\u6d41\u6d6a\u5730\u7403",
                    45
                ],
                [
                    "\u5510\u4eba\u8857\u63a2\u68483",
                    48
                ],
                [
                    "\u590d\u4ec7\u8005\u8054\u76df4\uff1a\u7ec8\u5c40\u4e4b\u6218",
                    49
                ],
                [
                    "\u7ea2\u6d77\u884c\u52a8",
                    39
                ],
                [
                    "\u5510\u4eba\u8857\u63a2\u68482",
                    39
                ],
                [
                    "\u7f8e\u4eba\u9c7c",
                    37
                ],
                [
                    "\u6211\u548c\u6211\u7684\u7956\u56fd",
                    38
                ],
                [
                    "\u516b\u4f70",
                    38
                ],
                [
                    "\u6211\u4e0d\u662f\u836f\u795e",
                    35
                ],
                [
                    "\u4e2d\u56fd\u673a\u957f",
                    37
                ],
                [
                    "\u6211\u548c\u6211\u7684\u5bb6\u4e61",
                    39
                ],
                [
                    "\u901f\u5ea6\u4e0e\u6fc0\u60c58",
                    37
                ],
                [
                    "\u897f\u8679\u5e02\u9996\u5bcc",
                    35
                ],
                [
                    "\u6349\u5996\u8bb0",
                    37
                ],
                [
                    "\u901f\u5ea6\u4e0e\u6fc0\u60c57",
                    39
                ],
                [
                    "\u590d\u4ec7\u8005\u8054\u76df3\uff1a\u65e0\u9650\u6218\u4e89",
                    38
                ],
                [
                    "\u6349\u5996\u8bb02",
                    38
                ],
                [
                    "\u75af\u72c2\u7684\u5916\u661f\u4eba",
                    42
                ],
                [
                    "\u7f9e\u7f9e\u7684\u94c1\u62f3",
                    33
                ],
                [
                    "\u6d77\u738b",
                    36
                ],
                [
                    "\u53d8\u5f62\u91d1\u521a4\uff1a\u7edd\u8ff9\u91cd\u751f",
                    42
                ],
                [
                    "\u524d\u4efb3\uff1a\u518d\u89c1\u524d\u4efb",
                    35
                ],
                [
                    "\u6bd2\u6db2\uff1a\u81f4\u547d\u5b88\u62a4\u8005",
                    36
                ],
                [
                    "\u529f\u592b\u745c\u4f3d",
                    38
                ],
                [
                    "\u98de\u9a70\u4eba\u751f",
                    42
                ],
                [
                    "\u70c8\u706b\u82f1\u96c4",
                    36
                ],
                [
                    "\u4f8f\u7f57\u7eaa\u4e16\u754c2",
                    36
                ],
                [
                    "\u5bfb\u9f99\u8bc0",
                    36
                ],
                [
                    "\u897f\u6e38\u4f0f\u5996\u7bc7",
                    39
                ],
                [
                    "\u6e2f\u56e7",
                    33
                ],
                [
                    "\u59dc\u5b50\u7259",
                    40
                ],
                [
                    "\u5c11\u5e74\u7684\u4f60",
                    36
                ],
                [
                    "\u53d8\u5f62\u91d1\u521a5\uff1a\u6700\u540e\u7684\u9a91\u58eb",
                    37
                ],
                [
                    "\u75af\u72c2\u52a8\u7269\u57ce",
                    34
                ],
                [
                    "\u6211\u548c\u6211\u7684\u7236\u8f88",
                    43
                ],
                [
                    "\u9b54\u517d",
                    37
                ],
                [
                    "\u590d\u4ec7\u8005\u8054\u76df2\uff1a\u5965\u521b\u7eaa\u5143",
                    40
                ],
                [
                    "\u590f\u6d1b\u7279\u70e6\u607c",
                    32
                ],
                [
                    "\u901f\u5ea6\u4e0e\u6fc0\u60c5\uff1a\u7279\u522b\u884c\u52a8",
                    36
                ],
                [
                    "\u9001\u4f60\u4e00\u6735\u5c0f\u7ea2\u82b1",
                    37
                ],
                [
                    "\u82b3\u534e",
                    34
                ],
                [
                    "\u4f8f\u7f57\u7eaa\u4e16\u754c",
                    38
                ],
                [
                    "\u8718\u86db\u4fa0\uff1a\u82f1\u96c4\u8fdc\u5f81",
                    36
                ],
                [
                    "\u5934\u53f7\u73a9\u5bb6",
                    36
                ],
                [
                    "\u901f\u5ea6\u4e0e\u6fc0\u60c59",
                    39
                ],
                [
                    "\u540e\u6765\u7684\u6211\u4eec",
                    34
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8
            },
            "logBase": 10,
            "lineStyle": {
                "normal": {
                    "width": 3,
                    "shadowColor": "rgba(0, 0, 0, 0.5)",
                    "shadowBlur": 5,
                    "shadowOffsetY": 10,
                    "shadowOffsetX": 10,
                    "curve": 0.5,
                    "color": "#66c18c"
                }
            },
            "areaStyle": {
                "opacity": 0
            },
            "itemStyle": {
                "normal": {
                    "color": new echarts.graphic.LinearGradient(0, 0, 0, 1, [{                                        offset: 0,                                        color: '#1a6840'                                    }, {                                        offset: 1,                                        color: '#66c18c'                                    }], false),
                    "opacity": 0.7,
                    "barBorderRadius": [
                        45,
                        45,
                        45,
                        45
                    ],
                    "shadowColor": "rgb(0, 160, 221)"
                }
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u7968\u623f/\u4ebf",
                "\u573a\u5747\u4eba\u6b21",
                "\u5e73\u5747\u7968\u4ef7"
            ],
            "selected": {
                "\u7968\u623f/\u4ebf": true,
                "\u573a\u5747\u4eba\u6b21": true,
                "\u5e73\u5747\u7968\u4ef7": true
            },
            "show": true,
            "top": 50,
            "orient": "horizontal",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "shadow"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "rotate": 360,
                "margin": 8
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u957f\u6d25\u6e56",
                "\u6218\u72fc2",
                "\u4f60\u597d\uff0c\u674e\u7115\u82f1",
                "\u54ea\u5412\u4e4b\u9b54\u7ae5\u964d\u4e16",
                "\u6d41\u6d6a\u5730\u7403",
                "\u5510\u4eba\u8857\u63a2\u68483",
                "\u590d\u4ec7\u8005\u8054\u76df4\uff1a\u7ec8\u5c40\u4e4b\u6218",
                "\u7ea2\u6d77\u884c\u52a8",
                "\u5510\u4eba\u8857\u63a2\u68482",
                "\u7f8e\u4eba\u9c7c",
                "\u6211\u548c\u6211\u7684\u7956\u56fd",
                "\u516b\u4f70",
                "\u6211\u4e0d\u662f\u836f\u795e",
                "\u4e2d\u56fd\u673a\u957f",
                "\u6211\u548c\u6211\u7684\u5bb6\u4e61",
                "\u901f\u5ea6\u4e0e\u6fc0\u60c58",
                "\u897f\u8679\u5e02\u9996\u5bcc",
                "\u6349\u5996\u8bb0",
                "\u901f\u5ea6\u4e0e\u6fc0\u60c57",
                "\u590d\u4ec7\u8005\u8054\u76df3\uff1a\u65e0\u9650\u6218\u4e89",
                "\u6349\u5996\u8bb02",
                "\u75af\u72c2\u7684\u5916\u661f\u4eba",
                "\u7f9e\u7f9e\u7684\u94c1\u62f3",
                "\u6d77\u738b",
                "\u53d8\u5f62\u91d1\u521a4\uff1a\u7edd\u8ff9\u91cd\u751f",
                "\u524d\u4efb3\uff1a\u518d\u89c1\u524d\u4efb",
                "\u6bd2\u6db2\uff1a\u81f4\u547d\u5b88\u62a4\u8005",
                "\u529f\u592b\u745c\u4f3d",
                "\u98de\u9a70\u4eba\u751f",
                "\u70c8\u706b\u82f1\u96c4",
                "\u4f8f\u7f57\u7eaa\u4e16\u754c2",
                "\u5bfb\u9f99\u8bc0",
                "\u897f\u6e38\u4f0f\u5996\u7bc7",
                "\u6e2f\u56e7",
                "\u59dc\u5b50\u7259",
                "\u5c11\u5e74\u7684\u4f60",
                "\u53d8\u5f62\u91d1\u521a5\uff1a\u6700\u540e\u7684\u9a91\u58eb",
                "\u75af\u72c2\u52a8\u7269\u57ce",
                "\u6211\u548c\u6211\u7684\u7236\u8f88",
                "\u9b54\u517d",
                "\u590d\u4ec7\u8005\u8054\u76df2\uff1a\u5965\u521b\u7eaa\u5143",
                "\u590f\u6d1b\u7279\u70e6\u607c",
                "\u901f\u5ea6\u4e0e\u6fc0\u60c5\uff1a\u7279\u522b\u884c\u52a8",
                "\u9001\u4f60\u4e00\u6735\u5c0f\u7ea2\u82b1",
                "\u82b3\u534e",
                "\u4f8f\u7f57\u7eaa\u4e16\u754c",
                "\u8718\u86db\u4fa0\uff1a\u82f1\u96c4\u8fdc\u5f81",
                "\u5934\u53f7\u73a9\u5bb6",
                "\u901f\u5ea6\u4e0e\u6fc0\u60c59",
                "\u540e\u6765\u7684\u6211\u4eec"
            ]
        }
    ],
    "yAxis": [
        {
            "type": "value",
            "name": "\u7968\u623f/\u4ebf",
            "show": true,
            "scale": false,
            "nameLocation": "middle",
            "nameGap": 70,
            "gridIndex": 0,
            "axisLine": {
                "show": false,
                "onZero": true,
                "onZeroAxisIndex": 0
            },
            "axisTick": {
                "show": false,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "formatter": "{value}"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "max": 57.74,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        },
        {
            "type": "value",
            "show": false,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": false,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#2486b9"
                }
            },
            "axisTick": {
                "show": false,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "formatter": "{value}"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "min": -100,
            "max": 100,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u7535\u5f71\u7968\u623f - top50",
            "target": "blank",
            "subtext": "\u56fd\u4ea7/\u8fdb\u53e3",
            "subtarget": "blank",
            "left": "center",
            "top": "0.8%",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "fontSize": 20
            }
        }
    ],
    "dataZoom": {
        "show": true,
        "type": "slider",
        "showDetail": true,
        "showDataShadow": true,
        "realtime": true,
        "start": 20,
        "end": 80,
        "orient": "horizontal",
        "zoomLock": false,
        "filterMode": "filter"
    }
};
        chart_********************************.setOption(option_********************************);
    </script>
<br/>                <div id="a44b727d9f0a43c8a95f445fe93662fe" class="chart-container" style="position: absolute; width: 1011px; height: 602px; top: 31px; left: 1165px;"></div>
    <script>
        var chart_a44b727d9f0a43c8a95f445fe93662fe = echarts.init(
            document.getElementById('a44b727d9f0a43c8a95f445fe93662fe'), 'light', {renderer: 'canvas'});
        var option_a44b727d9f0a43c8a95f445fe93662fe = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "pie",
            "name": "\u56fd\u4ea7\u5e74\u4efd",
            "colorBy": "data",
            "legendHoverLink": true,
            "selectedMode": false,
            "selectedOffset": 10,
            "clockwise": true,
            "startAngle": 90,
            "minAngle": 0,
            "minShowLabelAngle": 0,
            "avoidLabelOverlap": true,
            "stillShowZeroSum": true,
            "percentPrecision": 2,
            "showEmptyCircle": true,
            "emptyCircleStyle": {
                "color": "lightgray",
                "borderColor": "#000",
                "borderWidth": 0,
                "borderType": "solid",
                "borderDashOffset": 0,
                "borderCap": "butt",
                "borderJoin": "bevel",
                "borderMiterLimit": 10,
                "opacity": 1
            },
            "data": [
                {
                    "name": "2019",
                    "value": 11
                },
                {
                    "name": "2018",
                    "value": 8
                },
                {
                    "name": "2021",
                    "value": 7
                },
                {
                    "name": "2017",
                    "value": 6
                },
                {
                    "name": "2020",
                    "value": 6
                },
                {
                    "name": "2015",
                    "value": 5
                },
                {
                    "name": "2016",
                    "value": 4
                },
                {
                    "name": "2012",
                    "value": 1
                },
                {
                    "name": "2013",
                    "value": 1
                },
                {
                    "name": "2014",
                    "value": 1
                }
            ],
            "radius": [
                "55",
                "100"
            ],
            "center": [
                "33%",
                "30%"
            ],
            "label": {
                "show": true,
                "margin": 8,
                "fontSize": 14,
                "formatter": "{b}: {c}"
            },
            "labelLine": {
                "show": true,
                "showAbove": false,
                "length": 15,
                "length2": 15,
                "smooth": false,
                "minTurnAngle": 90,
                "maxSurfaceAngle": 90
            },
            "tooltip": {
                "show": true,
                "trigger": "item",
                "triggerOn": "mousemove|click",
                "axisPointer": {
                    "type": "line"
                },
                "showContent": true,
                "alwaysShowContent": false,
                "showDelay": 0,
                "hideDelay": 100,
                "enterable": false,
                "confine": false,
                "appendToBody": false,
                "transitionDuration": 0.4,
                "formatter": "{a} <br/>{b}: {c} ({d}%)",
                "textStyle": {
                    "fontSize": 14
                },
                "borderWidth": 0,
                "padding": 5,
                "order": "seriesAsc"
            },
            "itemStyle": {
                "normal": {
                    "shadowBlur": 2,
                    "borderColor": "#87CEFA",
                    "borderWidth": 3,
                    "shadowColor": "#87CEFA",
                    "opacity": 1
                }
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "pie",
            "name": "\u8fdb\u53e3",
            "colorBy": "data",
            "legendHoverLink": true,
            "selectedMode": false,
            "selectedOffset": 10,
            "clockwise": true,
            "startAngle": 90,
            "minAngle": 0,
            "minShowLabelAngle": 0,
            "avoidLabelOverlap": true,
            "stillShowZeroSum": true,
            "percentPrecision": 2,
            "showEmptyCircle": true,
            "emptyCircleStyle": {
                "color": "lightgray",
                "borderColor": "#000",
                "borderWidth": 0,
                "borderType": "solid",
                "borderDashOffset": 0,
                "borderCap": "butt",
                "borderJoin": "bevel",
                "borderMiterLimit": 10,
                "opacity": 1
            },
            "data": [
                {
                    "name": "2017",
                    "value": 11
                },
                {
                    "name": "2019",
                    "value": 10
                },
                {
                    "name": "2018",
                    "value": 9
                },
                {
                    "name": "2016",
                    "value": 7
                },
                {
                    "name": "2015",
                    "value": 5
                },
                {
                    "name": "2014",
                    "value": 2
                },
                {
                    "name": "2021",
                    "value": 2
                },
                {
                    "name": "2010",
                    "value": 1
                },
                {
                    "name": "2011",
                    "value": 1
                },
                {
                    "name": "2012",
                    "value": 1
                },
                {
                    "name": "2013",
                    "value": 1
                }
            ],
            "radius": [
                "55",
                "100"
            ],
            "center": [
                "75%",
                "30%"
            ],
            "label": {
                "show": true,
                "margin": 8,
                "fontSize": 14,
                "formatter": "{b}: {c}"
            },
            "labelLine": {
                "show": true,
                "showAbove": false,
                "length": 15,
                "length2": 15,
                "smooth": false,
                "minTurnAngle": 90,
                "maxSurfaceAngle": 90
            },
            "tooltip": {
                "show": true,
                "trigger": "item",
                "triggerOn": "mousemove|click",
                "axisPointer": {
                    "type": "line"
                },
                "showContent": true,
                "alwaysShowContent": false,
                "showDelay": 0,
                "hideDelay": 100,
                "enterable": false,
                "confine": false,
                "appendToBody": false,
                "transitionDuration": 0.4,
                "formatter": "{a} <br/>{b}: {c} ({d}%)",
                "textStyle": {
                    "fontSize": 14
                },
                "borderWidth": 0,
                "padding": 5,
                "order": "seriesAsc"
            },
            "itemStyle": {
                "normal": {
                    "shadowBlur": 2,
                    "borderColor": "#87CEFA",
                    "borderWidth": 3,
                    "shadowColor": "#87CEFA",
                    "opacity": 1
                }
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "pie",
            "name": "\u56fd\u4ea7\u7535\u5f71\u6807\u7b7e",
            "colorBy": "data",
            "legendHoverLink": true,
            "selectedMode": false,
            "selectedOffset": 10,
            "clockwise": true,
            "startAngle": 90,
            "minAngle": 0,
            "minShowLabelAngle": 0,
            "avoidLabelOverlap": true,
            "stillShowZeroSum": true,
            "percentPrecision": 2,
            "showEmptyCircle": true,
            "emptyCircleStyle": {
                "color": "lightgray",
                "borderColor": "#000",
                "borderWidth": 0,
                "borderType": "solid",
                "borderDashOffset": 0,
                "borderCap": "butt",
                "borderJoin": "bevel",
                "borderMiterLimit": 10,
                "opacity": 1
            },
            "data": [
                {
                    "name": "\u5267\u60c5",
                    "value": 18
                },
                {
                    "name": "\u6218\u4e89",
                    "value": 5
                },
                {
                    "name": "\u5386\u53f2",
                    "value": 3
                },
                {
                    "name": "\u4e3b\u65cb\u5f8b",
                    "value": 11
                },
                {
                    "name": "\u52a8\u4f5c",
                    "value": 20
                },
                {
                    "name": "\u559c\u5267",
                    "value": 24
                },
                {
                    "name": "\u4eb2\u60c5",
                    "value": 2
                },
                {
                    "name": "\u52a8\u753b",
                    "value": 2
                },
                {
                    "name": "\u7384\u5e7b",
                    "value": 1
                },
                {
                    "name": "\u79d1\u5e7b",
                    "value": 3
                },
                {
                    "name": "\u5192\u9669",
                    "value": 6
                },
                {
                    "name": "\u60ac\u7591",
                    "value": 4
                },
                {
                    "name": "\u4fa6\u63a2",
                    "value": 1
                },
                {
                    "name": "\u72af\u7f6a",
                    "value": 7
                },
                {
                    "name": "\u7231\u60c5",
                    "value": 8
                },
                {
                    "name": "\u6000\u65e7",
                    "value": 2
                },
                {
                    "name": "\u707e\u96be",
                    "value": 2
                },
                {
                    "name": "\u5947\u5e7b",
                    "value": 7
                },
                {
                    "name": "\u8fd0\u52a8",
                    "value": 1
                },
                {
                    "name": "\u62a2\u9669",
                    "value": 1
                },
                {
                    "name": "\u60ca\u609a",
                    "value": 1
                },
                {
                    "name": "\u9752\u6625",
                    "value": 2
                },
                {
                    "name": "\u5bb6\u5ead",
                    "value": 1
                },
                {
                    "name": "\u516c\u8def",
                    "value": 2
                }
            ],
            "radius": [
                "55",
                "100"
            ],
            "center": [
                "33%",
                "80%"
            ],
            "label": {
                "show": true,
                "margin": 8,
                "fontSize": 14,
                "formatter": "{b}: {c}"
            },
            "labelLine": {
                "show": true,
                "showAbove": false,
                "length": 15,
                "length2": 15,
                "smooth": false,
                "minTurnAngle": 90,
                "maxSurfaceAngle": 90
            },
            "tooltip": {
                "show": true,
                "trigger": "item",
                "triggerOn": "mousemove|click",
                "axisPointer": {
                    "type": "line"
                },
                "showContent": true,
                "alwaysShowContent": false,
                "showDelay": 0,
                "hideDelay": 100,
                "enterable": false,
                "confine": false,
                "appendToBody": false,
                "transitionDuration": 0.4,
                "formatter": "{a} <br/>{b}: {c} ({d}%)",
                "textStyle": {
                    "fontSize": 14
                },
                "borderWidth": 0,
                "padding": 5,
                "order": "seriesAsc"
            },
            "itemStyle": {
                "normal": {
                    "shadowBlur": 2,
                    "borderColor": "#87CEFA",
                    "borderWidth": 3,
                    "shadowColor": "#87CEFA",
                    "opacity": 1
                }
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "pie",
            "name": "\u8fdb\u53e3\u7535\u5f71\u6807\u7b7e",
            "colorBy": "data",
            "legendHoverLink": true,
            "selectedMode": false,
            "selectedOffset": 10,
            "clockwise": true,
            "startAngle": 90,
            "minAngle": 0,
            "minShowLabelAngle": 0,
            "avoidLabelOverlap": true,
            "stillShowZeroSum": true,
            "percentPrecision": 2,
            "showEmptyCircle": true,
            "emptyCircleStyle": {
                "color": "lightgray",
                "borderColor": "#000",
                "borderWidth": 0,
                "borderType": "solid",
                "borderDashOffset": 0,
                "borderCap": "butt",
                "borderJoin": "bevel",
                "borderMiterLimit": 10,
                "opacity": 1
            },
            "data": [
                {
                    "name": "\u52a8\u4f5c",
                    "value": 39
                },
                {
                    "name": "\u79d1\u5e7b",
                    "value": 27
                },
                {
                    "name": "\u5192\u9669",
                    "value": 39
                },
                {
                    "name": "\u72af\u7f6a",
                    "value": 4
                },
                {
                    "name": "\u5947\u5e7b",
                    "value": 9
                },
                {
                    "name": "\u60ca\u609a",
                    "value": 4
                },
                {
                    "name": "\u52a8\u753b",
                    "value": 5
                },
                {
                    "name": "\u559c\u5267",
                    "value": 4
                },
                {
                    "name": "\u8fd0\u52a8",
                    "value": 1
                },
                {
                    "name": "\u5267\u60c5",
                    "value": 5
                },
                {
                    "name": "\u4f20\u8bb0",
                    "value": 1
                },
                {
                    "name": "\u4eb2\u60c5",
                    "value": 2
                },
                {
                    "name": "\u97f3\u4e50",
                    "value": 1
                },
                {
                    "name": "\u7231\u60c5",
                    "value": 2
                },
                {
                    "name": "\u707e\u96be",
                    "value": 2
                },
                {
                    "name": "\u60ac\u7591",
                    "value": 1
                },
                {
                    "name": "\u7a7f\u8d8a",
                    "value": 1
                },
                {
                    "name": "\u6b4c\u821e",
                    "value": 2
                }
            ],
            "radius": [
                "55",
                "100"
            ],
            "center": [
                "75%",
                "80%"
            ],
            "label": {
                "show": true,
                "margin": 8,
                "fontSize": 14,
                "formatter": "{b}: {c}"
            },
            "labelLine": {
                "show": true,
                "showAbove": false,
                "length": 15,
                "length2": 15,
                "smooth": false,
                "minTurnAngle": 90,
                "maxSurfaceAngle": 90
            },
            "tooltip": {
                "show": true,
                "trigger": "item",
                "triggerOn": "mousemove|click",
                "axisPointer": {
                    "type": "line"
                },
                "showContent": true,
                "alwaysShowContent": false,
                "showDelay": 0,
                "hideDelay": 100,
                "enterable": false,
                "confine": false,
                "appendToBody": false,
                "transitionDuration": 0.4,
                "formatter": "{a} <br/>{b}: {c} ({d}%)",
                "textStyle": {
                    "fontSize": 14
                },
                "borderWidth": 0,
                "padding": 5,
                "order": "seriesAsc"
            },
            "itemStyle": {
                "normal": {
                    "shadowBlur": 2,
                    "borderColor": "#87CEFA",
                    "borderWidth": 3,
                    "shadowColor": "#87CEFA",
                    "opacity": 1
                }
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "2019",
                "2018",
                "2021",
                "2017",
                "2020",
                "2015",
                "2016",
                "2012",
                "2013",
                "2014",
                "2010",
                "2011",
                "\u5267\u60c5",
                "\u6218\u4e89",
                "\u5386\u53f2",
                "\u4e3b\u65cb\u5f8b",
                "\u52a8\u4f5c",
                "\u559c\u5267",
                "\u4eb2\u60c5",
                "\u52a8\u753b",
                "\u7384\u5e7b",
                "\u79d1\u5e7b",
                "\u5192\u9669",
                "\u60ac\u7591",
                "\u4fa6\u63a2",
                "\u72af\u7f6a",
                "\u7231\u60c5",
                "\u6000\u65e7",
                "\u707e\u96be",
                "\u5947\u5e7b",
                "\u8fd0\u52a8",
                "\u62a2\u9669",
                "\u60ca\u609a",
                "\u9752\u6625",
                "\u5bb6\u5ead",
                "\u516c\u8def",
                "\u4f20\u8bb0",
                "\u97f3\u4e50",
                "\u7a7f\u8d8a",
                "\u6b4c\u821e"
            ],
            "selected": {},
            "show": false,
            "top": "5%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "text": "\u56fd\u4ea7-\u8fdb\u53e3\u4e0a\u699c - TOP50 - \u8be6\u60c5\u5206\u5e03",
            "left": "center",
            "top": "1%",
            "textStyle": {
                "color": "#000",
                "fontSize": 24
            }
        },
        {
            "text": "\u56fd\u4ea7\u5206\u5e03",
            "left": "28%",
            "top": "10%",
            "textStyle": {
                "color": "#999999",
                "fontSize": 18
            }
        },
        {
            "text": "\u8fdb\u53e3\u5206\u5e03",
            "left": "70%",
            "top": "10%",
            "textStyle": {
                "color": "#999999",
                "fontSize": 18
            }
        },
        {
            "text": "\u56fd\u4ea7\u7535\u5f71\u6807\u7b7e",
            "left": "28%",
            "top": "55%",
            "textStyle": {
                "color": "#999999",
                "fontSize": 18
            }
        },
        {
            "text": "\u8fdb\u53e3\u7535\u5f71\u6807\u7b7e",
            "left": "70%",
            "top": "55%",
            "textStyle": {
                "color": "#999999",
                "fontSize": 18
            }
        }
    ]
};
        chart_a44b727d9f0a43c8a95f445fe93662fe.setOption(option_a44b727d9f0a43c8a95f445fe93662fe);
    </script>
<br/>                <div id="169c6629ec5a477fb8a048f2d4fd8434" class="chart-container" style="position: absolute; width: 1063px; height: 599px; top: 632.3333740234375px; left: 99px;"></div>
    <script>
        var chart_169c6629ec5a477fb8a048f2d4fd8434 = echarts.init(
            document.getElementById('169c6629ec5a477fb8a048f2d4fd8434'), 'light', {renderer: 'canvas'});
        var option_169c6629ec5a477fb8a048f2d4fd8434 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u9996\u6620\u7968\u623f/\u4ebf",
            "legendHoverLink": true,
            "data": [
                2.05,
                1.02,
                2.91,
                1.44,
                1.91,
                10.11,
                1.3,
                3.41,
                2.72,
                2.9,
                1.41,
                1.6,
                2.09,
                2.75,
                2.27,
                1.63,
                5.47,
                4.1,
                1.33,
                0.64,
                1.38,
                3.22,
                1.22,
                1.62,
                3.56,
                1.99,
                3.62,
                1.5,
                0.65,
                0.23,
                2.37,
                0.76,
                2.86,
                1.52,
                0.47,
                0.59,
                0.94,
                0.79,
                1.35,
                0.55,
                0.39,
                0.83,
                1.67,
                0.47,
                0.39,
                0.98,
                1.24,
                1.03,
                1.33,
                1.05
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stack": "stack1",
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c} \u4ebf"
            },
            "itemStyle": {
                "normal": {
                    "color": new echarts.graphic.LinearGradient(0, 0, 0, 1, [{                    offset: 0,                    color: '#126bae'                }, {                    offset: 1,                    color: '#619ac3'                }], false),
                    "opacity": 0.8,
                    "shadowBlur": 4,
                    "shadowColor": "rgba(0, 0, 0, 0.3)",
                    "shadowOffsetX": 5,
                    "shadowOffsetY": 5,
                    "borderColor": "rgb(220,220,220)",
                    "borderWidth": 1
                }
            }
        },
        {
            "type": "bar",
            "name": "\u9996\u5468\u7968\u623f/\u4ebf",
            "legendHoverLink": true,
            "data": [
                15.28,
                9.97,
                10.5,
                6.65,
                20.19,
                25.79,
                4.65,
                9.9,
                18.18,
                20.88,
                5.77,
                11.73,
                17.66,
                10.81,
                9.03,
                6.65,
                12.04,
                14.62,
                3.1,
                2.81,
                2.69,
                10.54,
                5.16,
                5.92,
                5.78,
                6.73,
                10.37,
                5.89,
                5.63,
                2.58,
                7.61,
                2.93,
                5.62,
                5.35,
                2.04,
                2.27,
                3.49,
                4.19,
                4.23,
                0.55,
                3.07,
                0.83,
                7.69,
                2.46,
                1.64,
                3.24,
                4.24,
                6.11,
                4.24,
                3.59
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stack": "stack1",
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c} \u4ebf"
            },
            "itemStyle": {
                "normal": {
                    "color": new echarts.graphic.LinearGradient(0, 0, 0, 1, [{                    offset: 0,                    color: '#ea7293'                }, {                    offset: 1,                    color: '#ec8aa4'                }], false),
                    "opacity": 0.8,
                    "shadowBlur": 4,
                    "shadowColor": "rgba(0, 0, 0, 0.3)",
                    "shadowOffsetX": 5,
                    "shadowOffsetY": 5,
                    "borderColor": "rgb(220,220,220)",
                    "borderWidth": 1
                }
            }
        },
        {
            "type": "bar",
            "name": "\u9996\u5468\u672b\u7968\u623f/\u4ebf",
            "legendHoverLink": true,
            "data": [
                13.22,
                8.94,
                10.5,
                6.65,
                12.25,
                25.79,
                4.65,
                9.9,
                8.03,
                7.35,
                5.77,
                10.13,
                7.39,
                8.05,
                9.03,
                5.02,
                12.04,
                5.24,
                3.1,
                2.81,
                2.69,
                3.75,
                3.94,
                5.92,
                5.78,
                6.73,
                6.75,
                5.89,
                4.98,
                1.94,
                5.25,
                2.93,
                5.62,
                5.35,
                2.04,
                2.27,
                3.49,
                3.4,
                4.23,
                0.55,
                2.3,
                0.83,
                2.77,
                2.46,
                1.64,
                3.24,
                4.24,
                2.78,
                4.24,
                3.59
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stack": "stack1",
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c} \u4ebf"
            },
            "itemStyle": {
                "normal": {
                    "color": new echarts.graphic.LinearGradient(0, 0, 0, 1, [{                    offset: 0,                    color: '#9eccab'                }, {                    offset: 1,                    color: '#a4cab6'                }], false),
                    "opacity": 0.8,
                    "shadowBlur": 4,
                    "shadowColor": "rgba(0, 0, 0, 0.3)",
                    "shadowOffsetX": 5,
                    "shadowOffsetY": 5,
                    "borderColor": "rgb(220,220,220)",
                    "borderWidth": 1
                }
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u9996\u6620\u7968\u623f/\u4ebf",
                "\u9996\u5468\u7968\u623f/\u4ebf",
                "\u9996\u5468\u672b\u7968\u623f/\u4ebf"
            ],
            "selected": {
                "\u9996\u6620\u7968\u623f/\u4ebf": true,
                "\u9996\u5468\u7968\u623f/\u4ebf": true,
                "\u9996\u5468\u672b\u7968\u623f/\u4ebf": true
            },
            "show": true,
            "top": 30,
            "orient": "horizontal",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "shadow"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "value",
            "show": true,
            "scale": false,
            "nameLocation": "middle",
            "nameGap": 70,
            "gridIndex": 0,
            "axisLine": {
                "show": false,
                "onZero": true,
                "onZeroAxisIndex": 0
            },
            "axisTick": {
                "show": false,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "formatter": "{value}"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "yAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u957f\u6d25\u6e56",
                "\u6218\u72fc2",
                "\u4f60\u597d\uff0c\u674e\u7115\u82f1",
                "\u54ea\u5412\u4e4b\u9b54\u7ae5\u964d\u4e16",
                "\u6d41\u6d6a\u5730\u7403",
                "\u5510\u4eba\u8857\u63a2\u68483",
                "\u7ea2\u6d77\u884c\u52a8",
                "\u5510\u4eba\u8857\u63a2\u68482",
                "\u7f8e\u4eba\u9c7c",
                "\u6211\u548c\u6211\u7684\u7956\u56fd",
                "\u516b\u4f70",
                "\u6211\u4e0d\u662f\u836f\u795e",
                "\u4e2d\u56fd\u673a\u957f",
                "\u6211\u548c\u6211\u7684\u5bb6\u4e61",
                "\u897f\u8679\u5e02\u9996\u5bcc",
                "\u6349\u5996\u8bb0",
                "\u6349\u5996\u8bb02",
                "\u75af\u72c2\u7684\u5916\u661f\u4eba",
                "\u7f9e\u7f9e\u7684\u94c1\u62f3",
                "\u524d\u4efb3\uff1a\u518d\u89c1\u524d\u4efb",
                "\u529f\u592b\u745c\u4f3d",
                "\u98de\u9a70\u4eba\u751f",
                "\u70c8\u706b\u82f1\u96c4",
                "\u5bfb\u9f99\u8bc0",
                "\u897f\u6e38\u4f0f\u5996\u7bc7",
                "\u6e2f\u56e7",
                "\u59dc\u5b50\u7259",
                "\u5c11\u5e74\u7684\u4f60",
                "\u6211\u548c\u6211\u7684\u7236\u8f88",
                "\u590f\u6d1b\u7279\u70e6\u607c",
                "\u9001\u4f60\u4e00\u6735\u5c0f\u7ea2\u82b1",
                "\u82b3\u534e",
                "\u540e\u6765\u7684\u6211\u4eec",
                "\u4e00\u51fa\u597d\u620f",
                "\u8bef\u6740",
                "\u6012\u706b\u00b7\u91cd\u6848",
                "\u4e2d\u56fd\u533b\u751f",
                "\u62c6\u5f39\u4e13\u5bb62",
                "\u626b\u6bd22\uff1a\u5929\u5730\u5bf9\u51b3",
                "\u65e0\u53cc",
                "\u4eba\u518d\u56e7\u9014\u4e4b\u6cf0\u56e7",
                "\u897f\u6e38\u964d\u9b54\u7bc7",
                "\u897f\u6e38\u8bb0\u4e4b\u5b59\u609f\u7a7a\u4e09\u6253\u767d\u9aa8\u7cbe",
                "\u60ac\u5d16\u4e4b\u4e0a",
                "\u6e44\u516c\u6cb3\u884c\u52a8",
                "\u53f6\u95ee4\uff1a\u5b8c\u7ed3\u7bc7",
                "\u957f\u57ce",
                "\u5fc3\u82b1\u8def\u653e",
                "\u714e\u997c\u4fa0",
                "\u91d1\u521a\u5ddd"
            ]
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u56fd\u4ea7\u7535\u5f71\u4e0a\u6620\u9996\u5468\u7968\u623f\u8868\u73b0 -Top50",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "fontSize": 20
            }
        }
    ],
    "dataZoom": {
        "show": true,
        "type": "slider",
        "showDetail": true,
        "showDataShadow": true,
        "realtime": true,
        "start": 20,
        "end": 80,
        "orient": "vertical",
        "zoomLock": false,
        "filterMode": "filter"
    }
};
        chart_169c6629ec5a477fb8a048f2d4fd8434.setOption(option_169c6629ec5a477fb8a048f2d4fd8434);
    </script>
<br/>                <div id="1642bbd7f68040219f472394b3fb30f2" class="chart-container" style="position: absolute; width: 1139px; height: 601px; top: 633.6666870117188px; left: 1167px;"></div>
    <script>
        var chart_1642bbd7f68040219f472394b3fb30f2 = echarts.init(
            document.getElementById('1642bbd7f68040219f472394b3fb30f2'), 'light', {renderer: 'canvas'});
        var option_1642bbd7f68040219f472394b3fb30f2 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "bar",
            "name": "\u9996\u6620\u7968\u623f/\u4ebf",
            "legendHoverLink": true,
            "data": [
                5.38,
                4.17,
                3.46,
                3.87,
                1.57,
                1.75,
                2.23,
                2.02,
                2.9,
                0.22,
                2.52,
                1.86,
                2.49,
                1.01,
                2.26,
                0.86,
                3.18,
                0.32,
                0.16,
                1.81,
                1.68,
                1.37,
                0.13,
                1.33,
                1.51,
                1.11,
                1.43,
                2.14,
                0.99,
                1.38,
                2.15,
                0.95,
                0.78,
                0.2,
                0.71,
                1.36,
                1.26,
                0.33,
                1.08,
                0.74,
                0.96,
                1.54,
                1.96,
                1.12,
                1.35,
                0.9,
                1.09,
                0.82,
                0.44,
                1.06
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stack": "stack1",
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c} \u4ebf"
            },
            "itemStyle": {
                "normal": {
                    "color": new echarts.graphic.LinearGradient(0, 0, 0, 1, [{                    offset: 0,                    color: '#126bae'                }, {                    offset: 1,                    color: '#619ac3'                }], false),
                    "opacity": 0.8,
                    "shadowBlur": 4,
                    "shadowColor": "rgba(0, 0, 0, 0.3)",
                    "shadowOffsetX": 5,
                    "shadowOffsetY": 5,
                    "borderColor": "rgb(220,220,220)",
                    "borderWidth": 1
                }
            }
        },
        {
            "type": "bar",
            "name": "\u9996\u5468\u7968\u623f/\u4ebf",
            "legendHoverLink": true,
            "data": [
                20.42,
                12.89,
                3.46,
                12.13,
                6.43,
                6.11,
                7.51,
                7.21,
                8.3,
                1.55,
                9.8,
                9.37,
                6.78,
                5.98,
                6.55,
                3.88,
                8.17,
                2.76,
                0.87,
                6.11,
                5.21,
                4.59,
                1.19,
                4.54,
                4.94,
                4.0,
                4.3,
                6.39,
                3.89,
                4.41,
                5.83,
                3.46,
                3.18,
                3.34,
                4.34,
                4.59,
                4.3,
                2.67,
                5.44,
                3.75,
                3.7,
                4.55,
                3.32,
                3.78,
                4.53,
                3.08,
                3.91,
                2.97,
                1.75,
                3.55
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stack": "stack1",
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c} \u4ebf"
            },
            "itemStyle": {
                "normal": {
                    "color": new echarts.graphic.LinearGradient(0, 0, 0, 1, [{                    offset: 0,                    color: '#ea7293'                }, {                    offset: 1,                    color: '#ec8aa4'                }], false),
                    "opacity": 0.8,
                    "shadowBlur": 4,
                    "shadowColor": "rgba(0, 0, 0, 0.3)",
                    "shadowOffsetX": 5,
                    "shadowOffsetY": 5,
                    "borderColor": "rgb(220,220,220)",
                    "borderWidth": 1
                }
            }
        },
        {
            "type": "bar",
            "name": "\u9996\u5468\u672b\u7968\u623f/\u4ebf",
            "legendHoverLink": true,
            "data": [
                11.87,
                12.89,
                3.46,
                12.13,
                6.43,
                6.11,
                7.51,
                7.21,
                8.3,
                1.55,
                4.29,
                5.34,
                6.78,
                4.17,
                6.55,
                3.88,
                8.17,
                1.48,
                0.87,
                6.11,
                5.21,
                4.59,
                1.19,
                4.54,
                4.94,
                4.0,
                4.3,
                6.39,
                2.9,
                4.41,
                5.83,
                3.46,
                3.18,
                3.14,
                2.53,
                4.59,
                4.3,
                2.02,
                2.92,
                3.75,
                3.7,
                4.55,
                3.32,
                3.78,
                4.53,
                3.08,
                2.27,
                2.97,
                1.75,
                3.55
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stack": "stack1",
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "position": "top",
                "margin": 8,
                "formatter": "{c} \u4ebf"
            },
            "itemStyle": {
                "normal": {
                    "color": new echarts.graphic.LinearGradient(0, 0, 0, 1, [{                    offset: 0,                    color: '#9eccab'                }, {                    offset: 1,                    color: '#a4cab6'                }], false),
                    "opacity": 0.8,
                    "shadowBlur": 4,
                    "shadowColor": "rgba(0, 0, 0, 0.3)",
                    "shadowOffsetX": 5,
                    "shadowOffsetY": 5,
                    "borderColor": "rgb(220,220,220)",
                    "borderWidth": 1
                }
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u9996\u6620\u7968\u623f/\u4ebf",
                "\u9996\u5468\u7968\u623f/\u4ebf",
                "\u9996\u5468\u672b\u7968\u623f/\u4ebf"
            ],
            "selected": {
                "\u9996\u6620\u7968\u623f/\u4ebf": true,
                "\u9996\u5468\u7968\u623f/\u4ebf": true,
                "\u9996\u5468\u672b\u7968\u623f/\u4ebf": true
            },
            "show": true,
            "top": 30,
            "orient": "horizontal",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "shadow"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "value",
            "show": true,
            "scale": false,
            "nameLocation": "middle",
            "nameGap": 70,
            "gridIndex": 0,
            "axisLine": {
                "show": false,
                "onZero": true,
                "onZeroAxisIndex": 0
            },
            "axisTick": {
                "show": false,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "margin": 8,
                "formatter": "{value}"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "yAxis": [
        {
            "type": "category",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u590d\u4ec7\u8005\u8054\u76df4\uff1a\u7ec8\u5c40\u4e4b\u6218",
                "\u901f\u5ea6\u4e0e\u6fc0\u60c58",
                "\u901f\u5ea6\u4e0e\u6fc0\u60c57",
                "\u590d\u4ec7\u8005\u8054\u76df3\uff1a\u65e0\u9650\u6218\u4e89",
                "\u6d77\u738b",
                "\u53d8\u5f62\u91d1\u521a4\uff1a\u7edd\u8ff9\u91cd\u751f",
                "\u6bd2\u6db2\uff1a\u81f4\u547d\u5b88\u62a4\u8005",
                "\u4f8f\u7f57\u7eaa\u4e16\u754c2",
                "\u53d8\u5f62\u91d1\u521a5\uff1a\u6700\u540e\u7684\u9a91\u58eb",
                "\u75af\u72c2\u52a8\u7269\u57ce",
                "\u9b54\u517d",
                "\u590d\u4ec7\u8005\u8054\u76df2\uff1a\u5965\u521b\u7eaa\u5143",
                "\u901f\u5ea6\u4e0e\u6fc0\u60c5\uff1a\u7279\u522b\u884c\u52a8",
                "\u4f8f\u7f57\u7eaa\u4e16\u754c",
                "\u8718\u86db\u4fa0\uff1a\u82f1\u96c4\u8fdc\u5f81",
                "\u5934\u53f7\u73a9\u5bb6",
                "\u901f\u5ea6\u4e0e\u6fc0\u60c59",
                "\u963f\u51e1\u8fbe",
                "\u6454\u8de4\u5427\uff01\u7238\u7238",
                "\u7f8e\u56fd\u961f\u957f3\uff1a\u82f1\u96c4\u5185\u6218",
                "\u789f\u4e2d\u8c0d6\uff1a\u5168\u9762\u74e6\u89e3",
                "\u54e5\u65af\u62c9\u5927\u6218\u91d1\u521a",
                "\u5bfb\u68a6\u73af\u6e38\u8bb0",
                "\u52a0\u52d2\u6bd4\u6d77\u76d75\uff1a\u6b7b\u65e0\u5bf9\u8bc1",
                "\u91d1\u521a\uff1a\u9ab7\u9ac5\u5c9b",
                "\u5927\u9ec4\u8702",
                "\u6781\u9650\u7279\u5de5\uff1a\u7ec8\u6781\u56de\u5f52",
                "\u751f\u5316\u5371\u673a\uff1a\u7ec8\u7ae0",
                "\u53d8\u5f62\u91d1\u521a3",
                "\u795e\u5077\u5976\u72383",
                "\u60ca\u5947\u961f\u957f",
                "\u72c2\u66b4\u5de8\u517d",
                "\u5947\u5e7b\u68ee\u6797",
                "\u6bd4\u60b2\u4f24\u66f4\u60b2\u4f24\u7684\u6545\u4e8b",
                "\u6cf0\u5766\u5c3c\u514b\u53f73D",
                "\u54e5\u65af\u62c92\uff1a\u602a\u517d\u4e4b\u738b",
                "\u963f\u4e3d\u5854\uff1a\u6218\u6597\u5929\u4f7f",
                "\u661f\u9645\u7a7f\u8d8a",
                "\u789f\u4e2d\u8c0d5\uff1a\u795e\u79d8\u56fd\u5ea6",
                "\u51b0\u96ea\u5947\u7f182",
                "\u72ee\u5b50\u738b",
                "\u8681\u4eba2\uff1a\u9ec4\u8702\u5973\u73b0\u8eab",
                "\u661f\u7403\u5927\u6218\uff1a\u539f\u529b\u89c9\u9192",
                "X\u6218\u8b66\uff1a\u5929\u542f",
                "\u8718\u86db\u4fa0\uff1a\u82f1\u96c4\u5f52\u6765",
                "\u970d\u6bd4\u7279\u4eba3\uff1a\u4e94\u519b\u4e4b\u6218",
                "\u94a2\u94c1\u4fa03",
                "\u5947\u5f02\u535a\u58eb",
                "\u795e\u79d8\u5de8\u661f",
                "\u96f7\u795e3\uff1a\u8bf8\u795e\u9ec4\u660f"
            ]
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u8fdb\u53e3\u7535\u5f71\u4e0a\u6620\u9996\u5468\u7968\u623f\u8868\u73b0 -Top50",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "fontSize": 20
            }
        }
    ],
    "dataZoom": {
        "show": true,
        "type": "slider",
        "showDetail": true,
        "showDataShadow": true,
        "realtime": true,
        "start": 20,
        "end": 80,
        "orient": "vertical",
        "zoomLock": false,
        "filterMode": "filter"
    }
};
        chart_1642bbd7f68040219f472394b3fb30f2.setOption(option_1642bbd7f68040219f472394b3fb30f2);
    </script>
<br/>                <div id="1db101d008754af9b88058bc6c70d77a" class="chart-container" style="position: absolute; width: 1469px; height: 817px; top: 1233px; left: 398px;"></div>
    <script>
        var chart_1db101d008754af9b88058bc6c70d77a = echarts.init(
            document.getElementById('1db101d008754af9b88058bc6c70d77a'), 'light', {renderer: 'canvas'});
        var option_1db101d008754af9b88058bc6c70d77a = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "series": [
        {
            "type": "line",
            "name": "\u5f53\u524d\u7968\u623f/\u5343\u4e07",
            "connectNulls": false,
            "xAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": false,
            "smooth": true,
            "clip": true,
            "step": false,
            "data": [
                [
                    "2021-09-30",
                    20.52
                ],
                [
                    "2021-10-01",
                    41.08
                ],
                [
                    "2021-10-02",
                    43.86
                ],
                [
                    "2021-10-03",
                    47.31
                ],
                [
                    "2021-10-04",
                    48.44
                ],
                [
                    "2021-10-05",
                    49.38
                ],
                [
                    "2021-10-06",
                    51.38
                ],
                [
                    "2021-10-07",
                    39.19
                ],
                [
                    "2021-10-08",
                    16.52
                ],
                [
                    "2021-10-09",
                    23.85
                ],
                [
                    "2021-10-10",
                    30.21
                ],
                [
                    "2021-10-11",
                    10.32
                ],
                [
                    "2021-10-12",
                    8.65
                ],
                [
                    "2021-10-13",
                    7.54
                ],
                [
                    "2021-10-14",
                    7.16
                ],
                [
                    "2021-10-15",
                    11.25
                ],
                [
                    "2021-10-16",
                    20.6
                ],
                [
                    "2021-10-17",
                    14.51
                ],
                [
                    "2021-10-18",
                    4.91
                ],
                [
                    "2021-10-19",
                    4.58
                ],
                [
                    "2021-10-20",
                    4.21
                ],
                [
                    "2021-10-21",
                    3.89
                ],
                [
                    "2021-10-22",
                    5.65
                ],
                [
                    "2021-10-23",
                    9.18
                ],
                [
                    "2021-10-24",
                    5.76
                ],
                [
                    "2021-10-25",
                    2.01
                ],
                [
                    "2021-10-26",
                    2.0
                ],
                [
                    "2021-10-27",
                    1.84
                ],
                [
                    "2021-10-28",
                    1.72
                ],
                [
                    "2021-10-29",
                    2.84
                ],
                [
                    "2021-10-30",
                    5.53
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8
            },
            "logBase": 10,
            "lineStyle": {
                "normal": {
                    "shadowColor": "rgba(0, 0, 0, .5)",
                    "shadowBlur": 0,
                    "shadowOffsetY": 1,
                    "shadowOffsetX": 1
                }
            },
            "areaStyle": {
                "opacity": 0.5
            },
            "zlevel": 0,
            "z": 100,
            "markArea": {
                "silent": true,
                "label": {
                    "show": true,
                    "position": "bottom",
                    "color": "#000000",
                    "margin": 8
                },
                "data": [
                    [
                        {
                            "name": "\u6b63\u5f0f\u4e0a\u6620\n\u56fd\u5e86\u6863",
                            "xAxis": "2021-09-30"
                        },
                        {
                            "xAxis": "2021-10-01"
                        }
                    ],
                    [
                        {
                            "name": "\u9ad8\u5cf0\u671f",
                            "xAxis": "2021-10-05"
                        },
                        {
                            "xAxis": "2021-10-07"
                        }
                    ],
                    [
                        {
                            "name": "\u7b2c\u4e09\u5468\n\u5c0f\u9ad8\u5cf0",
                            "xAxis": "2021-10-15"
                        },
                        {
                            "xAxis": "2021-10-17"
                        }
                    ]
                ],
                "itemStyle": {
                    "color": "#1E90FF",
                    "opacity": 0.2
                }
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "line",
            "name": "\u5f53\u524d\u4eba\u6b21/\u767e\u4e07",
            "connectNulls": false,
            "xAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": false,
            "smooth": true,
            "clip": true,
            "step": false,
            "data": [
                [
                    "2021-09-30",
                    4.25
                ],
                [
                    "2021-10-01",
                    8.37
                ],
                [
                    "2021-10-02",
                    8.95
                ],
                [
                    "2021-10-03",
                    9.69
                ],
                [
                    "2021-10-04",
                    9.95
                ],
                [
                    "2021-10-05",
                    10.17
                ],
                [
                    "2021-10-06",
                    10.59
                ],
                [
                    "2021-10-07",
                    8.1
                ],
                [
                    "2021-10-08",
                    3.49
                ],
                [
                    "2021-10-09",
                    5.07
                ],
                [
                    "2021-10-10",
                    6.38
                ],
                [
                    "2021-10-11",
                    2.2
                ],
                [
                    "2021-10-12",
                    1.85
                ],
                [
                    "2021-10-13",
                    1.63
                ],
                [
                    "2021-10-14",
                    1.56
                ],
                [
                    "2021-10-15",
                    2.45
                ],
                [
                    "2021-10-16",
                    4.43
                ],
                [
                    "2021-10-17",
                    3.1
                ],
                [
                    "2021-10-18",
                    1.06
                ],
                [
                    "2021-10-19",
                    0.99
                ],
                [
                    "2021-10-20",
                    0.92
                ],
                [
                    "2021-10-21",
                    0.85
                ],
                [
                    "2021-10-22",
                    1.38
                ],
                [
                    "2021-10-23",
                    2.3
                ],
                [
                    "2021-10-24",
                    1.45
                ],
                [
                    "2021-10-25",
                    0.51
                ],
                [
                    "2021-10-26",
                    0.51
                ],
                [
                    "2021-10-27",
                    0.48
                ],
                [
                    "2021-10-28",
                    0.45
                ],
                [
                    "2021-10-29",
                    0.75
                ],
                [
                    "2021-10-30",
                    1.45
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8
            },
            "logBase": 10,
            "lineStyle": {
                "normal": {
                    "shadowColor": "rgba(0, 0, 0, .5)",
                    "shadowBlur": 0,
                    "shadowOffsetY": 1,
                    "shadowOffsetX": 1
                }
            },
            "areaStyle": {
                "opacity": 0.5
            },
            "zlevel": 0,
            "z": 100,
            "markArea": {
                "silent": true,
                "label": {
                    "show": true,
                    "position": "bottom",
                    "color": "#000000",
                    "margin": 8
                },
                "data": [
                    [
                        {
                            "name": "\u6b63\u5f0f\u4e0a\u6620\n\u56fd\u5e86\u6863",
                            "xAxis": "2021-09-30"
                        },
                        {
                            "xAxis": "2021-10-01"
                        }
                    ],
                    [
                        {
                            "name": "\u9ad8\u5cf0\u671f",
                            "xAxis": "2021-10-05"
                        },
                        {
                            "xAxis": "2021-10-07"
                        }
                    ],
                    [
                        {
                            "name": "\u7b2c\u4e09\u5468\n\u5c0f\u9ad8\u5cf0",
                            "xAxis": "2021-10-15"
                        },
                        {
                            "xAxis": "2021-10-17"
                        }
                    ]
                ],
                "itemStyle": {
                    "color": "#1E90FF",
                    "opacity": 0.2
                }
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "line",
            "name": "\u5f53\u524d\u573a\u6b21/\u4e07",
            "connectNulls": false,
            "xAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": false,
            "smooth": true,
            "clip": true,
            "step": false,
            "data": [
                [
                    "2021-09-30",
                    16.47
                ],
                [
                    "2021-10-01",
                    15.33
                ],
                [
                    "2021-10-02",
                    16.11
                ],
                [
                    "2021-10-03",
                    16.29
                ],
                [
                    "2021-10-04",
                    16.76
                ],
                [
                    "2021-10-05",
                    17.18
                ],
                [
                    "2021-10-06",
                    17.63
                ],
                [
                    "2021-10-07",
                    17.64
                ],
                [
                    "2021-10-08",
                    15.68
                ],
                [
                    "2021-10-09",
                    16.01
                ],
                [
                    "2021-10-10",
                    16.84
                ],
                [
                    "2021-10-11",
                    15.47
                ],
                [
                    "2021-10-12",
                    15.29
                ],
                [
                    "2021-10-13",
                    15.15
                ],
                [
                    "2021-10-14",
                    15.09
                ],
                [
                    "2021-10-15",
                    14.07
                ],
                [
                    "2021-10-16",
                    15.01
                ],
                [
                    "2021-10-17",
                    15.17
                ],
                [
                    "2021-10-18",
                    13.88
                ],
                [
                    "2021-10-19",
                    13.6
                ],
                [
                    "2021-10-20",
                    13.37
                ],
                [
                    "2021-10-21",
                    13.2
                ],
                [
                    "2021-10-22",
                    9.33
                ],
                [
                    "2021-10-23",
                    9.72
                ],
                [
                    "2021-10-24",
                    10.4
                ],
                [
                    "2021-10-25",
                    9.35
                ],
                [
                    "2021-10-26",
                    9.23
                ],
                [
                    "2021-10-27",
                    9.06
                ],
                [
                    "2021-10-28",
                    8.95
                ],
                [
                    "2021-10-29",
                    5.94
                ],
                [
                    "2021-10-30",
                    6.82
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "margin": 8
            },
            "logBase": 10,
            "lineStyle": {
                "normal": {
                    "shadowColor": "rgba(0, 0, 0, .5)",
                    "shadowBlur": 0,
                    "shadowOffsetY": 1,
                    "shadowOffsetX": 1
                }
            },
            "areaStyle": {
                "opacity": 0.5
            },
            "zlevel": 0,
            "z": 100,
            "markArea": {
                "silent": true,
                "label": {
                    "show": true,
                    "position": "bottom",
                    "color": "#000000",
                    "margin": 8
                },
                "data": [
                    [
                        {
                            "name": "\u6b63\u5f0f\u4e0a\u6620\n\u56fd\u5e86\u6863",
                            "xAxis": "2021-09-30"
                        },
                        {
                            "xAxis": "2021-10-01"
                        }
                    ],
                    [
                        {
                            "name": "\u9ad8\u5cf0\u671f",
                            "xAxis": "2021-10-05"
                        },
                        {
                            "xAxis": "2021-10-07"
                        }
                    ],
                    [
                        {
                            "name": "\u7b2c\u4e09\u5468\n\u5c0f\u9ad8\u5cf0",
                            "xAxis": "2021-10-15"
                        },
                        {
                            "xAxis": "2021-10-17"
                        }
                    ]
                ],
                "itemStyle": {
                    "color": "#1E90FF",
                    "opacity": 0.2
                }
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5f53\u524d\u7968\u623f/\u5343\u4e07",
                "\u5f53\u524d\u4eba\u6b21/\u767e\u4e07",
                "\u5f53\u524d\u573a\u6b21/\u4e07"
            ],
            "selected": {
                "\u5f53\u524d\u7968\u623f/\u5343\u4e07": true,
                "\u5f53\u524d\u4eba\u6b21/\u767e\u4e07": true,
                "\u5f53\u524d\u573a\u6b21/\u4e07": true
            },
            "show": true,
            "top": 45,
            "orient": "horizontal",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisTick": {
                "show": false,
                "alignWithLabel": false,
                "inside": false
            },
            "axisLabel": {
                "show": true,
                "color": "black",
                "margin": 30
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "boundaryGap": false,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "2021-09-30",
                "2021-10-01",
                "2021-10-02",
                "2021-10-03",
                "2021-10-04",
                "2021-10-05",
                "2021-10-06",
                "2021-10-07",
                "2021-10-08",
                "2021-10-09",
                "2021-10-10",
                "2021-10-11",
                "2021-10-12",
                "2021-10-13",
                "2021-10-14",
                "2021-10-15",
                "2021-10-16",
                "2021-10-17",
                "2021-10-18",
                "2021-10-19",
                "2021-10-20",
                "2021-10-21",
                "2021-10-22",
                "2021-10-23",
                "2021-10-24",
                "2021-10-25",
                "2021-10-26",
                "2021-10-27",
                "2021-10-28",
                "2021-10-29",
                "2021-10-30"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0
            },
            "axisTick": {
                "show": false,
                "alignWithLabel": false,
                "inside": false
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#483D8B"
                }
            }
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u957f\u6d25\u6e56\u4e0a\u6620\u540e\u4e09\u5341\u65e5\u7535\u5f71\u7968\u623f\u8868\u73b0",
            "target": "blank",
            "subtext": "2021-09-30~2021-10-30",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "fontSize": 18
            }
        }
    ],
    "graphic": [
        {
            "type": "group",
            "diffChildrenByName": false,
            "children": [
                {
                    "type": "image",
                    "id": "logo",
                    "$action": "merge",
                    "rotation": 0,
                    "left": "center",
                    "bounding": "all",
                    "z": -1,
                    "zlevel": 0,
                    "silent": false,
                    "invisible": false,
                    "ignore": false,
                    "cursor": "pointer",
                    "draggable": false,
                    "progressive": false,
                    "width": 0,
                    "height": 0,
                    "style": {
                        "image": "https://img2.baidu.com/it/u=3979355417,3562690433&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=388",
                        "x": 0,
                        "y": 0,
                        "width": 1000,
                        "height": 600,
                        "opacity": 0.5
                    }
                }
            ],
            "id": "1",
            "$action": "merge",
            "rotation": 0,
            "left": "center",
            "top": "center",
            "bounding": "all",
            "z": -1,
            "zlevel": 0,
            "silent": false,
            "invisible": false,
            "ignore": false,
            "cursor": "pointer",
            "draggable": false,
            "progressive": false,
            "width": 0,
            "height": 0
        }
    ],
    "color": [
        "#80FFA5",
        "#00DDFF",
        "#FF0087"
    ]
};
        chart_1db101d008754af9b88058bc6c70d77a.setOption(option_1db101d008754af9b88058bc6c70d77a);
    </script>
<br/>                <div id="c6ac5813c05f4f44936a4d460087b6ed" class="chart-container" style="position: absolute; width: 899px; height: 501px; top: 2047.3333740234375px; left: 1212px;"></div>
    <script>
        var chart_c6ac5813c05f4f44936a4d460087b6ed = echarts.init(
            document.getElementById('c6ac5813c05f4f44936a4d460087b6ed'), 'white', {renderer: 'canvas'});
        var option_c6ac5813c05f4f44936a4d460087b6ed = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "gauge",
            "title": {
                "show": true,
                "offsetCenter": [
                    0,
                    "-35%"
                ],
                "color": "rgba(217, 48, 118, 0.9)",
                "fontStyle": "normal",
                "fontWeight": "bolder",
                "fontFamily": "sans-serif",
                "fontSize": 20,
                "backgroundColor": "transparent",
                "borderColor": "transparent",
                "borderWidth": 0,
                "borderRadius": 0,
                "padding": 0,
                "shadowColor": "transparent",
                "shadowBlur": 0,
                "shadowOffsetX": 0,
                "shadowOffsetY": 0,
                "overflow": "none",
                "valueAnimation": true
            },
            "detail": {
                "show": true,
                "backgroundColor": "transparent",
                "borderWidth": 0,
                "borderColor": "transparent",
                "offsetCenter": [
                    0,
                    "15%"
                ],
                "formatter": "{value}",
                "color": "#464646",
                "fontStyle": "normal",
                "fontWeight": "bolder",
                "fontFamily": "sans-serif",
                "fontSize": 50,
                "borderRadius": 8,
                "padding": 0,
                "shadowColor": "transparent",
                "shadowBlur": 0,
                "shadowOffsetX": 0,
                "shadowOffsetY": 0,
                "overflow": "none",
                "valueAnimation": true
            },
            "min": 0,
            "max": 10,
            "splitNumber": 10,
            "center": [
                "50%",
                "50%"
            ],
            "radius": "75%",
            "startAngle": 200,
            "endAngle": -20,
            "clockwise": true,
            "data": [
                {
                    "name": "\u732b\u773c\u8bc4\u5206",
                    "value": 9.5
                }
            ],
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 30,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": [
                        [
                            0.8,
                            "#67e0e3"
                        ],
                        [
                            0.98,
                            "#D4587A"
                        ],
                        [
                            1,
                            "#67e0e3"
                        ]
                    ]
                }
            },
            "progress": {
                "show": false,
                "overlap": true,
                "width": 10,
                "roundCap": false,
                "clip": false
            },
            "anchor": {
                "show": true,
                "showAbove": false,
                "size": 6,
                "icon": "circle",
                "offsetCenter": [
                    0,
                    0
                ],
                "keepAspect": false
            },
            "pointer": {
                "show": true,
                "length": "80%",
                "width": 8
            },
            "itemStyle": {
                "color": "rgba(50, 163, 107, 0.3)"
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {
                "": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u957f\u6d25\u6e56",
            "target": "blank",
            "subtarget": "blank",
            "right": "0%",
            "bottom": "30%",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "rgba(217, 48, 118, 0.1)",
                "fontSize": 80
            }
        }
    ]
};
        chart_c6ac5813c05f4f44936a4d460087b6ed.setOption(option_c6ac5813c05f4f44936a4d460087b6ed);
    </script>
<br/>                <div id="ccd656cb2a3a4bc7a9582cad795a07f2" class="chart-container" style="position: absolute; width: 899px; height: 499px; top: 2050.666748046875px; left: 212px;"></div>
    <script>
        var chart_ccd656cb2a3a4bc7a9582cad795a07f2 = echarts.init(
            document.getElementById('ccd656cb2a3a4bc7a9582cad795a07f2'), 'white', {renderer: 'canvas'});
        var option_ccd656cb2a3a4bc7a9582cad795a07f2 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "gauge",
            "title": {
                "show": true,
                "offsetCenter": [
                    0,
                    "-35%"
                ],
                "color": "rgba(217, 48, 118, 0.9)",
                "fontStyle": "normal",
                "fontWeight": "bolder",
                "fontFamily": "sans-serif",
                "fontSize": 20,
                "backgroundColor": "transparent",
                "borderColor": "transparent",
                "borderWidth": 0,
                "borderRadius": 0,
                "padding": 0,
                "shadowColor": "transparent",
                "shadowBlur": 0,
                "shadowOffsetX": 0,
                "shadowOffsetY": 0,
                "overflow": "none",
                "valueAnimation": true
            },
            "detail": {
                "show": true,
                "backgroundColor": "transparent",
                "borderWidth": 0,
                "borderColor": "transparent",
                "offsetCenter": [
                    0,
                    "15%"
                ],
                "formatter": "{value}",
                "color": "#464646",
                "fontStyle": "normal",
                "fontWeight": "bolder",
                "fontFamily": "sans-serif",
                "fontSize": 50,
                "borderRadius": 8,
                "padding": 0,
                "shadowColor": "transparent",
                "shadowBlur": 0,
                "shadowOffsetX": 0,
                "shadowOffsetY": 0,
                "overflow": "none",
                "valueAnimation": true
            },
            "min": 0,
            "max": 10,
            "splitNumber": 10,
            "center": [
                "50%",
                "50%"
            ],
            "radius": "75%",
            "startAngle": 200,
            "endAngle": -20,
            "clockwise": true,
            "data": [
                {
                    "name": "\u8c46\u74e3\u8bc4\u5206",
                    "value": 7.4
                }
            ],
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 30,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": [
                        [
                            0.7,
                            "#37a2da"
                        ],
                        [
                            0.8,
                            "#D4587A"
                        ],
                        [
                            1,
                            "#37a2da"
                        ]
                    ]
                }
            },
            "progress": {
                "show": false,
                "overlap": true,
                "width": 10,
                "roundCap": false,
                "clip": false
            },
            "anchor": {
                "show": true,
                "showAbove": false,
                "size": 6,
                "icon": "circle",
                "offsetCenter": [
                    0,
                    0
                ],
                "keepAspect": false
            },
            "pointer": {
                "show": true,
                "length": "80%",
                "width": 8
            },
            "itemStyle": {
                "color": "rgba(50, 163, 107, 0.3)"
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {
                "": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u957f\u6d25\u6e56",
            "target": "blank",
            "subtarget": "blank",
            "right": "0%",
            "bottom": "30%",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "rgba(217, 48, 118, 0.1)",
                "fontSize": 80
            }
        }
    ]
};
        chart_ccd656cb2a3a4bc7a9582cad795a07f2.setOption(option_ccd656cb2a3a4bc7a9582cad795a07f2);
    </script>
<br/>    </div>
    <script>
            $('#********************************').css('border-style', 'dashed').css('border-width', '0px');$("#********************************>div:nth-child(1)").width("100%").height("100%");
            new ResizeSensor(jQuery('#********************************'), function() { chart_********************************.resize()});
            $('#a44b727d9f0a43c8a95f445fe93662fe').css('border-style', 'dashed').css('border-width', '0px');$("#a44b727d9f0a43c8a95f445fe93662fe>div:nth-child(1)").width("100%").height("100%");
            new ResizeSensor(jQuery('#a44b727d9f0a43c8a95f445fe93662fe'), function() { chart_a44b727d9f0a43c8a95f445fe93662fe.resize()});
            $('#169c6629ec5a477fb8a048f2d4fd8434').css('border-style', 'dashed').css('border-width', '0px');$("#169c6629ec5a477fb8a048f2d4fd8434>div:nth-child(1)").width("100%").height("100%");
            new ResizeSensor(jQuery('#169c6629ec5a477fb8a048f2d4fd8434'), function() { chart_169c6629ec5a477fb8a048f2d4fd8434.resize()});
            $('#1642bbd7f68040219f472394b3fb30f2').css('border-style', 'dashed').css('border-width', '0px');$("#1642bbd7f68040219f472394b3fb30f2>div:nth-child(1)").width("100%").height("100%");
            new ResizeSensor(jQuery('#1642bbd7f68040219f472394b3fb30f2'), function() { chart_1642bbd7f68040219f472394b3fb30f2.resize()});
            $('#1db101d008754af9b88058bc6c70d77a').css('border-style', 'dashed').css('border-width', '0px');$("#1db101d008754af9b88058bc6c70d77a>div:nth-child(1)").width("100%").height("100%");
            new ResizeSensor(jQuery('#1db101d008754af9b88058bc6c70d77a'), function() { chart_1db101d008754af9b88058bc6c70d77a.resize()});
            $('#c6ac5813c05f4f44936a4d460087b6ed').css('border-style', 'dashed').css('border-width', '0px');$("#c6ac5813c05f4f44936a4d460087b6ed>div:nth-child(1)").width("100%").height("100%");
            new ResizeSensor(jQuery('#c6ac5813c05f4f44936a4d460087b6ed'), function() { chart_c6ac5813c05f4f44936a4d460087b6ed.resize()});
            $('#ccd656cb2a3a4bc7a9582cad795a07f2').css('border-style', 'dashed').css('border-width', '0px');$("#ccd656cb2a3a4bc7a9582cad795a07f2>div:nth-child(1)").width("100%").height("100%");
            new ResizeSensor(jQuery('#ccd656cb2a3a4bc7a9582cad795a07f2'), function() { chart_ccd656cb2a3a4bc7a9582cad795a07f2.resize()});
            var charts_id = ['********************************','a44b727d9f0a43c8a95f445fe93662fe','169c6629ec5a477fb8a048f2d4fd8434','1642bbd7f68040219f472394b3fb30f2','1db101d008754af9b88058bc6c70d77a','c6ac5813c05f4f44936a4d460087b6ed','ccd656cb2a3a4bc7a9582cad795a07f2'];
function downloadCfg () {
    const fileName = 'chart_config.json'
    let downLink = document.createElement('a')
    downLink.download = fileName

    let result = []
    for(let i=0; i<charts_id.length; i++) {
        chart = $('#'+charts_id[i])
        result.push({
            cid: charts_id[i],
            width: chart.css("width"),
            height: chart.css("height"),
            top: chart.offset().top + "px",
            left: chart.offset().left + "px"
        })
    }

    let blob = new Blob([JSON.stringify(result)])
    downLink.href = URL.createObjectURL(blob)
    document.body.appendChild(downLink)
    downLink.click()
    document.body.removeChild(downLink)
}
    </script>
</body>
</html>

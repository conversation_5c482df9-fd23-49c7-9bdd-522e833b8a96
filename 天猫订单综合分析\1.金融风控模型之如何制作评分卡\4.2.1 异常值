4.2.1 异常值

但在银行数据中，我们希望排除的“异常值”不是一些超高或超低的数字，而是 一些不符合常理的数据：比如，收入不能为负数，但是一个超高水平的收入却是合理的，可以存在的。所以在银行 业中，我们往往就使用普通的描述性统计来观察数据的异常与否与数据的分布情况。

------------------------------------------------

data.describe([0.01,0.1,0.25,.5,.75,.9,.99])
# 年龄最小值为0，异常值也被我们观察到，年龄的最小值居然有0，这不符合银行的业务需求，即便是儿童账户也要至少8岁

(data["age"] == 0).sum()
# 发现只有一个人年龄为0，可以判断这肯定是录入失误造成的，可以当成是缺失值来处理，直接删除掉这个样本


------------------------------------------------

另外，有三个指标看起来很奇怪： 
  
"NumberOfTime30-59DaysPastDueNotWorse" 
"NumberOfTime60-89DaysPastDueNotWorse" 
"NumberOfTimes90DaysLate" 
  
这三个指标分别是“过去两年内出现35-59天逾期但是没有发展的更坏的次数”，“过去两年内出现60-89天逾期但是没 有发展的更坏的次数”,“过去两年内出现90天逾期的次数”。这三个指标，最大值却是 
98，看起来非常奇怪。一个人在过去两年内逾期35~59天98次，二个月才60天，两年内逾期98次这是怎么算出来的？ 
  
我们可以去咨询业务人员，请教他们这个逾期次数是如何计算的。如果这个指标是正常的，那这些两年内逾期了98次的 客户，应该都是坏客户。在我们无法询问他们情况下，我们查看一下有多少个样本存在这种异常：

------------------------------------------------

data[data.loc[:,"NumberOfTimes90DaysLate"] > 90] # 逾期都是98次

------------------------------------------------

data.loc[:,"NumberOfTimes90DaysLate"].value_counts() # 220人违约了98次

------------------------------------------------

data = data[data.loc[:,"NumberOfTimes90DaysLate"] < 90] 保留小于90次

------------------------------------------------


注意：为什么不统一量纲，也不标准化数据分布？

无论算法有什么样的规定，无论统计学中有什么样的要求，我们的最终目的都是要为业务服务。现在我们要制作评 分卡，评分卡是要给业务人员们使用的基于新客户填写的各种信息为客户打分的一张卡片，而为了制作这张卡片， 我们需要对我们的数据进行一个“分档”，比如说，年龄20~30岁为一档，年龄30~50岁为一档，月收入1W以上为一 档，5000~1W为一档，每档的分数不同。 


一旦我们将数据统一量纲，或者标准化了之后，数据大小和范围都会改变，统计结果是漂亮了，但是对于业务人员 来说，他们完全无法理解，标准化后的年龄在0.00328~0.00467之间为一档是什么含义。并且，新客户填写的信 
息，天生就是量纲不统一的，我们的确可以将所有的信息录入之后，统一进行标准化，然后导入算法计算，但是最 终落到业务人员手上去判断的时候，他们会完全不理解为什么录入的信息变成了一串统计上很美但实际上根本看不 懂的数字。由于业务要求，在制作评分卡的时候，我们要尽量保持数据的原貌，年龄就是8~110的数字，收入就是 大于0，最大值可以无限的数字，即便量纲不统一，我们也不对数据进行标准化处理。 




















{"cells": [{"cell_type": "markdown", "id": "baking-shaft", "metadata": {"toc": true}, "source": ["<h1>Table of Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#数据预处理\" data-toc-modified-id=\"数据预处理-1\">数据预处理</a></span></li><li><span><a href=\"#分词\" data-toc-modified-id=\"分词-2\">分词</a></span></li><li><span><a href=\"#特征提取\" data-toc-modified-id=\"特征提取-3\">特征提取</a></span></li><li><span><a href=\"#建立朴素贝叶斯模型\" data-toc-modified-id=\"建立朴素贝叶斯模型-4\">建立朴素贝叶斯模型</a></span></li><li><span><a href=\"#参考\" data-toc-modified-id=\"参考-5\">参考</a></span></li></ul></div>"]}, {"cell_type": "markdown", "id": "speaking-bowling", "metadata": {}, "source": ["安装包：\n", "\n", "pip install gensim -i https://pypi.tuna.tsinghua.edu.cn/simple/\n", "\n", "pip install nltk -i https://pypi.tuna.tsinghua.edu.cn/simple/"]}, {"cell_type": "markdown", "id": "pleased-respondent", "metadata": {}, "source": ["## 数据预处理"]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "irish-justice", "metadata": {}, "source": ["数据\n", "\n", "每个文本文件包含相应类的数据\n", "![image.png](attachment:image.png)\n", "0：喜悦；1：愤怒；2：厌恶；3：低落"]}, {"cell_type": "code", "execution_count": 3, "id": "juvenile-evans", "metadata": {}, "outputs": [], "source": ["#把记事本中的数据去掉词语的词性重新写入csv文件，并且标记好分类\n", "import re\n", "import pandas as pd\n", "def proc_text(text):\n", "    pos_words=[]\n", "    pos_list=[]\n", "    type_list = []\n", "    for file in text:\n", "        with open(file,encoding='UTF-8') as f:\n", "            line = f.readline()\n", "            while(line):\n", "                line = line.strip('\\n')\n", "                line=re.sub('/[a-zA-Z]+', '',line).replace(\" \",\"\")\n", "                pos_list.append(line)\n", "                type_list.append(int(file[7]))\n", "                line = f.readline()\n", "    dataframe = pd.DataFrame({'text': pos_list, 'type': type_list})\n", "    dataframe.to_csv(\"./data/data.csv\", index=False, sep=',',encoding=\"utf_8_sig\")\n", "texts_list =['./data/0_simplifyweibo.txt','./data/1_simplifyweibo.txt','./data/2_simplifyweibo.txt','./data/3_simplifyweibo.txt']\n", "proc_text(texts_list)"]}, {"cell_type": "markdown", "id": "advanced-roulette", "metadata": {}, "source": ["## 分词"]}, {"cell_type": "code", "execution_count": 2, "id": "1ab71888", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple/\n", "Collecting gensim\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/74/c7/b444026e4aa625a2a346a2fa6098631941e4ef9495c41bf156dcf98871e7/gensim-4.3.0-cp39-cp39-win_amd64.whl (24.0 MB)\n", "     ---------------------------------------- 24.0/24.0 MB 2.3 MB/s eta 0:00:00\n", "Collecting Cython==0.29.32\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/1c/24/e3935e545b128a90146e743212703420287ed35885074a9f36b21f3bb68d/Cython-0.29.32-py2.py3-none-any.whl (986 kB)\n", "     ------------------------------------ 986.3/986.3 kB 992.0 kB/s eta 0:00:00\n", "Collecting FuzzyTM>=0.4.0\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/06/4d/8d2dd5d81afdea2aa790860d5c7e12f80154923ba827e3fa36759f0bf2cd/FuzzyTM-2.0.5-py3-none-any.whl (29 kB)\n", "Requirement already satisfied: numpy>=1.18.5 in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from gensim) (1.24.1)\n", "Requirement already satisfied: scipy>=1.7.0 in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from gensim) (1.10.0)\n", "Collecting smart-open>=1.8.1\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/47/80/c2d1bdd36c6b64ae566d9a29724291510e4f3796ce99639d3c2999286284/smart_open-6.3.0-py3-none-any.whl (56 kB)\n", "     ---------------------------------------- 56.8/56.8 kB 2.9 MB/s eta 0:00:00\n", "Requirement already satisfied: pandas in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from FuzzyTM>=0.4.0->gensim) (1.5.2)\n", "Collecting pyfume\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/0f/81/8f35156b9af9c9585de8518b354e42bcaf3ee3e5a77e530613c37de22b1e/pyFUME-0.2.25-py3-none-any.whl (67 kB)\n", "     ---------------------------------------- 67.1/67.1 kB 1.8 MB/s eta 0:00:00\n", "Requirement already satisfied: python-dateutil>=2.8.1 in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from pandas->FuzzyTM>=0.4.0->gensim) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from pandas->FuzzyTM>=0.4.0->gensim) (2022.7)\n", "Collecting simpful\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/d1/61/227b58d8377e47c384c9abe4236c5fcda7069100cb2d2e860196c05212ad/simpful-2.9.0-py3-none-any.whl (30 kB)\n", "Collecting fst-pso\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/5a/d7/f7f93c41fde5b8c1f9d52cc0f9a104a56eca13dc6876c6d2f967ddef88d7/fst-pso-1.8.1.tar.gz (18 kB)\n", "  Preparing metadata (setup.py): started\n", "  Preparing metadata (setup.py): finished with status 'done'\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from python-dateutil>=2.8.1->pandas->FuzzyTM>=0.4.0->gensim) (1.16.0)\n", "Collecting miniful\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/05/c9/cc1fdd9956e6339b20a80ebfb98216e9b19cd2eac4e1ba9865ca2ef46fbc/miniful-0.0.6.tar.gz (2.8 kB)\n", "  Preparing metadata (setup.py): started\n", "  Preparing metadata (setup.py): finished with status 'done'\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from simpful->pyfume->FuzzyTM>=0.4.0->gensim) (2.28.1)\n", "Requirement already satisfied: charset-normalizer<3,>=2 in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from requests->simpful->pyfume->FuzzyTM>=0.4.0->gensim) (2.1.1)\n", "Requirement already satisfied: urllib3<1.27,>=1.21.1 in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from requests->simpful->pyfume->FuzzyTM>=0.4.0->gensim) (1.26.13)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from requests->simpful->pyfume->FuzzyTM>=0.4.0->gensim) (3.4)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from requests->simpful->pyfume->FuzzyTM>=0.4.0->gensim) (2022.12.7)\n", "Building wheels for collected packages: fst-pso, miniful\n", "  Building wheel for fst-pso (setup.py): started\n", "  Building wheel for fst-pso (setup.py): finished with status 'done'\n", "  Created wheel for fst-pso: filename=fst_pso-1.8.1-py3-none-any.whl size=20448 sha256=8b8b51293e80ef380566a80efbae612468c4ba3d79cf5795560ed2db94a58748\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\91\\01\\74\\270f8856df7b02b845aee049119557bef96fc2b85c1213f976\n", "  Building wheel for miniful (setup.py): started\n", "  Building wheel for miniful (setup.py): finished with status 'done'\n", "  Created wheel for miniful: filename=miniful-0.0.6-py3-none-any.whl size=3522 sha256=90d0c3d3492ddf6600b4c159d39c0133accf51198aa74b482353cbc0bfbfff9e\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\db\\00\\56\\9f02bbddf0c76cc74254a3a46fb0f4316f7ca80dd984e159fc\n", "Successfully built fst-pso miniful\n", "Installing collected packages: smart-open, Cython, simpful, miniful, fst-pso, pyfume, FuzzyTM, gensim\n", "Successfully installed Cython-0.29.32 FuzzyTM-2.0.5 fst-pso-1.8.1 gensim-4.3.0 miniful-0.0.6 pyfume-0.2.25 simpful-2.9.0 smart-open-6.3.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip available: 22.3.1 -> 23.0\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple/\n", "Collecting nltk\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a6/0a/0d20d2c0f16be91b9fa32a77b76c60f9baf6eba419e5ef5deca17af9c582/nltk-3.8.1-py3-none-any.whl (1.5 MB)\n", "     ---------------------------------------- 1.5/1.5 MB 2.5 MB/s eta 0:00:00\n", "Requirement already satisfied: joblib in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from nltk) (1.2.0)\n", "Collecting click\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/c2/f1/df59e28c642d583f7dacffb1e0965d0e00b218e0186d7858ac5233dce840/click-8.1.3-py3-none-any.whl (96 kB)\n", "     -------------------------------------- 96.6/96.6 kB 345.4 kB/s eta 0:00:00\n", "Collecting regex>=2021.8.3\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/92/3c/17432c77b7d3929adb73077584606b236be4ed832243d426f51f5a0f72f9/regex-2022.10.31-cp39-cp39-win_amd64.whl (267 kB)\n", "     -------------------------------------- 267.8/267.8 kB 3.3 MB/s eta 0:00:00\n", "Requirement already satisfied: tqdm in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from nltk) (4.64.1)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from click->nltk) (0.4.6)\n", "Installing collected packages: regex, click, nltk\n", "Successfully installed click-8.1.3 nltk-3.8.1 regex-2022.10.31\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip available: 22.3.1 -> 23.0\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["!pip install gensim -i https://pypi.tuna.tsinghua.edu.cn/simple/\n", "\n", "!pip install nltk -i https://pypi.tuna.tsinghua.edu.cn/simple/"]}, {"cell_type": "code", "execution_count": 1, "id": "practical-equity", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\jieba.cache\n", "Loading model cost 1.256 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0    啊呀呀 ！ 要死 啦 ！ 么 么 么 ！ 只 穿 外套 就 好 了 ， 我 认为 里面 那 ...\n", "1                         风格 不 一样 嘛 ， 都 喜欢 ！ 最 喜欢 哪张 ？\n", "2    好 呀 ， 试试 D . I . Y . 去死皮 面膜 1 . 将 燕麦片 加水 中 浸泡 ...\n", "3    张 1 老师 ， 谢谢 侬 的 1 信任 ！ 粉丝 多少 无所谓 重在 质地 近日 发现 一...\n", "4    第二 條看 來 有點 吸引力 呵呵 【 美国 相亲 节目 与 中国 的 1 几大 不同 】 ...\n", "5    喜欢 苹果 IPHONE4 。 功能强大 ， 时尚 ， 手机 功能 多 。 沃爱 平谷 第二...\n", "6    回复 太牛 了 买房子 送 瓷砖 呗 ！ 昨晚 上 经过 中润 ， 看到 的 1 一个 立柱...\n", "7    人们 脱口而出 的 1 一般 都 是 没有 实际意义 的 1 话 — — — — 噗 … …...\n", "8    开张大吉 ！ 祝贺 买卖 兴隆 我家 的 1 酸辣粉 小店 开业 了 欢迎 铁岭 的 1 朋...\n", "9    方 大水 你 怎么 可以 这么 萌 这么 有 爱 捏 啊 呜 ~ ~ 偶 不要 变成 HC ...\n", "Name: cut_text, dtype: object\n"]}], "source": ["import re\n", "import jieba\n", "from gensim.models import word2vec\n", "# from nltk.classify import NaiveBayesClassifier\n", "from sklearn import naive_bayes\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "import pandas as pd\n", "\n", "\n", "def chinese_word_cut(mytext):\n", "    return \" \".join(jieba.cut(mytext))\n", "\n", "#把停用词转换为一个列表存储\n", "def get_custom_stopwords(stop_words_file):\n", "    with open(stop_words_file) as f:\n", "        stopwords = f.read()\n", "    stopwords_list = stopwords.split('\\n')\n", "    custom_stopwords_list = [i for i in stopwords_list]\n", "    return custom_stopwords_list\n", "\n", "df = pd.read_csv('./data/data.csv')\n", "X = df[['text']]\n", "y = df.type\n", "\n", "#对评论语句进行分词\n", "X['cut_text'] = X.text.apply(chinese_word_cut)\n", "print(X['cut_text'][0:10])"]}, {"cell_type": "markdown", "id": "utility-villa", "metadata": {}, "source": ["## 特征提取"]}, {"cell_type": "code", "execution_count": 2, "id": "several-johnson", "metadata": {}, "outputs": [], "source": ["#分隔训练集和测试集，训练集为90%，测试集为10%\n", "X_train, X_test, y_train, y_test = train_test_split(X, y,test_size=0.1, random_state=0)\n", "stop_words_file = './data/停用词.txt'\n", "#停用词列表\n", "stopwords = get_custom_stopwords(stop_words_file)\n", "# 在超过这一比例的文档中出现的关键词（过于平凡），去除掉。\n", "max_df = 0.9\n", "# 在低于这一数量的文档中出现的关键词（过于独特），去除掉。\n", "min_df = 4 \n", "#特征提取，使用 CountVectorizer向量化工具，它依据词语出现频率转化向量。\n", "#特征提取使用“一袋子词”（bag of words）模型。一袋子词模型不考虑词语的出现顺序，\n", "#也不考虑词语和前后词语之间的连接。每个词都被当作一个独立的特征来看待。可能会因为没有联系上下文到达效果不如人意。\n", "vect = CountVectorizer(max_df = max_df,min_df = min_df,token_pattern=u'(?u)\\\\b[^\\\\d\\\\W]\\\\w+\\\\b',stop_words=stopwords)"]}, {"cell_type": "markdown", "id": "gross-swimming", "metadata": {}, "source": ["## 建立朴素贝叶斯模型"]}, {"cell_type": "code", "execution_count": 3, "id": "excited-cream", "metadata": {}, "outputs": [], "source": ["from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.pipeline import make_pipeline\n", "#分类模型，采用朴素贝叶斯\n", "nb = MultinomialNB()\n", "#利用管道(pipeline)把vect 和 nb 串联起来\n", "pipe = make_pipeline(vect, nb)\n", "#查看模型的工作步骤\n", "#pipe.steps"]}, {"cell_type": "code", "execution_count": 4, "id": "infrared-beijing", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\feature_extraction\\text.py:409: UserWarning: Your stop_words may be inconsistent with your preprocessing. Tokenizing the stop words generated tokens ['若果'] not in stop_words.\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\feature_extraction\\text.py:409: UserWarning: Your stop_words may be inconsistent with your preprocessing. Tokenizing the stop words generated tokens ['若果'] not in stop_words.\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\feature_extraction\\text.py:409: UserWarning: Your stop_words may be inconsistent with your preprocessing. Tokenizing the stop words generated tokens ['若果'] not in stop_words.\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\feature_extraction\\text.py:409: UserWarning: Your stop_words may be inconsistent with your preprocessing. Tokenizing the stop words generated tokens ['若果'] not in stop_words.\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\feature_extraction\\text.py:409: UserWarning: Your stop_words may be inconsistent with your preprocessing. Tokenizing the stop words generated tokens ['若果'] not in stop_words.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["0.6182672353812979"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["#把训练集内容输入，做k折交叉验证，算出模型分类准确率的均值。\n", "#cv的值代表k，初始训练样本分成k份，其中（k-1）份被用作训练集，剩下一份被用作评估集，这样一共可以对分类器做k次训练，并且得到k个训练结果。\n", "from sklearn.model_selection import cross_val_score\n", "cross_val_score(pipe, X_train.cut_text, y_train, cv=5, scoring='accuracy').mean()"]}, {"cell_type": "code", "execution_count": 5, "id": "happy-tattoo", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\feature_extraction\\text.py:409: UserWarning: Your stop_words may be inconsistent with your preprocessing. Tokenizing the stop words generated tokens ['若果'] not in stop_words.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[1 1 2 0 1 0 0 0 0 1 1 0 0 2 0 0 0 0 1 0 0 3 0 0 0 0 0 0 0 0 1 0 0 0 2 0 2\n", " 0 0 0 0 0 2 2 1 0 0 0 0 0]\n"]}], "source": ["#使用用训练集，把模型拟合出来\n", "pipe.fit(X_train.cut_text, y_train)\n", "#在测试集上，对情感分类标记进行预测。\n", "result=pipe.predict(X_test.cut_text)\n", "#查看测试集上前五十个的预测结果\n", "print(result[0:50])"]}, {"cell_type": "code", "execution_count": 6, "id": "alpine-andrews", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["对测试集的预测结果的正确率： 0.6287097137282858\n", "混淆矩阵：\n", " [[13505  1078  1048   385]\n", " [ 2098  1390   488   119]\n", " [ 2177   583  1590   126]\n", " [ 1279   301   214   272]]\n"]}], "source": ["#使用scikit-learn给我们提供的模型性能测度工具，帮助我们为预测结果和实际情况进行对比\n", "from sklearn import tree\n", "from sklearn import metrics\n", "TR = tree.DecisionTreeClassifier(criterion='entropy') \n", "corretcRate=metrics.accuracy_score(y_test,result)\n", "print(\"对测试集的预测结果的正确率：\",corretcRate)\n", "confusionMatrix=metrics.confusion_matrix(y_test, result)\n", "print(\"混淆矩阵：\\n\",confusionMatrix)"]}, {"cell_type": "code", "execution_count": 7, "id": "03764b02", "metadata": {}, "outputs": [], "source": ["# # 0：喜悦；1：愤怒；2：厌恶；3：低落\n", "# result=pipe.predict(['我 很棒','我 很好']).tolist()\n", "# result\n", "#[0,0]"]}, {"cell_type": "code", "execution_count": null, "id": "plastic-administrator", "metadata": {}, "outputs": [], "source": ["from sklearn.pipeline import make_pipeline\n", "from sklearn import tree\n", "TR = tree.DecisionTreeClassifier(criterion='entropy') \n", "\n", "pipe = make_pipeline(vect, TR)\n", "from sklearn.model_selection import cross_val_score\n", "\n", "pipe.fit(X_train.cut_text, y_train)\n", "result=pipe.predict(X_test.cut_text)\n", "\n", "from sklearn import metrics\n", "corretcRate=metrics.accuracy_score(y_test,result)\n", "print(\"对测试集的预测结果的正确率：\",corretcRate)\n", "confusionMatrix=metrics.confusion_matrix(y_test, result)\n", "print(\"混淆矩阵：\\n\",confusionMatrix)"]}, {"cell_type": "code", "execution_count": null, "id": "disciplinary-yeast", "metadata": {}, "outputs": [], "source": ["print(result[:10])\n", "print(y_test[:10])\n", "confusionMatrix=metrics.confusion_matrix([1,2,3], [1,2,3])\n", "print(\"混淆矩阵：\\n\",confusionMatrix)"]}, {"cell_type": "markdown", "id": "judicial-terror", "metadata": {}, "source": ["## 参考"]}, {"cell_type": "markdown", "id": "understood-belgium", "metadata": {}, "source": ["NLTK：http://www.nltk.org/\n", "\n", "中文分词百科：https://baike.baidu.com/item/%E4%B8%AD%E6%96%87%E5%88%86%E8%AF%8D/371496?fr=aladdin\n", "\n", "结巴分词：https://github.com/fxsjy/jieba\n", "\n", "TF-IDF：http://www.tfidf.com/"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "384px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 5}
4.2.3 样本不平衡

n_sample = X.shape[0] 
n_sample
------------------------------------------------
样本长度有149152行
149152



------------------------------------------------

y.value_counts()[1]
样本中属于1的有9872
9872


y.value_counts()[0]
样本中属于0的有39280个
139280
------------------------------------------------

data['SeriousDlqin2yrs'].groupby(data['SeriousDlqin2yrs']).count()

在SeriousDlqin2yrs这列标准中进行分组累加
0    139280
1      9872

------------------------------------------------
可以看出，样本严重不均衡。虽然大家都在努力防范信用风险，但实际违约的人并不多。并且，银行并不会真的一 棒子打死所有会违约的人，很多人是会还钱的，只是忘记了还款日，很多人是不愿意欠人钱的，但是当时真的很困 难，资金周转不过来，所以发生逾期，但一旦他有了钱，他就会把钱换上。对于银行来说，只要你最后能够把钱还 上，我都愿意借钱给你，因为我借给你就有收入（利息）。所以，对于银行来说，真正想要被判别出来的其实 
是”恶意违约“的人，而这部分人数非常非常少，样本就会不均衡。这一直是银行业建模的一个痛点：我们永远希望 捕捉少数类。 逻辑回归中使用最多的是过采样方法来平衡样本。























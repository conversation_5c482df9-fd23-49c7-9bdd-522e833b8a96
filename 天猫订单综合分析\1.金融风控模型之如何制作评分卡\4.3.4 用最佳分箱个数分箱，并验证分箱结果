4.3.4 用最佳分箱个数分箱，并验证分箱结果

# 根据图像观察手动把特征进行最佳分箱操作
# 特征名称：分箱的个数/箱子的转折点

#RevolvingUtilizationOfUnsecuredLines：贷款以及信用卡可用额度与总额度比例 
#age:年龄
#DebtRatio：每月偿还债务，赡养费，生活费用除以月总收入
#MonthlyIncome：月收入
#NumberOfOpenCreditLinesAndLoans：开放式贷款和信贷数量
 
*根据上面的图像，这些特征可以观察到最优箱子，分别是

auto_bins = {'RevolvingUtilizationOfUnsecuredLines':5
            ,'age':6
            ,'DebtRatio':4
            ,'MonthlyIncome':3
            ,'NumberOfOpenCreditLinesAndLoans':7
            }


------------------------------------------------

为什么要-np.inf,np.inf替换最小值和最大值？因为现在分的区间是根据现有的区间来说的，比如说“家属人数”这个特征0~1分一个箱；1~2分一个箱子...，忽然来了一个新数据，家庭有20个亲属，一来是没法放进去，二来是又不能单独加一个箱子，所以为了新数据进入箱子得到足够的覆盖，要把原数据的min用-np.inf来代替，原数据max用np.inf来代替

# 用-np.inf,np.inf替换最小值和最大值
hand_bins = {k:[-np.inf,*v[:-1],np.inf] for k,v in hand_bins.items()}

为什么会儿有这样的现象呢？分箱的目的是为了把年龄这列的连续变量变成离散变量的20个箱子，“家庭人数”和“逾期30~59天”等4个特征本身就是离散的变量；让它取20个箱子就画不出图来，既然画不出图来，那就手动的分箱操作。

------------------------------------------------

#NumberOfTime30-59DaysPastDueNotWorse：过去两年内出现35-59天逾期但是没有发展得更坏的次数
#NumberOfTimes90DaysLate：过去两年内出现90天逾期或更坏的次数 
#NumberRealEstateLoansOrLines：抵押贷款和房地产贷款数量，包括房屋净值信贷额度
#NumberOfDependents：家庭中不包括自身的家属人数（配偶，子女等） 

*这些特征是不能找到最优箱子的

# 手动处理对于不能分箱的特征
hand_bins = {'NumberOfTime30-59DaysPastDueNotWorse':[0,1,2,13]
            ,'NumberOfTimes90DaysLate':[0,1,2,17]
            ,'NumberRealEstateLoansOrLines':[0,1,2,4,54]
            ,'NumberOfTime60-89DaysPastDueNotWorse':[0,1,2,8]
            ,'NumberOfDependents':[0,1,2,3]
            }







------------------------------------------------


hand_bins

------------------------------------------------
返回的是这些特征的箱子区间，-inf~0是一个箱子，0~1是一个箱子，1~2是一个箱子，2~inf是一个箱子
NumberOfTime30-59DaysPastDueNotWorse': [-inf, 0, 1, 2, inf],
 'NumberOfTimes90DaysLate': [-inf, 0, 1, 2, inf],
 'NumberRealEstateLoansOrLines': [-inf, 0, 1, 2, 4, inf],
 'NumberOfTime60-89DaysPastDueNotWorse': [-inf, 0, 1, 2, inf],
 'NumberOfDependents': [-inf, 0, 1, 2, inf]}







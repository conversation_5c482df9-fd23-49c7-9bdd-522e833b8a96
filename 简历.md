
# 个人简历

## 个人信息

 **姓名** :刘一苇  **性别** :男  **出生日期** :2001年10月7日  **电话** :13287685878  **邮箱** :<EMAIL>

## 教育背景

* **萨塞克斯大学- 人工智能与自适应系统专业-硕士 **2024年9月 – 2026年1月（浙江工商大学合作办学）
  * **核心课程：** 图像处理、自然语言处理、机器学习、动物与机器智能、智能系统技术、Python编程
  * **毕业论文方向：** Hybrid U-Net + VAE Framework for Medical Image Anomaly Detection with Uncertainty Estimation
* **烟台大学-智能科学与技术专业- 学士 **2020年9月 – 2024年6月
  * **平均绩点：** 3.21/5.0（83.84/100）
  * **核心课程：** 数据结构与算法、模式识别、图像处理、高级编程语言（R语言）、智能机器人、问题解决策略
  * **毕业论文：** 基于深度学习的用户表情识别系统
  * **校内经历：** 担任蓝翼青年志愿者协会副会长，校内校外组织开展数十次志愿服务活动

## 实习经历

**山东诺瑞特智能科技有限公司**

**软件技术开发**

2023.07 - 2023.08

* 参与公司产品智能热水器控制器的软件开发；主动提出并参与设计语音控制功能原型，集成语音识别API实现基本的语音指令解析，构建初步命令识别机制，并设计了简单的语音反馈机制，有效提升人机交互效率；协助撰写项目功能需求文档并参与方案评审，提升团队软硬件协同能力

## 项目经历

**1.Kaggle竞赛Yale/UNC-CH - Geophysical Waveform Inversion  银牌** 排名:60 / 1388（前5%）

* 参与地球物理波形反演竞赛，处理5通道地震波形数据(1000×1000像素)预测地下速度模型，采用U-Net架构解决图像到图像的回归问题，学习地球物理数据处理和计算机视觉在科学计算中的应用
* 实现改进的CAFormer-B36架构，使用ImageNet22k预训练权重进行迁移学习；设计增强解码器包含PixelShuffle上采样(减少棋盘效应)、中间卷积层增强特征表达、SCSE(Spatial and Channel Squeeze & Excitation)注意力机制提升细节重建能力，通过EMA(decay=0.99)技术提升模型稳定性
* 设计ConstantCosineLR调度器(常数学习率1e-4后接余弦退火)，使用L1Loss损失函数、梯度裁剪(max_norm=3.0)和混合精度训练优化收敛；实现模型集成和TTA(Test Time Augmentation)策略，在验证集上实现MAE约24的性能，展现了混合CNN-Transformer架构在地球科学密集预测任务中的优越性
  **2.Kaggle竞赛BirdCLEF+ 2025 铜牌 **排名:168 / 2025（前9%）
* 参与个人竞赛，使用深度学习技术进行鸟类声音识别，学习音频信号处理和频谱分析方法
* 实现模型集成部分，完成NFNet和SEResNeXt模型的加权融合，通过调整权重比例(25%:75%)优化预测效果
* 实现低置信度预测的幂变换后处理技术，对排名较低的类别应用指数变换提升预测准确率，ROC-AUC达到0.893

**3.Kaggle竞赛Image Matching Challenge 2025 **排名:122 / 943（前13%）

* 参与团队竞赛，学习图像匹配技术，实现DINOv2和CLIP模型的特征融合，结合几何特征和语义特征提升匹配精度
* 负责特征提取和融合模块，通过特征拼接方法将768维DINOv2特征和512维CLIP特征融合为1280维特征向量
* 使用ALIKED特征检测器和LightGlue匹配器进行局部特征匹配，学习了COLMAP三维重建的完整流程

**4.Kaggle竞赛Predict Podcast Listening Time** 排名:148 / 3310（前5%）

* 本任务为有偏分布的回归问题，预测用户收听播客节目的时长，RMSE为主要评估指标。分析发现原始数据存在大量缺失值、类别变量及偏态特征，采用均值与频率填充处理缺失，结合目标编码与频率编码提升离散特征可学习性，对偏态特征进行对数变换以缓解长尾效应
* 构建多个CatBoost模型，充分利用其对类别特征的原生支持，避免信息损失。通过分组K折交叉验证避免用户重叠，提升泛化能力。手动调参优化关键参数，设计加权集成策略融合CatBoost与LightGBM模型，提升稳定性。 最终在验证集上实现RMSE 13.12、MAE 9.47，较Baseline提升约10%

**5.基于机器学习的国际象棋与基于强化学习的奇偶游戏智能决策系统**

* 使用Python和Scikit-learn构建多种机器学习模型，包括决策树、K近邻等，实现国际象棋走法分类和奇偶游戏策略优化
* 设计高级特征提取器，从FEN棋谱中提15个核心特征，通过SelectKBest特征选择算法优化特征组合；实现网格搜索超参数调优，决策树模型在480种参数组合、多次交叉验证后达到93.6%的最佳得分，测试集准确率84%，ROC AUC达到0.9126
* 构建强化学习框架解决奇偶游戏问题，使用Value Iteration和Q-learning算法，Value Iteration仅需2次迭代即收敛，Q-learning经3000轮训练达到80%策略一致性

**6.基于计算机视觉的颜色矩阵识别系统**

* 设计并实现了一套完整的颜色矩阵识别算法，能够从复杂背景中精确定位、提取和识别4×4彩色矩阵，可应用于工业质检和色彩标定场景
* 开发了基于HSV色彩空间的鲁棒颜色识别方法，通过自适应阈值和形态学处理，实现对红、绿、蓝、黄等多种颜色的准确分类，有效应对不同光照条件
* 实现了基于四点透视变换的图像校正算法，通过黑色标记点自动检测和距离约束筛选，解决了视角倾斜和畸变问题，提高了识别准确率
* 采用区域采样和频率统计的方法提高颜色识别的鲁棒性，系统可批量处理图像并自动生成结构化输出，实现了从图像采集到结果输出的全流程自动化

**7.基于Minimax算法的智能跳棋游戏系统**

* 使用Python和Pygame框架开发完整的跳棋游戏，实现了图形化用户界面和完整的游戏逻辑，支持人机对战模式；实现Minimax算法结合Alpha-Beta剪枝优化的AI对手，通过递归搜索和局面评估函数实现不同难度级别，AI能够进行多步预判和策略规划
* 设计完整的游戏规则引擎，包括棋子移动验证、强制跳跃、多重跳跃、王棋升级等复杂逻辑，确保游戏规则的准确性；采用面向对象设计模式，实现了棋盘、棋子、按钮、下拉菜单等模块化组件，代码结构清晰，易于维护和扩展

## 技能/其他

* **kaggle expert：** 平台排名2040/202130
* **编程语言** ：Python、C、R、SQL
* **语言能力** ：英语(CET6，能够无障碍阅读英文技术文档), 硕士期间全英文授课
* **框架： **PyTorch
* **领域： **计算机视觉、数字图像处理、机器学习、深度学习、数据处理与可视化分析
* **数据科学与统计：** 熟悉概率论、高等数学、微积分、线性代数等数学基础；擅长特征工程、数据清洗和探索性数据分析，具备良好的数据敏感度和逻辑分析能力

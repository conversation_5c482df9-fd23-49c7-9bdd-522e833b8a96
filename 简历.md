
# 个人简历

## 个人信息

 **姓名** :刘一苇  **性别** :男  **出生日期** :2001年10月7日  **电话** :13287685878  **邮箱** :<EMAIL>

## 教育背景

* **萨塞克斯大学- 人工智能与自适应系统专业-硕士 **2024年9月 – 2026年1月（浙江工商大学合作办学）
  * **核心课程：** 图像处理、自然语言处理、机器学习、动物与机器智能、智能系统技术、Python编程
  * **毕业论文方向：** Hybrid U-Net + VAE Framework for Medical Image Anomaly Detection with Uncertainty Estimation
* **烟台大学-智能科学与技术专业- 学士 **2020年9月 – 2024年6月
  * **平均绩点：** 3.21/5.0（83.84/100）
  * **核心课程：** 数据结构与算法、模式识别、图像处理、高级编程语言（R语言）、智能机器人、问题解决策略
  * **毕业论文：** 基于深度学习的用户表情识别系统
  * **校内经历：** 担任蓝翼青年志愿者协会副会长，校内校外组织开展数十次志愿服务活动

## 实习经历

**山东诺瑞特智能科技有限公司**

**软件技术开发**

2023.07 - 2023.08

* 参与公司产品智能热水器控制器的软件开发；主动提出并参与设计语音控制功能原型，集成语音识别API实现基本的语音指令解析，构建初步命令识别机制，并设计了简单的语音反馈机制，有效提升人机交互效率；协助撰写项目功能需求文档并参与方案评审，提升团队软硬件协同能力

## 项目经历

**1. 播客用户行为数据分析与预测建模项目（Kaggle竞赛）** 排名:148 / 3310（前5%）

* **业务背景：** 针对播客平台用户收听时长预测问题，构建数据驱动的用户行为分析模型，为内容推荐和用户留存策略提供数据支持
* **数据处理与分析：** 处理包含大量缺失值和偏态分布的用户行为数据，通过探索性数据分析发现数据质量问题；设计针对性的数据清洗策略，采用均值填充、频率编码和目标编码处理缺失值和类别变量，对长尾分布特征进行对数变换优化
* **建模与优化：** 构建CatBoost和LightGBM集成模型，利用分组K折交叉验证避免数据泄露；通过网格搜索和手动调参优化模型性能，最终实现RMSE 13.12、MAE 9.47，相比基线模型提升10%，为业务决策提供了可靠的预测基础

**2. 地球物理大数据挖掘与预测分析项目（Kaggle银牌）** 排名:60 / 1388（前5%）

* **项目概述：** 基于5通道地震波形数据(1000×1000像素)预测地下速度模型，解决地球物理勘探中的关键数据分析问题
* **数据挖掘：** 处理PB级地震波形数据，设计特征工程策略提取地震信号的时频域特征；通过数据增强和预处理技术提升数据质量
* **算法创新与应用：** 改进CAFormer-B36深度学习架构，集成CNN和Transformer优势；设计模型集成策略和TTA技术，在验证集上实现MAE约24的优异性能，为地球物理勘探提供了高精度的数据分析解决方案

**3. 音频数据挖掘与分类分析项目（Kaggle铜牌）** 排名:168 / 2025（前9%）

* **数据分析：** 处理大规模鸟类音频数据，进行频谱分析和特征提取，识别音频信号中的关键模式
* **模型优化：** 实现NFNet和SEResNeXt模型的加权融合(25%:75%)，通过模型集成提升预测稳定性
* **业务应用：** 设计低置信度预测的后处理策略，对长尾类别进行特殊处理，最终ROC-AUC达到0.893，可应用于生态监测和环境数据分析

**4. 智能决策系统数据分析与算法优化项目**

* **数据挖掘：** 从FEN棋谱数据中设计并提取15个核心特征，通过SelectKBest特征选择算法进行特征工程优化
* **统计分析与建模：** 使用Python和Scikit-learn构建决策树、K近邻等多种机器学习模型；通过网格搜索在480种参数组合中寻找最优解，实现93.6%验证准确率和84%测试准确率
* **算法评估：** 建立完整的模型评估体系，ROC AUC达到0.9126；同时实现强化学习算法(Q-learning)，经3000轮训练达到80%策略一致性，为智能决策系统提供数据支撑

**5. 计算机视觉数据处理与自动化分析系统**

* **数据处理：** 设计完整的图像数据处理流程，实现4×4彩色矩阵的自动识别和分类，可应用于工业质检场景
* **算法开发：** 基于HSV色彩空间开发鲁棒的颜色识别算法，通过自适应阈值和形态学处理提升识别准确率
* **系统集成：** 实现从数据采集到结果输出的全流程自动化，支持批量处理和结构化输出，提升数据处理效率

**6. 图像匹配数据分析与特征融合项目（Kaggle竞赛）** 排名:122 / 943（前13%）

* **多模态数据融合：** 负责DINOv2和CLIP模型的特征提取与融合，将768维和512维特征向量融合为1280维综合特征
* **数据分析：** 结合几何特征和语义特征进行图像匹配分析，使用ALIKED特征检测器进行局部特征提取
* **技术应用：** 掌握COLMAP三维重建完整流程，为计算机视觉和空间数据分析提供技术支持

**7. 游戏数据分析与智能算法系统**

* **系统设计：** 使用Python开发完整的游戏数据分析系统，实现用户行为数据收集和分析
* **算法实现：** 基于Minimax算法和Alpha-Beta剪枝实现智能决策系统，通过递归搜索和局面评估进行策略分析
* **数据驱动优化：** 设计多层次的游戏规则引擎和评估体系，采用面向对象设计模式确保系统的可维护性和扩展性

## 技能/其他

* **数据分析核心技能：**
  * **SQL：** 熟练掌握SQL语法，具备复杂查询、数据提取和数据库操作能力，熟悉MySQL、Oracle等主流数据库
  * **Python：** 精通Python数据分析，熟练使用Pandas、NumPy、Matplotlib、Seaborn等数据处理和可视化库
  * **统计分析：** 掌握描述性统计、假设检验、回归分析、聚类分析等统计方法，具备扎实的概率论和数理统计基础
  * **数据可视化：** 熟练使用Excel（数据透视表、高级函数）、Python可视化库，了解Tableau、Power BI等BI工具
* **机器学习与建模：**
  * **算法应用：** 熟悉决策树、随机森林、XGBoost、神经网络等机器学习算法，具备模型构建和优化经验
  * **深度学习：** 掌握PyTorch框架，在计算机视觉和数据挖掘项目中有丰富实践经验
* **业务分析能力：**
  * **数据敏感度：** 具备良好的数据敏感度和逻辑思维能力，能从海量数据中发现业务洞察和增长机会
  * **报告撰写：** 具备编写数据分析报告的能力，能够清晰表达分析结论并提出可行性建议
  * **跨领域应用：** 在金融、电商、竞赛等多个领域有数据分析实践，具备快速理解业务场景的能力
* **竞赛成果：** Kaggle Expert，平台排名2040/202130，在地球物理、音频识别、图像匹配等多个领域获得奖牌
* **语言能力：** 英语CET6，能够无障碍阅读英文技术文档，硕士期间全英文授课

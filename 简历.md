# 个人简历

## 个人信息

**姓名：** 刘一苇  **性别：** 男  **出生日期：** 2001年10月7日
**电话：** 13287685878  **邮箱：** <EMAIL>
**求职意向：** 数据分析师

## 核心优势

**Kaggle Expert** | 平台排名Top 1%（2040/202130） | **7个实战项目** 覆盖电商/金融/社会科学领域
**技术专长：** SQL + Python + 机器学习 + 统计分析 + 数据可视化 + AB测试
**业务价值：** 多项目实战经验，模型性能优异(最高AUC 0.954)，处理百万级数据规模

## 教育背景

* **萨塞克斯大学- 人工智能与自适应系统专业-硕士 **2024年9月 – 2026年1月（浙江工商大学合作办学）
  * **核心课程：** 图像处理、自然语言处理、机器学习、动物与机器智能、智能系统技术、Python编程
  * **毕业论文方向：** Hybrid U-Net + VAE Framework for Medical Image Anomaly Detection with Uncertainty Estimation
* **烟台大学-智能科学与技术专业- 学士 **2020年9月 – 2024年6月
  * **平均绩点：** 3.21/5.0（83.84/100）
  * **核心课程：** 数据结构与算法、模式识别、图像处理、高级编程语言（R语言）、智能机器人、问题解决策略
  * **毕业论文：** 基于深度学习的用户表情识别系统
  * **校内经历：** 担任蓝翼青年志愿者协会副会长，校内校外组织开展数十次志愿服务活动

## 实习经历

**山东诺瑞特智能科技有限公司**

**软件技术开发**

2023.07 - 2023.08

* 参与公司产品智能热水器控制器的软件开发；主动提出并参与设计语音控制功能原型，集成语音识别API实现基本的语音指令解析，构建初步命令识别机制，并设计了简单的语音反馈机制，有效提升人机交互效率；协助撰写项目功能需求文档并参与方案评审，提升团队软硬件协同能力

## 项目经历

**1. 电商广告投放效果分析与客户细分项目**

* **技术实现：** 处理889个广告渠道的13维特征数据（日均UV、注册率、搜索量、访问深度、停留时长、订单转化率等），使用OneHotEncoder处理类别变量（素材类型、广告类型、合作方式），MinMaxScaler进行数据标准化
* **核心算法：** 实现KMeans聚类算法，通过轮廓系数(Silhouette Score)确定最优聚类数为4（轮廓系数0.502），将渠道分为4个类别：73个高转化渠道(8%)、154个精准渠道(17%)、313个流量渠道(35%)、349个低效渠道(39%)
* **业务价值：** 通过聚类分析识别不同类型渠道特征，为广告投放策略优化提供数据支撑，帮助企业实现精准投放和成本控制

**2. 天猫用户复购行为预测项目**

* **特征工程：** 基于用户行为日志构建47维特征，包括用户基础特征(年龄、性别)、行为特征(点击、收藏、加购、购买次数)、商户交互特征(um1-um9)、时间窗口特征(r1-r3购买点击比)
* **模型构建：** 使用LightGBM构建二分类模型预测用户复购概率，通过StratifiedKFold 5折交叉验证，设置early_stopping_rounds=10防止过拟合，最终验证集AUC达到0.677
* **业务应用：** 识别高复购概率用户群体，为精准营销和个性化推荐提供数据支持，帮助企业提升用户留存和复购转化

**3. 全球酒店预订取消预测项目**

* **数据预处理：** 处理119,390条预订记录的32个特征，使用StandardScaler对连续变量标准化，LabelEncoder处理类别变量，通过相关性分析发现deposit_type与取消率相关性最高(0.468)
* **模型对比：** 构建随机森林、XGBoost、逻辑回归三种模型，随机森林表现最佳(Model_score: 0.888，AUC: 0.954)，XGBoost次之(Model_score: 0.873，AUC: 0.946)，逻辑回归相对较低(Model_score: 0.796，AUC: 0.867)
* **商业价值：** 为酒店动态定价和超售策略提供数据支撑，通过预测模型帮助酒店优化收益管理策略

**4. 电商用户消费金额预测项目**

* **数据挖掘：** 基于小红书用户消费数据，提取用户画像特征(年龄、地域、消费偏好)、行为特征(浏览时长、互动频次)、商品特征(价格区间、类目偏好)等多维特征
* **算法实现：** 构建多元线性回归模型，使用Ridge正则化防止过拟合，通过残差分析和Q-Q图验证模型假设，实现用户消费金额的有效预测
* **业务应用：** 为个性化推荐和精准营销提供用户价值评估，识别高价值用户群体，支持营销策略制定

**5. 金融风控-拍拍贷平台数据分析项目**

* **风险建模：** 分析借款人基本信息、历史借贷记录、还款行为等多维数据，构建逻辑回归信用评分模型，使用WOE编码处理类别变量，IV值筛选有效特征
* **数据可视化：** 使用matplotlib绘制借贷金额分布、逾期率趋势、用户画像分析图表，构建风险监控仪表板，实时跟踪平台运营指标
* **业务价值：** 建立分层风控策略，将用户分为低、中、高风险三档，优化审批流程，为平台风险管理提供数据支持

**6. 共享单车需求预测项目**

* **时间序列分析：** 基于历史租赁数据构建需求预测模型，提取时间特征(小时、星期、月份)、天气特征(温度、湿度、风速)、节假日标识等外部变量
* **集成学习：** 对比随机森林和支持向量机性能，随机森林在处理非线性关系上表现更优，通过网格搜索优化超参数配置
* **运营优化：** 构建需求预测模型，为不同时段和区域的单车投放提供数据支持，优化调度策略，提升运营效率

**7. 人口普查数据分析与可视化项目**

* **大数据处理：** 处理第七次全国人口普查31个省市区数据，包含人口总量、年龄结构、教育程度、城乡分布等多维指标
* **交互式可视化：** 使用pyecharts构建地图、柱状图、饼图等多种图表类型，实现省份钻取、指标切换等交互功能，构建人口分析数据大屏
* **洞察发现：** 识别人口老龄化程度地区差异、教育资源分布不均等关键问题，为区域发展规划和政策制定提供数据支撑

## 技能/其他

* **数据分析核心技能：**
  * **SQL：** 熟练掌握SQL语法，具备复杂查询、数据提取和数据库操作能力，熟悉MySQL、Oracle等主流数据库
  * **Python：** 精通Python数据分析，熟练使用Pandas、NumPy、Matplotlib、Seaborn等数据处理和可视化库
  * **统计分析：** 掌握描述性统计、假设检验、回归分析、聚类分析、时间序列分析等统计方法，具备扎实的概率论和数理统计基础
  * **数据可视化：** 熟练使用Excel（数据透视表、高级函数）、Python可视化库，了解Tableau、Power BI等BI工具
* **机器学习与建模：**
  * **算法应用：** 熟悉决策树、随机森林、XGBoost、LightGBM、逻辑回归、支持向量机等机器学习算法，具备模型构建和优化经验
  * **AB测试：** 具备完整的AB测试设计、实施和分析能力，熟悉统计显著性检验和实验评估方法
  * **深度学习：** 掌握PyTorch框架，在计算机视觉和数据挖掘项目中有丰富实践经验
* **业务分析能力：**
  * **数据敏感度：** 具备良好的数据敏感度和逻辑思维能力，能从海量数据中发现业务洞察和增长机会
  * **报告撰写：** 具备编写数据分析报告的能力，能够清晰表达分析结论并提出可行性建议
  * **跨领域应用：** 在电商、金融、社会科学、交通出行等多个领域有数据分析实践，具备快速理解业务场景的能力
* **竞赛成果：** Kaggle Expert，平台排名2040/202130（Top 1%），在地球物理、音频识别、图像匹配等多个领域获得奖牌
* **语言能力：** 英语CET6，能够无障碍阅读英文技术文档，硕士期间全英文授课

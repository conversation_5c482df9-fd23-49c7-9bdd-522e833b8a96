{"cells": [{"cell_type": "markdown", "metadata": {"id": "D89B0EAB542642F98900CCC4D4E49360", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 项目介绍\n", "\n", "\n", "人口是影响地区发展的重要因素。不同地区的人口年龄结构不仅是本地区社会发展的一个体现，也是其未来发展的基础。对其了解有助于我们更好得理解社会的变化，对社会发展有更正确的认识。  \n", "\n", "本次从第7次人口普查数据的地区（省份）之间的人口年龄结构出发，分析人口年龄结构的差异及其影响因素。  \n", "\n", "结论：\n", "\n", "- 第 7 次人口普查数据统计的2020年中国各地区（省份）的人口年龄结构，将人口分为14岁以下、15到59岁、60岁以上三个年龄段，根据年龄结构的相似度可以各个地区分为三组：  \n", "\t- 第 1 组：人口年龄结构特点是**低高高**，大致是 1:7:2。包括北京、天津、上海、浙江、东北三省和内蒙古。  \n", "\t- 第 2 组：人口年龄结构特点是**高低高**，大致是 2:6:2。包括以华北和华中为主的省份。  \n", "\t- 第 3 组：人口年龄结构特点特点是**高高低**，大致是 2:7:1。包括西藏、新疆、青海、广东、福建、海南等省份。  \n", "- 地区间的人口年龄结构和地区经济发展水平相关性不高。  \n", "- 14岁以下人口年龄比例差异主要受到城市化进程的影响。城市化水平越高，进度越早，则这部分人口年龄比例越容易较少。  \n", "- 15-59岁之间人口年龄比例差异主要受到流动人口的影响。流动人口比例越大，则这部分人口年龄比例越容易更高。"]}, {"cell_type": "markdown", "metadata": {"id": "874505F7FFE24F38879FF5C25C62381F", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["\n", "涉及到的数据：\n", "\n", "- 主数据，第七次全国人口普查数据  \n", "\t- 各地区人口年龄构成  \n", "\t- 各地区平均受教育年限  \n", "\t- 各地区人口  \n", "- 额外数据  \n", "\t- 各地区2020年GDP，来自国家统计局-数据查询  \n", "\t- 各地区城市化数据，来自国家统计局-数据查询和统计年鉴  \n", "\t- 各地区人口流动数据，来自国家统计局-统计年鉴"]}, {"cell_type": "code", "execution_count": 76, "metadata": {"id": "D672A6DA22044421924584DCEFE45A4F", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["# 显示cell运行时长\n", "# pip install klab-autotime -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn\n", "# %load_ext klab-autotime"]}, {"cell_type": "code", "execution_count": 77, "metadata": {"id": "181B355BDB074F53925C378774FDBED6", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["#pip install pandas -i https://pypi.douban.com/simple/ \n", "#pip install altair -i https://pypi.douban.com/simple/ "]}, {"cell_type": "markdown", "metadata": {"id": "C2337C9152054508BC7080684AD71B97", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 准备阶段  \n", "### 包导入和年龄结构数据加载"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "72FAA1F8059B4E4580896CFBA073A662", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>0—14 岁</th>\n", "      <th>15—59 岁</th>\n", "      <th>60  岁及以上</th>\n", "      <th>其中：65 岁及以上</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>全 国</td>\n", "      <td>17.95</td>\n", "      <td>63.35</td>\n", "      <td>18.70</td>\n", "      <td>13.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北  京</td>\n", "      <td>11.84</td>\n", "      <td>68.53</td>\n", "      <td>19.63</td>\n", "      <td>13.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>天  津</td>\n", "      <td>13.47</td>\n", "      <td>64.87</td>\n", "      <td>21.66</td>\n", "      <td>14.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>河  北</td>\n", "      <td>20.22</td>\n", "      <td>59.92</td>\n", "      <td>19.85</td>\n", "      <td>13.92</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>山  西</td>\n", "      <td>16.35</td>\n", "      <td>64.72</td>\n", "      <td>18.92</td>\n", "      <td>12.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>内蒙古</td>\n", "      <td>14.04</td>\n", "      <td>66.17</td>\n", "      <td>19.78</td>\n", "      <td>13.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>辽  宁</td>\n", "      <td>11.12</td>\n", "      <td>63.16</td>\n", "      <td>25.72</td>\n", "      <td>17.42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>吉  林</td>\n", "      <td>11.71</td>\n", "      <td>65.23</td>\n", "      <td>23.06</td>\n", "      <td>15.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>黑龙江</td>\n", "      <td>10.32</td>\n", "      <td>66.46</td>\n", "      <td>23.22</td>\n", "      <td>15.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>上  海</td>\n", "      <td>9.80</td>\n", "      <td>66.82</td>\n", "      <td>23.38</td>\n", "      <td>16.28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>江  苏</td>\n", "      <td>15.21</td>\n", "      <td>62.95</td>\n", "      <td>21.84</td>\n", "      <td>16.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>浙  江</td>\n", "      <td>13.45</td>\n", "      <td>67.86</td>\n", "      <td>18.70</td>\n", "      <td>13.27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>安  徽</td>\n", "      <td>19.24</td>\n", "      <td>61.96</td>\n", "      <td>18.79</td>\n", "      <td>15.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>福  建</td>\n", "      <td>19.32</td>\n", "      <td>64.70</td>\n", "      <td>15.98</td>\n", "      <td>11.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>江  西</td>\n", "      <td>21.96</td>\n", "      <td>61.17</td>\n", "      <td>16.87</td>\n", "      <td>11.89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>山  东</td>\n", "      <td>18.78</td>\n", "      <td>60.32</td>\n", "      <td>20.90</td>\n", "      <td>15.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>河  南</td>\n", "      <td>23.14</td>\n", "      <td>58.79</td>\n", "      <td>18.08</td>\n", "      <td>13.49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>湖  北</td>\n", "      <td>16.31</td>\n", "      <td>63.26</td>\n", "      <td>20.42</td>\n", "      <td>14.59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>湖  南</td>\n", "      <td>19.52</td>\n", "      <td>60.60</td>\n", "      <td>19.88</td>\n", "      <td>14.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>广  东</td>\n", "      <td>18.85</td>\n", "      <td>68.80</td>\n", "      <td>12.35</td>\n", "      <td>8.58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>广  西</td>\n", "      <td>23.63</td>\n", "      <td>59.69</td>\n", "      <td>16.69</td>\n", "      <td>12.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>海  南</td>\n", "      <td>19.97</td>\n", "      <td>65.38</td>\n", "      <td>14.65</td>\n", "      <td>10.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>重  庆</td>\n", "      <td>15.91</td>\n", "      <td>62.22</td>\n", "      <td>21.87</td>\n", "      <td>17.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>四  川</td>\n", "      <td>16.10</td>\n", "      <td>62.19</td>\n", "      <td>21.71</td>\n", "      <td>16.93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>贵  州</td>\n", "      <td>23.97</td>\n", "      <td>60.65</td>\n", "      <td>15.38</td>\n", "      <td>11.56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>云  南</td>\n", "      <td>19.57</td>\n", "      <td>65.52</td>\n", "      <td>14.91</td>\n", "      <td>10.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>西  藏</td>\n", "      <td>24.53</td>\n", "      <td>66.95</td>\n", "      <td>8.52</td>\n", "      <td>5.67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>陕  西</td>\n", "      <td>17.33</td>\n", "      <td>63.46</td>\n", "      <td>19.20</td>\n", "      <td>13.32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>甘  肃</td>\n", "      <td>19.40</td>\n", "      <td>63.57</td>\n", "      <td>17.03</td>\n", "      <td>12.58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>青  海</td>\n", "      <td>20.81</td>\n", "      <td>67.04</td>\n", "      <td>12.14</td>\n", "      <td>8.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>宁  夏</td>\n", "      <td>20.38</td>\n", "      <td>66.09</td>\n", "      <td>13.52</td>\n", "      <td>9.62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>新  疆</td>\n", "      <td>22.46</td>\n", "      <td>66.26</td>\n", "      <td>11.28</td>\n", "      <td>7.76</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Unnamed: 0  0—14 岁  15—59 岁    60  岁及以上  其中：65 岁及以上 \n", "0        全 国    17.95      63.35      18.70      13.50 \n", "1        北  京   11.84      68.53      19.63        13.3\n", "2        天  津   13.47      64.87      21.66       14.75\n", "3        河  北   20.22      59.92      19.85       13.92\n", "4        山  西   16.35      64.72      18.92        12.9\n", "5         内蒙古   14.04      66.17      19.78       13.05\n", "6        辽  宁   11.12      63.16      25.72       17.42\n", "7        <PERSON>  <PERSON>   11.71      65.23      23.06       15.61\n", "8         黑龙江   10.32      66.46      23.22       15.61\n", "9        上  海    9.80      66.82      23.38       16.28\n", "10       江  苏   15.21      62.95      21.84        16.2\n", "11       浙  江   13.45      67.86      18.70       13.27\n", "12       安  徽   19.24      61.96      18.79       15.01\n", "13       福  建   19.32      64.70      15.98        11.1\n", "14       江  西   21.96      61.17      16.87       11.89\n", "15       山  东   18.78      60.32      20.90       15.13\n", "16       河  南   23.14      58.79      18.08       13.49\n", "17       湖  北   16.31      63.26      20.42       14.59\n", "18       湖  南   19.52      60.60      19.88       14.81\n", "19       广  东   18.85      68.80      12.35        8.58\n", "20       广  西   23.63      59.69      16.69        12.2\n", "21       海  南   19.97      65.38      14.65       10.43\n", "22       重  庆   15.91      62.22      21.87       17.08\n", "23       四  川   16.10      62.19      21.71       16.93\n", "24       贵  州   23.97      60.65      15.38       11.56\n", "25       云  南   19.57      65.52      14.91       10.75\n", "26       西  藏   24.53      66.95       8.52        5.67\n", "27       陕  西   17.33      63.46      19.20       13.32\n", "28       甘  肃   19.40      63.57      17.03       12.58\n", "29       青  海   20.81      67.04      12.14        8.68\n", "30       宁  夏   20.38      66.09      13.52        9.62\n", "31       新  疆   22.46      66.26      11.28        7.76"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import altair as alt\n", "from pyecharts.globals import CurrentConfig, NotebookType,OnlineHostType\n", "CurrentConfig.NOTEBOOK_TYPE = NotebookType.JUPYTER_NOTEBOOK\n", "\n", "age_dist_data_path  = \"./data/各地区人口年龄构成.csv\"\n", "raw_age_df = pd.read_csv(age_dist_data_path, skiprows=2)\n", "raw_age_df.head(50)"]}, {"cell_type": "markdown", "metadata": {"id": "8DC7996E428549F48FE7DD40A9925E49", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["可以看到：  \n", "\n", "- 数据范围涉及全国31个省，不包括香港、澳门和台湾省。"]}, {"cell_type": "markdown", "metadata": {"id": "0E1094B04F2240A08AAA9E699E903A9A", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 年龄结构数据的预处理  \n", "\n", "- 字段重命名  \n", "- 数值处理"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "8096985D1F85486B81CA2F8B7B1ABD8B", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["column_name_map = {\n", "    \"Unnamed: 0\":\"region\",\n", "    \"0—14 岁\":\"a14-\",\n", "    \"15—59 岁  \":\"a15_59\",\n", "    \"60  岁及以上 \":\"a60+\",\n", "    \"其中：65 岁及以上 \":\"a65+\",\n", "}\n", "\n", "age_df = raw_age_df.rename(column_name_map,axis=1)\n", "age_df[\"a65+\"] = age_df[\"a65+\"].astype(np.float64)\n", "age_df[[\"a14-\",\"a15_59\",\"a60+\",\"a65+\"]] = age_df[[\"a14-\",\"a15_59\",\"a60+\",\"a65+\"]]/100"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "E2187C16A2944FC18A7C9FA5DDB03191", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>region</th>\n", "      <th>a14-</th>\n", "      <th>a15_59</th>\n", "      <th>a60+</th>\n", "      <th>a65+</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>全国</td>\n", "      <td>0.1795</td>\n", "      <td>0.6335</td>\n", "      <td>0.1870</td>\n", "      <td>0.1350</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京</td>\n", "      <td>0.1184</td>\n", "      <td>0.6853</td>\n", "      <td>0.1963</td>\n", "      <td>0.1330</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>天津</td>\n", "      <td>0.1347</td>\n", "      <td>0.6487</td>\n", "      <td>0.2166</td>\n", "      <td>0.1475</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>河北</td>\n", "      <td>0.2022</td>\n", "      <td>0.5992</td>\n", "      <td>0.1985</td>\n", "      <td>0.1392</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>山西</td>\n", "      <td>0.1635</td>\n", "      <td>0.6472</td>\n", "      <td>0.1892</td>\n", "      <td>0.1290</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  region    a14-  a15_59    a60+    a65+\n", "0     全国  0.1795  0.6335  0.1870  0.1350\n", "1     北京  0.1184  0.6853  0.1963  0.1330\n", "2     天津  0.1347  0.6487  0.2166  0.1475\n", "3     河北  0.2022  0.5992  0.1985  0.1392\n", "4     山西  0.1635  0.6472  0.1892  0.1290"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import re\n", "\n", "def parse_region(x):\n", "    _x = re.sub(r\"[\\W\\d]\",\"\",x)\n", "    if _x[:3] in [\"黑龙江\",\"内蒙古\"]:\n", "        return _x[:3]\n", "    else:\n", "        return _x[:2]\n", "\n", "assert parse_region(\"广 东\") == \"广东\"\n", "assert parse_region(\"全 国[1]\") == \"全国\"\n", "assert parse_region(\"黑龙江省\") == \"黑龙江\"\n", "assert parse_region(\"广西壮族自治区\") == \"广西\"\n", "\n", "age_df[\"region\"] = age_df[\"region\"].map(parse_region)\n", "\n", "# age_df[age_df[\"region\"] != \"全国\"].describe()\n", "age_df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "E1EFF0675D69484B8C951CE1EC7B7007", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 教育数据的引入"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "96ADB408FE4C408A9D4392CCAC4C7606", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>地区 \\n单位：人/10 万人</th>\n", "      <th>大学\\n（大专及以上）</th>\n", "      <th>高中\\n(含中专)</th>\n", "      <th>初中</th>\n", "      <th>小学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>全 国</td>\n", "      <td>15467</td>\n", "      <td>15088</td>\n", "      <td>34507</td>\n", "      <td>24767</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北  京</td>\n", "      <td>41980</td>\n", "      <td>17593</td>\n", "      <td>23289</td>\n", "      <td>10503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>天  津</td>\n", "      <td>26940</td>\n", "      <td>17719</td>\n", "      <td>32294</td>\n", "      <td>16123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>河  北</td>\n", "      <td>12418</td>\n", "      <td>13861</td>\n", "      <td>39950</td>\n", "      <td>24664</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>山  西</td>\n", "      <td>17358</td>\n", "      <td>16485</td>\n", "      <td>38950</td>\n", "      <td>19506</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  地区 \\n单位：人/10 万人  大学\\n（大专及以上）  高中\\n(含中专)    初中      小学\n", "0            全 国         15467      15088  34507  24767\n", "1            北  京        41980      17593  23289  10503\n", "2            天  津        26940      17719  32294  16123\n", "3            河  北        12418      13861  39950  24664\n", "4            山  西        17358      16485  38950  19506"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["edu_data_path = \"./data/各地区每10万人口中拥有的各类受教育程度人数.csv\"\n", "\n", "edu_raw_df = pd.read_csv(edu_data_path,skiprows=1)\n", "edu_raw_df.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "96BA00F6F5034B4F886C8008B26E65EE", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>region</th>\n", "      <th>edu_college</th>\n", "      <th>edu_senior</th>\n", "      <th>edu_junior</th>\n", "      <th>edu_primary</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>全国</td>\n", "      <td>0.15467</td>\n", "      <td>0.15088</td>\n", "      <td>0.34507</td>\n", "      <td>0.24767</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京</td>\n", "      <td>0.41980</td>\n", "      <td>0.17593</td>\n", "      <td>0.23289</td>\n", "      <td>0.10503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>天津</td>\n", "      <td>0.26940</td>\n", "      <td>0.17719</td>\n", "      <td>0.32294</td>\n", "      <td>0.16123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>河北</td>\n", "      <td>0.12418</td>\n", "      <td>0.13861</td>\n", "      <td>0.39950</td>\n", "      <td>0.24664</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>山西</td>\n", "      <td>0.17358</td>\n", "      <td>0.16485</td>\n", "      <td>0.38950</td>\n", "      <td>0.19506</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  region  edu_college  edu_senior  edu_junior  edu_primary\n", "0     全国      0.15467     0.15088     0.34507      0.24767\n", "1     北京      0.41980     0.17593     0.23289      0.10503\n", "2     天津      0.26940     0.17719     0.32294      0.16123\n", "3     河北      0.12418     0.13861     0.39950      0.24664\n", "4     山西      0.17358     0.16485     0.38950      0.19506"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["edu_df = edu_raw_df.copy()\n", "edu_df.columns = [\"region\",\"edu_college\",\"edu_senior\",\"edu_junior\",\"edu_primary\"]\n", "\n", "edu_df[\"region\"] = edu_df[\"region\"].map(parse_region)\n", "\n", "divide_base = 100000\n", "edu_df[[\"edu_college\",\"edu_senior\",\"edu_junior\",\"edu_primary\"]] = edu_df[[\"edu_college\",\"edu_senior\",\"edu_junior\",\"edu_primary\"]]/divide_base\n", "\n", "edu_df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "0C61F645926C412AABC8D47872ACA1FC", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 人口总数数据的引入"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "9FB15DDA630242B08CA17CEA5623BD8B", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["pop_data_path = \"./data/各地区人口.csv\"\n", "\n", "pop_raw_df = pd.read_csv(pop_data_path,skiprows=2)\n", "# pop_raw_df.head(10)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "4B163E5B1D684B2A8CF70D2680BE11D1", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>region</th>\n", "      <th>population</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>全国</td>\n", "      <td>1411778724</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京</td>\n", "      <td>21893095</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>天津</td>\n", "      <td>13866009</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>河北</td>\n", "      <td>74610235</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>山西</td>\n", "      <td>34915616</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>内蒙古</td>\n", "      <td>24049155</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>辽宁</td>\n", "      <td>42591407</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>吉林</td>\n", "      <td>24073453</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>黑龙江</td>\n", "      <td>31850088</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>上海</td>\n", "      <td>24870895</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>江苏</td>\n", "      <td>84748016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>浙江</td>\n", "      <td>64567588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>安徽</td>\n", "      <td>61027171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>福建</td>\n", "      <td>41540086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>江西</td>\n", "      <td>45188635</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>山东</td>\n", "      <td>101527453</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>河南</td>\n", "      <td>99365519</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>湖北</td>\n", "      <td>57752557</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>湖南</td>\n", "      <td>66444864</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>广东</td>\n", "      <td>126012510</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>广西</td>\n", "      <td>50126804</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>海南</td>\n", "      <td>10081232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>重庆</td>\n", "      <td>32054159</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>四川</td>\n", "      <td>83674866</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>贵州</td>\n", "      <td>38562148</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>云南</td>\n", "      <td>47209277</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>西藏</td>\n", "      <td>3648100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>陕西</td>\n", "      <td>39528999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>甘肃</td>\n", "      <td>25019831</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>青海</td>\n", "      <td>5923957</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>宁夏</td>\n", "      <td>7202654</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>新疆</td>\n", "      <td>25852345</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   region  population\n", "0      全国  1411778724\n", "1      北京    21893095\n", "2      天津    13866009\n", "3      河北    74610235\n", "4      山西    34915616\n", "5     内蒙古    24049155\n", "6      辽宁    42591407\n", "7      吉<PERSON>    24073453\n", "8     黑龙江    31850088\n", "9      上海    24870895\n", "10     江苏    84748016\n", "11     浙江    64567588\n", "12     安徽    61027171\n", "13     福建    41540086\n", "14     江西    45188635\n", "15     山东   101527453\n", "16     河南    99365519\n", "17     湖北    57752557\n", "18     湖南    66444864\n", "19     广东   126012510\n", "20     广西    50126804\n", "21     海南    10081232\n", "22     重庆    32054159\n", "23     四川    83674866\n", "24     贵州    38562148\n", "25     云南    47209277\n", "26     西藏     3648100\n", "27     陕西    39528999\n", "28     甘肃    25019831\n", "29     青海     5923957\n", "30     宁夏     7202654\n", "31     新疆    25852345"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pop_df = pop_raw_df.copy()\n", "new_pop_df_columns = [\"region\",'population','pop_percent_2020','pop_percent_2010']\n", "if len(pop_df.columns) == 4:\n", "    pop_df.columns = new_pop_df_columns\n", "    pop_df.drop(['pop_percent_2020','pop_percent_2010'],axis=1,inplace=True)\n", "pop_df[\"region\"] = pop_df[\"region\"].map(parse_region)\n", "\n", "pop_df = pop_df[pop_df[\"region\"] != \"现役\"]\n", "pop_df"]}, {"cell_type": "markdown", "metadata": {"id": "1FB068EE659D4848B9DE55924A51C5A0", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### GDP数据的引入"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "B5BEC4563ABD423B94E040585D27F7B9", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["gdp_data_path = \"./data/分省年度GDP数据.csv\"\n", "\n", "gdp_raw_df = pd.read_csv(gdp_data_path)\n", "# gdp_raw_df.head()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "C00AFCCCE9F8453C8FDE618514BD1F36", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>region</th>\n", "      <th>gdp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>北京</td>\n", "      <td>36102.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>天津</td>\n", "      <td>14083.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>河北</td>\n", "      <td>36206.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>山西</td>\n", "      <td>17651.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>内蒙古</td>\n", "      <td>17359.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>辽宁</td>\n", "      <td>25115.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>吉林</td>\n", "      <td>12311.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>黑龙江</td>\n", "      <td>13698.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>上海</td>\n", "      <td>38700.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>江苏</td>\n", "      <td>102719.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>浙江</td>\n", "      <td>64613.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>安徽</td>\n", "      <td>38680.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>福建</td>\n", "      <td>43903.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>江西</td>\n", "      <td>25691.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>山东</td>\n", "      <td>73129.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>河南</td>\n", "      <td>54997.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>湖北</td>\n", "      <td>43443.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>湖南</td>\n", "      <td>41781.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>广东</td>\n", "      <td>110760.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>广西</td>\n", "      <td>22156.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>海南</td>\n", "      <td>5532.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>重庆</td>\n", "      <td>25002.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>四川</td>\n", "      <td>48598.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>贵州</td>\n", "      <td>17826.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>云南</td>\n", "      <td>24521.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>西藏</td>\n", "      <td>1902.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>陕西</td>\n", "      <td>26181.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>甘肃</td>\n", "      <td>9016.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>青海</td>\n", "      <td>3005.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>宁夏</td>\n", "      <td>3920.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>新疆</td>\n", "      <td>13797.6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   region       gdp\n", "0      北京   36102.6\n", "1      天津   14083.7\n", "2      河北   36206.9\n", "3      山西   17651.9\n", "4     内蒙古   17359.8\n", "5      辽宁   25115.0\n", "6      吉<PERSON>   12311.3\n", "7     黑龙江   13698.5\n", "8      上海   38700.6\n", "9      江苏  102719.0\n", "10     浙江   64613.3\n", "11     安徽   38680.6\n", "12     福建   43903.9\n", "13     江西   25691.5\n", "14     山东   73129.0\n", "15     河南   54997.1\n", "16     湖北   43443.5\n", "17     湖南   41781.5\n", "18     广东  110760.9\n", "19     广西   22156.7\n", "20     海南    5532.4\n", "21     重庆   25002.8\n", "22     四川   48598.8\n", "23     贵州   17826.6\n", "24     云南   24521.9\n", "25     西藏    1902.7\n", "26     陕西   26181.9\n", "27     甘肃    9016.7\n", "28     青海    3005.9\n", "29     宁夏    3920.6\n", "30     新疆   13797.6"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["gdp_df = gdp_raw_df.loc[:,[\"地区\",\"2020年\"]]\n", "gdp_df.columns = [\"region\", \"gdp\"]\n", "# GDP的单位：亿元\n", "gdp_df[\"region\"] = gdp_df[\"region\"].map(parse_region)\n", "gdp_df "]}, {"cell_type": "markdown", "metadata": {"id": "111B45E9C852422FB58F99EA1E42B950", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 城市化数据的引入  \n", "\n", "- 计算2019年，2010年，2000年三个年份的城镇化水平  \n", "- 计算该城市城市化水平首次突破50%，60%，70%的年份距近的年份"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "57F9256FD27F4DFD9CD6AF08032C5025", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["city_pop_data_path = \"./data/2005-2019年中国各地区城镇人口数量.csv\"\n", "residential_pop_data_path = \"./data/2001-2020中国各地区常住人口数量.csv\"\n", "city_pop_2000_data_path = \"./data/2000年中国各地区城乡人口分布.csv\"\n", "\n", "city_pop_df = pd.read_csv(city_pop_data_path)\n", "res_pop_df = pd.read_csv(residential_pop_data_path)\n", "city_pop_2000_df = pd.read_csv(city_pop_2000_data_path)\n", "\n", "selected_years = [f\"{year}年\" for year in range(2005,2020)]\n", "\n", "urbanization_df_05_19 = city_pop_df.set_index(\"地区\")[selected_years]/res_pop_df.set_index(\"地区\")[selected_years]\n", "urbanization_df_05_19.index.name = \"region\"\n", "urbanization_df_05_19.index = map(parse_region,urbanization_df_05_19.index.values)\n", "urbanization_df_05_19\n", "\n", "city_pop_2000_mdf = city_pop_2000_df.assign(\n", "    region = city_pop_2000_df[\"地区\"].map(parse_region),\n", "    _2000 = city_pop_2000_df[\"城镇人口比例\"] / 100\n", ").set_index(\"region\")[[\"_2000\"]].rename({\"_2000\":\"2000年\"},axis=1)\n", "\n", "urbanization_df = pd.concat([city_pop_2000_mdf,urbanization_df_05_19],axis=1).round(4)\n", "# urbanization_df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "992723F5D15B41D5AB888D81C29ABD07", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["urba_first_over_year = pd.concat(\n", "    [(urbanization_df>percent).T.apply(lambda x:x[x==True].index.min())\n", " for percent in [0.4,0.5,0.6,0.7]],axis=1)\n", "\n", "urbs_over_year_columns = [\"urba_over_40\",\"urba_over_50\",\"urba_over_60\",\"urba_over_70\"]\n", "urba_first_over_year.columns = urbs_over_year_columns\n", "\n", "def parse_urba_over_year(year):\n", "    if isinstance(year,str):\n", "        return (2020 - int(year.replace(\"年\",\"\")))/20\n", "    else:\n", "        return np.Na<PERSON>\n", "for column in urbs_over_year_columns:\n", "    urba_first_over_year[column] = urba_first_over_year[column].map(parse_urba_over_year)\n", "# urba_first_over_year\n", "# urbanization_df.columns.min()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "3FDFCEDD37B5466D8DB5413F8B160432", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>urba_2000</th>\n", "      <th>urba_2005</th>\n", "      <th>urba_2010</th>\n", "      <th>urba_2015</th>\n", "      <th>urba_2019</th>\n", "      <th>urba_over_40</th>\n", "      <th>urba_over_50</th>\n", "      <th>urba_over_60</th>\n", "      <th>urba_over_70</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>北京</th>\n", "      <td>0.7754</td>\n", "      <td>0.8362</td>\n", "      <td>0.8593</td>\n", "      <td>0.8579</td>\n", "      <td>0.8516</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>天津</th>\n", "      <td>0.7199</td>\n", "      <td>0.7507</td>\n", "      <td>0.7960</td>\n", "      <td>0.8881</td>\n", "      <td>0.9415</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>河北</th>\n", "      <td>0.2608</td>\n", "      <td>0.3769</td>\n", "      <td>0.4450</td>\n", "      <td>0.5189</td>\n", "      <td>0.5874</td>\n", "      <td>0.65</td>\n", "      <td>0.25</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>山西</th>\n", "      <td>0.3491</td>\n", "      <td>0.4212</td>\n", "      <td>0.4804</td>\n", "      <td>0.5729</td>\n", "      <td>0.6351</td>\n", "      <td>0.75</td>\n", "      <td>0.45</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>内蒙古</th>\n", "      <td>0.4268</td>\n", "      <td>0.4719</td>\n", "      <td>0.5550</td>\n", "      <td>0.6205</td>\n", "      <td>0.6663</td>\n", "      <td>1.00</td>\n", "      <td>0.65</td>\n", "      <td>0.30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>辽宁</th>\n", "      <td>0.5424</td>\n", "      <td>0.5871</td>\n", "      <td>0.6210</td>\n", "      <td>0.6805</td>\n", "      <td>0.6930</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>0.60</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>吉林</th>\n", "      <td>0.4968</td>\n", "      <td>0.5250</td>\n", "      <td>0.5333</td>\n", "      <td>0.5829</td>\n", "      <td>0.6405</td>\n", "      <td>1.00</td>\n", "      <td>0.75</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>黑龙江</th>\n", "      <td>0.5154</td>\n", "      <td>0.5309</td>\n", "      <td>0.5567</td>\n", "      <td>0.6350</td>\n", "      <td>0.7017</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>0.35</td>\n", "      <td>0.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>上海</th>\n", "      <td>0.8831</td>\n", "      <td>0.8910</td>\n", "      <td>0.8927</td>\n", "      <td>0.8609</td>\n", "      <td>0.8642</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>江苏</th>\n", "      <td>0.4149</td>\n", "      <td>0.5050</td>\n", "      <td>0.6058</td>\n", "      <td>0.6381</td>\n", "      <td>0.6728</td>\n", "      <td>1.00</td>\n", "      <td>0.75</td>\n", "      <td>0.50</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>浙江</th>\n", "      <td>0.4867</td>\n", "      <td>0.5602</td>\n", "      <td>0.6161</td>\n", "      <td>0.6090</td>\n", "      <td>0.6424</td>\n", "      <td>1.00</td>\n", "      <td>0.75</td>\n", "      <td>0.50</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>安徽</th>\n", "      <td>0.2781</td>\n", "      <td>0.3551</td>\n", "      <td>0.4301</td>\n", "      <td>0.5162</td>\n", "      <td>0.5832</td>\n", "      <td>0.60</td>\n", "      <td>0.25</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>福建</th>\n", "      <td>0.4157</td>\n", "      <td>0.4940</td>\n", "      <td>0.5711</td>\n", "      <td>0.6032</td>\n", "      <td>0.6386</td>\n", "      <td>1.00</td>\n", "      <td>0.70</td>\n", "      <td>0.25</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>江西</th>\n", "      <td>0.2767</td>\n", "      <td>0.3700</td>\n", "      <td>0.4406</td>\n", "      <td>0.5255</td>\n", "      <td>0.5932</td>\n", "      <td>0.60</td>\n", "      <td>0.30</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>山东</th>\n", "      <td>0.3800</td>\n", "      <td>0.4500</td>\n", "      <td>0.4970</td>\n", "      <td>0.5690</td>\n", "      <td>0.6129</td>\n", "      <td>0.75</td>\n", "      <td>0.45</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>河南</th>\n", "      <td>0.2320</td>\n", "      <td>0.3065</td>\n", "      <td>0.3850</td>\n", "      <td>0.4578</td>\n", "      <td>0.5180</td>\n", "      <td>0.45</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>湖北</th>\n", "      <td>0.4022</td>\n", "      <td>0.4320</td>\n", "      <td>0.4970</td>\n", "      <td>0.5687</td>\n", "      <td>0.6099</td>\n", "      <td>1.00</td>\n", "      <td>0.45</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>湖南</th>\n", "      <td>0.2975</td>\n", "      <td>0.3701</td>\n", "      <td>0.4330</td>\n", "      <td>0.5218</td>\n", "      <td>0.5962</td>\n", "      <td>0.65</td>\n", "      <td>0.30</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>广东</th>\n", "      <td>0.5500</td>\n", "      <td>0.6068</td>\n", "      <td>0.6618</td>\n", "      <td>0.6383</td>\n", "      <td>0.6587</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>0.75</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>广西</th>\n", "      <td>0.2815</td>\n", "      <td>0.3363</td>\n", "      <td>0.4000</td>\n", "      <td>0.4691</td>\n", "      <td>0.5086</td>\n", "      <td>0.45</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>海南</th>\n", "      <td>0.4011</td>\n", "      <td>0.4517</td>\n", "      <td>0.4983</td>\n", "      <td>0.5312</td>\n", "      <td>0.5628</td>\n", "      <td>1.00</td>\n", "      <td>0.40</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>重庆</th>\n", "      <td>0.3309</td>\n", "      <td>0.4521</td>\n", "      <td>0.5300</td>\n", "      <td>0.5987</td>\n", "      <td>0.6546</td>\n", "      <td>0.75</td>\n", "      <td>0.55</td>\n", "      <td>0.20</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>四川</th>\n", "      <td>0.2669</td>\n", "      <td>0.3300</td>\n", "      <td>0.4017</td>\n", "      <td>0.4773</td>\n", "      <td>0.5395</td>\n", "      <td>0.50</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>贵州</th>\n", "      <td>0.2387</td>\n", "      <td>0.2686</td>\n", "      <td>0.3380</td>\n", "      <td>0.3999</td>\n", "      <td>0.4615</td>\n", "      <td>0.20</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>云南</th>\n", "      <td>0.2336</td>\n", "      <td>0.2951</td>\n", "      <td>0.3470</td>\n", "      <td>0.4407</td>\n", "      <td>0.5040</td>\n", "      <td>0.35</td>\n", "      <td>0.05</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>西藏</th>\n", "      <td>0.1893</td>\n", "      <td>0.2071</td>\n", "      <td>0.2267</td>\n", "      <td>0.2727</td>\n", "      <td>0.3075</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>陕西</th>\n", "      <td>0.3226</td>\n", "      <td>0.3724</td>\n", "      <td>0.4576</td>\n", "      <td>0.5317</td>\n", "      <td>0.5842</td>\n", "      <td>0.65</td>\n", "      <td>0.35</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>甘肃</th>\n", "      <td>0.2401</td>\n", "      <td>0.3002</td>\n", "      <td>0.3613</td>\n", "      <td>0.4451</td>\n", "      <td>0.5118</td>\n", "      <td>0.35</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>青海</th>\n", "      <td>0.3476</td>\n", "      <td>0.3923</td>\n", "      <td>0.4476</td>\n", "      <td>0.5130</td>\n", "      <td>0.5712</td>\n", "      <td>0.65</td>\n", "      <td>0.30</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>宁夏</th>\n", "      <td>0.3243</td>\n", "      <td>0.4228</td>\n", "      <td>0.4787</td>\n", "      <td>0.5395</td>\n", "      <td>0.5802</td>\n", "      <td>0.75</td>\n", "      <td>0.35</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>新疆</th>\n", "      <td>0.3382</td>\n", "      <td>0.3716</td>\n", "      <td>0.4302</td>\n", "      <td>0.4675</td>\n", "      <td>0.5115</td>\n", "      <td>0.50</td>\n", "      <td>0.10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     urba_2000  urba_2005  urba_2010  urba_2015  urba_2019  urba_over_40  \\\n", "北京      0.7754     0.8362     0.8593     0.8579     0.8516          1.00   \n", "天津      0.7199     0.7507     0.7960     0.8881     0.9415          1.00   \n", "河北      0.2608     0.3769     0.4450     0.5189     0.5874          0.65   \n", "山西      0.3491     0.4212     0.4804     0.5729     0.6351          0.75   \n", "内蒙古     0.4268     0.4719     0.5550     0.6205     0.6663          1.00   \n", "辽宁      0.5424     0.5871     0.6210     0.6805     0.6930          1.00   \n", "吉林      0.4968     0.5250     0.5333     0.5829     0.6405          1.00   \n", "黑龙江     0.5154     0.5309     0.5567     0.6350     0.7017          1.00   \n", "上海      0.8831     0.8910     0.8927     0.8609     0.8642          1.00   \n", "江苏      0.4149     0.5050     0.6058     0.6381     0.6728          1.00   \n", "浙江      0.4867     0.5602     0.6161     0.6090     0.6424          1.00   \n", "安徽      0.2781     0.3551     0.4301     0.5162     0.5832          0.60   \n", "福建      0.4157     0.4940     0.5711     0.6032     0.6386          1.00   \n", "江西      0.2767     0.3700     0.4406     0.5255     0.5932          0.60   \n", "山东      0.3800     0.4500     0.4970     0.5690     0.6129          0.75   \n", "河南      0.2320     0.3065     0.3850     0.4578     0.5180          0.45   \n", "湖北      0.4022     0.4320     0.4970     0.5687     0.6099          1.00   \n", "湖南      0.2975     0.3701     0.4330     0.5218     0.5962          0.65   \n", "广东      0.5500     0.6068     0.6618     0.6383     0.6587          1.00   \n", "广西      0.2815     0.3363     0.4000     0.4691     0.5086          0.45   \n", "海南      0.4011     0.4517     0.4983     0.5312     0.5628          1.00   \n", "重庆      0.3309     0.4521     0.5300     0.5987     0.6546          0.75   \n", "四川      0.2669     0.3300     0.4017     0.4773     0.5395          0.50   \n", "贵州      0.2387     0.2686     0.3380     0.3999     0.4615          0.20   \n", "云南      0.2336     0.2951     0.3470     0.4407     0.5040          0.35   \n", "西藏      0.1893     0.2071     0.2267     0.2727     0.3075           NaN   \n", "陕西      0.3226     0.3724     0.4576     0.5317     0.5842          0.65   \n", "甘肃      0.2401     0.3002     0.3613     0.4451     0.5118          0.35   \n", "青海      0.3476     0.3923     0.4476     0.5130     0.5712          0.65   \n", "宁夏      0.3243     0.4228     0.4787     0.5395     0.5802          0.75   \n", "新疆      0.3382     0.3716     0.4302     0.4675     0.5115          0.50   \n", "\n", "     urba_over_50  urba_over_60  urba_over_70  \n", "北京           1.00          1.00          1.00  \n", "天津           1.00          1.00          1.00  \n", "河北           0.25           NaN           NaN  \n", "山西           0.45          0.15           NaN  \n", "内蒙古          0.65          0.30           NaN  \n", "辽宁           1.00          0.60           NaN  \n", "吉林           0.75          0.15           NaN  \n", "黑龙江          1.00          0.35          0.05  \n", "上海           1.00          1.00          1.00  \n", "江苏           0.75          0.50           NaN  \n", "浙江           0.75          0.50           NaN  \n", "安徽           0.25           NaN           NaN  \n", "福建           0.70          0.25           NaN  \n", "江西           0.30           NaN           NaN  \n", "山东           0.45          0.15           NaN  \n", "河南           0.10           NaN           NaN  \n", "湖北           0.45          0.10           NaN  \n", "湖南           0.30           NaN           NaN  \n", "广东           1.00          0.75           NaN  \n", "广西           0.10           NaN           NaN  \n", "海南           0.40           NaN           NaN  \n", "重庆           0.55          0.20           NaN  \n", "四川           0.15           NaN           NaN  \n", "贵州            NaN           NaN           NaN  \n", "云南           0.05           NaN           NaN  \n", "西藏            NaN           NaN           NaN  \n", "陕西           0.35           NaN           NaN  \n", "甘肃           0.10           NaN           NaN  \n", "青海           0.30           NaN           NaN  \n", "宁夏           0.35           NaN           NaN  \n", "新疆           0.10           NaN           NaN  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["urba_year_column_map = {\n", "    \"2000年\":\"urba_2000\",\n", "\"2005年\":\"urba_2005\",\n", "\"2010年\":\"urba_2010\",\n", "\"2015年\":\"urba_2015\",\n", "\"2019年\":\"urba_2019\",\n", "}\n", "\n", "urba_info_df = pd.concat([\n", "    urbanization_df[urba_year_column_map.keys()].rename(urba_year_column_map,axis=1),\n", "    urba_first_over_year],axis=1)\n", "urba_info_df"]}, {"cell_type": "markdown", "metadata": {"id": "D96BF08437DD4B4E9CDCA89C649D21B0", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 流动人口数据的引入"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "7C5B5E1B149B42DE8821F9527A8C0896", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>floating_pop_inside</th>\n", "      <th>floating_pop_outside</th>\n", "    </tr>\n", "    <tr>\n", "      <th>region</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>北京</th>\n", "      <td>4991158</td>\n", "      <td>8418418</td>\n", "    </tr>\n", "    <tr>\n", "      <th>天津</th>\n", "      <td>2944879</td>\n", "      <td>3534816</td>\n", "    </tr>\n", "    <tr>\n", "      <th>河北</th>\n", "      <td>16620369</td>\n", "      <td>3155272</td>\n", "    </tr>\n", "    <tr>\n", "      <th>山西</th>\n", "      <td>11270656</td>\n", "      <td>1620518</td>\n", "    </tr>\n", "    <tr>\n", "      <th>内蒙古</th>\n", "      <td>9776541</td>\n", "      <td>1686420</td>\n", "    </tr>\n", "    <tr>\n", "      <th>辽宁</th>\n", "      <td>12822813</td>\n", "      <td>2847308</td>\n", "    </tr>\n", "    <tr>\n", "      <th>吉林</th>\n", "      <td>9349212</td>\n", "      <td>1001471</td>\n", "    </tr>\n", "    <tr>\n", "      <th>黑龙江</th>\n", "      <td>10720408</td>\n", "      <td>829176</td>\n", "    </tr>\n", "    <tr>\n", "      <th>上海</th>\n", "      <td>4654606</td>\n", "      <td>10479652</td>\n", "    </tr>\n", "    <tr>\n", "      <th>江苏</th>\n", "      <td>19671338</td>\n", "      <td>10308610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>浙江</th>\n", "      <td>13921361</td>\n", "      <td>16186454</td>\n", "    </tr>\n", "    <tr>\n", "      <th>安徽</th>\n", "      <td>16549409</td>\n", "      <td>1550509</td>\n", "    </tr>\n", "    <tr>\n", "      <th>福建</th>\n", "      <td>11574735</td>\n", "      <td>4889876</td>\n", "    </tr>\n", "    <tr>\n", "      <th>江西</th>\n", "      <td>12241920</td>\n", "      <td>1279014</td>\n", "    </tr>\n", "    <tr>\n", "      <th>山东</th>\n", "      <td>23897755</td>\n", "      <td>4129007</td>\n", "    </tr>\n", "    <tr>\n", "      <th>河南</th>\n", "      <td>24365959</td>\n", "      <td>1273646</td>\n", "    </tr>\n", "    <tr>\n", "      <th>湖北</th>\n", "      <td>16226947</td>\n", "      <td>2249614</td>\n", "    </tr>\n", "    <tr>\n", "      <th>湖南</th>\n", "      <td>15998284</td>\n", "      <td>1577563</td>\n", "    </tr>\n", "    <tr>\n", "      <th>广东</th>\n", "      <td>31012976</td>\n", "      <td>29622110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>广西</th>\n", "      <td>11879397</td>\n", "      <td>1359384</td>\n", "    </tr>\n", "    <tr>\n", "      <th>海南</th>\n", "      <td>2410018</td>\n", "      <td>1088143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>重庆</th>\n", "      <td>10902860</td>\n", "      <td>2193575</td>\n", "    </tr>\n", "    <tr>\n", "      <th>四川</th>\n", "      <td>25233163</td>\n", "      <td>2590041</td>\n", "    </tr>\n", "    <tr>\n", "      <th>贵州</th>\n", "      <td>10548217</td>\n", "      <td>1146546</td>\n", "    </tr>\n", "    <tr>\n", "      <th>云南</th>\n", "      <td>9978920</td>\n", "      <td>2230394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>西藏</th>\n", "      <td>624011</td>\n", "      <td>407121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>陕西</th>\n", "      <td>11333383</td>\n", "      <td>1933712</td>\n", "    </tr>\n", "    <tr>\n", "      <th>甘肃</th>\n", "      <td>6586817</td>\n", "      <td>765648</td>\n", "    </tr>\n", "    <tr>\n", "      <th>青海</th>\n", "      <td>1653356</td>\n", "      <td>417304</td>\n", "    </tr>\n", "    <tr>\n", "      <th>宁夏</th>\n", "      <td>2687551</td>\n", "      <td>675119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>新疆</th>\n", "      <td>5476334</td>\n", "      <td>3390712</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        floating_pop_inside  floating_pop_outside\n", "region                                           \n", "北京                  4991158               8418418\n", "天津                  2944879               3534816\n", "河北                 16620369               3155272\n", "山西                 11270656               1620518\n", "内蒙古                 9776541               1686420\n", "辽宁                 12822813               2847308\n", "吉林                  9349212               1001471\n", "黑龙江                10720408                829176\n", "上海                  4654606              10479652\n", "江苏                 19671338              10308610\n", "浙江                 13921361              16186454\n", "安徽                 16549409               1550509\n", "福建                 11574735               4889876\n", "江西                 12241920               1279014\n", "山东                 23897755               4129007\n", "河南                 24365959               1273646\n", "湖北                 16226947               2249614\n", "湖南                 15998284               1577563\n", "广东                 31012976              29622110\n", "广西                 11879397               1359384\n", "海南                  2410018               1088143\n", "重庆                 10902860               2193575\n", "四川                 25233163               2590041\n", "贵州                 10548217               1146546\n", "云南                  9978920               2230394\n", "西藏                   624011                407121\n", "陕西                 11333383               1933712\n", "甘肃                  6586817                765648\n", "青海                  1653356                417304\n", "宁夏                  2687551                675119\n", "新疆                  5476334               3390712"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from io import StringIO\n", "\n", "# 数据来自国家统计年鉴2021\n", "floating_pop_data_txt = '''\n", "地区\t省内\t省外\n", "北京市\t4991158\t8418418\n", "天津市\t2944879\t3534816\n", "河北省\t16620369\t3155272\n", "山西省\t11270656\t1620518\n", "内蒙古自治区\t9776541\t1686420\n", "辽宁省\t12822813\t2847308\n", "吉林省\t9349212\t1001471\n", "黑龙江省\t10720408\t829176\n", "上海市\t4654606\t10479652\n", "江苏省\t19671338\t10308610\n", "浙江省\t13921361\t16186454\n", "安徽省\t16549409\t1550509\n", "福建省\t11574735\t4889876\n", "江西省\t12241920\t1279014\n", "山东省\t23897755\t4129007\n", "河南省\t24365959\t1273646\n", "湖北省\t16226947\t2249614\n", "湖南省\t15998284\t1577563\n", "广东省\t31012976\t29622110\n", "广西壮族自治区\t11879397\t1359384\n", "海南省\t2410018\t1088143\n", "重庆市\t10902860\t2193575\n", "四川省\t25233163\t2590041\n", "贵州省\t10548217\t1146546\n", "云南省\t9978920\t2230394\n", "西藏自治区\t624011\t407121\n", "陕西省\t11333383\t1933712\n", "甘肃省\t6586817\t765648\n", "青海省\t1653356\t417304\n", "宁夏回族自治区\t2687551\t675119\n", "新疆维吾尔自治区\t5476334\t3390712\n", "'''\n", "\n", "floating_pop_df = pd.read_csv(StringIO(floating_pop_data_txt),sep=\"\\t\")\n", "floating_pop_df = floating_pop_df.assign(\n", "    region=floating_pop_df[\"地区\"].map(parse_region),\n", ").rename(\n", "    {\n", "        \"省内\":\"floating_pop_inside\",\n", "        \"省外\":\"floating_pop_outside\"\n", "    },\n", "    axis=1\n", ")[[\"floating_pop_inside\",\"floating_pop_outside\",\"region\"]].set_index(\"region\")\n", "\n", "floating_pop_df"]}, {"cell_type": "markdown", "metadata": {"id": "E49C682C7FB644EDBA4E532C7B57CFA1", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 数据聚合"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "FB8ED58C83574BC9862B1600EA5064C5", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["# age_df.set_index(\"region\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "2CD71C8051DD4271A381B874979E49D0", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a14-</th>\n", "      <th>a15_59</th>\n", "      <th>a60+</th>\n", "      <th>a65+</th>\n", "      <th>population</th>\n", "      <th>gdp</th>\n", "      <th>edu_college</th>\n", "      <th>edu_senior</th>\n", "      <th>edu_junior</th>\n", "      <th>edu_primary</th>\n", "      <th>...</th>\n", "      <th>urba_over_40</th>\n", "      <th>urba_over_50</th>\n", "      <th>urba_over_60</th>\n", "      <th>urba_over_70</th>\n", "      <th>floating_pop_inside</th>\n", "      <th>floating_pop_outside</th>\n", "      <th>gdp_avg</th>\n", "      <th>floating_inside_rate</th>\n", "      <th>floating_outside_rate</th>\n", "      <th>floating_rate</th>\n", "    </tr>\n", "    <tr>\n", "      <th>region</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>北京</th>\n", "      <td>0.1184</td>\n", "      <td>0.6853</td>\n", "      <td>0.1963</td>\n", "      <td>0.1330</td>\n", "      <td>21893095</td>\n", "      <td>36102.6</td>\n", "      <td>0.41980</td>\n", "      <td>0.17593</td>\n", "      <td>0.23289</td>\n", "      <td>0.10503</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>4991158.0</td>\n", "      <td>8418418.0</td>\n", "      <td>16.490405</td>\n", "      <td>0.227979</td>\n", "      <td>0.384524</td>\n", "      <td>0.612503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>天津</th>\n", "      <td>0.1347</td>\n", "      <td>0.6487</td>\n", "      <td>0.2166</td>\n", "      <td>0.1475</td>\n", "      <td>13866009</td>\n", "      <td>14083.7</td>\n", "      <td>0.26940</td>\n", "      <td>0.17719</td>\n", "      <td>0.32294</td>\n", "      <td>0.16123</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>2944879.0</td>\n", "      <td>3534816.0</td>\n", "      <td>10.156996</td>\n", "      <td>0.212381</td>\n", "      <td>0.254927</td>\n", "      <td>0.467308</td>\n", "    </tr>\n", "    <tr>\n", "      <th>河北</th>\n", "      <td>0.2022</td>\n", "      <td>0.5992</td>\n", "      <td>0.1985</td>\n", "      <td>0.1392</td>\n", "      <td>74610235</td>\n", "      <td>36206.9</td>\n", "      <td>0.12418</td>\n", "      <td>0.13861</td>\n", "      <td>0.39950</td>\n", "      <td>0.24664</td>\n", "      <td>...</td>\n", "      <td>0.65</td>\n", "      <td>0.25</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>16620369.0</td>\n", "      <td>3155272.0</td>\n", "      <td>4.852806</td>\n", "      <td>0.222763</td>\n", "      <td>0.042290</td>\n", "      <td>0.265053</td>\n", "    </tr>\n", "    <tr>\n", "      <th>山西</th>\n", "      <td>0.1635</td>\n", "      <td>0.6472</td>\n", "      <td>0.1892</td>\n", "      <td>0.1290</td>\n", "      <td>34915616</td>\n", "      <td>17651.9</td>\n", "      <td>0.17358</td>\n", "      <td>0.16485</td>\n", "      <td>0.38950</td>\n", "      <td>0.19506</td>\n", "      <td>...</td>\n", "      <td>0.75</td>\n", "      <td>0.45</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>11270656.0</td>\n", "      <td>1620518.0</td>\n", "      <td>5.055589</td>\n", "      <td>0.322797</td>\n", "      <td>0.046412</td>\n", "      <td>0.369209</td>\n", "    </tr>\n", "    <tr>\n", "      <th>内蒙古</th>\n", "      <td>0.1404</td>\n", "      <td>0.6617</td>\n", "      <td>0.1978</td>\n", "      <td>0.1305</td>\n", "      <td>24049155</td>\n", "      <td>17359.8</td>\n", "      <td>0.18688</td>\n", "      <td>0.14814</td>\n", "      <td>0.33861</td>\n", "      <td>0.23627</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>0.65</td>\n", "      <td>0.30</td>\n", "      <td>NaN</td>\n", "      <td>9776541.0</td>\n", "      <td>1686420.0</td>\n", "      <td>7.218466</td>\n", "      <td>0.406523</td>\n", "      <td>0.070124</td>\n", "      <td>0.476647</td>\n", "    </tr>\n", "    <tr>\n", "      <th>辽宁</th>\n", "      <td>0.1112</td>\n", "      <td>0.6316</td>\n", "      <td>0.2572</td>\n", "      <td>0.1742</td>\n", "      <td>42591407</td>\n", "      <td>25115.0</td>\n", "      <td>0.18216</td>\n", "      <td>0.14670</td>\n", "      <td>0.42799</td>\n", "      <td>0.18888</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>0.60</td>\n", "      <td>NaN</td>\n", "      <td>12822813.0</td>\n", "      <td>2847308.0</td>\n", "      <td>5.896729</td>\n", "      <td>0.301066</td>\n", "      <td>0.066852</td>\n", "      <td>0.367917</td>\n", "    </tr>\n", "    <tr>\n", "      <th>吉林</th>\n", "      <td>0.1171</td>\n", "      <td>0.6523</td>\n", "      <td>0.2306</td>\n", "      <td>0.1561</td>\n", "      <td>24073453</td>\n", "      <td>12311.3</td>\n", "      <td>0.16738</td>\n", "      <td>0.17080</td>\n", "      <td>0.38234</td>\n", "      <td>0.22318</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>0.75</td>\n", "      <td>0.15</td>\n", "      <td>NaN</td>\n", "      <td>9349212.0</td>\n", "      <td>1001471.0</td>\n", "      <td>5.114057</td>\n", "      <td>0.388362</td>\n", "      <td>0.041601</td>\n", "      <td>0.429963</td>\n", "    </tr>\n", "    <tr>\n", "      <th>黑龙江</th>\n", "      <td>0.1032</td>\n", "      <td>0.6646</td>\n", "      <td>0.2322</td>\n", "      <td>0.1561</td>\n", "      <td>31850088</td>\n", "      <td>13698.5</td>\n", "      <td>0.14793</td>\n", "      <td>0.15525</td>\n", "      <td>0.42793</td>\n", "      <td>0.21863</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>0.35</td>\n", "      <td>0.05</td>\n", "      <td>10720408.0</td>\n", "      <td>829176.0</td>\n", "      <td>4.300930</td>\n", "      <td>0.336590</td>\n", "      <td>0.026034</td>\n", "      <td>0.362623</td>\n", "    </tr>\n", "    <tr>\n", "      <th>上海</th>\n", "      <td>0.0980</td>\n", "      <td>0.6682</td>\n", "      <td>0.2338</td>\n", "      <td>0.1628</td>\n", "      <td>24870895</td>\n", "      <td>38700.6</td>\n", "      <td>0.33872</td>\n", "      <td>0.19020</td>\n", "      <td>0.28935</td>\n", "      <td>0.11929</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>4654606.0</td>\n", "      <td>10479652.0</td>\n", "      <td>15.560598</td>\n", "      <td>0.187151</td>\n", "      <td>0.421362</td>\n", "      <td>0.608513</td>\n", "    </tr>\n", "    <tr>\n", "      <th>江苏</th>\n", "      <td>0.1521</td>\n", "      <td>0.6295</td>\n", "      <td>0.2184</td>\n", "      <td>0.1620</td>\n", "      <td>84748016</td>\n", "      <td>102719.0</td>\n", "      <td>0.18663</td>\n", "      <td>0.16191</td>\n", "      <td>0.33308</td>\n", "      <td>0.22742</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>0.75</td>\n", "      <td>0.50</td>\n", "      <td>NaN</td>\n", "      <td>19671338.0</td>\n", "      <td>10308610.0</td>\n", "      <td>12.120520</td>\n", "      <td>0.232116</td>\n", "      <td>0.121638</td>\n", "      <td>0.353754</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 25 columns</p>\n", "</div>"], "text/plain": ["          a14-  a15_59    a60+    a65+  population       gdp  edu_college  \\\n", "region                                                                      \n", "北京      0.1184  0.6853  0.1963  0.1330    21893095   36102.6      0.41980   \n", "天津      0.1347  0.6487  0.2166  0.1475    13866009   14083.7      0.26940   \n", "河北      0.2022  0.5992  0.1985  0.1392    74610235   36206.9      0.12418   \n", "山西      0.1635  0.6472  0.1892  0.1290    34915616   17651.9      0.17358   \n", "内蒙古     0.1404  0.6617  0.1978  0.1305    24049155   17359.8      0.18688   \n", "辽宁      0.1112  0.6316  0.2572  0.1742    42591407   25115.0      0.18216   \n", "吉林      0.1171  0.6523  0.2306  0.1561    24073453   12311.3      0.16738   \n", "黑龙江     0.1032  0.6646  0.2322  0.1561    31850088   13698.5      0.14793   \n", "上海      0.0980  0.6682  0.2338  0.1628    24870895   38700.6      0.33872   \n", "江苏      0.1521  0.6295  0.2184  0.1620    84748016  102719.0      0.18663   \n", "\n", "        edu_senior  edu_junior  edu_primary  ...  urba_over_40  urba_over_50  \\\n", "region                                       ...                               \n", "北京         0.17593     0.23289      0.10503  ...          1.00          1.00   \n", "天津         0.17719     0.32294      0.16123  ...          1.00          1.00   \n", "河北         0.13861     0.39950      0.24664  ...          0.65          0.25   \n", "山西         0.16485     0.38950      0.19506  ...          0.75          0.45   \n", "内蒙古        0.14814     0.33861      0.23627  ...          1.00          0.65   \n", "辽宁         0.14670     0.42799      0.18888  ...          1.00          1.00   \n", "吉林         0.17080     0.38234      0.22318  ...          1.00          0.75   \n", "黑龙江        0.15525     0.42793      0.21863  ...          1.00          1.00   \n", "上海         0.19020     0.28935      0.11929  ...          1.00          1.00   \n", "江苏         0.16191     0.33308      0.22742  ...          1.00          0.75   \n", "\n", "        urba_over_60  urba_over_70  floating_pop_inside  floating_pop_outside  \\\n", "region                                                                          \n", "北京              1.00          1.00            4991158.0             8418418.0   \n", "天津              1.00          1.00            2944879.0             3534816.0   \n", "河北               NaN           NaN           16620369.0             3155272.0   \n", "山西              0.15           NaN           11270656.0             1620518.0   \n", "内蒙古             0.30           NaN            9776541.0             1686420.0   \n", "辽宁              0.60           NaN           12822813.0             2847308.0   \n", "吉林              0.15           NaN            9349212.0             1001471.0   \n", "黑龙江             0.35          0.05           10720408.0              829176.0   \n", "上海              1.00          1.00            4654606.0            10479652.0   \n", "江苏              0.50           NaN           19671338.0            10308610.0   \n", "\n", "          gdp_avg  floating_inside_rate  floating_outside_rate  floating_rate  \n", "region                                                                         \n", "北京      16.490405              0.227979               0.384524       0.612503  \n", "天津      10.156996              0.212381               0.254927       0.467308  \n", "河北       4.852806              0.222763               0.042290       0.265053  \n", "山西       5.055589              0.322797               0.046412       0.369209  \n", "内蒙古      7.218466              0.406523               0.070124       0.476647  \n", "辽宁       5.896729              0.301066               0.066852       0.367917  \n", "吉林       5.114057              0.388362               0.041601       0.429963  \n", "黑龙江      4.300930              0.336590               0.026034       0.362623  \n", "上海      15.560598              0.187151               0.421362       0.608513  \n", "江苏      12.120520              0.232116               0.121638       0.353754  \n", "\n", "[10 rows x 25 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# df = pd.merge(pd.merge(pd.merge(age_df,edu_df,on=\"region\"),pop_df,on=\"region\"),gdp_df,on=\"region\")\n", "df = pd.concat([\n", "    age_df.set_index(\"region\"), \n", "    pop_df.set_index(\"region\"),\n", "    gdp_df.set_index(\"region\"),\n", "    edu_df.set_index(\"region\"),\n", "    urba_info_df,\n", "    floating_pop_df],axis=1).drop(\"全国\")\n", "assert len(df.index) == 31\n", "\n", "df = df.assign(\n", "    gdp_avg = df[\"gdp\"] * 10000 / df[\"population\"],\n", "    floating_inside_rate = df[\"floating_pop_inside\"]/df[\"population\"],\n", "    floating_outside_rate = df[\"floating_pop_outside\"]/df[\"population\"],\n", "    floating_rate = (df[\"floating_pop_inside\"] + df[\"floating_pop_outside\"])/df[\"population\"]\n", ")\n", "df.index.name = \"region\"\n", "df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "1C70AE9D66B346C7893465C9E1259D8C", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {"id": "443FF70CC57E41C28557382CAA6F4F8D", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 指标说明  \n", "\n", "- `region`：地区，不包括香港、澳门、台湾省在内的31个中国省份  \n", "- `a14-`：年龄小于等于14岁的人口比例  \n", "- `a15_59`：年龄在14岁到59岁的人口比例  \n", "- `a60+`：年龄大于等于60岁的人口比例  \n", "- `a65+`：年龄大于等于65岁的人口比例  \n", "- `edu_college`：教育水平为大学（含大专及以上）的人口比例  \n", "- `edu_senior`：教育水平为高中（含中专）的人口比例  \n", "- `edu_junior`：教育水平为初中的人口比例  \n", "- `edu_primary`：教育水平为小学的人口比例  \n", "- `population`：人口数，单位为人  \n", "- `gdp`：国民生产总值，单位为亿元  \n", "- `gdp_avg`：人均国民生产总值，单位为万元  \n", "- `urba_2000`：2000年的城市化比例  \n", "- `urba_2005`：2005年的城市化比例  \n", "- `urba_2010`：2010年的城市化比例  \n", "- `urba_2015`：2015年的城市化比例  \n", "- `urba_2019`：2019年的城市化比例  \n", "- `urba_over_40`：城市化比例首次超过40%的年份的距今系数，0-1之间  \n", "- `urba_over_50`：城市化比例首次超过50%的年份的距今系数，0-1之间  \n", "- `urba_over_60`：城市化比例首次超过60%的年份的距今系数，0-1之间  \n", "- `urba_over_70`：城市化比例首次超过60%的年份的距今系数，0-1之间  \n", "- `floating_inside_rate`：来自省内的人户分离人口比例  \n", "- `floating_outside_rate`：来自省外的人户分离（流动人口）比例  \n", "- `floating_rate`：人户分离人口比例  \n", "\n", "城市化比例首次超过x%的年份的距今系数 = (2020 - 城市化比例首次超过x%的年份)/20，因为城市化数据只包括2000到2019之间的数据  \n", "\n", "除了城市化率利用了早年数据，其他数据的统计对象年份皆为 2020年"]}, {"cell_type": "markdown", "metadata": {"id": "C1DEB55876E044FA80ED584CCB3E4B31", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 分析阶段  \n", "\n", "### 基于人口年龄结构的差异性，可以将中国各地区分为哪几组？  \n", "\n", "基于层次聚类的地区分组"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["font1 = {'family' : 'Times New Roman',\n", "'weight' : 'normal',\n", "'size'   : 16,\n", "}\n", "from pylab import mpl\n", "# 设置中文显示字体\n", "mpl.rcParams[\"font.sans-serif\"] = [\"SimHei\"]"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "AE3950294C5E41E28AEC65A4859669B6", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"image/png": "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********************************+aVw+GQJBUuXFj16tXznf/jjz/022+/qXTp0umqYV+duspts91QPwAAAAAAAACkzq/XoGzbtq2qVq2qIUOGSJICAgL0+++/q2vXrrJYLMnaBgQEaO7cuRo7dqxKly6tdevW6dlnn1Xz5s21evVq3XPPPXK5XGZ0A7eIMzRUlv9N8QcAAAAAAEDW4NcjKG02m/r37689e/ZIUrJQ0mpNnq0GBAQoMTFRq1evVkJCgiZMmKB3331XL7/8sv74448U16dV2XVrFRwcfAO9wK1icToz9D0GAAAAAACAefw6oJQkt9utevXqaf369bp06ZKqVq2qEydO6NNPP03R1mq1qly5cqpSpYp69eqlTz75RAULFlS+fPky/HyryyUrIy8BAAAAAACAm8LvA8opU6boscceU+nSpWWz2bR582ZNmzZNBQsW1KVLl5K1nTt3riZPniyn06mDBw9qxIgRCggIUIUKFUyqHgAAAAAAAMC1+HVAeenSJQ0ZMkSLFi2SJI0ZM0aS1KFDB0nSf//7X1/bhIQEtW3bVr1795YkPfzww5o8ebJKlSolSapRo8YtrBwAAAAAAABAWvj1JjkHDhxQw4YNValSJUlSnTp1fOeGDBmi9957zzc60u12J7u2Vq1aCggIkCR1795dVqtV+fPnv0WVAwAAAAAAAEgLi2EYhtlFZER0dLQCAwMVGBh43baGYaR785To6GjlzZtXUVFRbJIDAAAAANlYbIJH97+9VJK0+73H5Qrw68mGAJBlpDVfy7K/ddMTGrKzMwAAAAAAAOCf/HqKNwAAAAAAAIDsjYASAAAAAAAAgGkIKAEAAAAAAACYhoASAAAAAAAAgGkIKAEAAAAAAACYhoASAAAAAAAAgGkIKAEAAAAAAACYxm52AQAAAAAA+IvYBI/ZJQDIAZwOmywWi9ll+A0CSgAAAAAA/qfqkJVmlwAgB6haMp9mv1CTkPJ/mOINAAAAAAAA3EKb/46UOzHJ7DL8RpYbQel2u7Vu3To99thjyY57vV7FxMQoICDgqulzYmKiHA6HgoKCbkWpAAAAAIAsZvPABnIFZLm/KgPIImITklR1yAqzy/A7We63rtfr1QsvvKBFixbpvvvu8x0/d+6c+vbtq8DAQFksFiUmJmr27Nl6+umnfW0SExP11FNPqUWLFiZUDgAAAADwd64AOwElANxifv9b9/DhwykCxbi4OLVv3z7ZsTFjxmjatGnq1auXduzYoYsXLyogIED79+/3tRk8eLDq1auXrufHJsbKnuj3H1OmctqdrIEAAAAAAACAW8LvkzePx6MLFy7o8OHDV23Trl07xcbGSpJ27typzp0764477kjW5quvvlJkZGS6n19/dn3ZnLZ0X5eVhRQK0eRGkwkpAQAAAAAAcNP5fUB55513asGCBdds8+abb6po0aKSJJvNpjNnzqQI1y5dukTglkbbTm+T2+OWy+EyuxQAAAAAAABkc2kOKB955JE0BXyrVq26oYL+7fz58+rUqZNcLpfv+QkJCXI4HL73cXFx+u9//6uCBQvK6/Vq7ty5cjqdye6zf/9+GYaR7uevarNKwcHBN96RLMDtcaved/XMLgMAAAAAAAA5SJoDyi5duvheb9u2TdOmTdOzzz6r0qVL68iRI5owYcJN2XymaNGi2rZtW7JjNWrU0OTJk1WmTJkU7StVqqQzZ84oPj5e5cqV8x0vXry4ChQokO7nuxwuRhICAAAAAAAAN0maA8rOnTv7Xg8bNkwLFy5UjRo1fMeaNm2qZ555JnOry4AXXnhBTz75pFwul86ePes7XrVqVdWtW9fEygAAAAAAAAD8mzUjF0VGRiopKSnZMa/Xq3PnzmVKURk1btw4denSRS1btlTNmjW1ZMkSffHFF5Kkfv36mVobAAAAAAAAgJQytEnOSy+9pObNm+vpp59WiRIldOLECU2fPl3PP/98ZteXjGEYcrvdio+PT3U9zJYtW6pr166y2WwaOnSoypYtq6SkJC1cuNC3iQ4AAAAAAAAA/5GhgHLQoEGqXLmyfvjhB61cuVKFChXSF198oVatWmV2fcnEx8frwQcfVKlSpVSsWLFk5y5duqRevXopIiJCMTExqlq1qiZPnqyYmBh99tln2rNnj+Li4tSxY0e98sorN7VOAAAAAAAAAGmToYBSkpo1a6ZmzZplZi3XFRQUpF27dqV6LleuXBo6dKjuuOMO5cqVK9m55s2bS5JiY2PTtBM5AAAAAAAAgFsjQ2tQ/vnnn3K73Zldyw0rU6ZMinDyn1wul5xO5y2sCAAAAAAAAMC1ZGgEZYMGDTRjxgzVq1cvk8uBv3B7/C+AliSn3ckoWAAAAAAAgGwkQwFl586dNXHiRALKbKzed/XMLiFVIYVCNLnRZEJKAAAAAACAbCJDU7wbNmyoffv26cknn9RPP/2ktWvX+r6Am2nb6W1+O7oTAAAAAAAA6ZehEZTdunWTJEVERKhnz56+4xaLRQcPHsycymCq1W1Xy2n3n/U63R63347qBAAAAAAAQMZlKKA8dOhQZtcBP+O0O+VyuMwuAwAAAAAAANlchqZ4/9PZs2czow4AAAAAAAAAOVCGAsr4+Hj17dtXuXLlUuHChZU7d269+uqrSkhIyOz6AAAAAAAAAGRjGQooX331VW3cuFHLly/XiRMntGzZMq1fv16vvvpqZtcHAAAAAAAAIBvL0BqU3377rcLDw3XPPfdIkgoXLqxJkyapTp06+uyzzzK1QAAAAAAAAADZV4ZGUObPn1979+5Ndmzv3r3Knz9/phQFAAAAAAAAIGfI0AjKIUOGqH379urQoYPuvvtuHTx4UNOnT9fkyZMzuz5FRETI4/HIYrFIkh588EH98ssvcjgcMgxDhmGoRIkS+uKLL/TII4/ovvvukyQ1aNBA3333nQoUKJDpNQEAAAAAAADIHBkKKFu3bq2SJUvqm2++0Zo1a1SiRAmtXr1aDzzwQGbXp7lz52rHjh2+gPLixYsaMWKEJMkwDOXPn1/Dhg1TaGio2rRpowEDBui+++7T1q1bFRYW5ruP2+1W37591bp163Q9PzYxVvbEDH1MWY7b4za7BAAAAAAAAOQwGUrevv/+e4WGhmrcuHGZXU8KjRo10rRp05Q7d25Jksfj0f79+yVd3k184MCBkqQaNWooPDxcbrdbPXr00OrVq1W5cuUbfn792fVlc9pu+D4AAAAAAAAAUspQQDls2DDt27dPNptNISEhCgkJUWhoqEJDQ30b52SGY8eOaeXKlerWrZvv2O+//6527dr53v/999/asGGD5s2bp8GDB2vlypWKiYlRs2bNVLhwYTkcDiUkJCgkJETjx4/PtNqys5BCIXLanWaXAQAAAAAAgBwgQwHlr7/+KknasWOHFixYoO+//14jR46UxWJRUlJSphVntVrldDoVEBDgOzZ27NhkbRITE5UrVy6VLFlSjRs31vLly5UvXz5t3rxZ999/v44cOaLHHntMn3/+eYZqWNVmlYKDg2+oH1mN0+70TakHAAAAAAAAbqYMBZQPPfSQ8ubNq6JFi6ps2bLq37+/ypYtq7Jly2ZqcUWLFlWnTp1UpUoV3xTvf9q7d6+WLVumSpUqqVKlSurUqZPCw8P10ksvKSEhQUWKFFFcXJy++eYbnTp1SsWKFdOrr76arhpcDpdcDldmdQkAAAAAAADAP2QooKxWrZoOHjyoY8eO6dy5czp79qzOnz+vCxcu6OGHH87sGhUYGKgWLVqkOD59+vRkoxsff/xx/fTTT3rttdcUHBysCxcuqGHDhjp+/LjWrVunPn36ZHptAAAAAAAAADIuQwHlxx9/rIMHD+qvv/7S77//rh9++EGjRo3SbbfdpvPnz2d2jbJYLKpatWqK44sXL5bNdnkDm1WrVumuu+7y1VCiRAndd999WrFihYoXLy6n0+lri6wtK+02znR5AAAAAACAa8tQQBkUFKTChQsrJCREVapU0ZtvvqmQkBDdddddmV2fJMkwDN9u3f+0d+9e3+vBgwdr5MiRkqTg4GC98847vnMnT56UxWJR3bp1VaVKlZtSI26det/VM7uENAspFKLJjSYTUgIAAAAAAFxFhgLK48ePq1ChQpldy1UlJiZq06ZNyY599tlnmjdvngoWLKjjx4+rYsWKqlatmiTpueee03PPPSdJ2rx5s7p3767JkyerYsWKt6xmQJK2nd4mt8fNOqYAAAAAAABXkaGAMiQkRGFhYQoLC1P16tUzu6YUtm7dmuJYr1691Lt3b0lSrly5rrpL93333ae5c+fetNGduPVWt10tp91pdhnX5Pa4s9RITwAAAAAAALNkKKCcPHmyfvjhB7Vu3Vo2m01t27ZVWFiYHnjggcyu76rSOmU2V65chJPZjNPuZEQiAAAAAABANmHNyEUNGzbU2LFjdfToUc2ePVsOh0OPPvqoypQpo0GDBunMmTOZXScAAAAAAACAbChDAeUVGzdu1OzZszVr1iwFBATo8ccf1+nTp/X4449nVn0AAAAAAAAAsrEMTfF+6aWX9OOPPyo+Pl5PPfWUJkyYoHr16slqterIkSO69957M7tOAAAAAAAAANlQhgLKuLg4ffPNN2rQoIFsNluyc4UKFdKBAwcypTgAAAAAAAAA2VuGAsqvv/76queCgoJUtGjRDBcEAAAAAAAAIOfI0BqUiYmJ+uCDD1S9enUVK1ZMu3btUrVq1Rg5CQAAAAAAACBdMhRQ9uzZU9999526du2qmJgYuVwuPfTQQ3r++eczuz4AAAAAAAAA2ViGAso5c+Zo7ty5ev7552Wz2WSz2dS/f39t2rQps+sDAAAAAAAAkI1lKKAsXry41q5d63tvsVi0a9cu3XXXXZlWGAAAAAAAAIDsL0Ob5Hz44Ydq0aKFxo8fr9jYWPXr10/r1q3TlClTMrs+AAAAAAAAANlYhkZQNmrUSDt37lTTpk317LPPKiQkRL/88osqV66c2fVJktxutwYNGpTsWHR0tHbu3KkJEyZo48aNSkxM9J3zeDzyer3J2nu9XhmGcVPqAwAAAAAAAJAxGRpBOWTIEA0cOFBvvfWWJOnixYsaMWKEPvvsM0VFRWVaccePH1ejRo2UK1cu7dmzR0uXLtVtt92mzp07a/ny5erRo4dq1aql/Pnzq1mzZrp06ZJ27NihJ554QjNnzkxxvz///FPlypXLtPoAAAAAAAAA3JgMBZQ//vijjh8/rtGjR2v8+PH64IMPVLNmTa1bty5TiytWrJimTp2qiIgIPfHEE3rhhRc0evRoLViwQKVKlVL16tV9bQcMGKCTJ09q4MCBeu2113T27Fl16NBBo0eP1oABAzRixIgMhZOxibGyJ2boY0ImcnvcZpcAAAAAAACAmyBDydvq1avVrl07FSpUSNWrV9fChQtVtWrVzK5NXq9XBQoU0HfffaeKFSvqxIkTstvtslqt2rlzp2bNmiVJCg4O1vDhw9W4cWP17NlThmEoISFBUVFRSkxM1IULF1JM+U6r+rPry+a0ZWa3AAAAAAAAAPxPhgLKXLly6ccff1Tv3r21efNmlS5dOrPrkiTNmDFDn3zyiWw2mz755BNVqFBB1apV05tvvim3260DBw5o4sSJeueddxQZGamGDRvKbrcrKSlJkjR37lzdfvvtmjZtmsqXL39TasStFVIoRE670+wy0oXRn/7FaXfKYrGYXQYAAAAAAPifNAeUd911V4q/1BuGoSNHjqhMmTLKmzevJOngwYOZVtzTTz+tsLAw2e12VaxYUZs2bVJiYqK+++47NWjQQJ06ddLy5cvVsWNH/fXXX9q1a5e++eYbNW3aVMWKFZPD4fDdy+PxaPXq1apXr166aljVZpWCg4MzrU+4MVkxXKr3XT2zS8A/hBQK0eRGk7PczxEAAAAAANlVmgPKSZMm3cQyUrdz504988wzstvtOnjwoKpXr67g4GA9/vjjKlKkiK/dtm3bNHnyZBUrVkwlS5ZU9erVNX36dDVp0kTfffedOnXqpKlTp2ratGnprsHlcMnlcGVmtwCYaNvpbXJ73Py5BgAAAADAT6Q5oHz44YdvZh2pqlSpktq2basiRYroo48+0uOPP65OnTrprbfe0nvvvedrd++996pBgwYqW7aswsPDZbVaU9zLbmejG5hjddvVWW5aenbk9rgZzQoAAAAAgB/y69Tup59+0po1a/T999/ro48+UqtWrfTjjz9q586dKleunM6dOydJWrhwoSwWi0qUKKGgoCAFBATotttuU3h4uIoWLaoVK1aoWLFiJvcGOZXT7mS0HgAAAAAAwFWkHGroRx599FHNmTNHzz77rJo1a6YqVaooICBAL7zwgiwWi+Li4pSYmKi2bdvqv//9r8LDw5WQkKCff/5ZVqtVQUFBvi/DMNS5c2ezuwQAAAAAAADgH/x6BKXdbpfdbk+2dmTv3r19r++8806tX7/e937cuHG+1/379781RQIAAAAAAADIML8eQQkAAAAAAAAgeyOgBAAAAAAAAGAaAkoAAAAAAAAApiGgBAAAAAAAAGAaAkoAAAAAAAAApiGgBAAAAAAAAGAaAkoAAAAAAAAApiGgBAAAAAAAAGAaAkoAAAAAAAAApiGgBAAAAAAAAGAaAkoAAAAAAAAApslyAWWDBg105MiRa7ZZuXKlevbseYsqAgAAAAAAAJBRWS6gfPLJJ7Vp0ybf+8TERHk8HiUlJfmOjRgxQm3atPG993q9io+Pv6V1AgAAAAAAALg+u9kFXM+MGTP08ssvq0SJEsmOjxgxQpLk8Xg0YMAArV69Wlu2bFFERITi4+P15ptvJmtfsGBBLViwIN3Pj02MlT3R7z8m+Bm3x212CQAAAAAAAFmC3ydvuXLlUsuWLTVu3LhrtmvTpo1iY2NVp04dTZo0SSdOnJDb7VaLFi1u6Pn1Z9eXzWm7oXsA8C8EyJAkp90pi8VidhkAAAAAkOP5fUBps9nk8Xiu2y4mJkYdO3bUc889p4oVK2rlypWKiYnxBZSGYfAXUdxyIYVC5LQ7zS4D/1Lvu3pmlwA/EFIoRJMbTebfDQAAAABgMr8PKA3DSFO7nTt3at++fYqOjtacOXN07NgxeTwerVmzRomJiSpXrpy++uqrdD9/VZtVCg4OTvd1gMQILcCfbTu9TW6PWy6Hy+xSAAAAACBH8/uAMjIyUi7X9f/yWLNmTe3atUvbt29X5cqVNXbsWF24cEH9+/dXREREijUs08rlcPGXVyCbWd12NSNbczC3x80oWgAAAADwI34fUO7evVt33XVXmtpOmDBBY8aM0W+//eY79uuvv6p9+/ZauHChKlWqdLPKBJCFOO1O/scDAAAAAAB+wmp2AdcSHx+vWbNm6bHHHrtu26lTp+rzzz/XypUrFRgYKLfbLYvFolq1amncuHF6/PHHtWvXrltQNQAAAAAAAIC08usRlN9++61CQkJUvnz567Y9cOCAli9froIFC6pXr16aN2+eZs6cKUlq3Lixpk+frlKlSt3kigEAAAAAAACkh18HlJ06dVKbNm3S1Hbw4MG+1yNHjtSYMWOSna9fv35mlgYAAAAAAAAgE/j1FG9JcjrTv5FFYGDgTagEAAAAAAAAQGbz+4ASAAAAAAAAQPZFQAkAAAAAAADANASUAAAAAAAAAExDQAkAAAAAAADANASUAAAAAAAAAExDQAkAAAAAAADANASUAAAAAAAAAExDQAkAAAAAAADANASUAAAAAAAAAExDQAkAAAAAAADANNk6oExMTJTH4zG7DAAAAAAAAABX4dcBpWEYSkpKumabpKQkeb1eeb1eSdKhQ4f0n//8R5I0btw4LV++3Nf2ShsAAAAAAAAA/sFudgHXsmPHDvXo0UMOh0OStH37dlWuXNn3T+ly6Dh8+HC98sorKly4sNxut/7880+1aNFChw8fVu7cufXVV18pIiJC/fr1U1hYWLpqiE2MlT3Rrz8mAGng9rjNLgEAAAAAAKTCr5O3SpUqaf369b73DRs21IoVK3z//KdXXnlFDRs21J9//qlffvlFTz75pI4cOaIff/xRrVq10jfffKNGjRqlu4b6s+vL5rTdcF8AAAAAAAAApOTXAaUkrVq1Sm+88YYCAgK0c+dO1a5d2/dPj8ej1157Ta1atdLMmTN16dIl/fDDD6pWrZrmz58vwzC0adMmFShQQOXLl9eJEyeUN29es7sEwEQhhULktDvNLgMAAAAAAPyP3weUbrdbjRs31oABA9SiRQstWrTIN4Jy2rRpiomJkSTt27dPH3zwgV588UW9//77yps3rwoVKqQ6deooLCxMU6ZMUdGiRdP9/FVtVik4ODizuwXAJE67UxaLxewyAAAAAADA//h9QGmzXZ5eHR4e7lt38t+SkpK0bds2zZw5UxcvXlTx4sVVqlQphYSEKDg4WPv379dPP/2kESNGpPv5LodLLofrhvoAAAAAAAAAIHV+vYv3FW63W2+//ba6du0qSSpQoIDq1q2rn376SZK0aNEiFSxYUO+9954cDoccDoe6du2qNWvWqFKlSvriiy/UpEkTM7sAAAAAAAAAIBV+P4JSkqxWq4YPH64yZcpIkqZMmaJTp05p+fLlkqRmzZqpZs2aqlWrlooVK6bIyEh9+OGHKlu2rB544AGFh4dr3LhxZnYBAAAAAAAAQCr8PqA0DEMzZ87Uhg0bUpw7deqUXn/9dW3atElFihTRtm3b9J///EeXLl3SyZMnFRMToz59+qh27dr67rvvdObMGY0cOdKEXgAAAAAAAABIjd8HlPHx8erSpYsGDx6c4ty0adN8a0727NlTAwcOVMmSJXX8+HFJ0iuvvKJKlSqpQ4cOatGihWrWrHmLqwcAAAAAAABwLX4fUDZr1uyq60d26NDB97po0aKaP39+svMff/yx7/WCBQtuToEAAAAAAAAAMszvA0qr1SqrNW17+TgcjptcDQAAAAAAAIDMlCV28QYAAAAAAACQPRFQAgAAAAAAADANASUAAAAAAAAA0xBQAgAAAAAAADANASUAAAAAAAAA0xBQAgAAAAAAADANASUAAAAAAAAA09jNLgAAADMYXodiE5Ikw2N2KQAAwGSxCUlmlwAAORoBJQAgR7q4931VfX+t2WUAAAAAQI7HFG8AAAAAACRVLZlPTofN7DIAIMfJsiMok5KSZBiG7PbUuxAaGqqFCxeqaNGit7gyAEBWkPveQVrddo1cDqfZpQAAAD/hdNhksVjMLgMAcpwsEVB+++232rRpk0aNGuU7NmnSJP3999967733JF0OLENDQ5UrVy5ZrVYdOHBArVu3ltVqVVJSkl599VW1atXKrC4AAPyMxZooV4BNLkeW+FchAAAAAGRbfv23sqFDh2rhwoW6cOGCYmJi9Ouvv/rOnT59WnFxcVq+fLmqVKmi1q1b69NPP/Wdf/nllzV48GAFBAT4As5y5cqpfPny6aohNjFW9kS//pgyxGl38n8GAQAAAAAAYDq/Tt4GDBigAQMGaM6cOdq4caNGjhzpOzdp0iQdPnxYgwcPVmxsrCZOnKjAwEDfebfbrYMHD8put+vcuXOKi4vTqVOn0h1Q1p9dXzZn9luDJKRQiCY3mkxICQAAAAAAAFP5dUCZVi6XS127dlWtWrUUEBAgSYqIiNDEiRNlsVgUERGhAQMGqH79+iZX6j+2nd4mt8ctl8NldikAAAAAAADIwbJMQDljxgyFh4f73p85c0YdO3b0vQ8MDNSiRYt0xx13SJLq1aunJUuWKCgoSPHx8Tpw4ECGnruqzSoFBwffWPF+xO1xq9539cwuAwAAAAAAAJCUhQLKp59+OtUp3lecOnVKjRs31ldffaU9e/ZIkhYvXiyr1aoqVaro5Zdf1rJly9L9XJfDxShDAAAAAAAA4Caxml1AZtm9e7eqVq2qiRMnqkCBApKkSpUqacCAAbJYLLJas01XAQAAAAAAgGwjS6R2Xq9XM2bMUI0aNXxf77//vgzD8LX54YcfVKtWLf3+++9q1KiRDMPQnXfeqXbt2unvv/82sXoAAAAAAAAAV5MlAkrDMPTMM89o48aNvq9BgwbJ4/FIki5duqTffvtNbdq00YYNG2Sz2RQdHa34+HgNGjRIpUqVUq5cuUzuBQAAAAAAAIB/yxJrUIaFhSksLCzZsS5duvhe58qVSxs2bJDFYvEd27Ztm+91iRIlNHfu3JteJwAAAAAAAID0yRIjKNPin+EkAAAAAAAAgKwh2wSUAAAAAAAAALKeLDHFGzeH2+M2u4Sbxml3MqoWAAAAAAAgCyCgzMHqfVfP7BJumpBCIZrcaDIhJQAAAAAAgJ9jijeypW2nt2XrEaIAAAAAAADZBSMoc7DVbVfLaXeaXUamcnvc2XpkKAAAAAAAQHZDQJmDOe1OuRwus8sAAAAAAABADsYUbwAAAAAAAACmIaAEAAAAAAAAYBoCSgAAAAAAAACmIaAEAAAAAAAAYBq/DyhjY2M1YcIESZLH45HH40l23uPxKCkpSfHx8fJ6vZKk8+fP6++//1b37t01Y8YMHTt2TBcuXLjVpQMAAAAAAAC4Dr/fxdvlcunUqVOaNWuW4uPjNXnyZFmt/5+rJiYmqn///vr++++1c+dONWnSRLVq1dKaNWu0Z88eJSUlaeXKlcqfP78++ugjE3sCAAAAAAAA4N/8OqAMDw/XgAED5HA4tHjxYi1atEhz585VcHCwJCkpKUlOp1ONGzdW48aN1aJFC3Xo0EFPP/207Ha7Dh8+rOjoaEnSuXPnFBwcrEGDBqWrhtjEWNkT/fpjShe3x212CQAAAAAAAICPXydvtWvX1po1a3TixAl169ZN+fLl07lz5/Thhx9KkmJiYjRgwABJ0pYtW1SgQAGVLFlS06dP18mTJzVu3DjVrFlTkrR//3698cYb6a6h/uz6sjltmdcpAAAAAAAAAD5+HVBe8frrr+utt96SJLVq1UrTpk3znQsLC5Mkff755zp06JAaNWqkoUOHatOmTTp+/Lh2796tsmXLqnnz5nI4HKbU749CCoXIaXeaXQYAAAAAAAByOL8PKD///HNNnz5dgYGBOnz4sObPny+73S632y2n06l169apQIEC6tSpk1wul8aNG6f58+dr9erV+vvvv3X+/Hnt2LFDgYGBql27drqfv6rNKt+U8uzEaXfKYrGYXQYAAAAAAAByOL8OKBctWqSFCxcqJCRE1atX1+LFi9WlSxd16dJFFSpU0Pr16zVy5EhFRkZq1KhRatiwoR544AE988wzevvttzVgwAA1atRIderUUWJiYoZqcDlccjlcmdwzAAAAAAAAAJKfB5T169fXI488ovr166t79+7yer0aPny4Jk2apEOHDqlevXo6evSoBg0apIEDB6pJkybau3evmjZtKovFov3792v58uXKly+f4uLitHz5cjmdTGsGAAAAAAAA/IVfB5T/DhMNw9Abb7zhG0G5evVqjRw5UoZh6OTJk6pcubJeeuklDRgwQLVq1dJbb72lRo0a6dFHHzWpBwAAAAAAAPi/9u48OqoqX/v4U5VUZcIkQBjCIARFFKIEVAgyBDFKjCCNKEK3gp1GUFRAFBuZFPTSoiKDIAgXBGUQmd5WEGwRYjMPLYIgMiogg8yZk6qkzvtHmrpGAmSo5FSS72etrM6Zfydud7uetffZwLV4dUB5WWZmprKysuR0OjVlyhTNmzdPNWvWVGxsrI4dO6bnnntOixYt0rfffqtffvlFU6dO1ZAhQ5SVlaUvv/xSvr6+SktL01NPPaVRo0aZ/ToAAAAAAAAA/qtMBJTffPON7Ha7BgwYoAEDBuR7Tv/+/WWz2dS4cWNNnTo1z7GcnBxJko+PT4nXCgAAAAAAAKDgykRAWbVq1eueY7PZrnqMYBIAAAAAAADwTlazCwAAAAAAAABQcZWJEZRAUWRkZ5hdAgAvRN8AAAAAAN6FgBLlVvvP2ptdAgAAAAAAAK6DKd4AgAqpWfVmCvANMLsMAAAAAKjwGEGJciuxeyLhA4CrCvANkMViMbsMAAAAAKjwCChRbgX4BijQFmh2GQAAAAAAALgGpngDAAAAAAAAMA0BJQAAAAAAAADTEFACAAAAAAAAMA0BJQAAAAAAAADTlNuAMjMz0+wSAAAAAAAAAFxHmQgoHQ5HvvudTqdcLle+x7p166bNmzeXZFkAAAAAAAAAisnX7AIKokuXLjp79uwV+7Ozs/Xxxx+rSpUq6tixo8LCwhQQECBJ2rhxo0aOHClf39xXzMrK0n333acRI0YU6tnpznT5Oov+ZwrwDZDFYiny9QAAAAAAAEB5ViYCylWrVl33nL1797p/dzqdatKkidasWVPsZ3dY3EE+AT5Fvr5Z9WaaGzeXkBIAAAAAAADIh1cHlC6XSy6Xyz0KMj85OTmSpKZNmyokJEQ2m02SVKNGDbVv39593u7du7V9+3bddNNNJVrzH+08s1MZ2RkKtAWW6nMBAAAAAACAssCrA8odO3ZowIAB7oDy5MmTqlWrVp5zsrOzNXToUNntdi1dulTnzp3ThAkTNGvWLH3yySc6d+6cXnzxRUVHRyswsPAh4drH1io4OLjQ12VkZ6j9Z+0LfR0AAAAAAABQkXh1QNmiRQtt2bJFUu5oysjISP3jH/9Q27Ztrzj3rbfektPpVGJiohwOh2bOnKnRo0dr0KBB2r17tyQVaZp1oC2Q0Y8AAAAAAABACSkTq3hL0owZM9S6dWu1bdtWJ06c0Pjx4684x2q16tZbb1W/fv30wQcfaOLEibr77rtVuXJlEyoGAAAAAAAAcD1ePYLysiNHjmjWrFlat26dJKlmzZr65ptvVLlyZSUkJLjPW7p0qebOnauAgAAdOXJE48aNk91uV2RkpFmlAwAAAAAAALgGrw8oT548qccee0zDhg3Td999p/Pnz+vChQtq1qyZ+vfvr0aNGql169ZyOBzq3r27BgwYIEmKiYnR3LlzVb9+fUlSdHS0iW8BAAAAAAAAID9eH1CePXtWoaGhWrVqlWrXrq3w8HDVqFFDjRo1Ups2bTR//ny1bt1aGRkZea5r3bq17Ha7JKlv376yWq2qUqWKGa8AAAAAAAAA4Cq8PqBs2rSpvvnmm6sef/DBByVJ+/bty7N/7Nix7t8//PDDIi2QAwAAAAAAAKBklZlFcoqDcBIAAAAAAADwThUioAQAAAAAAADgnbx+ind5kJGdcf2T4BH8rQEAAAAAAMoWAspS0P6z9maXAAAAAAAAAHglpnijXGpWvZkCfAPMLgMAAAAAAADXwQjKUpDYPZGwrJQF+AawOBIAAAAAAEAZQEBZCgJ8AxRoCzS7DAAAAAAAAMDrMMUbAAAAAAAAgGkIKAEAAAAAAACYhoASAAAAAAAAgGnKbUCZmZlpdgkAAAAAAAAArsPrA8px48bp119/lSTl5OSoVatWcjgc172uW7du2rx5c0mXBwAAAAAAAKAYvH4V79tvv1333nuvZsyYoaSkJP3yyy+Kj493H09LS9O4ceP07LPPKiwsTAEBAZKkjRs3auTIkfL1zX3FrKws3XfffRoxYoQp7wEAAAAAAADgSl4fUMbHx6tp06YKCgpS586d9d133yk8PPyK8/bu3ev+3el0qkmTJlqzZk2xn5/uTJevs/B/pozsjGI/GwAAAAAAACjvvDqgXLt2rbZt26aXXnpJkydPlt1uV0xMjEJCQuTn56ekpCT17NlTw4YNU2RkpEJCQmSz2SRJNWrUUPv27d332r17t7Zv366bbrqpUDV0WNxBPgE+nnwtAAAAAAAAAP/l1QFldHS01q9frx49emjhwoWqVq2a/P39debMGUVERMjPz889ctJut2vp0qU6d+6cJkyYoFmzZumTTz7RuXPn9OKLLyo6OlqBgYGl/g7NqjdTgG9AqT8XAAAAAAAAKAu8OqAMDAzUa6+9puTkZC1evFijR4+Wv7+/goODZRiGUlNTlZGRobCwMNntdjmdTiUmJsrhcGjmzJkaPXq0Bg0apN27d0uSLBZLoWtY+9haBQcHF/kdAnwDivRcAAAAAAAAoCLw6oBSyl3cJjY2Vhs3btTBgwfVuXNnrV27Vv369dOKFStktVr16KOP6v3335fVatWtt96qqKgovfDCC5o4caKqVaumypUrF/n5gbZABdpKf+QlAAAAAAAAUBF4fUA5f/58xcbGymaz6fz58woNDVWzZs20ceNGpaamqnr16vLxyf1G5NKlSzV37lwFBAToyJEjGjdunOx2uyIjI01+CwAAAAAAAAD58eqA0uFw6J133nGvxl29enX17t3bffzo0aMKCQnRPffcI4fDoe7du2vAgAGSpJiYGM2dO1f169eXlPs9SwAAAAAAAADexWp2Adeyb98+de/eXbVr15YkjRw5Uhs2bNCGDRs0cuRIhYSEKDExUdWrV1dGRkaea1u3bi273S5J6tu3r6xWq6pUqVLq7wAAAAAAAADg6iyGYRhmF1EU58+fl8PhUHh4+HXPNQyj0AvVJCcnKyQkRElJScVaJAcAAAAAAACQpHRHthqP+kqS9OOYjgq0e/Xk5mIraL5WZv8KVatWLfC5rKINAAAAAAAAeCevnuINAAAAAAAAoHwjoAQAAAAAAABgGgJKAAAAAAAAAKYhoAQAAAAAAABgGgJKAAAAAAAAAKYhoAQAAAAAAABgGgJKAAAAAAAAAKYhoAQAAAAAAABgGgJKAAAAAAAAAKYhoAQAAAAAAABgmjIXUNaqVUvZ2dn5Hnv44Ye1ZcsW93Z6errq1q1bWqUBAAAAAAAAKCSvDiizs7PldDrz7LPZbPL19c1zzuXA0uVy5TkWGBgoHx+f0ikWAAAAAAAAQKH5Xv8U86xatUpvv/227Ha7LBaLJOnMmTOKjY11n+N0OtWhQwetXLlShw4dUkJCgrp06aJjx47p8OHDOn36tNq0aSOn06lBgwapZ8+ehSvCkSY5CDmLzBYo/fefHQAAAAAAAPBHFsMwDLOLuBbDMNzhpCTVr19fv/zyS55zXC6XMjMzVadOHa1evVpRUVGyWCyaPXu2nnnmGe3evVtNmjS5YoTltSQnJyskJERJQ29QsB8BW5HVjZYSVhNSAgAAAACACi/dka3Go76SJP04pqMC7V49drDY3PlaUpKCg4Ovep5XT/GWpDfeeEMtW7ZU+/bt1b59e4WHh7t/b9u2rUaOHCmr1ap///vfunjxokaMGKHDhw/LZrNp3rx5stvtmjBhgqxWa4HDSXjQ8S2SM93sKgAAAAAAAOClvD6xGzVqlEaNGnXd8959913deuutatq0qWbOnKnWrVvrtttu0/Hjx3X06FF9/fXXuv/++wtfwEv7pWskvLgKR7r07s1mVwEAAAAAAAAv5/UBpSRVqlRJUVFRV+zftWuXUlJS9M0338hut+umm27SY489pnr16umee+7RmjVr9K9//UtTp05Vhw4dNHfu3MKHlPag3B8AAAAAAAAAHlcmAsr69etrw4YNV+yPjIyUJLVr105169bV4MGDZbVaNXDgQPXs2VMRERGSpFtvvVWzZ8/W5s2bizaKEgAAAAAAAECJKBMB5eWVuP/o1KlTkiSbzaZbbrlFLpdLLpdLH374oYKC8o56jIuLU1xcXKnUCwAAAAAAAKBgykRAWbly5XxHUDZs2DDPdlZWlhwOh0JCQiRJ2dnZysrKKpUaAQAAAAAAABRemQgoDx48WKD933zzTZ5tX19f9yhLAAAAAAAAAN7HanYBAAAAAAAAACouAkoAAAAAAAAApikTU7xRxjnSza6gbLEFShaL2VUAAAAAAACUCgJKlLx3bza7grKlbrSUsJqQEgAAAAAAVAhM8Qa8zfEtkpNRpwAAAAAAoGJgBCVK3suHJHug2VV4P0c6o00BAAAAAECFQ0CJkmcPlOxBZlcBAAAAAAAAL8QUbwAAAAAAAACmIaAEAAAAAAAAYBoCSgAAAAAAAACmIaAEAAAAAAAAYBoCSgAAAAAAAACm8fqA0uVyyel05nvM6XQqJydHkrRt2zZdunQpz/G77rpL2dnZJV0iAAAAAAAAgCLyNbuA69myZYv69u0rX9/cUn/88Uc1btxYkuRwOPTOO+/ooYce0t69e5WQkKAVK1boL3/5i/z8/HTw4EHFxcUpIyNDcXFxGjlyZOELcKRJDh9PvlLF4Eg3uwIAAAAAAACUAV4fUN5zzz3as2ePe7t+/fr6/vvvrzjvr3/9q6pVq6ZKlSpp/fr1slqtio6O1po1a5SYmKgvv/xS2dnZ7qCzwMY3kvwsxXwLAAAAAAAAAPnx+oAyMzNTfn5+sljyDwmdTqeSk5P13nvv6dVXX9XQoUO1Z88e+fr6at++fYqNjdXFixd17tw51apVS4MGDSrdF6jo6kZLtkCzqwAAAAAAAICX8vqA8o477pDdbpckGYaho0ePqmHDhvLz85OUO8175syZCgoKUo8ePbRixQrNnz9fdrtdx44d0zPPPKO9e/cqNTW1aOHkS/ul4GAPvlEFYwuUrhIuAwAAAAAAAF4fUB44cMD9+8aNGxUXF6dnnnlGL730Up7zYmJitH37dklSWFiYbDabfH19FRYWppiYGNWpU6doBdiDcn8AAAAAAAAAeJzXB5S/984772jevHkaNWqUnnzySVWvXt19LDk5Wb169dJ7772nsWPHyt/fXydPntRbb70ll8uloKAgLV++3MTqAQAAAAAAAPxRmQkop0yZopSUFHXp0kVpaWnq3r27VqxYoUqVKkmS3n//ffXt21f333+/4uLiZLFYFB0drdWrV0uScnJy5HK5ZLVazXwNAAAAAAAAAL/j9QFlcnKyXn75ZW3dulVr1qyRJP35z3/Wzz//rLvvvlsTJ05UVFSUZs6cqT179uiFF17QgQMHZLFY3IvkSFJ2drbeeOMNtW3b1szXAQAAAAAAAPA7Xh1Qrl+/Xt26dVPPnj21ZcsWBQQEuI8NHz5cd911l1555RUlJCRozJgxqlSpkqZNm+Y+p1mzZlq5cqV7QR0AAAAAAAAA3sWrA8q2bdvq+++/V61atfI93rFjR3Xs2FGGYciSz0rRO3fuLOkSAQAAAAAAABSD13+Q8Wrh5O/lF04CAAAAAAAA8H5eH1ACAAAAAAAAKL+8eoo3UGE50j17P1ugxEhjAAAAAADghQgoAW/07s2evV/daClhNSElAAAAAADwOkzxBiqC41skp4dHZQIAAAAAAHgAIygBb/TyIckeWPz7ONI9PxoTAAAAAADAgwgoAW9kD5TsQWZXAQAAAAAAUOKY4g0AAAAAAADANASUAAAAAAAAAExDQAkAAAAAAADANGUqoHQ4HEpJSTG7DAAAAAAAAAAeUqYCyi+//FL9+vXL99gHH3ygffv2ubfvu+8+nT9/vrRKAwAAAAAAAFAEZWoVb39/f9nt9nyPNW/eXI899piGDx+u2267Td99950ef/xx9/GMjAy9+OKLevTRR0urXAAAAAAAAADXUaYCymuJjo7Whg0blJGRoWeffVaJiYlq2rRp8W/sSJMcPsW/zx/ZAiWLxfP3BQAAAAAAAMqQMhdQLlu2TBs2bJCU+03KqKgojRs3TrNnz9brr7+ub775RikpKXr44YdVo0YN2Ww2ORwONWvWTDNmzCj8A8c3kvxKIEisGy0lrCakBAAAAAAAQIVW5gLKRx55RHPmzMmzLzs7W/Xq1VN8fLy+/vprVa5cWTt27FDjxo117NgxPfDAA5o6dao5BV/N8S2SM12yB5ldCQAAAAAAAGCaMhdQ5sfX11fPP/+8evXqpQ0bNuj555+Xw+FQzZo1lZmZqVmzZum3335T7dq19fLLLxfu5i/tl4KDPVesI11692bP3Q8AAAAAAAAow8pFQHlZx44dtWrVKg0ZMkTBwcG6dOmSYmNjdeLECa1fv14DBw4s/E3tQYxyBAAAAAAAAEqI1ewCPGXt2rWKiIhQaGioLly4oNDQULVq1Upr1qxRamqqAgIC5ONTAovdAAAAAAAAACgyrx5BmZOTI5fLJZvNdt1zXn/9db377ruSpODgYL322mvuc06fPi2LxaJ27dopKiqqpMsGAAAAAAAAUEBeHVD+85//1IgRI2S32/Ps/33ImJOTo0cffVS33367WrRoIUl6+umn9fTTT0uSduzYob59+2ru3Lm6/fbbS612AAAAAAAAANfn1QHlI488okceeaRY97jtttu0dOlSRUREeKgqAAAAAAAAAJ5Sbr5BeTVBQUGEkwAAAAAAAICXKvcBJQAAAAAAAADv5dVTvMs9R7rZFcCb0B4AAAAAAEAFREBppndvNrsCAAAAAAAAwFRM8Qa8Td1oyRZodhUAAAAAAAClghGUZnr5kGQniMIf2AIli8XsKgAAAAAAAEoFAaWZ7IGSPcjsKgAAAAAAAADTMMUbAAAAAAAAgGkYQQlUFKwSDm/BZwwAAAAAAL9DQAlUFKwaD29RN1pKWE1ICQAAAACQxBRvAEBpO75FcjKiFwAAAACQy5QRlHPmzNHcuXPl4+MjSXI4HLLb7ZIkwzAUGRmpSZMm5blm/vz58vPz06OPPqrz58+ratWqeY4fPHhQe/fu1Z/+9KdSeQegzGHVeJjNkc5IXgAAAADAFUwJKE+dOqXevXsrMDBQ3bp1U9OmTbVnzx4tWrRId9xxhwYPHnzFNYcOHVK1atUkSU888YR69uypXr16uY/b7XZNnjxZW7Zs0YEDB7R3717Z7XZlZ2fLx8dHFy5c0LJlyxQdHV1q7wl4FVaNBwAAAAAAXsiUgPLyyMmlS5fK6XRKkjZu3Kh3331XixYtkuW/3yVzOp2y2WySpOzsbHdA+fHHH2vFihXKzMyU1WqV3W5XvXr1tGbNGp09e1Y1atSQJK1evVqffvqp5syZU/RiHWmSw6fo119xP6Y1AgAAAAAAAJeZElBmZWVp7ty5Cg0N1VdffaWTJ09qzpw5qlevnkaOHCmHwyFJiomJkcVikcVi0Y8//qjq1aurX79+aty4sSRp+vTpevXVV9W8eXMNHDhQgwYNUkxMjGeLHd9I8mMhBwAAAAAAAKAkmBJQvvDCC3ryySfd21u3btXw4cPd2wEBAZKkTZs2uffdcsstmjNnjt58802tWLFCZ86cUVhYmKzW3HV+EhISNHXqVLVq1cr9PUuvVjdasvE9QAAAAAAAAFRspgSUn332mSZPnqxKlSpJko4fP64+ffpIyh1dGRkZqWnTprnPP3z4sPz9/VWzZk1JudO9O3TooLfeekudOnWSJHXu3FlRUVH66aefdMcdd3iu2Jf2S8HBnrvfZbZAycLITAAAAAAAAFRspR5QpqenKyEhQX379nXvi4yM1Jo1a9zbTqdTKSkpuuGGGyRJY8aMUf/+/d3H33zzTT388MPucPKy9957T/7+/p4NKO1BLCwCAAAAAAAAlJBSDyinTp2qFStWuBe/kaSaNWsqNjbWvZ2Tk6MmTZpoypQpWrx4sXbt2qUZM2bo1KlT2rFjh+x2u5YsWZLnvqdPn9aSJUv0448/ltq7AAAAAAAAACgea2k/cMiQIapZs6ZmzJihqVOnqkuXLlqzZo1uvvlmffzxx/r4449VrVo1TZkyRZ988okGDBigBQsW6PDhw3rmmWdUrVo1ffbZZ7Jardq+fbvGjRsnSRo4cKC6d++uVatW6aabblKTJk303HPPafXq1YqMjNTNN9+s+Pj40n5dAAAAAAAAANdQ6iMof/jhB504cUINGjTQkSNH9PHHH+uFF15QcHCwtm/fri5duujkyZP69ttvNWHCBK1cuVJpaWnq16+f/ud//kd/+9vf1K5dO1mtVh07dkxvvPGGXC6XOnfurJYtW6phw4bq3r17ab8WAAAAAAAAgCKwGIZhlOYDnU6nfvvtN9WpU0eGYSg5OVkhISF5znE4HLLb7TIMQxaTFpK5XFdSUpKCS2KRHKA0ONKksbVyfx92ku+pwly0RwAAAAAVXLojW41HfSVJ+nFMRwXaTVm/utQUNF8r9SneNptNderUkSRZLJYrwklJstvt7uMAAAAAAAAAyq9SDygBAAAAAAAA4DICSgAAAAAAAACmIaAEAAAAAAAAYBoCSgAAAAAAAACmIaAEAAAAAAAAYBoCSgAAAAAAAACmIaAEAAAAAAAAYBoCSgAAAAAAAACmIaAEAAAAAAAAYBoCSgAAAAAAAACmKZcBZXZ2tlasWGF2GQAAAAAAAACuw+sDysTERK1fv15SbvBYEFarVW+88Ya++uqrkiwNAAAAAAAAQDH5ml3A9SQlJWnatGlavXq1nnrqKR0+fFh+fn6SpAsXLujnn3/Wnj171KVLlzzXnTt3Tq+88or+/ve/u/e9+uqrevzxxwtXgCNNcvgU+z0AUzjSza4AAAAAAADgmrw+oIyPj9fy5cslSfPmzXPv//e//63nn39eCxcuVO3atbVr1y4ZhnHV+wwdOlSXLl0qfAHjG0l+lsJfBwAAAAAAAOC6vDqgdLlc8vHx0Zw5c/LsP3TokHr37q3169erTp06crlc2r59+zXvlZCQIH9//xKsFvBidaMlW6DZVQAAAAAAAFzBqwPKTz75RBMmTNC+fft0/vx5VapUSZLk7++vGjVqqE6dOpJyvznZp08fBQUFyWLJHe3odDrl4+MjqzX3M5tZWVkaM2aMbrzxxsIV8dJ+KTjYcy8FmMEWKFkYCQwAAAAAALyPVweUvXv3Vu/evVW/fn13OClJvr5Xlv3999/n2e7Ro4f69Omj2NjY4hVhD8r9AQAAAAAAAOBxXr+KNwAAAAAAAIDyq8wFlF9//bUSEhIUHh5udikAAAAAAAAAismrA0rDMDR69Gi5XC6NGDFCJ0+eVNOmTfXcc8/lWTjnj6t3Z2RkKD093f09SgAAAAAAAADeyau/Qbly5Urt2rVLR48e1VdffaWnn35ax44dU3Z2thwOh5xOp9LS0vTnP/9Z77//vvu6Bx54QL6+vmrSpImJ1QMAAAAAAAC4Hq8OKDt16qT4+HhZLBbFxcUpLi7Ofczlcik7O1s5OTmy2Wx5rlu/fn1plwoAAAAAAACgCLw6oJQkqzX/WehWq1V2u72UqwEAAAAAAADgSV79DUoAAAAAAAAA5ZvXj6AEAJQv6Yaf5MiRlG12KQAAAABQqtIdOWaX4JUIKAEApapx1kfSm3wrGAAAAACQiyneAAAAAAAAQCm6q15lBdh8zC7DazCCEgBQqn70+6s05JBkDzS7FAAAAAAwRYDNRxaLxewyvAYBJQCgVAVasiS7j2Tn/4IAAAAAAEzxBgAAAAAAAGAiAkoAAAAAAAAApiGgBAAAAAAAAGAarw8oly1bJsMwJElDhw7V7t27denSJS1evFjvvPOO4uPjtWXLFm3ZskXr1683uVoAAAAAAAAAheHVAWV2drbWr1+vzp07KycnR6dOnZLD4dDu3bv1wQcfqEWLFpoyZYqioqIUFBSkgQMHatq0aWrZsqVuu+02RUZGqnHjxmrcuLFq1qypkydPmv1KAAAAAAAAAH7HYlwenujF9uzZo3/9619asGCBWrRooaioKG3atElz5szJc15mZqYyMjJUuXJlSdL06dN1+vRpvf7664V+ZnJyskJCQpSUlKTg4GAPvAUAVHCONGlsrdzfh52U7EHm1gMAAAAAKFEFzdd8S7GmQtu7d69WrlypF198UU6nU4mJiWrVqpXCwsK0atUqtWnTRoZh6IEHHtDBgwf10ksvqVmzZp4twpEmOXw8e8+yzhYoWSxmVwEAAAAAAIBywKsDyho1aujnn3/W448/rmXLlqlKlSpKT0/X+fPn9dBDD6lXr16aM2eORowYoQULFmjKlCmaNWuWZ4sY30jyI4zLo260lLCakBIAAAAAAADF5tUBZVhYmKZNm6aDBw9q0KBBWr16tRwOh2JjY2Wz2dzn+fj46Mknn9Rdd92lI0eOqEGDBiZWXQEc3yI505meCQAAAAAAgGLz6oBSko4ePaoFCxaoSZMmateunQYPHqyDBw8qJCTkinOHDRumuLg49evXz3MFvLRf4huUuRzp0rs3m10FAAAAAAAAyhGvDygXLVqkrKwsPf3009q4caMk6bvvvtNtt92W57xdu3bpp59+0tKlSz1bgD2IkYIAAAAAAABACfH6gHLu3LlatmyZsrKylJKSoqysLC1ZskQbNmzQgQMHJEnZ2dnq37+/+vfvr4kTJ2ratGmy2+1KTU1VTk6OFi9erMzMTMXGxurDDz80+Y0AAAAAAAAAXObVAeX3338vwzDUqFEjvfzyy/L19dXFixfVsWNH1a5dW3v37lVWVpZycnL0l7/8RT179lTlypU1ePBgs0sHAAAAAAAAUAAWwzAMs4u4lszMTPn7+5f6c5OTkxUSEqKkpCQF8w3KXI40aWyt3N+HnWTqO4DCoQ8BAAAAgAqloPmatRRrKhIzwkkAAAAAAAAApcPrA0oAAAAAAAAA5RcBJQAAAAAAAADTePUiOfBijnSzK5BsgZLFYnYVAAAAAAAAKAYCShTNuzebXYFUN1pKWE1ICQAAAAAAUIYxxRtl1/EtktMLRnICAAAAAACgyBhBiaJ5+ZBkDzTn2Y507xjBCQAAAAAAgGIjoETR2AMle5DZVQAAAAAAAKCMY4o3AAAAAAAAANMQUAIAAAAAAAAwDQElAAAAAAAAANMQUAIAAAAAAAAwTbkNKF0ul9klAAAAAAAAALiOchlQpqSkqHnz5srIyDC7FAAAAAAAAADX4Gt2AQW1detWTZ48WfPnz1dcXJw++OADNWjQQJJ0/vx5DR06VHa7XRaLRZKUlJSkbt26uc9xuVxq166devToUbgHO9Ikh49H36XMcqSbXQEAAAAAAADKmTITUPr4+Mhms0mSfH19ZbXmDv78/PPPFR0drQEDBriPS9Lzzz+f53rDMBQaGlr4B49vJPlZilw3AAAAAAAAgKvz+oCydevWslgsSk1NVVRUlHv/unXrtGzZMjVq1Ej33HOPbr/9dk2ZMkWTJk1S1apV89zjyJEjOnPmTClXXo7VjZZsgWZXAQAAAAAAgHLA6wPKjRs3SpJ27NihKVOmuPdv375dH374oWrVquXeZxiGnnvuOQ0aNCjPPW6++eaiF/DSfik4uOjXl0e2QMnCqFIAAAAAAAAUn9cHlFfzyiuv5AknJSkrK0tTpkzRp59+mmf/b7/9VvQH2YNyfwAAAAAAAAB4XJkNKPMzePBgSdLAgQP17LPPqkePHgoICFBERITJlQEAAAAAAADIj9cHlDk5OdqxY4fmz5+f7/FLly7J399fPj4++tvf/qagoCD5+v7fa508eVJPPPGE5s6dq3bt2pVW2SgtrCwOlB38+woAAAAAyIdXB5Rbt27Vn/70J3Xo0EERERFKTk6WlLui95EjR1S/fn19/PHH+s9//qPTp0+rWbNmeuutt+RwOHTp0iX5+PjoscceU4MGDdS1a1etWbNGt9xyi8lvBY96txjfFwUAAAAAAIDpvDqgbNmypX766SeFhIRo/fr1mj59uiSpV69eGjVqlIYNG6agoCBNmzZNvr6+atCggSTp4Ycflr+/v5o3by5JuvPOO7Vr1y5VrlzZtHcBAPxX3ejcxbYAAAAAAJBkMQzDMLsIb5ScnKyQkBAlJSUpmFW8vYsjTRr73wWSXj4k2Qk6gDLFFihZLGZXAQAAAAAoYQXN17x6BCVwXfZAVlkHAAAAAAAow6xmFwAAAAAAAACg4iKgBAAAAAAAAGAaAkoAAAAAAAAApiGgBAAAAAAAAGAaAkoAAAAAAAAApiGgBAAAAAAAAGAaAkoAAAAAAAAApiGgBAAAAAAAAGAaAkoAAAAAAAAApiGgBAAAAAAAAGAarwgoc3JyzC4BAAAAAAAAgAl8zS5AkqpXr66jR4+qUqVK7n0ZGRnasWOHbDab9u3bp2XLlmn48OH6/PPPdebMGfXp00eGYSg4OFhNmjS54p6PPPKIunTpot69e5fmqwAAAAAAAAAoBK8IKKtUqZInnJRyA8pvv/1Wvr6++vXXX3X27FklJibq4MGDSklJUWJiogzDUMOGDfMNKG+44QZVq1at+MU50iSHT/HvA89xpJtdAQAAAAAAADzEYhiGUdoPbdq0qaxWq/z8/CRJu3fv1h133OE+vnPnTmVlZenBBx+U0+lUdna2srKyFBQUpKysLLlcLgUEBCgzM1NDhgyR1WrVyy+/rKCgIFmtubPWf/nlF1WpUkXBwcGS/m8a+c6dOwtUY3JyskJCQpQ09AYF+1k8+frwpGEnJXuQ2VUAAAAAAADgD9z5WlKSO6PLjykjKP38/PTZZ5+pfv36unTpku69915t2bLFffzyyMdffvlFs2fP1sKFC2W3293Hs7Oz1aFDBx06dEgZGRnq2rWrdu3aJT8/P1ksuWHiU089pR49eiguLk6S5HQ6lZaWVopviRJXN1qyBZpdBQAAAAAAAIrBlIDy8ihHSfrqq6908uRJ7dy5U82aNZMk+fjkTqnevXu3zp8/r6eeeirPNYZhqFq1aurYsaPsdrs7lLwWm82m0NDQwhf70n7pGgkvTGQLlArwzx4AAAAAAADey9RvUJ47d06jR4/WjBkz9PTTT6tv377q27ev+/jkyZO1fPly+fv769SpUzpw4IDatm0rq9Uqh8OhzMxM90hMSbr77rvldDrl6+ur1NRU7dy5UyNGjNCFCxf02muvFW3BHHsQU4gBAAAAAACAEmJaQHnkyBENHDhQgwcPVpcuXXTvvfcqLi5ON954o/uc9u3by2azacuWLapfv74yMjI0evRoXbx4UWPGjNHy5ctVt25d9/lpaWn68ssv3YHlZc8//7xcLldpvRoAAAAAAACAAjItoAwPD9f48eP1wAMPSJKCg4OVmJiY51uTklSzZk116NBBn3/+uf72t78pJiZGnTp10uDBg/OEk1LeqeN/VJBp4AAAAAAAAABKlykBpWEYCggIcIeTl9ntdvdq25L06aefauvWrXriiScUFxenVatW6f7779e4cePUp08fhYaG6sEHH3SHjy6XS/Hx8VeEnCdOnFDz5s1L/sUAAAAAAAAAFMrVhxyWoNTU1Hz3f/nll4qIiFDHjh0lSZGRkWrWrJnmzJmjnJwcPfHEE/r1118VFhamxYsXa9KkSZoxY4b7eqfTqS+//FLff/99np/HH39cTqezVN4NAAAAAAAAQMFZDMMwzC7iMsMwlJ2dLZvNJkn66KOPVL9+fcXExMhqtWrWrFmqV6+eYmNj81xTEtO3k5OTFRISoqSkJAWzijcAAAAAAABQKAXN17wqoPQmBJQAAAAAAABA0RU0XzNlijcAAAAAAAAASCau4u3tLg8sTU5ONrkSAAAAAAAAoOy5nKtdbwI3AeVVnD9/XpJUt25dkysBAAAAAAAAyq6UlBSFhIRc9TgB5VVUqVJFknTs2LFr/gEBb5ecnKy6devq+PHjfE8VZRptGeUFbRnlBW0Z5QVtGeUB7RjeyjAMpaSkqFatWtc8j4DyKqzW3M9zhoSE8C83yoXg4GDaMsoF2jLKC9oyygvaMsoL2jLKA9oxvFFBBv6xSA4AAAAAAAAA0xBQAgAAAAAAADANAeVV+Pn56bXXXpOfn5/ZpQDFQltGeUFbRnlBW0Z5QVtGeUFbRnlAO0ZZZzGut843AAAAAAAAAJQQRlACAAAAAAAAMA0BJQAAAAAAAADTEFACAAAAAAAAMA0BJQAAAAAAQDlx7Ngx7dixQw6Hw+xSgAKrMAHlnj17dPfdd6ty5coaMmSICrI20JIlS1SvXj3VqlVLCxcuzHNs6tSpqlGjhho0aKC1a9eWVNnAFTzZlg3DUGhoqCwWi/vnzTffLMnyAbeitGVJ2rRpkxo1anTF/mv12UBJ8nRbvuOOO/L0y3369PF0yUC+itKWR48erSpVqsjPz09du3ZVSkqK+xj9Mszi6bZMvwyzFKUtDx48WM2bN9ef//xnRURE6KeffnIfo1+GVzMqgMzMTKN+/fpGv379jEOHDhnx8fHG7Nmzr3nNDz/8YNjtdmPmzJnG7t27jZtvvtn46aefDMMwjNWrVxv+/v7G//t//8/YuHGjERERYZw7d640XgUVnKfb8v79+4169eoZFy9edP9kZmaWxquggitKWzYMw9ixY4dRvXp1o169enn2X6udAyXJ0205LS3NCAwMNM6cOePul9PT00uoeuD/FKUtz5s3z2jYsKGxdetW4+DBg0ajRo2MYcOGGYZBvwzzeLot0y/DLEVpy+vWrTMaNmxoJCUlGYZhGE899ZTRu3dvwzDol+H9KkRAuXz5cqNy5cpGWlqaYRiG8f333xutW7e+5jUDBw40Onbs6N6eOHGiMXz4cMMwDKNLly5Gv3793McGDRpkzJw5swQqB/LydFueN2+e0aNHj5IrGLiKorTl1NRU48YbbzTGjBlzRahzrXYOlCRPt+UNGzYY0dHRJVUucFVFacv/+Mc/jE2bNrm3R40aZTz44IOGYdAvwzyebsv0yzBLUdrypk2bjMTERPf2pEmTjE6dOhmGQb8M71chpnjv2rVL0dHRCgwMlJQ7RP/HH3+87jUdOnRwb7do0UL/+c9/rnsMKEmebsvbtm3Ttm3bFBoaqurVq2vEiBEFnpoIFEdR2rLNZtOmTZvUtm3bfO9HvwwzeLotb9u2Tb/++quqVaum0NBQPfvss8rKyiqR2oHfK0pbHjp0qFq1auXe3r9/vxo2bOi+H/0yzODptky/DLMUpS23atVKMTExkqRz585p9uzZ6tq1q/t+9MvwZhUioExOTlZERIR722KxyMfHRxcvXizwNcHBwTp58uR1jwElydNt+cCBA+rcubN27typBQsWaPr06Vq0aFHJvQDwX0Vpy3a7XbVr1y7Q/eiXUVo83Zb379+vNm3aaMOGDfrqq6/09ddfa8KECR6vG/ijorTl3ztw4ICWL1+uvn375ns/+mWUFk+3ZfplmKU4bXnmzJm68cYbVbNmTSUkJOR7P/pleJsKEVD6+vrKz88vzz5/f3+lp6cX+Jrfn3+tY0BJ8nRbXrVqlSZOnKiIiAjFxsZqwIABWrJkSckUD/xOUdpyYe5Hv4zS4um2PH36dC1cuFCNGjVSy5YtNWrUKPpllIritGWXy6WEhAT16dNHTZo0yfd+9MsoLZ5uy/TLMEtx2nKvXr302Wefae/evZoyZUq+96NfhrepEAFllSpVdPbs2Tz7UlJSZLfbC3zN78+/1jGgJHm6Lf9R9erVdeLECc8UC1xDUdpyYe5Hv4zS4um2/Ef0yygtxWnLb7zxhi5cuKB33nnnqvejX0Zp8XRb/iP6ZZSW4rRlPz8/derUSWPGjNGsWbPyvR/9MrxNhQgo7777bm3evNm9/fPPPysrK0tVqlQp8DU7d+50T8e61jGgJHmyLWdkZOj2229XRkaG+9jmzZtVr169kike+J2itOXC3I9+GaXF0225VatWOn78uHubfhmlpaht+YsvvtB7772npUuXur+Tlt/96JdRWjzdlumXYZaitOVJkyZpwYIF7m273S4fH59870e/DG9TIQLKdu3aKTk5WR999JEkaezYsYqNjZWPj48uXbqknJycK67p1q2bPv30U/3www9KTU3V5MmT1bFjR0nSo48+qg8++EAnTpzQb7/9plmzZrmPASXJk205ICBANWrUUP/+/bVjxw5NmDBBCxYs0LPPPlvar4UKqCht+Vqu1WcDJcnTbblJkybq16+ftm7dqrlz52r8+PH0yygVRWnL+/btU8+ePfX++++rbt26Sk1NdU8XpF+GWTzdlumXYZaitOUGDRpo0KBBWrdunfbv36933nlHjz32mCT6ZZQBZi8jXlr++c9/GoGBgUbVqlWNatWqGXv37jUMwzAkGTt37sz3mmHDhhl2u90IDg427rzzTiM9Pd0wDMNwuVzGE088YQQEBBgBAQFGp06dDJfLVVqvggrOk2356NGjxr333mv4+fkZjRo1MpYsWVJarwEUqS0bhmGsW7fOqFev3hX7r9bOgZLmybZ88eJF409/+pMREBBg1KtXz/jggw9KsHIgr8K25UGDBhmS8vz8vk3TL8MsnmzL9MswU1H+G2P8+PFGeHi4ERYWZgwdOtTIyclxH6NfhjezGIZhmJCLmuL06dP6z3/+o+joaFWtWrVA1/z44486ceKEYmJirvg+w/bt25WWlqaYmBhZLJaSKBnIl6fbMmCWorTla6GdwyyebsuAWeiXUV7QL6O8oF9GRVGhAkoAAAAAAAAA3qVCfIMSAAAAAAAAgHcioAQAAAAAAABgGgJKAAAAAAAAAKYhoAQAAAAAAABgGgJKAAAAAAAAAKYhoAQAAECxpaWladasWWrZsqVGjx7t3j927FiFhoZq7dq1JlYHAAAAb0ZACQAAgGJbtmyZ/vnPf2rbtm0yDMO9//jx40pKStLZs2dNrA4AAADejIASAAAAxfbkk0/qmWeeuWL/1KlTdeLECT3++OOFvudvv/2mcePGeaI8AAAAeDECSgAAAHiEv7//FfusVqtq1apVpPsNGTJEGRkZxS0LAAAAXo6AEgAAAB5htXruPy1HjhypTz75xGP3AwAAgPcioAQAAEChOZ1OjRs3Ts2bN1fbtm0VHR2tNWvW5Dnn4MGDGj58uG688UYlJibmOfbFF1+offv2atu2rapWrSqLxaLXX39dkjRu3Dh98cUXkqQ5c+aoffv2+utf/+q+dsGCBbrnnnsUGxurBg0aqG3btnme/e2332rQoEGKiIjQ6NGjtX37dg0ePFh33nmnwsPDNX369CveJysrS2PHjlWHDh3Upk0bNWjQQEOGDFFqamqe8y5evKjBgwerU6dOuuWWW9SwYUNNnjy5OH9KAACACs/X7AIAAABQtrhcLnXr1k3Hjh3TqlWrFB4erp9//llt27Z1n5OUlKS1a9dq+fLlOn78eJ7rf/jhB73wwgvauXOnKleurMzMTP3lL39xH//73/+uli1b6t5779VTTz3lDi4l6aOPPlJCQoI+//xzde7cWSkpKYqKilLXrl3166+/KiQkRDExMTp16pQmTZqkVatW6a677tJ7770nwzD0wAMP6LnnnlObNm0UGRkpScrMzNQDDzygyMhIff311/Lx8dH06dP17LPPav/+/fr8888lSZcuXdJ9992nCRMmKCYmRoZhaPjw4Ro4cKDOnTunMWPGlOBfHQAAoPxiBCUAAAAKZfLkyfriiy80a9YshYeHS5IiIiL0yiuvuM8JCQlRv3791KlTpyuu//rrryVJN9xwg6Tcb1dOnjxZNpvtus9evny5JKl58+bue8THxys1NVX79+93n1etWjVJ0v3336+HHnpIkmSxWBQfHy+Xy6V169a5zx0zZowOHDig8ePHy8fHR5LUpUsXBQcH64cffnCfN2LECLVt21YxMTHu+7366quSpLffflspKSnXrR8AAABXYgQlAAAACswwDL399tuqXbu27rzzzjzHbr/99ivODwgIuGJfvXr1dPToUXXs2FHvvfeemjZtqtq1a2v48OHXff6wYcPUsWNH1a5dW5KUnJysCxcuSFKeBXUuB42X//ey0NBQSbmjIS+/z6xZs3TXXXflqTU8PFynT592h6aGYejTTz9V1apV1b59+yveR5KOHDmipk2bXvcdAAAAkBcBJQAAAArsp59+0qlTp9SiRYsrjlkslgLt69q1q/r3769p06YpKipKcXFxev3119WyZcvrPj86OlrR0dHasGGD/vd//1c+Pj7uYNIwjOtef7menJwcSdLZs2d15syZfEdv/j6wPHfunM6fP6/Ro0frueeeu+5zAAAAUHBM8QYAAECBXR6t6HQ6i3wPq9WqqVOnauvWrYqPj9fq1avVqlUrvf3229e99syZM+rUqZPefPNNvfnmm5o1a5buuOOOItdyOdTcu3dvvsddLpecTqc70Pzuu++K/CwAAADkj4ASAAAABVarVi1JuSt0Z2dnF+ted999t1auXKk1a9aoSpUqGjZsmH799dernm8Yhjp37qzvvvtOn3/+uerUqVOs50u536oMDQ3VoUOHtHLlyiuOz5w5U7/99puqVaumSpUqadGiRfrll1+uOO/TTz/VsWPHil0PAABARURACQAAgAKLiIhQ06ZNlZqaqiVLluR7zu+DS5fLJSnv9OuJEyfq6NGj7u377rtPr7zyinJycnT69GlJ//ftyMvXS7nTsbdt26awsDDZ7Xb3/sv3/v0zLo94vNq078v7rVarHn30UUlSv379tH37dvfxhQsXasWKFapTp458fHzUtWtXpaWlKTY2Vps3b3bfa926dZo9e7ZuvPHGfJ8FAACAayOgBAAAQKFMmzZNAQEBev7557Vt2zZJuYHgsmXLJEn79u1TamqqXC6XDh06JEk6fPiw+/rs7Gw9/vjjOn78uKTcMHDv3r267bbbFBUVJUnuRXAuj1ZcsWKFqlatqrCwMP3www966623tGXLFg0bNkyLFy+WJG3atEnjxo2TJB04cOCK50rSyZMnJeUuaHPZ2LFjVb9+fZ04cUItWrRQ3bp1FRoaqv79+2vSpEnu88aNG6e6devq8OHDuueeexQeHq6aNWvqwQcfLND0dAAAAOSPgBIAAACF0qpVK61fv15RUVG699579dBDD6l///6qX7++qlatKh8fH82ZM0d33nmnFi5cKEl64YUX1Lt3b/c9tm7dqptuuknNmjVTmzZt5Ovrq3Xr1snXN3cNxwYNGmjw4MFatmyZevXqpbCwMPn4+GjRokW69dZbNXbsWA0bNkzx8fGaOnWqbrjhBq1evVrdunXTM888oxdffFGSNH/+fN155506ffq0YmJi9Prrr0uS5s2bp7vuuks5OTmqVq2aNm/erN69e6ty5cq6cOGC2rZtq02bNqlBgwbumsPDw7V161b99a9/VVhYmC5evKhbbrlF69atcwerAAAAKDyLUZDlDgEAAAAAAACgBDCCEgAAAAAAAIBpCCgBAAAAAAAAmIaAEgAAAAAAAIBpCCgBAAAAAAAAmIaAEgAAAAAAAIBpCCgBAAAAAAAAmIaAEgAAAAAAAIBpCCgBAAAAAAAAmIaAEgAAAAAAAIBpCCgBAAAAAAAAmIaAEgAAAAAAAIBpCCgBAAAAAAAAmIaAEgAAAAAAAIBp/j+Hp5GUT99Y/QAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 1600x900 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from scipy.cluster import hierarchy\n", "from matplotlib import pyplot as plt\n", "\n", "def draw_dendrogram(data, featureNames):\n", "    model = hierarchy.linkage(data, \"ward\")\n", "    # print(c)\n", "    plt.figure(figsize=(16, 9))\n", "    plt.title(\"地区之间的年龄结构层次聚类图\")\n", "    plt.xlabel(\"distance\",font1)\n", "    plt.ylabel(\"keyword\")\n", "    hierarchy.dendrogram(\n", "        model,\n", "        leaf_rotation=0,\n", "        leaf_font_size=8.0,\n", "        labels=featureNames,\n", "        orientation=\"right\",\n", "    )\n", "    plt.show()\n", "    return model\n", "\n", "model = draw_dendrogram(df[[\"a14-\",\"a15_59\",\"a60+\"]].values.tolist(),df.index.values.tolist())"]}, {"cell_type": "markdown", "metadata": {"id": "98A80829CC2D450988780270A0A3F698", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["上图中，在谱系树中越接近，则二者之间的人口年龄结构接近。  \n", "\n", "基于谱系树的第三层级，可以将各地区分为三组。"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "A704F1914F76466685634029F8D341D2", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["from scipy.cluster import hierarchy\n", "\n", "gdf = df.assign(\n", "    group = hierarchy.cut_tree(model,3)+1\n", ").sort_values(\"group\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"scrolled": true}, "outputs": [], "source": ["dictcode = {'北京': '北京市',\n", " '天津': '天津市',\n", " '河北': '河北省',\n", " '山西': '山西省',\n", " '内蒙古': '内蒙古自治区',\n", " '辽宁': '辽宁省',\n", " '吉林': '吉林省',\n", " '黑龙江': '黑龙江省',\n", " '上海': '上海市',\n", " '江苏': '江苏省',\n", " '浙江': '浙江省',\n", " '安徽': '安徽省',\n", " '福建': '福建省',\n", " '江西': '江西省',\n", " '山东': '山东省',\n", " '河南': '河南省',\n", " '湖北': '湖北省',\n", " '湖南': '湖南省',\n", " '广东': '广东省',\n", " '广西': '广西壮族自治区',\n", " '海南': '海南省',\n", " '重庆': '重庆市',\n", " '四川': '四川省',\n", " '贵州': '贵州省',\n", " '云南': '云南省',\n", " '西藏': '西藏自治区',\n", " '陕西': '陕西省',\n", " '甘肃': '甘肃省',\n", " '青海': '青海省',\n", " '宁夏': '宁夏回族自治区',\n", " '新疆': '新疆维吾尔自治区'}"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["['北京市',\n", " '天津市',\n", " '内蒙古自治区',\n", " '辽宁省',\n", " '吉林省',\n", " '黑龙江省',\n", " '上海市',\n", " '浙江省',\n", " '陕西省',\n", " '贵州省',\n", " '四川省',\n", " '重庆市',\n", " '广西壮族自治区',\n", " '湖南省',\n", " '湖北省',\n", " '河南省',\n", " '山东省',\n", " '江西省',\n", " '安徽省',\n", " '江苏省',\n", " '山西省',\n", " '河北省',\n", " '广东省',\n", " '福建省',\n", " '海南省',\n", " '云南省',\n", " '西藏自治区',\n", " '甘肃省',\n", " '青海省',\n", " '宁夏回族自治区',\n", " '新疆维吾尔自治区']"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["[dictcode[x] for x in gdf.index.values]"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"id": "C8738D25B70142018B0F72A15E677F1E", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min', 'china':'https://assets.pyecharts.org/assets/v5/maps/china'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"0eb421e5e9a54d6cabb68dff072940b9\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts', 'china'], function(echarts) {\n", "                var chart_0eb421e5e9a54d6cabb68dff072940b9 = echarts.init(\n", "                    document.getElementById('0eb421e5e9a54d6cabb68dff072940b9'), 'white', {renderer: 'canvas'});\n", "                var option_0eb421e5e9a54d6cabb68dff072940b9 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"map\",\n", "            \"name\": \"\\u5206\\u7ec4\",\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"margin\": 8\n", "            },\n", "            \"map\": \"china\",\n", "            \"data\": [\n", "                {\n", "                    \"name\": \"\\u5317\\u4eac\\u5e02\",\n", "                    \"value\": 1.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u5929\\u6d25\\u5e02\",\n", "                    \"value\": 1.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u5185\\u8499\\u53e4\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": 1.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u8fbd\\u5b81\\u7701\",\n", "                    \"value\": 1.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u5409\\u6797\\u7701\",\n", "                    \"value\": 1.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u9ed1\\u9f99\\u6c5f\\u7701\",\n", "                    \"value\": 1.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e0a\\u6d77\\u5e02\",\n", "                    \"value\": 1.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u6d59\\u6c5f\\u7701\",\n", "                    \"value\": 1.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u9655\\u897f\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u8d35\\u5dde\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u56db\\u5ddd\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u91cd\\u5e86\\u5e02\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u5e7f\\u897f\\u58ee\\u65cf\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e56\\u5357\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e56\\u5317\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u6cb3\\u5357\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c71\\u4e1c\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c5f\\u897f\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u5b89\\u5fbd\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c5f\\u82cf\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c71\\u897f\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u6cb3\\u5317\\u7701\",\n", "                    \"value\": 2.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u5e7f\\u4e1c\\u7701\",\n", "                    \"value\": 3.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u798f\\u5efa\\u7701\",\n", "                    \"value\": 3.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u6d77\\u5357\\u7701\",\n", "                    \"value\": 3.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e91\\u5357\\u7701\",\n", "                    \"value\": 3.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u897f\\u85cf\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": 3.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u7518\\u8083\\u7701\",\n", "                    \"value\": 3.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u9752\\u6d77\\u7701\",\n", "                    \"value\": 3.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u5b81\\u590f\\u56de\\u65cf\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": 3.0\n", "                },\n", "                {\n", "                    \"name\": \"\\u65b0\\u7586\\u7ef4\\u543e\\u5c14\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": 3.0\n", "                }\n", "            ],\n", "            \"roam\": true,\n", "            \"aspectScale\": 0.75,\n", "            \"nameProperty\": \"name\",\n", "            \"selectedMode\": false,\n", "            \"zoom\": 1,\n", "            \"mapValueCalculation\": \"sum\",\n", "            \"showLegendSymbol\": true,\n", "            \"emphasis\": {}\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"\\u5206\\u7ec4\"\n", "            ],\n", "            \"selected\": {\n", "                \"\\u5206\\u7ec4\": true\n", "            },\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u4e2d\\u56fd\\u4eba\\u53e3\\u5e74\\u9f84\\u7ed3\\u6784\\u5206\\u7ec4\\u5730\\u56fe\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ],\n", "    \"visualMap\": {\n", "        \"show\": true,\n", "        \"type\": \"continuous\",\n", "        \"min\": 0,\n", "        \"max\": 3,\n", "        \"inRange\": {\n", "            \"color\": [\n", "                \"#50a3ba\",\n", "                \"#eac763\",\n", "                \"#d94e5d\"\n", "            ]\n", "        },\n", "        \"calculable\": true,\n", "        \"inverse\": false,\n", "        \"splitNumber\": 5,\n", "        \"hoverLink\": true,\n", "        \"orient\": \"vertical\",\n", "        \"padding\": 5,\n", "        \"showLabel\": true,\n", "        \"itemWidth\": 20,\n", "        \"itemHeight\": 140,\n", "        \"borderWidth\": 0\n", "    }\n", "};\n", "                chart_0eb421e5e9a54d6cabb68dff072940b9.setOption(option_0eb421e5e9a54d6cabb68dff072940b9);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x22d44bcfa90>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["from pyecharts import options as opts\n", "from pyecharts.charts import Map\n", "\n", "c = (\n", "    Map()\n", "    .add(\"分组\", [list(z) for z in zip([dictcode[x] for x in gdf.index.values], gdf[\"group\"].values * 1.0)], \"china\")\n", "    .set_global_opts(\n", "        title_opts=opts.TitleOpts(title=\"中国人口年龄结构分组地图\"),\n", "        visualmap_opts=opts.VisualMapOpts(max_=3),\n", "    )\n", ")\n", "c.render('中国人口年龄结构分组地图.html')\n", "c.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "FC922C6AFE9445DAB289BAB5CF23AF94", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["三组的地理分布情况  \n", "\n", "- 第1组：绿色  \n", "- 第2组：黄色  \n", "- 第3组：红色"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "E4A50AB87A484A7684E6A34D9FD97193", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<div id=\"altair-viz-352bbdbad22046cd8f1bcf47e894bfbb\"></div>\n", "<script type=\"text/javascript\">\n", "  var VEGA_DEBUG = (typeof VEGA_DEBUG == \"undefined\") ? {} : VEGA_DEBUG;\n", "  (function(spec, embedOpt){\n", "    let outputDiv = document.currentScript.previousElementSibling;\n", "    if (outputDiv.id !== \"altair-viz-352bbdbad22046cd8f1bcf47e894bfbb\") {\n", "      outputDiv = document.getElementById(\"altair-viz-352bbdbad22046cd8f1bcf47e894bfbb\");\n", "    }\n", "    const paths = {\n", "      \"vega\": \"https://cdn.jsdelivr.net/npm//vega@5?noext\",\n", "      \"vega-lib\": \"https://cdn.jsdelivr.net/npm//vega-lib?noext\",\n", "      \"vega-lite\": \"https://cdn.jsdelivr.net/npm//vega-lite@4.17.0?noext\",\n", "      \"vega-embed\": \"https://cdn.jsdelivr.net/npm//vega-embed@6?noext\",\n", "    };\n", "\n", "    function maybeLoadScript(lib, version) {\n", "      var key = `${lib.replace(\"-\", \"\")}_version`;\n", "      return (VEGA_DEBUG[key] == version) ?\n", "        Promise.resolve(paths[lib]) :\n", "        new Promise(function(resolve, reject) {\n", "          var s = document.createElement('script');\n", "          document.getElementsByTagName(\"head\")[0].appendChild(s);\n", "          s.async = true;\n", "          s.onload = () => {\n", "            VEGA_DEBUG[key] = version;\n", "            return resolve(paths[lib]);\n", "          };\n", "          s.onerror = () => reject(`Error loading script: ${paths[lib]}`);\n", "          s.src = paths[lib];\n", "        });\n", "    }\n", "\n", "    function showError(err) {\n", "      outputDiv.innerHTML = `<div class=\"error\" style=\"color:red;\">${err}</div>`;\n", "      throw err;\n", "    }\n", "\n", "    function displayChart(vegaEmbed) {\n", "      vegaEmbed(outputDiv, spec, embedOpt)\n", "        .catch(err => showError(`Javascript Error: ${err.message}<br>This usually means there's a typo in your chart specification. See the javascript console for the full traceback.`));\n", "    }\n", "\n", "    if(typeof define === \"function\" && define.amd) {\n", "      requirejs.config({paths});\n", "      require([\"vega-embed\"], displayChart, err => showError(`Error loading script: ${err.message}`));\n", "    } else {\n", "      maybeLoadScript(\"vega\", \"5\")\n", "        .then(() => maybeLoadScript(\"vega-lite\", \"4.17.0\"))\n", "        .then(() => maybeLoadScript(\"vega-embed\", \"6\"))\n", "        .catch(showError)\n", "        .then(() => displayChart(vegaEmbed));\n", "    }\n", "  })({\"config\": {\"view\": {\"continuousWidth\": 400, \"continuousHeight\": 300}}, \"data\": {\"name\": \"data-24e0339f6d49e3373926d91bc8086d3c\"}, \"facet\": {\"column\": {\"field\": \"group\", \"type\": \"quantitative\"}}, \"spec\": {\"mark\": \"bar\", \"encoding\": {\"tooltip\": [{\"field\": \"metric\", \"type\": \"ordinal\"}, {\"field\": \"value\", \"type\": \"quantitative\"}], \"x\": {\"field\": \"metric\", \"type\": \"nominal\"}, \"y\": {\"field\": \"value\", \"type\": \"quantitative\"}}, \"width\": 200}, \"$schema\": \"https://vega.github.io/schema/vega-lite/v4.17.0.json\", \"datasets\": {\"data-24e0339f6d49e3373926d91bc8086d3c\": [{\"group\": 1, \"metric\": \"a14\", \"value\": 0.12}, {\"group\": 1, \"metric\": \"a15_59\", \"value\": 0.66}, {\"group\": 1, \"metric\": \"a60\", \"value\": 0.22}, {\"group\": 2, \"metric\": \"a14\", \"value\": 0.19}, {\"group\": 2, \"metric\": \"a15_59\", \"value\": 0.62}, {\"group\": 2, \"metric\": \"a60\", \"value\": 0.19}, {\"group\": 3, \"metric\": \"a14\", \"value\": 0.21}, {\"group\": 3, \"metric\": \"a15_59\", \"value\": 0.66}, {\"group\": 3, \"metric\": \"a60\", \"value\": 0.13}]}}, {\"mode\": \"vega-lite\"});\n", "</script>"], "text/plain": ["alt.<PERSON><PERSON><PERSON><PERSON>(...)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "gdf_summary = gdf.groupby(\"group\").agg(\n", "    a14=(\"a14-\",\"mean\"),\n", "    a15_59=(\"a15_59\",\"mean\"),\n", "    a60=(\"a60+\",\"mean\"),\n", ").round(2)\n", "\n", "alt.Chart(gdf_summary.stack().reset_index().rename({\"level_1\":\"metric\",0:\"value\"},axis=1)).mark_bar().encode(\n", "    x=\"metric\",\n", "    y=\"value\",\n", "    tooltip=[\"metric:O\",\"value:Q\"]\n", ").properties(\n", "    width=200\n", ").facet(\n", "    column=\"group\"\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "69D81B3CA8B74F0CB6C700EB9113F5FF", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["上图是不同组的平均人口年龄结构，可以看到  \n", "\n", "- 第 1 组：人口年龄结构特点是**低高高**，大致是 1:7:2。既有北京、天津、上海、浙江等经济发达地区，也有东北三省和内蒙古。  \n", "- 第 2 组：人口年龄结构特点是**高低高**，大致是 2:6:2。以华北和华中为主的省份。  \n", "- 第 3 组：人口年龄结构特点特点是**高高低**，大致是 2:7:1。既有西藏、新疆、青海、甘肃、云南等西部省份，也有广东、福建、海南等东南部省份。  \n", "\n", "虽然被归为同一个组，但是同组的不同地区的经济发展水平、自然地理环境都有着较大的差异。下面探索与人口年龄结构差异有关的影响因素。"]}, {"cell_type": "markdown", "metadata": {"id": "3B8EAC11AF694FD6867ABDFFCAE44B05", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 对于人口年龄结构的影响因素可能有哪些？  \n", "通过相关性来观察"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"id": "5CE4E5A204FE41D39BB8D38BD762A3E0", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_1d825_row0_col0, #T_1d825_row1_col1, #T_1d825_row2_col2, #T_1d825_row3_col3, #T_1d825_row4_col4, #T_1d825_row5_col5, #T_1d825_row6_col6, #T_1d825_row7_col7, #T_1d825_row8_col8, #T_1d825_row9_col9, #T_1d825_row10_col10, #T_1d825_row11_col11, #T_1d825_row12_col12, #T_1d825_row13_col13, #T_1d825_row14_col14, #T_1d825_row15_col15, #T_1d825_row16_col16, #T_1d825_row17_col17, #T_1d825_row17_col18, #T_1d825_row18_col17, #T_1d825_row18_col18, #T_1d825_row19_col19, #T_1d825_row20_col20, #T_1d825_row21_col21, #T_1d825_row22_col22 {\n", "  background-color: #800026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row0_col1, #T_1d825_row0_col14, #T_1d825_row9_col22 {\n", "  background-color: #fffecb;\n", "  color: #000000;\n", "}\n", "#T_1d825_row0_col2, #T_1d825_row0_col3, #T_1d825_row0_col15, #T_1d825_row0_col16, #T_1d825_row0_col22, #T_1d825_row4_col1, #T_1d825_row9_col6, #T_1d825_row9_col7, #T_1d825_row9_col10, #T_1d825_row9_col11, #T_1d825_row9_col12, #T_1d825_row9_col13, #T_1d825_row9_col14, #T_1d825_row9_col17, #T_1d825_row9_col19, #T_1d825_row9_col21, #T_1d825_row16_col0, #T_1d825_row18_col4, #T_1d825_row18_col8, #T_1d825_row18_col9, #T_1d825_row18_col20, #T_1d825_row20_col5, #T_1d825_row20_col18 {\n", "  background-color: #ffffcc;\n", "  color: #000000;\n", "}\n", "#T_1d825_row0_col4, #T_1d825_row8_col11 {\n", "  background-color: #fd8f3d;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row0_col5 {\n", "  background-color: #ffeea3;\n", "  color: #000000;\n", "}\n", "#T_1d825_row0_col6, #T_1d825_row9_col5 {\n", "  background-color: #fff5b3;\n", "  color: #000000;\n", "}\n", "#T_1d825_row0_col7, #T_1d825_row4_col18 {\n", "  background-color: #fff1ab;\n", "  color: #000000;\n", "}\n", "#T_1d825_row0_col8, #T_1d825_row0_col20, #T_1d825_row7_col1, #T_1d825_row10_col5 {\n", "  background-color: #febb56;\n", "  color: #000000;\n", "}\n", "#T_1d825_row0_col9, #T_1d825_row1_col22, #T_1d825_row7_col10 {\n", "  background-color: #cf0c21;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row0_col10, #T_1d825_row10_col9, #T_1d825_row12_col9, #T_1d825_row13_col9 {\n", "  background-color: #fffcc4;\n", "  color: #000000;\n", "}\n", "#T_1d825_row0_col11, #T_1d825_row0_col12 {\n", "  background-color: #fffac0;\n", "  color: #000000;\n", "}\n", "#T_1d825_row0_col13, #T_1d825_row2_col0, #T_1d825_row8_col1, #T_1d825_row9_col15, #T_1d825_row11_col0, #T_1d825_row11_col9 {\n", "  background-color: #fffcc5;\n", "  color: #000000;\n", "}\n", "#T_1d825_row0_col17 {\n", "  background-color: #fee289;\n", "  color: #000000;\n", "}\n", "#T_1d825_row0_col18, #T_1d825_row8_col7, #T_1d825_row15_col21 {\n", "  background-color: #ec2c21;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row0_col19 {\n", "  background-color: #fff7b7;\n", "  color: #000000;\n", "}\n", "#T_1d825_row0_col21 {\n", "  background-color: #ffefa5;\n", "  color: #000000;\n", "}\n", "#T_1d825_row1_col0 {\n", "  background-color: #fedd7f;\n", "  color: #000000;\n", "}\n", "#T_1d825_row1_col2 {\n", "  background-color: #fecf6b;\n", "  color: #000000;\n", "}\n", "#T_1d825_row1_col3 {\n", "  background-color: #ffe793;\n", "  color: #000000;\n", "}\n", "#T_1d825_row1_col4, #T_1d825_row17_col0, #T_1d825_row18_col3 {\n", "  background-color: #fee084;\n", "  color: #000000;\n", "}\n", "#T_1d825_row1_col5, #T_1d825_row19_col9, #T_1d825_row21_col9 {\n", "  background-color: #ffec9d;\n", "  color: #000000;\n", "}\n", "#T_1d825_row1_col6, #T_1d825_row2_col11, #T_1d825_row7_col19, #T_1d825_row12_col2 {\n", "  background-color: #ea2920;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row1_col7, #T_1d825_row20_col22 {\n", "  background-color: #fd913e;\n", "  color: #000000;\n", "}\n", "#T_1d825_row1_col8, #T_1d825_row3_col9 {\n", "  background-color: #fed36f;\n", "  color: #000000;\n", "}\n", "#T_1d825_row1_col9, #T_1d825_row3_col18, #T_1d825_row20_col4 {\n", "  background-color: #fec05b;\n", "  color: #000000;\n", "}\n", "#T_1d825_row1_col10, #T_1d825_row1_col16, #T_1d825_row1_col17, #T_1d825_row2_col8, #T_1d825_row3_col7, #T_1d825_row7_col17 {\n", "  background-color: #e41c1d;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row1_col11, #T_1d825_row7_col3, #T_1d825_row15_col6 {\n", "  background-color: #e8241f;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row1_col12, #T_1d825_row11_col2, #T_1d825_row15_col17, #T_1d825_row16_col2 {\n", "  background-color: #ed2e21;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row1_col13, #T_1d825_row8_col4, #T_1d825_row20_col8 {\n", "  background-color: #f84528;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row1_col14 {\n", "  background-color: #fc572c;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row1_col15 {\n", "  background-color: #e6211e;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row1_col18, #T_1d825_row4_col2, #T_1d825_row13_col1 {\n", "  background-color: #fd7a37;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row1_col19 {\n", "  background-color: #fc512b;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row1_col20, #T_1d825_row14_col1 {\n", "  background-color: #fd8e3c;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row1_col21, #T_1d825_row2_col13 {\n", "  background-color: #dc151e;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col1, #T_1d825_row15_col9 {\n", "  background-color: #fff1a9;\n", "  color: #000000;\n", "}\n", "#T_1d825_row2_col3, #T_1d825_row3_col2 {\n", "  background-color: #880026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col4, #T_1d825_row3_col19, #T_1d825_row5_col2, #T_1d825_row8_col13 {\n", "  background-color: #fd7836;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col5, #T_1d825_row3_col21, #T_1d825_row21_col4 {\n", "  background-color: #feb651;\n", "  color: #000000;\n", "}\n", "#T_1d825_row2_col6 {\n", "  background-color: #fb4b29;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col7, #T_1d825_row7_col2 {\n", "  background-color: #db141e;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col9 {\n", "  background-color: #fee187;\n", "  color: #000000;\n", "}\n", "#T_1d825_row2_col10, #T_1d825_row17_col15 {\n", "  background-color: #ee3122;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col12, #T_1d825_row3_col8, #T_1d825_row7_col8, #T_1d825_row21_col15 {\n", "  background-color: #e9261f;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col14, #T_1d825_row5_col18, #T_1d825_row14_col2 {\n", "  background-color: #d7121f;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col15, #T_1d825_row5_col7, #T_1d825_row8_col20 {\n", "  background-color: #f54026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col16 {\n", "  background-color: #eb2b21;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col17, #T_1d825_row17_col2 {\n", "  background-color: #fea044;\n", "  color: #000000;\n", "}\n", "#T_1d825_row2_col18 {\n", "  background-color: #fed977;\n", "  color: #000000;\n", "}\n", "#T_1d825_row2_col19, #T_1d825_row4_col3, #T_1d825_row5_col8, #T_1d825_row7_col20, #T_1d825_row22_col20 {\n", "  background-color: #fc6c33;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col20, #T_1d825_row6_col1, #T_1d825_row7_col21, #T_1d825_row11_col1 {\n", "  background-color: #f94828;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row2_col21, #T_1d825_row4_col14 {\n", "  background-color: #fea747;\n", "  color: #000000;\n", "}\n", "#T_1d825_row2_col22, #T_1d825_row16_col20 {\n", "  background-color: #fd7c37;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row3_col0, #T_1d825_row22_col0 {\n", "  background-color: #fff4b2;\n", "  color: #000000;\n", "}\n", "#T_1d825_row3_col1 {\n", "  background-color: #fffdc8;\n", "  color: #000000;\n", "}\n", "#T_1d825_row3_col4, #T_1d825_row10_col3, #T_1d825_row15_col8, #T_1d825_row16_col3 {\n", "  background-color: #fc5f2f;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row3_col5, #T_1d825_row4_col11, #T_1d825_row16_col5 {\n", "  background-color: #fea848;\n", "  color: #000000;\n", "}\n", "#T_1d825_row3_col6, #T_1d825_row3_col15, #T_1d825_row5_col13, #T_1d825_row7_col4, #T_1d825_row15_col20 {\n", "  background-color: #fc6832;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row3_col10, #T_1d825_row6_col2 {\n", "  background-color: #fc4d2a;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row3_col11 {\n", "  background-color: #f74327;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row3_col12, #T_1d825_row4_col8, #T_1d825_row10_col1, #T_1d825_row15_col2 {\n", "  background-color: #f43d25;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row3_col13, #T_1d825_row6_col15, #T_1d825_row7_col22, #T_1d825_row14_col3 {\n", "  background-color: #e92720;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row3_col14 {\n", "  background-color: #e51e1d;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row3_col16, #T_1d825_row3_col20 {\n", "  background-color: #fc4f2a;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row3_col17, #T_1d825_row4_col20 {\n", "  background-color: #feac49;\n", "  color: #000000;\n", "}\n", "#T_1d825_row3_col22, #T_1d825_row8_col10, #T_1d825_row22_col3 {\n", "  background-color: #fd953f;\n", "  color: #000000;\n", "}\n", "#T_1d825_row4_col0, #T_1d825_row5_col17, #T_1d825_row8_col12, #T_1d825_row13_col20 {\n", "  background-color: #fd8c3c;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row4_col5, #T_1d825_row6_col19, #T_1d825_row10_col21, #T_1d825_row11_col21, #T_1d825_row18_col22, #T_1d825_row19_col11 {\n", "  background-color: #b20026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row4_col6, #T_1d825_row6_col4, #T_1d825_row8_col22, #T_1d825_row20_col0, #T_1d825_row21_col8, #T_1d825_row22_col4 {\n", "  background-color: #fece6a;\n", "  color: #000000;\n", "}\n", "#T_1d825_row4_col7 {\n", "  background-color: #fc6631;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row4_col9, #T_1d825_row20_col14 {\n", "  background-color: #fd923e;\n", "  color: #000000;\n", "}\n", "#T_1d825_row4_col10, #T_1d825_row5_col9 {\n", "  background-color: #feb34d;\n", "  color: #000000;\n", "}\n", "#T_1d825_row4_col12, #T_1d825_row6_col20 {\n", "  background-color: #fea145;\n", "  color: #000000;\n", "}\n", "#T_1d825_row4_col13, #T_1d825_row20_col11 {\n", "  background-color: #fea546;\n", "  color: #000000;\n", "}\n", "#T_1d825_row4_col15, #T_1d825_row4_col17, #T_1d825_row14_col5, #T_1d825_row17_col4, #T_1d825_row20_col6, #T_1d825_row22_col8 {\n", "  background-color: #feb44e;\n", "  color: #000000;\n", "}\n", "#T_1d825_row4_col16, #T_1d825_row13_col4, #T_1d825_row14_col4, #T_1d825_row20_col10 {\n", "  background-color: #fea948;\n", "  color: #000000;\n", "}\n", "#T_1d825_row4_col19, #T_1d825_row12_col4 {\n", "  background-color: #fea647;\n", "  color: #000000;\n", "}\n", "#T_1d825_row4_col21, #T_1d825_row8_col6, #T_1d825_row20_col1 {\n", "  background-color: #feca66;\n", "  color: #000000;\n", "}\n", "#T_1d825_row4_col22 {\n", "  background-color: #fedb7a;\n", "  color: #000000;\n", "}\n", "#T_1d825_row5_col0 {\n", "  background-color: #feb04b;\n", "  color: #000000;\n", "}\n", "#T_1d825_row5_col1, #T_1d825_row8_col19, #T_1d825_row17_col8 {\n", "  background-color: #fede80;\n", "  color: #000000;\n", "}\n", "#T_1d825_row5_col3, #T_1d825_row19_col3 {\n", "  background-color: #fd7234;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row5_col4, #T_1d825_row6_col11, #T_1d825_row11_col6, #T_1d825_row14_col17, #T_1d825_row17_col14, #T_1d825_row17_col21, #T_1d825_row22_col11 {\n", "  background-color: #a20026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row5_col6, #T_1d825_row21_col2 {\n", "  background-color: #fd9941;\n", "  color: #000000;\n", "}\n", "#T_1d825_row5_col10, #T_1d825_row6_col3 {\n", "  background-color: #fd7636;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row5_col11, #T_1d825_row14_col8 {\n", "  background-color: #fc6430;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row5_col12, #T_1d825_row12_col1 {\n", "  background-color: #fc552c;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row5_col14, #T_1d825_row8_col15, #T_1d825_row13_col8, #T_1d825_row15_col3, #T_1d825_row20_col3 {\n", "  background-color: #fd7034;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row5_col15, #T_1d825_row8_col14, #T_1d825_row19_col1, #T_1d825_row22_col2 {\n", "  background-color: #fd6e33;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row5_col16 {\n", "  background-color: #fc6330;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row5_col19 {\n", "  background-color: #f43e26;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row5_col20, #T_1d825_row8_col5, #T_1d825_row10_col4, #T_1d825_row18_col1 {\n", "  background-color: #feb953;\n", "  color: #000000;\n", "}\n", "#T_1d825_row5_col21, #T_1d825_row7_col5, #T_1d825_row20_col7 {\n", "  background-color: #fd8239;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row5_col22, #T_1d825_row10_col20 {\n", "  background-color: #fd9d43;\n", "  color: #000000;\n", "}\n", "#T_1d825_row6_col0 {\n", "  background-color: #fff3ae;\n", "  color: #000000;\n", "}\n", "#T_1d825_row6_col5 {\n", "  background-color: #feda78;\n", "  color: #000000;\n", "}\n", "#T_1d825_row6_col7, #T_1d825_row7_col6, #T_1d825_row15_col19, #T_1d825_row19_col7, #T_1d825_row21_col1, #T_1d825_row22_col7 {\n", "  background-color: #e61f1d;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row6_col8 {\n", "  background-color: #febe59;\n", "  color: #000000;\n", "}\n", "#T_1d825_row6_col9 {\n", "  background-color: #fff8ba;\n", "  color: #000000;\n", "}\n", "#T_1d825_row6_col10, #T_1d825_row6_col12, #T_1d825_row6_col18, #T_1d825_row11_col22, #T_1d825_row14_col16, #T_1d825_row16_col14, #T_1d825_row22_col12 {\n", "  background-color: #a40026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row6_col13, #T_1d825_row10_col22, #T_1d825_row19_col12, #T_1d825_row22_col18 {\n", "  background-color: #aa0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row6_col14, #T_1d825_row11_col19, #T_1d825_row22_col13 {\n", "  background-color: #b70026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row6_col16, #T_1d825_row15_col22 {\n", "  background-color: #d51020;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row6_col17, #T_1d825_row14_col6, #T_1d825_row15_col12, #T_1d825_row17_col6, #T_1d825_row19_col10 {\n", "  background-color: #b90026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row6_col21, #T_1d825_row12_col19, #T_1d825_row21_col12, #T_1d825_row22_col21 {\n", "  background-color: #b00026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row6_col22, #T_1d825_row16_col17, #T_1d825_row17_col16, #T_1d825_row19_col6, #T_1d825_row21_col22 {\n", "  background-color: #ae0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row7_col0 {\n", "  background-color: #fff0a8;\n", "  color: #000000;\n", "}\n", "#T_1d825_row7_col9, #T_1d825_row9_col16 {\n", "  background-color: #fff8bb;\n", "  color: #000000;\n", "}\n", "#T_1d825_row7_col11, #T_1d825_row19_col17 {\n", "  background-color: #c70723;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row7_col12, #T_1d825_row13_col19, #T_1d825_row15_col10, #T_1d825_row15_col14, #T_1d825_row17_col22, #T_1d825_row22_col14 {\n", "  background-color: #c20325;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row7_col13, #T_1d825_row15_col13 {\n", "  background-color: #be0126;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row7_col14, #T_1d825_row12_col15, #T_1d825_row13_col22, #T_1d825_row14_col7, #T_1d825_row19_col13 {\n", "  background-color: #bd0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row7_col15 {\n", "  background-color: #cb0a22;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row7_col16, #T_1d825_row9_col0 {\n", "  background-color: #d10e21;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row7_col18, #T_1d825_row10_col17 {\n", "  background-color: #9b0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row8_col0, #T_1d825_row8_col9 {\n", "  background-color: #fec561;\n", "  color: #000000;\n", "}\n", "#T_1d825_row8_col2 {\n", "  background-color: #e7231e;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row8_col3, #T_1d825_row17_col1 {\n", "  background-color: #f13624;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row8_col16, #T_1d825_row11_col8 {\n", "  background-color: #fd883b;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row8_col17, #T_1d825_row8_col21 {\n", "  background-color: #ffe691;\n", "  color: #000000;\n", "}\n", "#T_1d825_row8_col18, #T_1d825_row14_col9 {\n", "  background-color: #fff9be;\n", "  color: #000000;\n", "}\n", "#T_1d825_row9_col1 {\n", "  background-color: #fff0a7;\n", "  color: #000000;\n", "}\n", "#T_1d825_row9_col2, #T_1d825_row19_col0 {\n", "  background-color: #ffe997;\n", "  color: #000000;\n", "}\n", "#T_1d825_row9_col3 {\n", "  background-color: #ffe58f;\n", "  color: #000000;\n", "}\n", "#T_1d825_row9_col4, #T_1d825_row11_col20 {\n", "  background-color: #fd9a42;\n", "  color: #000000;\n", "}\n", "#T_1d825_row9_col8 {\n", "  background-color: #fec45f;\n", "  color: #000000;\n", "}\n", "#T_1d825_row9_col18, #T_1d825_row15_col0 {\n", "  background-color: #fff9bd;\n", "  color: #000000;\n", "}\n", "#T_1d825_row9_col20, #T_1d825_row20_col15 {\n", "  background-color: #fd863a;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row10_col0 {\n", "  background-color: #fffdc6;\n", "  color: #000000;\n", "}\n", "#T_1d825_row10_col2 {\n", "  background-color: #f03523;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row10_col6, #T_1d825_row12_col6, #T_1d825_row18_col19, #T_1d825_row22_col10 {\n", "  background-color: #a60026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row10_col7 {\n", "  background-color: #d00d21;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row10_col8, #T_1d825_row20_col9, #T_1d825_row20_col16 {\n", "  background-color: #fd903d;\n", "  color: #000000;\n", "}\n", "#T_1d825_row10_col11, #T_1d825_row11_col10, #T_1d825_row11_col12, #T_1d825_row12_col11, #T_1d825_row13_col14, #T_1d825_row13_col18, #T_1d825_row14_col13, #T_1d825_row18_col13 {\n", "  background-color: #820026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row10_col12, #T_1d825_row11_col13, #T_1d825_row12_col10, #T_1d825_row12_col17, #T_1d825_row12_col18, #T_1d825_row13_col11, #T_1d825_row17_col12, #T_1d825_row18_col12 {\n", "  background-color: #8a0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row10_col13, #T_1d825_row11_col14, #T_1d825_row11_col18, #T_1d825_row13_col10, #T_1d825_row14_col11, #T_1d825_row18_col11 {\n", "  background-color: #910026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row10_col14, #T_1d825_row11_col16, #T_1d825_row12_col16, #T_1d825_row14_col10, #T_1d825_row15_col16, #T_1d825_row16_col11, #T_1d825_row16_col12, #T_1d825_row18_col7 {\n", "  background-color: #9d0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row10_col15, #T_1d825_row14_col15, #T_1d825_row16_col22, #T_1d825_row19_col22 {\n", "  background-color: #c40524;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row10_col16, #T_1d825_row13_col16, #T_1d825_row16_col10, #T_1d825_row16_col13, #T_1d825_row16_col15, #T_1d825_row19_col18 {\n", "  background-color: #9f0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row10_col18, #T_1d825_row18_col21 {\n", "  background-color: #970026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row10_col19, #T_1d825_row11_col15, #T_1d825_row13_col7, #T_1d825_row22_col17 {\n", "  background-color: #c00225;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row11_col3 {\n", "  background-color: #fc532b;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row11_col4, #T_1d825_row15_col4 {\n", "  background-color: #feae4a;\n", "  color: #000000;\n", "}\n", "#T_1d825_row11_col5, #T_1d825_row16_col4 {\n", "  background-color: #fead4a;\n", "  color: #000000;\n", "}\n", "#T_1d825_row11_col7, #T_1d825_row19_col14 {\n", "  background-color: #c80723;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row11_col17, #T_1d825_row17_col11 {\n", "  background-color: #8f0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row12_col0 {\n", "  background-color: #fffbc2;\n", "  color: #000000;\n", "}\n", "#T_1d825_row12_col3 {\n", "  background-color: #fa4a29;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row12_col5 {\n", "  background-color: #fd9f44;\n", "  color: #000000;\n", "}\n", "#T_1d825_row12_col7 {\n", "  background-color: #c30424;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row12_col8 {\n", "  background-color: #fd8439;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row12_col13, #T_1d825_row13_col12 {\n", "  background-color: #840026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row12_col14 {\n", "  background-color: #8d0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row12_col20, #T_1d825_row19_col4 {\n", "  background-color: #fd9740;\n", "  color: #000000;\n", "}\n", "#T_1d825_row12_col21 {\n", "  background-color: #b60026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row12_col22, #T_1d825_row18_col6, #T_1d825_row19_col21, #T_1d825_row21_col19 {\n", "  background-color: #a80026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row13_col0, #T_1d825_row14_col0 {\n", "  background-color: #fffec9;\n", "  color: #000000;\n", "}\n", "#T_1d825_row13_col2 {\n", "  background-color: #df171d;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row13_col3 {\n", "  background-color: #ef3323;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row13_col5, #T_1d825_row21_col5 {\n", "  background-color: #feaf4b;\n", "  color: #000000;\n", "}\n", "#T_1d825_row13_col6, #T_1d825_row21_col6, #T_1d825_row21_col10, #T_1d825_row21_col11, #T_1d825_row22_col6 {\n", "  background-color: #ac0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row13_col15, #T_1d825_row22_col16 {\n", "  background-color: #c10325;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row13_col17, #T_1d825_row17_col13, #T_1d825_row18_col14 {\n", "  background-color: #950026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row13_col21, #T_1d825_row17_col19 {\n", "  background-color: #ca0923;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row14_col12 {\n", "  background-color: #8b0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row14_col18, #T_1d825_row21_col18 {\n", "  background-color: #930026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row14_col19 {\n", "  background-color: #cd0b22;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row14_col20, #T_1d825_row16_col8 {\n", "  background-color: #fd7e38;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row14_col21, #T_1d825_row16_col19, #T_1d825_row16_col21, #T_1d825_row22_col1 {\n", "  background-color: #da141e;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row14_col22, #T_1d825_row21_col13, #T_1d825_row22_col19 {\n", "  background-color: #c50624;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row15_col1, #T_1d825_row16_col1 {\n", "  background-color: #f23924;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row15_col5 {\n", "  background-color: #feab49;\n", "  color: #000000;\n", "}\n", "#T_1d825_row15_col7 {\n", "  background-color: #c90823;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row15_col11 {\n", "  background-color: #bb0026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row15_col18, #T_1d825_row16_col18, #T_1d825_row18_col15, #T_1d825_row18_col16 {\n", "  background-color: #000000;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row16_col6 {\n", "  background-color: #d6111f;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row16_col7, #T_1d825_row19_col16, #T_1d825_row21_col16, #T_1d825_row22_col15 {\n", "  background-color: #d30f20;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row16_col9 {\n", "  background-color: #fff3af;\n", "  color: #000000;\n", "}\n", "#T_1d825_row17_col3 {\n", "  background-color: #feb852;\n", "  color: #000000;\n", "}\n", "#T_1d825_row17_col5 {\n", "  background-color: #fec863;\n", "  color: #000000;\n", "}\n", "#T_1d825_row17_col7 {\n", "  background-color: #e31a1c;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row17_col9 {\n", "  background-color: #fff7b9;\n", "  color: #000000;\n", "}\n", "#T_1d825_row17_col10, #T_1d825_row18_col10 {\n", "  background-color: #990026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row17_col20, #T_1d825_row18_col2 {\n", "  background-color: #ffe794;\n", "  color: #000000;\n", "}\n", "#T_1d825_row18_col0 {\n", "  background-color: #f13824;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row18_col5, #T_1d825_row21_col7 {\n", "  background-color: #f33b25;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row19_col2, #T_1d825_row20_col2 {\n", "  background-color: #fc5b2e;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row19_col5 {\n", "  background-color: #fc6a32;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row19_col8 {\n", "  background-color: #fec15d;\n", "  color: #000000;\n", "}\n", "#T_1d825_row19_col15 {\n", "  background-color: #e2191c;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row19_col20 {\n", "  background-color: #febf5a;\n", "  color: #000000;\n", "}\n", "#T_1d825_row20_col12 {\n", "  background-color: #fea245;\n", "  color: #000000;\n", "}\n", "#T_1d825_row20_col13 {\n", "  background-color: #fd9841;\n", "  color: #000000;\n", "}\n", "#T_1d825_row20_col17 {\n", "  background-color: #fff5b5;\n", "  color: #000000;\n", "}\n", "#T_1d825_row20_col19, #T_1d825_row21_col0 {\n", "  background-color: #fee288;\n", "  color: #000000;\n", "}\n", "#T_1d825_row20_col21 {\n", "  background-color: #ffeda0;\n", "  color: #000000;\n", "}\n", "#T_1d825_row21_col3 {\n", "  background-color: #feb24c;\n", "  color: #000000;\n", "}\n", "#T_1d825_row21_col14 {\n", "  background-color: #d41020;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row21_col17 {\n", "  background-color: #a10026;\n", "  color: #f1f1f1;\n", "}\n", "#T_1d825_row21_col20 {\n", "  background-color: #fed06c;\n", "  color: #000000;\n", "}\n", "#T_1d825_row22_col5 {\n", "  background-color: #fed16e;\n", "  color: #000000;\n", "}\n", "#T_1d825_row22_col9 {\n", "  background-color: #ffefa4;\n", "  color: #000000;\n", "}\n", "</style>\n", "<table id=\"T_1d825\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_1d825_level0_col0\" class=\"col_heading level0 col0\" >a14-</th>\n", "      <th id=\"T_1d825_level0_col1\" class=\"col_heading level0 col1\" >a15_59</th>\n", "      <th id=\"T_1d825_level0_col2\" class=\"col_heading level0 col2\" >a60+</th>\n", "      <th id=\"T_1d825_level0_col3\" class=\"col_heading level0 col3\" >a65+</th>\n", "      <th id=\"T_1d825_level0_col4\" class=\"col_heading level0 col4\" >population</th>\n", "      <th id=\"T_1d825_level0_col5\" class=\"col_heading level0 col5\" >gdp</th>\n", "      <th id=\"T_1d825_level0_col6\" class=\"col_heading level0 col6\" >edu_college</th>\n", "      <th id=\"T_1d825_level0_col7\" class=\"col_heading level0 col7\" >edu_senior</th>\n", "      <th id=\"T_1d825_level0_col8\" class=\"col_heading level0 col8\" >edu_junior</th>\n", "      <th id=\"T_1d825_level0_col9\" class=\"col_heading level0 col9\" >edu_primary</th>\n", "      <th id=\"T_1d825_level0_col10\" class=\"col_heading level0 col10\" >urba_2000</th>\n", "      <th id=\"T_1d825_level0_col11\" class=\"col_heading level0 col11\" >urba_2005</th>\n", "      <th id=\"T_1d825_level0_col12\" class=\"col_heading level0 col12\" >urba_2010</th>\n", "      <th id=\"T_1d825_level0_col13\" class=\"col_heading level0 col13\" >urba_2015</th>\n", "      <th id=\"T_1d825_level0_col14\" class=\"col_heading level0 col14\" >urba_2019</th>\n", "      <th id=\"T_1d825_level0_col15\" class=\"col_heading level0 col15\" >urba_over_40</th>\n", "      <th id=\"T_1d825_level0_col16\" class=\"col_heading level0 col16\" >urba_over_50</th>\n", "      <th id=\"T_1d825_level0_col17\" class=\"col_heading level0 col17\" >urba_over_60</th>\n", "      <th id=\"T_1d825_level0_col18\" class=\"col_heading level0 col18\" >urba_over_70</th>\n", "      <th id=\"T_1d825_level0_col19\" class=\"col_heading level0 col19\" >gdp_avg</th>\n", "      <th id=\"T_1d825_level0_col20\" class=\"col_heading level0 col20\" >floating_inside_rate</th>\n", "      <th id=\"T_1d825_level0_col21\" class=\"col_heading level0 col21\" >floating_outside_rate</th>\n", "      <th id=\"T_1d825_level0_col22\" class=\"col_heading level0 col22\" >floating_rate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row0\" class=\"row_heading level0 row0\" >a14-</th>\n", "      <td id=\"T_1d825_row0_col0\" class=\"data row0 col0\" >1.000000</td>\n", "      <td id=\"T_1d825_row0_col1\" class=\"data row0 col1\" >-0.400000</td>\n", "      <td id=\"T_1d825_row0_col2\" class=\"data row0 col2\" >-0.770000</td>\n", "      <td id=\"T_1d825_row0_col3\" class=\"data row0 col3\" >-0.670000</td>\n", "      <td id=\"T_1d825_row0_col4\" class=\"data row0 col4\" >0.100000</td>\n", "      <td id=\"T_1d825_row0_col5\" class=\"data row0 col5\" >-0.120000</td>\n", "      <td id=\"T_1d825_row0_col6\" class=\"data row0 col6\" >-0.650000</td>\n", "      <td id=\"T_1d825_row0_col7\" class=\"data row0 col7\" >-0.620000</td>\n", "      <td id=\"T_1d825_row0_col8\" class=\"data row0 col8\" >-0.240000</td>\n", "      <td id=\"T_1d825_row0_col9\" class=\"data row0 col9\" >0.650000</td>\n", "      <td id=\"T_1d825_row0_col10\" class=\"data row0 col10\" >-0.780000</td>\n", "      <td id=\"T_1d825_row0_col11\" class=\"data row0 col11\" >-0.770000</td>\n", "      <td id=\"T_1d825_row0_col12\" class=\"data row0 col12\" >-0.760000</td>\n", "      <td id=\"T_1d825_row0_col13\" class=\"data row0 col13\" >-0.790000</td>\n", "      <td id=\"T_1d825_row0_col14\" class=\"data row0 col14\" >-0.790000</td>\n", "      <td id=\"T_1d825_row0_col15\" class=\"data row0 col15\" >-0.730000</td>\n", "      <td id=\"T_1d825_row0_col16\" class=\"data row0 col16\" >-0.810000</td>\n", "      <td id=\"T_1d825_row0_col17\" class=\"data row0 col17\" >-0.430000</td>\n", "      <td id=\"T_1d825_row0_col18\" class=\"data row0 col18\" >0.420000</td>\n", "      <td id=\"T_1d825_row0_col19\" class=\"data row0 col19\" >-0.530000</td>\n", "      <td id=\"T_1d825_row0_col20\" class=\"data row0 col20\" >-0.290000</td>\n", "      <td id=\"T_1d825_row0_col21\" class=\"data row0 col21\" >-0.450000</td>\n", "      <td id=\"T_1d825_row0_col22\" class=\"data row0 col22\" >-0.670000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row1\" class=\"row_heading level0 row1\" >a15_59</th>\n", "      <td id=\"T_1d825_row1_col0\" class=\"data row1 col0\" >-0.400000</td>\n", "      <td id=\"T_1d825_row1_col1\" class=\"data row1 col1\" >1.000000</td>\n", "      <td id=\"T_1d825_row1_col2\" class=\"data row1 col2\" >-0.270000</td>\n", "      <td id=\"T_1d825_row1_col3\" class=\"data row1 col3\" >-0.390000</td>\n", "      <td id=\"T_1d825_row1_col4\" class=\"data row1 col4\" >-0.410000</td>\n", "      <td id=\"T_1d825_row1_col5\" class=\"data row1 col5\" >-0.100000</td>\n", "      <td id=\"T_1d825_row1_col6\" class=\"data row1 col6\" >0.490000</td>\n", "      <td id=\"T_1d825_row1_col7\" class=\"data row1 col7\" >0.080000</td>\n", "      <td id=\"T_1d825_row1_col8\" class=\"data row1 col8\" >-0.380000</td>\n", "      <td id=\"T_1d825_row1_col9\" class=\"data row1 col9\" >-0.260000</td>\n", "      <td id=\"T_1d825_row1_col10\" class=\"data row1 col10\" >0.530000</td>\n", "      <td id=\"T_1d825_row1_col11\" class=\"data row1 col11\" >0.490000</td>\n", "      <td id=\"T_1d825_row1_col12\" class=\"data row1 col12\" >0.450000</td>\n", "      <td id=\"T_1d825_row1_col13\" class=\"data row1 col13\" >0.350000</td>\n", "      <td id=\"T_1d825_row1_col14\" class=\"data row1 col14\" >0.290000</td>\n", "      <td id=\"T_1d825_row1_col15\" class=\"data row1 col15\" >0.540000</td>\n", "      <td id=\"T_1d825_row1_col16\" class=\"data row1 col16\" >0.540000</td>\n", "      <td id=\"T_1d825_row1_col17\" class=\"data row1 col17\" >0.550000</td>\n", "      <td id=\"T_1d825_row1_col18\" class=\"data row1 col18\" >0.090000</td>\n", "      <td id=\"T_1d825_row1_col19\" class=\"data row1 col19\" >0.380000</td>\n", "      <td id=\"T_1d825_row1_col20\" class=\"data row1 col20\" >0.010000</td>\n", "      <td id=\"T_1d825_row1_col21\" class=\"data row1 col21\" >0.630000</td>\n", "      <td id=\"T_1d825_row1_col22\" class=\"data row1 col22\" >0.690000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row2\" class=\"row_heading level0 row2\" >a60+</th>\n", "      <td id=\"T_1d825_row2_col0\" class=\"data row2 col0\" >-0.770000</td>\n", "      <td id=\"T_1d825_row2_col1\" class=\"data row2 col1\" >-0.270000</td>\n", "      <td id=\"T_1d825_row2_col2\" class=\"data row2 col2\" >1.000000</td>\n", "      <td id=\"T_1d825_row2_col3\" class=\"data row2 col3\" >0.970000</td>\n", "      <td id=\"T_1d825_row2_col4\" class=\"data row2 col4\" >0.180000</td>\n", "      <td id=\"T_1d825_row2_col5\" class=\"data row2 col5\" >0.190000</td>\n", "      <td id=\"T_1d825_row2_col6\" class=\"data row2 col6\" >0.340000</td>\n", "      <td id=\"T_1d825_row2_col7\" class=\"data row2 col7\" >0.600000</td>\n", "      <td id=\"T_1d825_row2_col8\" class=\"data row2 col8\" >0.520000</td>\n", "      <td id=\"T_1d825_row2_col9\" class=\"data row2 col9\" >-0.500000</td>\n", "      <td id=\"T_1d825_row2_col10\" class=\"data row2 col10\" >0.440000</td>\n", "      <td id=\"T_1d825_row2_col11\" class=\"data row2 col11\" >0.470000</td>\n", "      <td id=\"T_1d825_row2_col12\" class=\"data row2 col12\" >0.490000</td>\n", "      <td id=\"T_1d825_row2_col13\" class=\"data row2 col13\" >0.580000</td>\n", "      <td id=\"T_1d825_row2_col14\" class=\"data row2 col14\" >0.620000</td>\n", "      <td id=\"T_1d825_row2_col15\" class=\"data row2 col15\" >0.410000</td>\n", "      <td id=\"T_1d825_row2_col16\" class=\"data row2 col16\" >0.470000</td>\n", "      <td id=\"T_1d825_row2_col17\" class=\"data row2 col17\" >0.000000</td>\n", "      <td id=\"T_1d825_row2_col18\" class=\"data row2 col18\" >-0.480000</td>\n", "      <td id=\"T_1d825_row2_col19\" class=\"data row2 col19\" >0.290000</td>\n", "      <td id=\"T_1d825_row2_col20\" class=\"data row2 col20\" >0.290000</td>\n", "      <td id=\"T_1d825_row2_col21\" class=\"data row2 col21\" >0.040000</td>\n", "      <td id=\"T_1d825_row2_col22\" class=\"data row2 col22\" >0.220000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row3\" class=\"row_heading level0 row3\" >a65+</th>\n", "      <td id=\"T_1d825_row3_col0\" class=\"data row3 col0\" >-0.670000</td>\n", "      <td id=\"T_1d825_row3_col1\" class=\"data row3 col1\" >-0.390000</td>\n", "      <td id=\"T_1d825_row3_col2\" class=\"data row3 col2\" >0.970000</td>\n", "      <td id=\"T_1d825_row3_col3\" class=\"data row3 col3\" >1.000000</td>\n", "      <td id=\"T_1d825_row3_col4\" class=\"data row3 col4\" >0.270000</td>\n", "      <td id=\"T_1d825_row3_col5\" class=\"data row3 col5\" >0.250000</td>\n", "      <td id=\"T_1d825_row3_col6\" class=\"data row3 col6\" >0.240000</td>\n", "      <td id=\"T_1d825_row3_col7\" class=\"data row3 col7\" >0.540000</td>\n", "      <td id=\"T_1d825_row3_col8\" class=\"data row3 col8\" >0.470000</td>\n", "      <td id=\"T_1d825_row3_col9\" class=\"data row3 col9\" >-0.370000</td>\n", "      <td id=\"T_1d825_row3_col10\" class=\"data row3 col10\" >0.320000</td>\n", "      <td id=\"T_1d825_row3_col11\" class=\"data row3 col11\" >0.360000</td>\n", "      <td id=\"T_1d825_row3_col12\" class=\"data row3 col12\" >0.390000</td>\n", "      <td id=\"T_1d825_row3_col13\" class=\"data row3 col13\" >0.480000</td>\n", "      <td id=\"T_1d825_row3_col14\" class=\"data row3 col14\" >0.530000</td>\n", "      <td id=\"T_1d825_row3_col15\" class=\"data row3 col15\" >0.260000</td>\n", "      <td id=\"T_1d825_row3_col16\" class=\"data row3 col16\" >0.320000</td>\n", "      <td id=\"T_1d825_row3_col17\" class=\"data row3 col17\" >-0.070000</td>\n", "      <td id=\"T_1d825_row3_col18\" class=\"data row3 col18\" >-0.320000</td>\n", "      <td id=\"T_1d825_row3_col19\" class=\"data row3 col19\" >0.250000</td>\n", "      <td id=\"T_1d825_row3_col20\" class=\"data row3 col20\" >0.260000</td>\n", "      <td id=\"T_1d825_row3_col21\" class=\"data row3 col21\" >-0.040000</td>\n", "      <td id=\"T_1d825_row3_col22\" class=\"data row3 col22\" >0.120000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row4\" class=\"row_heading level0 row4\" >population</th>\n", "      <td id=\"T_1d825_row4_col0\" class=\"data row4 col0\" >0.100000</td>\n", "      <td id=\"T_1d825_row4_col1\" class=\"data row4 col1\" >-0.410000</td>\n", "      <td id=\"T_1d825_row4_col2\" class=\"data row4 col2\" >0.180000</td>\n", "      <td id=\"T_1d825_row4_col3\" class=\"data row4 col3\" >0.270000</td>\n", "      <td id=\"T_1d825_row4_col4\" class=\"data row4 col4\" >1.000000</td>\n", "      <td id=\"T_1d825_row4_col5\" class=\"data row4 col5\" >0.870000</td>\n", "      <td id=\"T_1d825_row4_col6\" class=\"data row4 col6\" >-0.270000</td>\n", "      <td id=\"T_1d825_row4_col7\" class=\"data row4 col7\" >0.240000</td>\n", "      <td id=\"T_1d825_row4_col8\" class=\"data row4 col8\" >0.370000</td>\n", "      <td id=\"T_1d825_row4_col9\" class=\"data row4 col9\" >0.030000</td>\n", "      <td id=\"T_1d825_row4_col10\" class=\"data row4 col10\" >-0.150000</td>\n", "      <td id=\"T_1d825_row4_col11\" class=\"data row4 col11\" >-0.090000</td>\n", "      <td id=\"T_1d825_row4_col12\" class=\"data row4 col12\" >-0.040000</td>\n", "      <td id=\"T_1d825_row4_col13\" class=\"data row4 col13\" >-0.060000</td>\n", "      <td id=\"T_1d825_row4_col14\" class=\"data row4 col14\" >-0.060000</td>\n", "      <td id=\"T_1d825_row4_col15\" class=\"data row4 col15\" >-0.090000</td>\n", "      <td id=\"T_1d825_row4_col16\" class=\"data row4 col16\" >-0.080000</td>\n", "      <td id=\"T_1d825_row4_col17\" class=\"data row4 col17\" >-0.120000</td>\n", "      <td id=\"T_1d825_row4_col18\" class=\"data row4 col18\" >-0.780000</td>\n", "      <td id=\"T_1d825_row4_col19\" class=\"data row4 col19\" >0.050000</td>\n", "      <td id=\"T_1d825_row4_col20\" class=\"data row4 col20\" >-0.190000</td>\n", "      <td id=\"T_1d825_row4_col21\" class=\"data row4 col21\" >-0.140000</td>\n", "      <td id=\"T_1d825_row4_col22\" class=\"data row4 col22\" >-0.270000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row5\" class=\"row_heading level0 row5\" >gdp</th>\n", "      <td id=\"T_1d825_row5_col0\" class=\"data row5 col0\" >-0.120000</td>\n", "      <td id=\"T_1d825_row5_col1\" class=\"data row5 col1\" >-0.100000</td>\n", "      <td id=\"T_1d825_row5_col2\" class=\"data row5 col2\" >0.190000</td>\n", "      <td id=\"T_1d825_row5_col3\" class=\"data row5 col3\" >0.250000</td>\n", "      <td id=\"T_1d825_row5_col4\" class=\"data row5 col4\" >0.870000</td>\n", "      <td id=\"T_1d825_row5_col5\" class=\"data row5 col5\" >1.000000</td>\n", "      <td id=\"T_1d825_row5_col6\" class=\"data row5 col6\" >0.040000</td>\n", "      <td id=\"T_1d825_row5_col7\" class=\"data row5 col7\" >0.390000</td>\n", "      <td id=\"T_1d825_row5_col8\" class=\"data row5 col8\" >0.180000</td>\n", "      <td id=\"T_1d825_row5_col9\" class=\"data row5 col9\" >-0.180000</td>\n", "      <td id=\"T_1d825_row5_col10\" class=\"data row5 col10\" >0.170000</td>\n", "      <td id=\"T_1d825_row5_col11\" class=\"data row5 col11\" >0.230000</td>\n", "      <td id=\"T_1d825_row5_col12\" class=\"data row5 col12\" >0.290000</td>\n", "      <td id=\"T_1d825_row5_col13\" class=\"data row5 col13\" >0.220000</td>\n", "      <td id=\"T_1d825_row5_col14\" class=\"data row5 col14\" >0.200000</td>\n", "      <td id=\"T_1d825_row5_col15\" class=\"data row5 col15\" >0.240000</td>\n", "      <td id=\"T_1d825_row5_col16\" class=\"data row5 col16\" >0.250000</td>\n", "      <td id=\"T_1d825_row5_col17\" class=\"data row5 col17\" >0.120000</td>\n", "      <td id=\"T_1d825_row5_col18\" class=\"data row5 col18\" >0.580000</td>\n", "      <td id=\"T_1d825_row5_col19\" class=\"data row5 col19\" >0.450000</td>\n", "      <td id=\"T_1d825_row5_col20\" class=\"data row5 col20\" >-0.270000</td>\n", "      <td id=\"T_1d825_row5_col21\" class=\"data row5 col21\" >0.220000</td>\n", "      <td id=\"T_1d825_row5_col22\" class=\"data row5 col22\" >0.080000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row6\" class=\"row_heading level0 row6\" >edu_college</th>\n", "      <td id=\"T_1d825_row6_col0\" class=\"data row6 col0\" >-0.650000</td>\n", "      <td id=\"T_1d825_row6_col1\" class=\"data row6 col1\" >0.490000</td>\n", "      <td id=\"T_1d825_row6_col2\" class=\"data row6 col2\" >0.340000</td>\n", "      <td id=\"T_1d825_row6_col3\" class=\"data row6 col3\" >0.240000</td>\n", "      <td id=\"T_1d825_row6_col4\" class=\"data row6 col4\" >-0.270000</td>\n", "      <td id=\"T_1d825_row6_col5\" class=\"data row6 col5\" >0.040000</td>\n", "      <td id=\"T_1d825_row6_col6\" class=\"data row6 col6\" >1.000000</td>\n", "      <td id=\"T_1d825_row6_col7\" class=\"data row6 col7\" >0.530000</td>\n", "      <td id=\"T_1d825_row6_col8\" class=\"data row6 col8\" >-0.250000</td>\n", "      <td id=\"T_1d825_row6_col9\" class=\"data row6 col9\" >-0.780000</td>\n", "      <td id=\"T_1d825_row6_col10\" class=\"data row6 col10\" >0.860000</td>\n", "      <td id=\"T_1d825_row6_col11\" class=\"data row6 col11\" >0.870000</td>\n", "      <td id=\"T_1d825_row6_col12\" class=\"data row6 col12\" >0.860000</td>\n", "      <td id=\"T_1d825_row6_col13\" class=\"data row6 col13\" >0.840000</td>\n", "      <td id=\"T_1d825_row6_col14\" class=\"data row6 col14\" >0.790000</td>\n", "      <td id=\"T_1d825_row6_col15\" class=\"data row6 col15\" >0.510000</td>\n", "      <td id=\"T_1d825_row6_col16\" class=\"data row6 col16\" >0.630000</td>\n", "      <td id=\"T_1d825_row6_col17\" class=\"data row6 col17\" >0.790000</td>\n", "      <td id=\"T_1d825_row6_col18\" class=\"data row6 col18\" >0.850000</td>\n", "      <td id=\"T_1d825_row6_col19\" class=\"data row6 col19\" >0.830000</td>\n", "      <td id=\"T_1d825_row6_col20\" class=\"data row6 col20\" >-0.120000</td>\n", "      <td id=\"T_1d825_row6_col21\" class=\"data row6 col21\" >0.840000</td>\n", "      <td id=\"T_1d825_row6_col22\" class=\"data row6 col22\" >0.840000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row7\" class=\"row_heading level0 row7\" >edu_senior</th>\n", "      <td id=\"T_1d825_row7_col0\" class=\"data row7 col0\" >-0.620000</td>\n", "      <td id=\"T_1d825_row7_col1\" class=\"data row7 col1\" >0.080000</td>\n", "      <td id=\"T_1d825_row7_col2\" class=\"data row7 col2\" >0.600000</td>\n", "      <td id=\"T_1d825_row7_col3\" class=\"data row7 col3\" >0.540000</td>\n", "      <td id=\"T_1d825_row7_col4\" class=\"data row7 col4\" >0.240000</td>\n", "      <td id=\"T_1d825_row7_col5\" class=\"data row7 col5\" >0.390000</td>\n", "      <td id=\"T_1d825_row7_col6\" class=\"data row7 col6\" >0.530000</td>\n", "      <td id=\"T_1d825_row7_col7\" class=\"data row7 col7\" >1.000000</td>\n", "      <td id=\"T_1d825_row7_col8\" class=\"data row7 col8\" >0.470000</td>\n", "      <td id=\"T_1d825_row7_col9\" class=\"data row7 col9\" >-0.790000</td>\n", "      <td id=\"T_1d825_row7_col10\" class=\"data row7 col10\" >0.660000</td>\n", "      <td id=\"T_1d825_row7_col11\" class=\"data row7 col11\" >0.710000</td>\n", "      <td id=\"T_1d825_row7_col12\" class=\"data row7 col12\" >0.740000</td>\n", "      <td id=\"T_1d825_row7_col13\" class=\"data row7 col13\" >0.760000</td>\n", "      <td id=\"T_1d825_row7_col14\" class=\"data row7 col14\" >0.770000</td>\n", "      <td id=\"T_1d825_row7_col15\" class=\"data row7 col15\" >0.700000</td>\n", "      <td id=\"T_1d825_row7_col16\" class=\"data row7 col16\" >0.650000</td>\n", "      <td id=\"T_1d825_row7_col17\" class=\"data row7 col17\" >0.550000</td>\n", "      <td id=\"T_1d825_row7_col18\" class=\"data row7 col18\" >0.890000</td>\n", "      <td id=\"T_1d825_row7_col19\" class=\"data row7 col19\" >0.530000</td>\n", "      <td id=\"T_1d825_row7_col20\" class=\"data row7 col20\" >0.140000</td>\n", "      <td id=\"T_1d825_row7_col21\" class=\"data row7 col21\" >0.410000</td>\n", "      <td id=\"T_1d825_row7_col22\" class=\"data row7 col22\" >0.530000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row8\" class=\"row_heading level0 row8\" >edu_junior</th>\n", "      <td id=\"T_1d825_row8_col0\" class=\"data row8 col0\" >-0.240000</td>\n", "      <td id=\"T_1d825_row8_col1\" class=\"data row8 col1\" >-0.380000</td>\n", "      <td id=\"T_1d825_row8_col2\" class=\"data row8 col2\" >0.520000</td>\n", "      <td id=\"T_1d825_row8_col3\" class=\"data row8 col3\" >0.470000</td>\n", "      <td id=\"T_1d825_row8_col4\" class=\"data row8 col4\" >0.370000</td>\n", "      <td id=\"T_1d825_row8_col5\" class=\"data row8 col5\" >0.180000</td>\n", "      <td id=\"T_1d825_row8_col6\" class=\"data row8 col6\" >-0.250000</td>\n", "      <td id=\"T_1d825_row8_col7\" class=\"data row8 col7\" >0.470000</td>\n", "      <td id=\"T_1d825_row8_col8\" class=\"data row8 col8\" >1.000000</td>\n", "      <td id=\"T_1d825_row8_col9\" class=\"data row8 col9\" >-0.290000</td>\n", "      <td id=\"T_1d825_row8_col10\" class=\"data row8 col10\" >0.040000</td>\n", "      <td id=\"T_1d825_row8_col11\" class=\"data row8 col11\" >0.070000</td>\n", "      <td id=\"T_1d825_row8_col12\" class=\"data row8 col12\" >0.090000</td>\n", "      <td id=\"T_1d825_row8_col13\" class=\"data row8 col13\" >0.160000</td>\n", "      <td id=\"T_1d825_row8_col14\" class=\"data row8 col14\" >0.210000</td>\n", "      <td id=\"T_1d825_row8_col15\" class=\"data row8 col15\" >0.230000</td>\n", "      <td id=\"T_1d825_row8_col16\" class=\"data row8 col16\" >0.110000</td>\n", "      <td id=\"T_1d825_row8_col17\" class=\"data row8 col17\" >-0.470000</td>\n", "      <td id=\"T_1d825_row8_col18\" class=\"data row8 col18\" >-0.890000</td>\n", "      <td id=\"T_1d825_row8_col19\" class=\"data row8 col19\" >-0.270000</td>\n", "      <td id=\"T_1d825_row8_col20\" class=\"data row8 col20\" >0.330000</td>\n", "      <td id=\"T_1d825_row8_col21\" class=\"data row8 col21\" >-0.350000</td>\n", "      <td id=\"T_1d825_row8_col22\" class=\"data row8 col22\" >-0.190000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row9\" class=\"row_heading level0 row9\" >edu_primary</th>\n", "      <td id=\"T_1d825_row9_col0\" class=\"data row9 col0\" >0.650000</td>\n", "      <td id=\"T_1d825_row9_col1\" class=\"data row9 col1\" >-0.260000</td>\n", "      <td id=\"T_1d825_row9_col2\" class=\"data row9 col2\" >-0.500000</td>\n", "      <td id=\"T_1d825_row9_col3\" class=\"data row9 col3\" >-0.370000</td>\n", "      <td id=\"T_1d825_row9_col4\" class=\"data row9 col4\" >0.030000</td>\n", "      <td id=\"T_1d825_row9_col5\" class=\"data row9 col5\" >-0.180000</td>\n", "      <td id=\"T_1d825_row9_col6\" class=\"data row9 col6\" >-0.780000</td>\n", "      <td id=\"T_1d825_row9_col7\" class=\"data row9 col7\" >-0.790000</td>\n", "      <td id=\"T_1d825_row9_col8\" class=\"data row9 col8\" >-0.290000</td>\n", "      <td id=\"T_1d825_row9_col9\" class=\"data row9 col9\" >1.000000</td>\n", "      <td id=\"T_1d825_row9_col10\" class=\"data row9 col10\" >-0.830000</td>\n", "      <td id=\"T_1d825_row9_col11\" class=\"data row9 col11\" >-0.840000</td>\n", "      <td id=\"T_1d825_row9_col12\" class=\"data row9 col12\" >-0.830000</td>\n", "      <td id=\"T_1d825_row9_col13\" class=\"data row9 col13\" >-0.830000</td>\n", "      <td id=\"T_1d825_row9_col14\" class=\"data row9 col14\" >-0.800000</td>\n", "      <td id=\"T_1d825_row9_col15\" class=\"data row9 col15\" >-0.690000</td>\n", "      <td id=\"T_1d825_row9_col16\" class=\"data row9 col16\" >-0.720000</td>\n", "      <td id=\"T_1d825_row9_col17\" class=\"data row9 col17\" >-0.770000</td>\n", "      <td id=\"T_1d825_row9_col18\" class=\"data row9 col18\" >-0.880000</td>\n", "      <td id=\"T_1d825_row9_col19\" class=\"data row9 col19\" >-0.630000</td>\n", "      <td id=\"T_1d825_row9_col20\" class=\"data row9 col20\" >0.040000</td>\n", "      <td id=\"T_1d825_row9_col21\" class=\"data row9 col21\" >-0.630000</td>\n", "      <td id=\"T_1d825_row9_col22\" class=\"data row9 col22\" >-0.660000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row10\" class=\"row_heading level0 row10\" >urba_2000</th>\n", "      <td id=\"T_1d825_row10_col0\" class=\"data row10 col0\" >-0.780000</td>\n", "      <td id=\"T_1d825_row10_col1\" class=\"data row10 col1\" >0.530000</td>\n", "      <td id=\"T_1d825_row10_col2\" class=\"data row10 col2\" >0.440000</td>\n", "      <td id=\"T_1d825_row10_col3\" class=\"data row10 col3\" >0.320000</td>\n", "      <td id=\"T_1d825_row10_col4\" class=\"data row10 col4\" >-0.150000</td>\n", "      <td id=\"T_1d825_row10_col5\" class=\"data row10 col5\" >0.170000</td>\n", "      <td id=\"T_1d825_row10_col6\" class=\"data row10 col6\" >0.860000</td>\n", "      <td id=\"T_1d825_row10_col7\" class=\"data row10 col7\" >0.660000</td>\n", "      <td id=\"T_1d825_row10_col8\" class=\"data row10 col8\" >0.040000</td>\n", "      <td id=\"T_1d825_row10_col9\" class=\"data row10 col9\" >-0.830000</td>\n", "      <td id=\"T_1d825_row10_col10\" class=\"data row10 col10\" >1.000000</td>\n", "      <td id=\"T_1d825_row10_col11\" class=\"data row10 col11\" >0.990000</td>\n", "      <td id=\"T_1d825_row10_col12\" class=\"data row10 col12\" >0.960000</td>\n", "      <td id=\"T_1d825_row10_col13\" class=\"data row10 col13\" >0.930000</td>\n", "      <td id=\"T_1d825_row10_col14\" class=\"data row10 col14\" >0.890000</td>\n", "      <td id=\"T_1d825_row10_col15\" class=\"data row10 col15\" >0.740000</td>\n", "      <td id=\"T_1d825_row10_col16\" class=\"data row10 col16\" >0.880000</td>\n", "      <td id=\"T_1d825_row10_col17\" class=\"data row10 col17\" >0.900000</td>\n", "      <td id=\"T_1d825_row10_col18\" class=\"data row10 col18\" >0.900000</td>\n", "      <td id=\"T_1d825_row10_col19\" class=\"data row10 col19\" >0.780000</td>\n", "      <td id=\"T_1d825_row10_col20\" class=\"data row10 col20\" >-0.090000</td>\n", "      <td id=\"T_1d825_row10_col21\" class=\"data row10 col21\" >0.830000</td>\n", "      <td id=\"T_1d825_row10_col22\" class=\"data row10 col22\" >0.850000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row11\" class=\"row_heading level0 row11\" >urba_2005</th>\n", "      <td id=\"T_1d825_row11_col0\" class=\"data row11 col0\" >-0.770000</td>\n", "      <td id=\"T_1d825_row11_col1\" class=\"data row11 col1\" >0.490000</td>\n", "      <td id=\"T_1d825_row11_col2\" class=\"data row11 col2\" >0.470000</td>\n", "      <td id=\"T_1d825_row11_col3\" class=\"data row11 col3\" >0.360000</td>\n", "      <td id=\"T_1d825_row11_col4\" class=\"data row11 col4\" >-0.090000</td>\n", "      <td id=\"T_1d825_row11_col5\" class=\"data row11 col5\" >0.230000</td>\n", "      <td id=\"T_1d825_row11_col6\" class=\"data row11 col6\" >0.870000</td>\n", "      <td id=\"T_1d825_row11_col7\" class=\"data row11 col7\" >0.710000</td>\n", "      <td id=\"T_1d825_row11_col8\" class=\"data row11 col8\" >0.070000</td>\n", "      <td id=\"T_1d825_row11_col9\" class=\"data row11 col9\" >-0.840000</td>\n", "      <td id=\"T_1d825_row11_col10\" class=\"data row11 col10\" >0.990000</td>\n", "      <td id=\"T_1d825_row11_col11\" class=\"data row11 col11\" >1.000000</td>\n", "      <td id=\"T_1d825_row11_col12\" class=\"data row11 col12\" >0.990000</td>\n", "      <td id=\"T_1d825_row11_col13\" class=\"data row11 col13\" >0.960000</td>\n", "      <td id=\"T_1d825_row11_col14\" class=\"data row11 col14\" >0.930000</td>\n", "      <td id=\"T_1d825_row11_col15\" class=\"data row11 col15\" >0.770000</td>\n", "      <td id=\"T_1d825_row11_col16\" class=\"data row11 col16\" >0.890000</td>\n", "      <td id=\"T_1d825_row11_col17\" class=\"data row11 col17\" >0.940000</td>\n", "      <td id=\"T_1d825_row11_col18\" class=\"data row11 col18\" >0.930000</td>\n", "      <td id=\"T_1d825_row11_col19\" class=\"data row11 col19\" >0.810000</td>\n", "      <td id=\"T_1d825_row11_col20\" class=\"data row11 col20\" >-0.070000</td>\n", "      <td id=\"T_1d825_row11_col21\" class=\"data row11 col21\" >0.830000</td>\n", "      <td id=\"T_1d825_row11_col22\" class=\"data row11 col22\" >0.870000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row12\" class=\"row_heading level0 row12\" >urba_2010</th>\n", "      <td id=\"T_1d825_row12_col0\" class=\"data row12 col0\" >-0.760000</td>\n", "      <td id=\"T_1d825_row12_col1\" class=\"data row12 col1\" >0.450000</td>\n", "      <td id=\"T_1d825_row12_col2\" class=\"data row12 col2\" >0.490000</td>\n", "      <td id=\"T_1d825_row12_col3\" class=\"data row12 col3\" >0.390000</td>\n", "      <td id=\"T_1d825_row12_col4\" class=\"data row12 col4\" >-0.040000</td>\n", "      <td id=\"T_1d825_row12_col5\" class=\"data row12 col5\" >0.290000</td>\n", "      <td id=\"T_1d825_row12_col6\" class=\"data row12 col6\" >0.860000</td>\n", "      <td id=\"T_1d825_row12_col7\" class=\"data row12 col7\" >0.740000</td>\n", "      <td id=\"T_1d825_row12_col8\" class=\"data row12 col8\" >0.090000</td>\n", "      <td id=\"T_1d825_row12_col9\" class=\"data row12 col9\" >-0.830000</td>\n", "      <td id=\"T_1d825_row12_col10\" class=\"data row12 col10\" >0.960000</td>\n", "      <td id=\"T_1d825_row12_col11\" class=\"data row12 col11\" >0.990000</td>\n", "      <td id=\"T_1d825_row12_col12\" class=\"data row12 col12\" >1.000000</td>\n", "      <td id=\"T_1d825_row12_col13\" class=\"data row12 col13\" >0.980000</td>\n", "      <td id=\"T_1d825_row12_col14\" class=\"data row12 col14\" >0.950000</td>\n", "      <td id=\"T_1d825_row12_col15\" class=\"data row12 col15\" >0.780000</td>\n", "      <td id=\"T_1d825_row12_col16\" class=\"data row12 col16\" >0.890000</td>\n", "      <td id=\"T_1d825_row12_col17\" class=\"data row12 col17\" >0.960000</td>\n", "      <td id=\"T_1d825_row12_col18\" class=\"data row12 col18\" >0.960000</td>\n", "      <td id=\"T_1d825_row12_col19\" class=\"data row12 col19\" >0.840000</td>\n", "      <td id=\"T_1d825_row12_col20\" class=\"data row12 col20\" >-0.050000</td>\n", "      <td id=\"T_1d825_row12_col21\" class=\"data row12 col21\" >0.820000</td>\n", "      <td id=\"T_1d825_row12_col22\" class=\"data row12 col22\" >0.860000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row13\" class=\"row_heading level0 row13\" >urba_2015</th>\n", "      <td id=\"T_1d825_row13_col0\" class=\"data row13 col0\" >-0.790000</td>\n", "      <td id=\"T_1d825_row13_col1\" class=\"data row13 col1\" >0.350000</td>\n", "      <td id=\"T_1d825_row13_col2\" class=\"data row13 col2\" >0.580000</td>\n", "      <td id=\"T_1d825_row13_col3\" class=\"data row13 col3\" >0.480000</td>\n", "      <td id=\"T_1d825_row13_col4\" class=\"data row13 col4\" >-0.060000</td>\n", "      <td id=\"T_1d825_row13_col5\" class=\"data row13 col5\" >0.220000</td>\n", "      <td id=\"T_1d825_row13_col6\" class=\"data row13 col6\" >0.840000</td>\n", "      <td id=\"T_1d825_row13_col7\" class=\"data row13 col7\" >0.760000</td>\n", "      <td id=\"T_1d825_row13_col8\" class=\"data row13 col8\" >0.160000</td>\n", "      <td id=\"T_1d825_row13_col9\" class=\"data row13 col9\" >-0.830000</td>\n", "      <td id=\"T_1d825_row13_col10\" class=\"data row13 col10\" >0.930000</td>\n", "      <td id=\"T_1d825_row13_col11\" class=\"data row13 col11\" >0.960000</td>\n", "      <td id=\"T_1d825_row13_col12\" class=\"data row13 col12\" >0.980000</td>\n", "      <td id=\"T_1d825_row13_col13\" class=\"data row13 col13\" >1.000000</td>\n", "      <td id=\"T_1d825_row13_col14\" class=\"data row13 col14\" >0.990000</td>\n", "      <td id=\"T_1d825_row13_col15\" class=\"data row13 col15\" >0.760000</td>\n", "      <td id=\"T_1d825_row13_col16\" class=\"data row13 col16\" >0.880000</td>\n", "      <td id=\"T_1d825_row13_col17\" class=\"data row13 col17\" >0.920000</td>\n", "      <td id=\"T_1d825_row13_col18\" class=\"data row13 col18\" >0.990000</td>\n", "      <td id=\"T_1d825_row13_col19\" class=\"data row13 col19\" >0.770000</td>\n", "      <td id=\"T_1d825_row13_col20\" class=\"data row13 col20\" >0.020000</td>\n", "      <td id=\"T_1d825_row13_col21\" class=\"data row13 col21\" >0.720000</td>\n", "      <td id=\"T_1d825_row13_col22\" class=\"data row13 col22\" >0.790000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row14\" class=\"row_heading level0 row14\" >urba_2019</th>\n", "      <td id=\"T_1d825_row14_col0\" class=\"data row14 col0\" >-0.790000</td>\n", "      <td id=\"T_1d825_row14_col1\" class=\"data row14 col1\" >0.290000</td>\n", "      <td id=\"T_1d825_row14_col2\" class=\"data row14 col2\" >0.620000</td>\n", "      <td id=\"T_1d825_row14_col3\" class=\"data row14 col3\" >0.530000</td>\n", "      <td id=\"T_1d825_row14_col4\" class=\"data row14 col4\" >-0.060000</td>\n", "      <td id=\"T_1d825_row14_col5\" class=\"data row14 col5\" >0.200000</td>\n", "      <td id=\"T_1d825_row14_col6\" class=\"data row14 col6\" >0.790000</td>\n", "      <td id=\"T_1d825_row14_col7\" class=\"data row14 col7\" >0.770000</td>\n", "      <td id=\"T_1d825_row14_col8\" class=\"data row14 col8\" >0.210000</td>\n", "      <td id=\"T_1d825_row14_col9\" class=\"data row14 col9\" >-0.800000</td>\n", "      <td id=\"T_1d825_row14_col10\" class=\"data row14 col10\" >0.890000</td>\n", "      <td id=\"T_1d825_row14_col11\" class=\"data row14 col11\" >0.930000</td>\n", "      <td id=\"T_1d825_row14_col12\" class=\"data row14 col12\" >0.950000</td>\n", "      <td id=\"T_1d825_row14_col13\" class=\"data row14 col13\" >0.990000</td>\n", "      <td id=\"T_1d825_row14_col14\" class=\"data row14 col14\" >1.000000</td>\n", "      <td id=\"T_1d825_row14_col15\" class=\"data row14 col15\" >0.740000</td>\n", "      <td id=\"T_1d825_row14_col16\" class=\"data row14 col16\" >0.860000</td>\n", "      <td id=\"T_1d825_row14_col17\" class=\"data row14 col17\" >0.870000</td>\n", "      <td id=\"T_1d825_row14_col18\" class=\"data row14 col18\" >0.920000</td>\n", "      <td id=\"T_1d825_row14_col19\" class=\"data row14 col19\" >0.710000</td>\n", "      <td id=\"T_1d825_row14_col20\" class=\"data row14 col20\" >0.070000</td>\n", "      <td id=\"T_1d825_row14_col21\" class=\"data row14 col21\" >0.640000</td>\n", "      <td id=\"T_1d825_row14_col22\" class=\"data row14 col22\" >0.740000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row15\" class=\"row_heading level0 row15\" >urba_over_40</th>\n", "      <td id=\"T_1d825_row15_col0\" class=\"data row15 col0\" >-0.730000</td>\n", "      <td id=\"T_1d825_row15_col1\" class=\"data row15 col1\" >0.540000</td>\n", "      <td id=\"T_1d825_row15_col2\" class=\"data row15 col2\" >0.410000</td>\n", "      <td id=\"T_1d825_row15_col3\" class=\"data row15 col3\" >0.260000</td>\n", "      <td id=\"T_1d825_row15_col4\" class=\"data row15 col4\" >-0.090000</td>\n", "      <td id=\"T_1d825_row15_col5\" class=\"data row15 col5\" >0.240000</td>\n", "      <td id=\"T_1d825_row15_col6\" class=\"data row15 col6\" >0.510000</td>\n", "      <td id=\"T_1d825_row15_col7\" class=\"data row15 col7\" >0.700000</td>\n", "      <td id=\"T_1d825_row15_col8\" class=\"data row15 col8\" >0.230000</td>\n", "      <td id=\"T_1d825_row15_col9\" class=\"data row15 col9\" >-0.690000</td>\n", "      <td id=\"T_1d825_row15_col10\" class=\"data row15 col10\" >0.740000</td>\n", "      <td id=\"T_1d825_row15_col11\" class=\"data row15 col11\" >0.770000</td>\n", "      <td id=\"T_1d825_row15_col12\" class=\"data row15 col12\" >0.780000</td>\n", "      <td id=\"T_1d825_row15_col13\" class=\"data row15 col13\" >0.760000</td>\n", "      <td id=\"T_1d825_row15_col14\" class=\"data row15 col14\" >0.740000</td>\n", "      <td id=\"T_1d825_row15_col15\" class=\"data row15 col15\" >1.000000</td>\n", "      <td id=\"T_1d825_row15_col16\" class=\"data row15 col16\" >0.890000</td>\n", "      <td id=\"T_1d825_row15_col17\" class=\"data row15 col17\" >0.470000</td>\n", "      <td id=\"T_1d825_row15_col18\" class=\"data row15 col18\" >nan</td>\n", "      <td id=\"T_1d825_row15_col19\" class=\"data row15 col19\" >0.570000</td>\n", "      <td id=\"T_1d825_row15_col20\" class=\"data row15 col20\" >0.160000</td>\n", "      <td id=\"T_1d825_row15_col21\" class=\"data row15 col21\" >0.520000</td>\n", "      <td id=\"T_1d825_row15_col22\" class=\"data row15 col22\" >0.660000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row16\" class=\"row_heading level0 row16\" >urba_over_50</th>\n", "      <td id=\"T_1d825_row16_col0\" class=\"data row16 col0\" >-0.810000</td>\n", "      <td id=\"T_1d825_row16_col1\" class=\"data row16 col1\" >0.540000</td>\n", "      <td id=\"T_1d825_row16_col2\" class=\"data row16 col2\" >0.470000</td>\n", "      <td id=\"T_1d825_row16_col3\" class=\"data row16 col3\" >0.320000</td>\n", "      <td id=\"T_1d825_row16_col4\" class=\"data row16 col4\" >-0.080000</td>\n", "      <td id=\"T_1d825_row16_col5\" class=\"data row16 col5\" >0.250000</td>\n", "      <td id=\"T_1d825_row16_col6\" class=\"data row16 col6\" >0.630000</td>\n", "      <td id=\"T_1d825_row16_col7\" class=\"data row16 col7\" >0.650000</td>\n", "      <td id=\"T_1d825_row16_col8\" class=\"data row16 col8\" >0.110000</td>\n", "      <td id=\"T_1d825_row16_col9\" class=\"data row16 col9\" >-0.720000</td>\n", "      <td id=\"T_1d825_row16_col10\" class=\"data row16 col10\" >0.880000</td>\n", "      <td id=\"T_1d825_row16_col11\" class=\"data row16 col11\" >0.890000</td>\n", "      <td id=\"T_1d825_row16_col12\" class=\"data row16 col12\" >0.890000</td>\n", "      <td id=\"T_1d825_row16_col13\" class=\"data row16 col13\" >0.880000</td>\n", "      <td id=\"T_1d825_row16_col14\" class=\"data row16 col14\" >0.860000</td>\n", "      <td id=\"T_1d825_row16_col15\" class=\"data row16 col15\" >0.890000</td>\n", "      <td id=\"T_1d825_row16_col16\" class=\"data row16 col16\" >1.000000</td>\n", "      <td id=\"T_1d825_row16_col17\" class=\"data row16 col17\" >0.830000</td>\n", "      <td id=\"T_1d825_row16_col18\" class=\"data row16 col18\" >nan</td>\n", "      <td id=\"T_1d825_row16_col19\" class=\"data row16 col19\" >0.640000</td>\n", "      <td id=\"T_1d825_row16_col20\" class=\"data row16 col20\" >0.080000</td>\n", "      <td id=\"T_1d825_row16_col21\" class=\"data row16 col21\" >0.640000</td>\n", "      <td id=\"T_1d825_row16_col22\" class=\"data row16 col22\" >0.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row17\" class=\"row_heading level0 row17\" >urba_over_60</th>\n", "      <td id=\"T_1d825_row17_col0\" class=\"data row17 col0\" >-0.430000</td>\n", "      <td id=\"T_1d825_row17_col1\" class=\"data row17 col1\" >0.550000</td>\n", "      <td id=\"T_1d825_row17_col2\" class=\"data row17 col2\" >0.000000</td>\n", "      <td id=\"T_1d825_row17_col3\" class=\"data row17 col3\" >-0.070000</td>\n", "      <td id=\"T_1d825_row17_col4\" class=\"data row17 col4\" >-0.120000</td>\n", "      <td id=\"T_1d825_row17_col5\" class=\"data row17 col5\" >0.120000</td>\n", "      <td id=\"T_1d825_row17_col6\" class=\"data row17 col6\" >0.790000</td>\n", "      <td id=\"T_1d825_row17_col7\" class=\"data row17 col7\" >0.550000</td>\n", "      <td id=\"T_1d825_row17_col8\" class=\"data row17 col8\" >-0.470000</td>\n", "      <td id=\"T_1d825_row17_col9\" class=\"data row17 col9\" >-0.770000</td>\n", "      <td id=\"T_1d825_row17_col10\" class=\"data row17 col10\" >0.900000</td>\n", "      <td id=\"T_1d825_row17_col11\" class=\"data row17 col11\" >0.940000</td>\n", "      <td id=\"T_1d825_row17_col12\" class=\"data row17 col12\" >0.960000</td>\n", "      <td id=\"T_1d825_row17_col13\" class=\"data row17 col13\" >0.920000</td>\n", "      <td id=\"T_1d825_row17_col14\" class=\"data row17 col14\" >0.870000</td>\n", "      <td id=\"T_1d825_row17_col15\" class=\"data row17 col15\" >0.470000</td>\n", "      <td id=\"T_1d825_row17_col16\" class=\"data row17 col16\" >0.830000</td>\n", "      <td id=\"T_1d825_row17_col17\" class=\"data row17 col17\" >1.000000</td>\n", "      <td id=\"T_1d825_row17_col18\" class=\"data row17 col18\" >1.000000</td>\n", "      <td id=\"T_1d825_row17_col19\" class=\"data row17 col19\" >0.720000</td>\n", "      <td id=\"T_1d825_row17_col20\" class=\"data row17 col20\" >-0.650000</td>\n", "      <td id=\"T_1d825_row17_col21\" class=\"data row17 col21\" >0.880000</td>\n", "      <td id=\"T_1d825_row17_col22\" class=\"data row17 col22\" >0.760000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row18\" class=\"row_heading level0 row18\" >urba_over_70</th>\n", "      <td id=\"T_1d825_row18_col0\" class=\"data row18 col0\" >0.420000</td>\n", "      <td id=\"T_1d825_row18_col1\" class=\"data row18 col1\" >0.090000</td>\n", "      <td id=\"T_1d825_row18_col2\" class=\"data row18 col2\" >-0.480000</td>\n", "      <td id=\"T_1d825_row18_col3\" class=\"data row18 col3\" >-0.320000</td>\n", "      <td id=\"T_1d825_row18_col4\" class=\"data row18 col4\" >-0.780000</td>\n", "      <td id=\"T_1d825_row18_col5\" class=\"data row18 col5\" >0.580000</td>\n", "      <td id=\"T_1d825_row18_col6\" class=\"data row18 col6\" >0.850000</td>\n", "      <td id=\"T_1d825_row18_col7\" class=\"data row18 col7\" >0.890000</td>\n", "      <td id=\"T_1d825_row18_col8\" class=\"data row18 col8\" >-0.890000</td>\n", "      <td id=\"T_1d825_row18_col9\" class=\"data row18 col9\" >-0.880000</td>\n", "      <td id=\"T_1d825_row18_col10\" class=\"data row18 col10\" >0.900000</td>\n", "      <td id=\"T_1d825_row18_col11\" class=\"data row18 col11\" >0.930000</td>\n", "      <td id=\"T_1d825_row18_col12\" class=\"data row18 col12\" >0.960000</td>\n", "      <td id=\"T_1d825_row18_col13\" class=\"data row18 col13\" >0.990000</td>\n", "      <td id=\"T_1d825_row18_col14\" class=\"data row18 col14\" >0.920000</td>\n", "      <td id=\"T_1d825_row18_col15\" class=\"data row18 col15\" >nan</td>\n", "      <td id=\"T_1d825_row18_col16\" class=\"data row18 col16\" >nan</td>\n", "      <td id=\"T_1d825_row18_col17\" class=\"data row18 col17\" >1.000000</td>\n", "      <td id=\"T_1d825_row18_col18\" class=\"data row18 col18\" >1.000000</td>\n", "      <td id=\"T_1d825_row18_col19\" class=\"data row18 col19\" >0.870000</td>\n", "      <td id=\"T_1d825_row18_col20\" class=\"data row18 col20\" >-0.970000</td>\n", "      <td id=\"T_1d825_row18_col21\" class=\"data row18 col21\" >0.920000</td>\n", "      <td id=\"T_1d825_row18_col22\" class=\"data row18 col22\" >0.830000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row19\" class=\"row_heading level0 row19\" >gdp_avg</th>\n", "      <td id=\"T_1d825_row19_col0\" class=\"data row19 col0\" >-0.530000</td>\n", "      <td id=\"T_1d825_row19_col1\" class=\"data row19 col1\" >0.380000</td>\n", "      <td id=\"T_1d825_row19_col2\" class=\"data row19 col2\" >0.290000</td>\n", "      <td id=\"T_1d825_row19_col3\" class=\"data row19 col3\" >0.250000</td>\n", "      <td id=\"T_1d825_row19_col4\" class=\"data row19 col4\" >0.050000</td>\n", "      <td id=\"T_1d825_row19_col5\" class=\"data row19 col5\" >0.450000</td>\n", "      <td id=\"T_1d825_row19_col6\" class=\"data row19 col6\" >0.830000</td>\n", "      <td id=\"T_1d825_row19_col7\" class=\"data row19 col7\" >0.530000</td>\n", "      <td id=\"T_1d825_row19_col8\" class=\"data row19 col8\" >-0.270000</td>\n", "      <td id=\"T_1d825_row19_col9\" class=\"data row19 col9\" >-0.630000</td>\n", "      <td id=\"T_1d825_row19_col10\" class=\"data row19 col10\" >0.780000</td>\n", "      <td id=\"T_1d825_row19_col11\" class=\"data row19 col11\" >0.810000</td>\n", "      <td id=\"T_1d825_row19_col12\" class=\"data row19 col12\" >0.840000</td>\n", "      <td id=\"T_1d825_row19_col13\" class=\"data row19 col13\" >0.770000</td>\n", "      <td id=\"T_1d825_row19_col14\" class=\"data row19 col14\" >0.710000</td>\n", "      <td id=\"T_1d825_row19_col15\" class=\"data row19 col15\" >0.570000</td>\n", "      <td id=\"T_1d825_row19_col16\" class=\"data row19 col16\" >0.640000</td>\n", "      <td id=\"T_1d825_row19_col17\" class=\"data row19 col17\" >0.720000</td>\n", "      <td id=\"T_1d825_row19_col18\" class=\"data row19 col18\" >0.870000</td>\n", "      <td id=\"T_1d825_row19_col19\" class=\"data row19 col19\" >1.000000</td>\n", "      <td id=\"T_1d825_row19_col20\" class=\"data row19 col20\" >-0.310000</td>\n", "      <td id=\"T_1d825_row19_col21\" class=\"data row19 col21\" >0.860000</td>\n", "      <td id=\"T_1d825_row19_col22\" class=\"data row19 col22\" >0.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row20\" class=\"row_heading level0 row20\" >floating_inside_rate</th>\n", "      <td id=\"T_1d825_row20_col0\" class=\"data row20 col0\" >-0.290000</td>\n", "      <td id=\"T_1d825_row20_col1\" class=\"data row20 col1\" >0.010000</td>\n", "      <td id=\"T_1d825_row20_col2\" class=\"data row20 col2\" >0.290000</td>\n", "      <td id=\"T_1d825_row20_col3\" class=\"data row20 col3\" >0.260000</td>\n", "      <td id=\"T_1d825_row20_col4\" class=\"data row20 col4\" >-0.190000</td>\n", "      <td id=\"T_1d825_row20_col5\" class=\"data row20 col5\" >-0.270000</td>\n", "      <td id=\"T_1d825_row20_col6\" class=\"data row20 col6\" >-0.120000</td>\n", "      <td id=\"T_1d825_row20_col7\" class=\"data row20 col7\" >0.140000</td>\n", "      <td id=\"T_1d825_row20_col8\" class=\"data row20 col8\" >0.330000</td>\n", "      <td id=\"T_1d825_row20_col9\" class=\"data row20 col9\" >0.040000</td>\n", "      <td id=\"T_1d825_row20_col10\" class=\"data row20 col10\" >-0.090000</td>\n", "      <td id=\"T_1d825_row20_col11\" class=\"data row20 col11\" >-0.070000</td>\n", "      <td id=\"T_1d825_row20_col12\" class=\"data row20 col12\" >-0.050000</td>\n", "      <td id=\"T_1d825_row20_col13\" class=\"data row20 col13\" >0.020000</td>\n", "      <td id=\"T_1d825_row20_col14\" class=\"data row20 col14\" >0.070000</td>\n", "      <td id=\"T_1d825_row20_col15\" class=\"data row20 col15\" >0.160000</td>\n", "      <td id=\"T_1d825_row20_col16\" class=\"data row20 col16\" >0.080000</td>\n", "      <td id=\"T_1d825_row20_col17\" class=\"data row20 col17\" >-0.650000</td>\n", "      <td id=\"T_1d825_row20_col18\" class=\"data row20 col18\" >-0.970000</td>\n", "      <td id=\"T_1d825_row20_col19\" class=\"data row20 col19\" >-0.310000</td>\n", "      <td id=\"T_1d825_row20_col20\" class=\"data row20 col20\" >1.000000</td>\n", "      <td id=\"T_1d825_row20_col21\" class=\"data row20 col21\" >-0.420000</td>\n", "      <td id=\"T_1d825_row20_col22\" class=\"data row20 col22\" >0.140000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row21\" class=\"row_heading level0 row21\" >floating_outside_rate</th>\n", "      <td id=\"T_1d825_row21_col0\" class=\"data row21 col0\" >-0.450000</td>\n", "      <td id=\"T_1d825_row21_col1\" class=\"data row21 col1\" >0.630000</td>\n", "      <td id=\"T_1d825_row21_col2\" class=\"data row21 col2\" >0.040000</td>\n", "      <td id=\"T_1d825_row21_col3\" class=\"data row21 col3\" >-0.040000</td>\n", "      <td id=\"T_1d825_row21_col4\" class=\"data row21 col4\" >-0.140000</td>\n", "      <td id=\"T_1d825_row21_col5\" class=\"data row21 col5\" >0.220000</td>\n", "      <td id=\"T_1d825_row21_col6\" class=\"data row21 col6\" >0.840000</td>\n", "      <td id=\"T_1d825_row21_col7\" class=\"data row21 col7\" >0.410000</td>\n", "      <td id=\"T_1d825_row21_col8\" class=\"data row21 col8\" >-0.350000</td>\n", "      <td id=\"T_1d825_row21_col9\" class=\"data row21 col9\" >-0.630000</td>\n", "      <td id=\"T_1d825_row21_col10\" class=\"data row21 col10\" >0.830000</td>\n", "      <td id=\"T_1d825_row21_col11\" class=\"data row21 col11\" >0.830000</td>\n", "      <td id=\"T_1d825_row21_col12\" class=\"data row21 col12\" >0.820000</td>\n", "      <td id=\"T_1d825_row21_col13\" class=\"data row21 col13\" >0.720000</td>\n", "      <td id=\"T_1d825_row21_col14\" class=\"data row21 col14\" >0.640000</td>\n", "      <td id=\"T_1d825_row21_col15\" class=\"data row21 col15\" >0.520000</td>\n", "      <td id=\"T_1d825_row21_col16\" class=\"data row21 col16\" >0.640000</td>\n", "      <td id=\"T_1d825_row21_col17\" class=\"data row21 col17\" >0.880000</td>\n", "      <td id=\"T_1d825_row21_col18\" class=\"data row21 col18\" >0.920000</td>\n", "      <td id=\"T_1d825_row21_col19\" class=\"data row21 col19\" >0.860000</td>\n", "      <td id=\"T_1d825_row21_col20\" class=\"data row21 col20\" >-0.420000</td>\n", "      <td id=\"T_1d825_row21_col21\" class=\"data row21 col21\" >1.000000</td>\n", "      <td id=\"T_1d825_row21_col22\" class=\"data row21 col22\" >0.840000</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1d825_level0_row22\" class=\"row_heading level0 row22\" >floating_rate</th>\n", "      <td id=\"T_1d825_row22_col0\" class=\"data row22 col0\" >-0.670000</td>\n", "      <td id=\"T_1d825_row22_col1\" class=\"data row22 col1\" >0.690000</td>\n", "      <td id=\"T_1d825_row22_col2\" class=\"data row22 col2\" >0.220000</td>\n", "      <td id=\"T_1d825_row22_col3\" class=\"data row22 col3\" >0.120000</td>\n", "      <td id=\"T_1d825_row22_col4\" class=\"data row22 col4\" >-0.270000</td>\n", "      <td id=\"T_1d825_row22_col5\" class=\"data row22 col5\" >0.080000</td>\n", "      <td id=\"T_1d825_row22_col6\" class=\"data row22 col6\" >0.840000</td>\n", "      <td id=\"T_1d825_row22_col7\" class=\"data row22 col7\" >0.530000</td>\n", "      <td id=\"T_1d825_row22_col8\" class=\"data row22 col8\" >-0.190000</td>\n", "      <td id=\"T_1d825_row22_col9\" class=\"data row22 col9\" >-0.660000</td>\n", "      <td id=\"T_1d825_row22_col10\" class=\"data row22 col10\" >0.850000</td>\n", "      <td id=\"T_1d825_row22_col11\" class=\"data row22 col11\" >0.870000</td>\n", "      <td id=\"T_1d825_row22_col12\" class=\"data row22 col12\" >0.860000</td>\n", "      <td id=\"T_1d825_row22_col13\" class=\"data row22 col13\" >0.790000</td>\n", "      <td id=\"T_1d825_row22_col14\" class=\"data row22 col14\" >0.740000</td>\n", "      <td id=\"T_1d825_row22_col15\" class=\"data row22 col15\" >0.660000</td>\n", "      <td id=\"T_1d825_row22_col16\" class=\"data row22 col16\" >0.750000</td>\n", "      <td id=\"T_1d825_row22_col17\" class=\"data row22 col17\" >0.760000</td>\n", "      <td id=\"T_1d825_row22_col18\" class=\"data row22 col18\" >0.830000</td>\n", "      <td id=\"T_1d825_row22_col19\" class=\"data row22 col19\" >0.750000</td>\n", "      <td id=\"T_1d825_row22_col20\" class=\"data row22 col20\" >0.140000</td>\n", "      <td id=\"T_1d825_row22_col21\" class=\"data row22 col21\" >0.840000</td>\n", "      <td id=\"T_1d825_row22_col22\" class=\"data row22 col22\" >1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x22d445294f0>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# import seaborn as sns\n", "corr = df.drop([\"floating_pop_inside\",\"floating_pop_outside\"],axis=1).corr().round(2)\n", "corr.style.background_gradient(cmap='YlOrRd')\n", "# Set up the matplotlib figure\n"]}, {"cell_type": "markdown", "metadata": {"id": "7EFAFB62CA1C453BB7A9C8BBA2883CA8", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["通过对不同年龄结构比例和教育水平、人均GDP、城市化进度、流动人口比例等数据的相关性，发现：  \n", "\n", "- 14岁以下人口比例和城市化进度有着较高的相关系数。其中和**城市化比例超过50%的年份的距今系数**的相关系数达到 -0.81。也就是城市化水平越早达到50%，该地区的14岁以下人口比例越可能较低。  \n", "- 15到59岁人口比例和流动人口比例有着较高的相关系数，其中人户分离比例的相关系数达到0.69。  \n", "- 60岁以上人口比例和城市化进度有着较高的相关系数，其中与2019年的城市化水平相关系数为0.62。  \n", "- 人口年龄结构和人均GDP的相关性不高。对于14岁以下年龄的人口比例的相关系数只有-0.53，对于60岁以上年龄的人口比例的相关系数只有0.29。  \n", "\n", "结合人口结构变化的一些逻辑，我们对人口年龄结构的差异给出分析结果：  \n", "\n", "- 14岁以下人口年龄比例差异主要受到城市化进展的影响。城市化水平越高，进度越早，则这部分人口年龄比例更容易较少。城市化通过影响人们的婚姻和生育观念影响了儿童人口的数量。  \n", "- 15-59岁之间人口年龄比例差异主要受到流动人口的影响。流动人口比例越大，则对这部分人口年龄比例会更高。流动人口体现了地区在吸引其他地区人口，特别是目前以经济因素为主导的人口流动下，会吸引更多的劳动力人口。  \n", " "]}, {"cell_type": "code", "execution_count": 25, "metadata": {"id": "F3A1134477274BA8B4E46217C86D404D", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["\n", "# gdf.groupby(\"group\").agg(\n", "#     a14_mean=(\"a14-\",\"mean\"),\n", "#     a15_59_mean=(\"a15_59\",\"mean\"),\n", "#     a60_mean=(\"a60+\",\"mean\"),\n", "#     edu_college_mean=(\"edu_college\",\"mean\"),\n", "#     edu_senior_mean=(\"edu_senior\",\"mean\"),\n", "#     urba_over_50_mean=(\"urba_over_50\",\"mean\"),\n", "#     urba_2000_mean=(\"urba_2000\",\"mean\"),\n", "#     urba_2019_mean=(\"urba_2019\",\"mean\"),\n", "#     floating_outside_rate=(\"floating_outside_rate\",\"mean\"),\n", "#     floating_rate=(\"floating_rate\",\"mean\")\n", "# )"]}, {"cell_type": "markdown", "metadata": {"id": "A097C53CA5AE405AA7A6DFC10716F598", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 城市化进度对于人口年龄结构影响的观察"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "828C9C5FE1FD4870814933026B32F8AD", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<div id=\"altair-viz-793116c42e86490482016302a27e855d\"></div>\n", "<script type=\"text/javascript\">\n", "  var VEGA_DEBUG = (typeof VEGA_DEBUG == \"undefined\") ? {} : VEGA_DEBUG;\n", "  (function(spec, embedOpt){\n", "    let outputDiv = document.currentScript.previousElementSibling;\n", "    if (outputDiv.id !== \"altair-viz-793116c42e86490482016302a27e855d\") {\n", "      outputDiv = document.getElementById(\"altair-viz-793116c42e86490482016302a27e855d\");\n", "    }\n", "    const paths = {\n", "      \"vega\": \"https://cdn.jsdelivr.net/npm//vega@5?noext\",\n", "      \"vega-lib\": \"https://cdn.jsdelivr.net/npm//vega-lib?noext\",\n", "      \"vega-lite\": \"https://cdn.jsdelivr.net/npm//vega-lite@4.17.0?noext\",\n", "      \"vega-embed\": \"https://cdn.jsdelivr.net/npm//vega-embed@6?noext\",\n", "    };\n", "\n", "    function maybeLoadScript(lib, version) {\n", "      var key = `${lib.replace(\"-\", \"\")}_version`;\n", "      return (VEGA_DEBUG[key] == version) ?\n", "        Promise.resolve(paths[lib]) :\n", "        new Promise(function(resolve, reject) {\n", "          var s = document.createElement('script');\n", "          document.getElementsByTagName(\"head\")[0].appendChild(s);\n", "          s.async = true;\n", "          s.onload = () => {\n", "            VEGA_DEBUG[key] = version;\n", "            return resolve(paths[lib]);\n", "          };\n", "          s.onerror = () => reject(`Error loading script: ${paths[lib]}`);\n", "          s.src = paths[lib];\n", "        });\n", "    }\n", "\n", "    function showError(err) {\n", "      outputDiv.innerHTML = `<div class=\"error\" style=\"color:red;\">${err}</div>`;\n", "      throw err;\n", "    }\n", "\n", "    function displayChart(vegaEmbed) {\n", "      vegaEmbed(outputDiv, spec, embedOpt)\n", "        .catch(err => showError(`Javascript Error: ${err.message}<br>This usually means there's a typo in your chart specification. See the javascript console for the full traceback.`));\n", "    }\n", "\n", "    if(typeof define === \"function\" && define.amd) {\n", "      requirejs.config({paths});\n", "      require([\"vega-embed\"], displayChart, err => showError(`Error loading script: ${err.message}`));\n", "    } else {\n", "      maybeLoadScript(\"vega\", \"5\")\n", "        .then(() => maybeLoadScript(\"vega-lite\", \"4.17.0\"))\n", "        .then(() => maybeLoadScript(\"vega-embed\", \"6\"))\n", "        .catch(showError)\n", "        .then(() => displayChart(vegaEmbed));\n", "    }\n", "  })({\"config\": {\"view\": {\"continuousWidth\": 400, \"continuousHeight\": 300}}, \"data\": {\"name\": \"data-c2bdb2c588da4eeb8056fc1ba73b45df\"}, \"mark\": \"point\", \"encoding\": {\"color\": {\"field\": \"group\", \"type\": \"nominal\"}, \"tooltip\": [{\"field\": \"region\", \"type\": \"nominal\"}, {\"field\": \"urba_over_50\", \"type\": \"quantitative\"}, {\"field\": \"a14-\", \"type\": \"quantitative\"}], \"x\": {\"field\": \"urba_over_50\", \"type\": \"quantitative\"}, \"y\": {\"field\": \"a14-\", \"type\": \"quantitative\"}}, \"title\": \"\\u57ce\\u5e02\\u5316\\u8fdb\\u5ea6\\u4e0e14\\u5c81\\u4ee5\\u4e0b\\u4eba\\u53e3\\u5e74\\u9f84\\u7ed3\\u6784\\u6bd4\\u4f8b\\u7684\\u5173\\u7cfb\", \"$schema\": \"https://vega.github.io/schema/vega-lite/v4.17.0.json\", \"datasets\": {\"data-c2bdb2c588da4eeb8056fc1ba73b45df\": [{\"region\": \"\\u5317\\u4eac\", \"a14-\": 0.1184, \"a15_59\": 0.6853, \"a60+\": 0.1963, \"a65+\": 0.133, \"population\": 21893095, \"gdp\": 36102.6, \"edu_college\": 0.4198, \"edu_senior\": 0.17593, \"edu_junior\": 0.23289, \"edu_primary\": 0.10503, \"urba_2000\": 0.7754, \"urba_2005\": 0.8362, \"urba_2010\": 0.8593, \"urba_2015\": 0.8579, \"urba_2019\": 0.8516, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 1.0, \"urba_over_70\": 1.0, \"floating_pop_inside\": 4991158.0, \"floating_pop_outside\": 8418418.0, \"gdp_avg\": 16.490404851392643, \"floating_inside_rate\": 0.22797863892702241, \"floating_outside_rate\": 0.3845238875545006, \"floating_rate\": 0.612502526481523, \"group\": 1}, {\"region\": \"\\u5929\\u6d25\", \"a14-\": 0.13470000000000001, \"a15_59\": 0.6487, \"a60+\": 0.21660000000000001, \"a65+\": 0.1475, \"population\": 13866009, \"gdp\": 14083.7, \"edu_college\": 0.2694, \"edu_senior\": 0.17719, \"edu_junior\": 0.32294, \"edu_primary\": 0.16123, \"urba_2000\": 0.7199, \"urba_2005\": 0.7507, \"urba_2010\": 0.796, \"urba_2015\": 0.8881, \"urba_2019\": 0.9415, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 1.0, \"urba_over_70\": 1.0, \"floating_pop_inside\": 2944879.0, \"floating_pop_outside\": 3534816.0, \"gdp_avg\": 10.156996147918266, \"floating_inside_rate\": 0.21238115451966028, \"floating_outside_rate\": 0.25492670601901385, \"floating_rate\": 0.4673078605386741, \"group\": 1}, {\"region\": \"\\u5185\\u8499\\u53e4\", \"a14-\": 0.1404, \"a15_59\": 0.6617000000000001, \"a60+\": 0.1978, \"a65+\": 0.1305, \"population\": 24049155, \"gdp\": 17359.8, \"edu_college\": 0.18688, \"edu_senior\": 0.14814, \"edu_junior\": 0.33861, \"edu_primary\": 0.23627, \"urba_2000\": 0.4268, \"urba_2005\": 0.4719, \"urba_2010\": 0.555, \"urba_2015\": 0.6205, \"urba_2019\": 0.6663, \"urba_over_40\": 1.0, \"urba_over_50\": 0.65, \"urba_over_60\": 0.3, \"urba_over_70\": null, \"floating_pop_inside\": 9776541.0, \"floating_pop_outside\": 1686420.0, \"gdp_avg\": 7.218465679979192, \"floating_inside_rate\": 0.40652326453881643, \"floating_outside_rate\": 0.07012387753332705, \"floating_rate\": 0.4766471420721435, \"group\": 1}, {\"region\": \"\\u8fbd\\u5b81\", \"a14-\": 0.1112, \"a15_59\": 0.6315999999999999, \"a60+\": 0.2572, \"a65+\": 0.17420000000000002, \"population\": 42591407, \"gdp\": 25115.0, \"edu_college\": 0.18216, \"edu_senior\": 0.1467, \"edu_junior\": 0.42799, \"edu_primary\": 0.18888, \"urba_2000\": 0.5424, \"urba_2005\": 0.5871, \"urba_2010\": 0.621, \"urba_2015\": 0.6805, \"urba_2019\": 0.693, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 0.6, \"urba_over_70\": null, \"floating_pop_inside\": 12822813.0, \"floating_pop_outside\": 2847308.0, \"gdp_avg\": 5.896729356698641, \"floating_inside_rate\": 0.3010657290565677, \"floating_outside_rate\": 0.06685170086069239, \"floating_rate\": 0.36791742991726006, \"group\": 1}, {\"region\": \"\\u5409\\u6797\", \"a14-\": 0.11710000000000001, \"a15_59\": 0.6523, \"a60+\": 0.2306, \"a65+\": 0.1561, \"population\": 24073453, \"gdp\": 12311.3, \"edu_college\": 0.16738, \"edu_senior\": 0.1708, \"edu_junior\": 0.38234, \"edu_primary\": 0.22318, \"urba_2000\": 0.4968, \"urba_2005\": 0.525, \"urba_2010\": 0.5333, \"urba_2015\": 0.5829, \"urba_2019\": 0.6405, \"urba_over_40\": 1.0, \"urba_over_50\": 0.75, \"urba_over_60\": 0.15, \"urba_over_70\": null, \"floating_pop_inside\": 9349212.0, \"floating_pop_outside\": 1001471.0, \"gdp_avg\": 5.114056550175831, \"floating_inside_rate\": 0.3883619022165204, \"floating_outside_rate\": 0.041600637847840106, \"floating_rate\": 0.4299625400643605, \"group\": 1}, {\"region\": \"\\u9ed1\\u9f99\\u6c5f\", \"a14-\": 0.1032, \"a15_59\": 0.6646, \"a60+\": 0.2322, \"a65+\": 0.1561, \"population\": 31850088, \"gdp\": 13698.5, \"edu_college\": 0.14793, \"edu_senior\": 0.15525, \"edu_junior\": 0.42793, \"edu_primary\": 0.21863, \"urba_2000\": 0.5154, \"urba_2005\": 0.5309, \"urba_2010\": 0.5567, \"urba_2015\": 0.635, \"urba_2019\": 0.7017, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 0.35, \"urba_over_70\": 0.05, \"floating_pop_inside\": 10720408.0, \"floating_pop_outside\": 829176.0, \"gdp_avg\": 4.300930031967258, \"floating_inside_rate\": 0.3365895880727237, \"floating_outside_rate\": 0.026033711429619914, \"floating_rate\": 0.3626232995023436, \"group\": 1}, {\"region\": \"\\u4e0a\\u6d77\", \"a14-\": 0.098, \"a15_59\": 0.6681999999999999, \"a60+\": 0.23379999999999998, \"a65+\": 0.1628, \"population\": 24870895, \"gdp\": 38700.6, \"edu_college\": 0.33872, \"edu_senior\": 0.1902, \"edu_junior\": 0.28935, \"edu_primary\": 0.11929, \"urba_2000\": 0.8831, \"urba_2005\": 0.891, \"urba_2010\": 0.8927, \"urba_2015\": 0.8609, \"urba_2019\": 0.8642, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 1.0, \"urba_over_70\": 1.0, \"floating_pop_inside\": 4654606.0, \"floating_pop_outside\": 10479652.0, \"gdp_avg\": 15.560598040400235, \"floating_inside_rate\": 0.18715072376768105, \"floating_outside_rate\": 0.4213620780434319, \"floating_rate\": 0.608512801811113, \"group\": 1}, {\"region\": \"\\u6d59\\u6c5f\", \"a14-\": 0.13449999999999998, \"a15_59\": 0.6786, \"a60+\": 0.187, \"a65+\": 0.13269999999999998, \"population\": 64567588, \"gdp\": 64613.3, \"edu_college\": 0.1699, \"edu_senior\": 0.14555, \"edu_junior\": 0.32706, \"edu_primary\": 0.26384, \"urba_2000\": 0.4867, \"urba_2005\": 0.5602, \"urba_2010\": 0.6161, \"urba_2015\": 0.609, \"urba_2019\": 0.6424, \"urba_over_40\": 1.0, \"urba_over_50\": 0.75, \"urba_over_60\": 0.5, \"urba_over_70\": null, \"floating_pop_inside\": 13921361.0, \"floating_pop_outside\": 16186454.0, \"gdp_avg\": 10.007079713121698, \"floating_inside_rate\": 0.21560912264525042, \"floating_outside_rate\": 0.2506900830800742, \"floating_rate\": 0.46629920572532463, \"group\": 1}, {\"region\": \"\\u9655\\u897f\", \"a14-\": 0.17329999999999998, \"a15_59\": 0.6346, \"a60+\": 0.192, \"a65+\": 0.1332, \"population\": 39528999, \"gdp\": 26181.9, \"edu_college\": 0.18397, \"edu_senior\": 0.15581, \"edu_junior\": 0.33979, \"edu_primary\": 0.21686, \"urba_2000\": 0.3226, \"urba_2005\": 0.3724, \"urba_2010\": 0.4576, \"urba_2015\": 0.5317, \"urba_2019\": 0.5842, \"urba_over_40\": 0.65, \"urba_over_50\": 0.35, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 11333383.0, \"floating_pop_outside\": 1933712.0, \"gdp_avg\": 6.623466483429039, \"floating_inside_rate\": 0.286710599476602, \"floating_outside_rate\": 0.04891882033238434, \"floating_rate\": 0.3356294198089863, \"group\": 2}, {\"region\": \"\\u8d35\\u5dde\", \"a14-\": 0.2397, \"a15_59\": 0.6065, \"a60+\": 0.15380000000000002, \"a65+\": 0.11560000000000001, \"population\": 38562148, \"gdp\": 17826.6, \"edu_college\": 0.10952, \"edu_senior\": 0.09951, \"edu_junior\": 0.30464, \"edu_primary\": 0.31921, \"urba_2000\": 0.2387, \"urba_2005\": 0.2686, \"urba_2010\": 0.338, \"urba_2015\": 0.3999, \"urba_2019\": 0.4615, \"urba_over_40\": 0.2, \"urba_over_50\": null, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 10548217.0, \"floating_pop_outside\": 1146546.0, \"gdp_avg\": 4.622823396663485, \"floating_inside_rate\": 0.27353810788755856, \"floating_outside_rate\": 0.02973242050728087, \"floating_rate\": 0.3032705283948394, \"group\": 2}, {\"region\": \"\\u56db\\u5ddd\", \"a14-\": 0.161, \"a15_59\": 0.6219, \"a60+\": 0.21710000000000002, \"a65+\": 0.1693, \"population\": 83674866, \"gdp\": 48598.8, \"edu_college\": 0.13267, \"edu_senior\": 0.13301, \"edu_junior\": 0.31443, \"edu_primary\": 0.31317, \"urba_2000\": 0.2669, \"urba_2005\": 0.33, \"urba_2010\": 0.4017, \"urba_2015\": 0.4773, \"urba_2019\": 0.5395, \"urba_over_40\": 0.5, \"urba_over_50\": 0.15, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 25233163.0, \"floating_pop_outside\": 2590041.0, \"gdp_avg\": 5.808052324816391, \"floating_inside_rate\": 0.3015620365618512, \"floating_outside_rate\": 0.030953631882720913, \"floating_rate\": 0.3325156684445721, \"group\": 2}, {\"region\": \"\\u91cd\\u5e86\", \"a14-\": 0.1591, \"a15_59\": 0.6222, \"a60+\": 0.2187, \"a65+\": 0.17079999999999998, \"population\": 32054159, \"gdp\": 25002.8, \"edu_college\": 0.15412, \"edu_senior\": 0.15956, \"edu_junior\": 0.30582, \"edu_primary\": 0.29894, \"urba_2000\": 0.3309, \"urba_2005\": 0.4521, \"urba_2010\": 0.53, \"urba_2015\": 0.5987, \"urba_2019\": 0.6546, \"urba_over_40\": 0.75, \"urba_over_50\": 0.55, \"urba_over_60\": 0.2, \"urba_over_70\": null, \"floating_pop_inside\": 10902860.0, \"floating_pop_outside\": 2193575.0, \"gdp_avg\": 7.8001734501909725, \"floating_inside_rate\": 0.3401387008780982, \"floating_outside_rate\": 0.06843339736350594, \"floating_rate\": 0.4085720982416042, \"group\": 2}, {\"region\": \"\\u5e7f\\u897f\", \"a14-\": 0.23629999999999998, \"a15_59\": 0.5969, \"a60+\": 0.16690000000000002, \"a65+\": 0.122, \"population\": 50126804, \"gdp\": 22156.7, \"edu_college\": 0.10806, \"edu_senior\": 0.12962, \"edu_junior\": 0.36388, \"edu_primary\": 0.27855, \"urba_2000\": 0.2815, \"urba_2005\": 0.3363, \"urba_2010\": 0.4, \"urba_2015\": 0.4691, \"urba_2019\": 0.5086, \"urba_over_40\": 0.45, \"urba_over_50\": 0.1, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 11879397.0, \"floating_pop_outside\": 1359384.0, \"gdp_avg\": 4.420130196211991, \"floating_inside_rate\": 0.23698692220633097, \"floating_outside_rate\": 0.02711890428921022, \"floating_rate\": 0.2641058264955412, \"group\": 2}, {\"region\": \"\\u6e56\\u5357\", \"a14-\": 0.19519999999999998, \"a15_59\": 0.606, \"a60+\": 0.19879999999999998, \"a65+\": 0.1481, \"population\": 66444864, \"gdp\": 41781.5, \"edu_college\": 0.12239, \"edu_senior\": 0.17776, \"edu_junior\": 0.35636, \"edu_primary\": 0.25214, \"urba_2000\": 0.2975, \"urba_2005\": 0.3701, \"urba_2010\": 0.433, \"urba_2015\": 0.5218, \"urba_2019\": 0.5962, \"urba_over_40\": 0.65, \"urba_over_50\": 0.3, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 15998284.0, \"floating_pop_outside\": 1577563.0, \"gdp_avg\": 6.288145912978315, \"floating_inside_rate\": 0.24077532915109887, \"floating_outside_rate\": 0.023742437037721983, \"floating_rate\": 0.26451776618882084, \"group\": 2}, {\"region\": \"\\u6e56\\u5317\", \"a14-\": 0.1631, \"a15_59\": 0.6325999999999999, \"a60+\": 0.20420000000000002, \"a65+\": 0.1459, \"population\": 57752557, \"gdp\": 43443.5, \"edu_college\": 0.15502, \"edu_senior\": 0.17428, \"edu_junior\": 0.3428, \"edu_primary\": 0.2352, \"urba_2000\": 0.4022, \"urba_2005\": 0.432, \"urba_2010\": 0.497, \"urba_2015\": 0.5687, \"urba_2019\": 0.6099, \"urba_over_40\": 1.0, \"urba_over_50\": 0.45, \"urba_over_60\": 0.1, \"urba_over_70\": null, \"floating_pop_inside\": 16226947.0, \"floating_pop_outside\": 2249614.0, \"gdp_avg\": 7.522350915129178, \"floating_inside_rate\": 0.2809736545517803, \"floating_outside_rate\": 0.038952630270552346, \"floating_rate\": 0.31992628482233265, \"group\": 2}, {\"region\": \"\\u6cb3\\u5357\", \"a14-\": 0.2314, \"a15_59\": 0.5879, \"a60+\": 0.1808, \"a65+\": 0.1349, \"population\": 99365519, \"gdp\": 54997.1, \"edu_college\": 0.11744, \"edu_senior\": 0.15239, \"edu_junior\": 0.37518, \"edu_primary\": 0.24557, \"urba_2000\": 0.232, \"urba_2005\": 0.3065, \"urba_2010\": 0.385, \"urba_2015\": 0.4578, \"urba_2019\": 0.518, \"urba_over_40\": 0.45, \"urba_over_50\": 0.1, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 24365959.0, \"floating_pop_outside\": 1273646.0, \"gdp_avg\": 5.534827428416088, \"floating_inside_rate\": 0.2452154353463398, \"floating_outside_rate\": 0.012817786419452004, \"floating_rate\": 0.2580332217657918, \"group\": 2}, {\"region\": \"\\u5c71\\u4e1c\", \"a14-\": 0.18780000000000002, \"a15_59\": 0.6032, \"a60+\": 0.209, \"a65+\": 0.15130000000000002, \"population\": 101527453, \"gdp\": 73129.0, \"edu_college\": 0.14384, \"edu_senior\": 0.14334, \"edu_junior\": 0.35778, \"edu_primary\": 0.23693, \"urba_2000\": 0.38, \"urba_2005\": 0.45, \"urba_2010\": 0.497, \"urba_2015\": 0.569, \"urba_2019\": 0.6129, \"urba_over_40\": 0.75, \"urba_over_50\": 0.45, \"urba_over_60\": 0.15, \"urba_over_70\": null, \"floating_pop_inside\": 23897755.0, \"floating_pop_outside\": 4129007.0, \"gdp_avg\": 7.202879402480431, \"floating_inside_rate\": 0.2353821975618752, \"floating_outside_rate\": 0.04066887209314706, \"floating_rate\": 0.2760510696550223, \"group\": 2}, {\"region\": \"\\u6c5f\\u897f\", \"a14-\": 0.21960000000000002, \"a15_59\": 0.6117, \"a60+\": 0.16870000000000002, \"a65+\": 0.1189, \"population\": 45188635, \"gdp\": 25691.5, \"edu_college\": 0.11897, \"edu_senior\": 0.15145, \"edu_junior\": 0.35501, \"edu_primary\": 0.27514, \"urba_2000\": 0.2767, \"urba_2005\": 0.37, \"urba_2010\": 0.4406, \"urba_2015\": 0.5255, \"urba_2019\": 0.5932, \"urba_over_40\": 0.6, \"urba_over_50\": 0.3, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 12241920.0, \"floating_pop_outside\": 1279014.0, \"gdp_avg\": 5.685389700308495, \"floating_inside_rate\": 0.27090705439542484, \"floating_outside_rate\": 0.028303886585642608, \"floating_rate\": 0.2992109409810675, \"group\": 2}, {\"region\": \"\\u5b89\\u5fbd\", \"a14-\": 0.1924, \"a15_59\": 0.6196, \"a60+\": 0.18789999999999998, \"a65+\": 0.1501, \"population\": 61027171, \"gdp\": 38680.6, \"edu_college\": 0.1328, \"edu_senior\": 0.13294, \"edu_junior\": 0.33724, \"edu_primary\": 0.26875, \"urba_2000\": 0.2781, \"urba_2005\": 0.3551, \"urba_2010\": 0.4301, \"urba_2015\": 0.5162, \"urba_2019\": 0.5832, \"urba_over_40\": 0.6, \"urba_over_50\": 0.25, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 16549409.0, \"floating_pop_outside\": 1550509.0, \"gdp_avg\": 6.338258740520677, \"floating_inside_rate\": 0.27118099575679167, \"floating_outside_rate\": 0.025406863444481148, \"floating_rate\": 0.29658785920127284, \"group\": 2}, {\"region\": \"\\u6c5f\\u82cf\", \"a14-\": 0.1521, \"a15_59\": 0.6295000000000001, \"a60+\": 0.2184, \"a65+\": 0.162, \"population\": 84748016, \"gdp\": 102719.0, \"edu_college\": 0.18663, \"edu_senior\": 0.16191, \"edu_junior\": 0.33308, \"edu_primary\": 0.22742, \"urba_2000\": 0.4149, \"urba_2005\": 0.505, \"urba_2010\": 0.6058, \"urba_2015\": 0.6381, \"urba_2019\": 0.6728, \"urba_over_40\": 1.0, \"urba_over_50\": 0.75, \"urba_over_60\": 0.5, \"urba_over_70\": null, \"floating_pop_inside\": 19671338.0, \"floating_pop_outside\": 10308610.0, \"gdp_avg\": 12.120519729925006, \"floating_inside_rate\": 0.23211561672429004, \"floating_outside_rate\": 0.12163836378187308, \"floating_rate\": 0.35375398050616313, \"group\": 2}, {\"region\": \"\\u5c71\\u897f\", \"a14-\": 0.1635, \"a15_59\": 0.6472, \"a60+\": 0.1892, \"a65+\": 0.129, \"population\": 34915616, \"gdp\": 17651.9, \"edu_college\": 0.17358, \"edu_senior\": 0.16485, \"edu_junior\": 0.3895, \"edu_primary\": 0.19506, \"urba_2000\": 0.3491, \"urba_2005\": 0.4212, \"urba_2010\": 0.4804, \"urba_2015\": 0.5729, \"urba_2019\": 0.6351, \"urba_over_40\": 0.75, \"urba_over_50\": 0.45, \"urba_over_60\": 0.15, \"urba_over_70\": null, \"floating_pop_inside\": 11270656.0, \"floating_pop_outside\": 1620518.0, \"gdp_avg\": 5.055588880345115, \"floating_inside_rate\": 0.3227969971946077, \"floating_outside_rate\": 0.046412413288082904, \"floating_rate\": 0.3692094104826906, \"group\": 2}, {\"region\": \"\\u6cb3\\u5317\", \"a14-\": 0.2022, \"a15_59\": 0.5992000000000001, \"a60+\": 0.1985, \"a65+\": 0.1392, \"population\": 74610235, \"gdp\": 36206.9, \"edu_college\": 0.12418, \"edu_senior\": 0.13861, \"edu_junior\": 0.3995, \"edu_primary\": 0.24664, \"urba_2000\": 0.2608, \"urba_2005\": 0.3769, \"urba_2010\": 0.445, \"urba_2015\": 0.5189, \"urba_2019\": 0.5874, \"urba_over_40\": 0.65, \"urba_over_50\": 0.25, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 16620369.0, \"floating_pop_outside\": 3155272.0, \"gdp_avg\": 4.852806052681646, \"floating_inside_rate\": 0.22276258746537925, \"floating_outside_rate\": 0.04229006918420777, \"floating_rate\": 0.265052656649587, \"group\": 2}, {\"region\": \"\\u5e7f\\u4e1c\", \"a14-\": 0.1885, \"a15_59\": 0.688, \"a60+\": 0.1235, \"a65+\": 0.0858, \"population\": 126012510, \"gdp\": 110760.9, \"edu_college\": 0.15699, \"edu_senior\": 0.18224, \"edu_junior\": 0.35484, \"edu_primary\": 0.20676, \"urba_2000\": 0.55, \"urba_2005\": 0.6068, \"urba_2010\": 0.6618, \"urba_2015\": 0.6383, \"urba_2019\": 0.6587, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 0.75, \"urba_over_70\": null, \"floating_pop_inside\": 31012976.0, \"floating_pop_outside\": 29622110.0, \"gdp_avg\": 8.789674929893865, \"floating_inside_rate\": 0.24611029492230574, \"floating_outside_rate\": 0.2350727717430595, \"floating_rate\": 0.4811830666653652, \"group\": 3}, {\"region\": \"\\u798f\\u5efa\", \"a14-\": 0.1932, \"a15_59\": 0.647, \"a60+\": 0.1598, \"a65+\": 0.111, \"population\": 41540086, \"gdp\": 43903.9, \"edu_college\": 0.14148, \"edu_senior\": 0.14212, \"edu_junior\": 0.32218, \"edu_primary\": 0.28031, \"urba_2000\": 0.4157, \"urba_2005\": 0.494, \"urba_2010\": 0.5711, \"urba_2015\": 0.6032, \"urba_2019\": 0.6386, \"urba_over_40\": 1.0, \"urba_over_50\": 0.7, \"urba_over_60\": 0.25, \"urba_over_70\": null, \"floating_pop_inside\": 11574735.0, \"floating_pop_outside\": 4889876.0, \"gdp_avg\": 10.569044079494684, \"floating_inside_rate\": 0.2786401308846592, \"floating_outside_rate\": 0.11771463352290604, \"floating_rate\": 0.3963547644075653, \"group\": 3}, {\"region\": \"\\u6d77\\u5357\", \"a14-\": 0.1997, \"a15_59\": 0.6537999999999999, \"a60+\": 0.1465, \"a65+\": 0.1043, \"population\": 10081232, \"gdp\": 5532.4, \"edu_college\": 0.13919, \"edu_senior\": 0.15561, \"edu_junior\": 0.40174, \"edu_primary\": 0.19701, \"urba_2000\": 0.4011, \"urba_2005\": 0.4517, \"urba_2010\": 0.4983, \"urba_2015\": 0.5312, \"urba_2019\": 0.5628, \"urba_over_40\": 1.0, \"urba_over_50\": 0.4, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 2410018.0, \"floating_pop_outside\": 1088143.0, \"gdp_avg\": 5.48782132977398, \"floating_inside_rate\": 0.23905986887316946, \"floating_outside_rate\": 0.10793750208307873, \"floating_rate\": 0.3469973709562482, \"group\": 3}, {\"region\": \"\\u4e91\\u5357\", \"a14-\": 0.1957, \"a15_59\": 0.6552, \"a60+\": 0.1491, \"a65+\": 0.1075, \"population\": 47209277, \"gdp\": 24521.9, \"edu_college\": 0.11601, \"edu_senior\": 0.10338, \"edu_junior\": 0.29241, \"edu_primary\": 0.35667, \"urba_2000\": 0.2336, \"urba_2005\": 0.2951, \"urba_2010\": 0.347, \"urba_2015\": 0.4407, \"urba_2019\": 0.504, \"urba_over_40\": 0.35, \"urba_over_50\": 0.05, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 9978920.0, \"floating_pop_outside\": 2230394.0, \"gdp_avg\": 5.194296875167141, \"floating_inside_rate\": 0.21137625132450133, \"floating_outside_rate\": 0.04724482435941563, \"floating_rate\": 0.25862107568391696, \"group\": 3}, {\"region\": \"\\u897f\\u85cf\", \"a14-\": 0.24530000000000002, \"a15_59\": 0.6695, \"a60+\": 0.0852, \"a65+\": 0.0567, \"population\": 3648100, \"gdp\": 1902.7, \"edu_college\": 0.11019, \"edu_senior\": 0.07051, \"edu_junior\": 0.15757, \"edu_primary\": 0.32108, \"urba_2000\": 0.1893, \"urba_2005\": 0.2071, \"urba_2010\": 0.2267, \"urba_2015\": 0.2727, \"urba_2019\": 0.3075, \"urba_over_40\": null, \"urba_over_50\": null, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 624011.0, \"floating_pop_outside\": 407121.0, \"gdp_avg\": 5.215591677859708, \"floating_inside_rate\": 0.17105095803294865, \"floating_outside_rate\": 0.11159809215756147, \"floating_rate\": 0.2826490501905101, \"group\": 3}, {\"region\": \"\\u7518\\u8083\", \"a14-\": 0.19399999999999998, \"a15_59\": 0.6357, \"a60+\": 0.1703, \"a65+\": 0.1258, \"population\": 25019831, \"gdp\": 9016.7, \"edu_college\": 0.14506, \"edu_senior\": 0.12937, \"edu_junior\": 0.27423, \"edu_primary\": 0.29808, \"urba_2000\": 0.2401, \"urba_2005\": 0.3002, \"urba_2010\": 0.3613, \"urba_2015\": 0.4451, \"urba_2019\": 0.5118, \"urba_over_40\": 0.35, \"urba_over_50\": 0.1, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 6586817.0, \"floating_pop_outside\": 765648.0, \"gdp_avg\": 3.6038213047881897, \"floating_inside_rate\": 0.26326384858474866, \"floating_outside_rate\": 0.030601645550683378, \"floating_rate\": 0.293865494135432, \"group\": 3}, {\"region\": \"\\u9752\\u6d77\", \"a14-\": 0.20809999999999998, \"a15_59\": 0.6704000000000001, \"a60+\": 0.12140000000000001, \"a65+\": 0.0868, \"population\": 5923957, \"gdp\": 3005.9, \"edu_college\": 0.1488, \"edu_senior\": 0.10568, \"edu_junior\": 0.24344, \"edu_primary\": 0.32725, \"urba_2000\": 0.3476, \"urba_2005\": 0.3923, \"urba_2010\": 0.4476, \"urba_2015\": 0.513, \"urba_2019\": 0.5712, \"urba_over_40\": 0.65, \"urba_over_50\": 0.3, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 1653356.0, \"floating_pop_outside\": 417304.0, \"gdp_avg\": 5.074142165447858, \"floating_inside_rate\": 0.2790965565752756, \"floating_outside_rate\": 0.07044345527828781, \"floating_rate\": 0.3495400118535634, \"group\": 3}, {\"region\": \"\\u5b81\\u590f\", \"a14-\": 0.20379999999999998, \"a15_59\": 0.6609, \"a60+\": 0.1352, \"a65+\": 0.0962, \"population\": 7202654, \"gdp\": 3920.6, \"edu_college\": 0.1734, \"edu_senior\": 0.13432, \"edu_junior\": 0.29717, \"edu_primary\": 0.26111, \"urba_2000\": 0.3243, \"urba_2005\": 0.4228, \"urba_2010\": 0.4787, \"urba_2015\": 0.5395, \"urba_2019\": 0.5802, \"urba_over_40\": 0.75, \"urba_over_50\": 0.35, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 2687551.0, \"floating_pop_outside\": 675119.0, \"gdp_avg\": 5.443271327485674, \"floating_inside_rate\": 0.3731334310935941, \"floating_outside_rate\": 0.09373197712954141, \"floating_rate\": 0.46686540822313555, \"group\": 3}, {\"region\": \"\\u65b0\\u7586\", \"a14-\": 0.22460000000000002, \"a15_59\": 0.6626000000000001, \"a60+\": 0.1128, \"a65+\": 0.0776, \"population\": 25852345, \"gdp\": 13797.6, \"edu_college\": 0.16536, \"edu_senior\": 0.13208, \"edu_junior\": 0.31559, \"edu_primary\": 0.28405, \"urba_2000\": 0.3382, \"urba_2005\": 0.3716, \"urba_2010\": 0.4302, \"urba_2015\": 0.4675, \"urba_2019\": 0.5115, \"urba_over_40\": 0.5, \"urba_over_50\": 0.1, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 5476334.0, \"floating_pop_outside\": 3390712.0, \"gdp_avg\": 5.337078706012936, \"floating_inside_rate\": 0.21183122846302724, \"floating_outside_rate\": 0.13115684476591968, \"floating_rate\": 0.3429880732289469, \"group\": 3}]}}, {\"mode\": \"vega-lite\"});\n", "</script>"], "text/plain": ["alt.Chart(...)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["alt.Chart(gdf.reset_index()).mark_point().encode(\n", "    x=\"urba_over_50\",\n", "    y=\"a14-\",\n", "    color=\"group:N\",\n", "    tooltip=[\"region:N\",\"urba_over_50:Q\",\"a14-:Q\"]\n", ").properties(\n", "    title=\"城市化进度与14岁以下人口年龄结构比例的关系\"\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "D347926BAC364C019050908BD2B0539D", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["上图的横坐标为城市化比例超过50%的年份的距今系数，也就是 (2020 - 城市化首次比例超过50%的年份)/20，数值越大则越早实现城市化比例超过50%。纵坐标为年龄小于等于14岁的人口比例。  \n", "\n", "可以看到  \n", "\n", "- 第1组地区的特点是14岁以下人口比例较少，且普遍是城市化较早突破50%的地区。"]}, {"cell_type": "markdown", "metadata": {"id": "7B69840687FF48B4A7BF8D6F45FAB72B", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 流动人口对于人口年龄结构影响的观察"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"id": "F2450B7D6C11475D9FA53892CFFBD9EA", "jupyter": {}, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<div id=\"altair-viz-1fc62939def7463fbca021fd3f90b754\"></div>\n", "<script type=\"text/javascript\">\n", "  var VEGA_DEBUG = (typeof VEGA_DEBUG == \"undefined\") ? {} : VEGA_DEBUG;\n", "  (function(spec, embedOpt){\n", "    let outputDiv = document.currentScript.previousElementSibling;\n", "    if (outputDiv.id !== \"altair-viz-1fc62939def7463fbca021fd3f90b754\") {\n", "      outputDiv = document.getElementById(\"altair-viz-1fc62939def7463fbca021fd3f90b754\");\n", "    }\n", "    const paths = {\n", "      \"vega\": \"https://cdn.jsdelivr.net/npm//vega@5?noext\",\n", "      \"vega-lib\": \"https://cdn.jsdelivr.net/npm//vega-lib?noext\",\n", "      \"vega-lite\": \"https://cdn.jsdelivr.net/npm//vega-lite@4.17.0?noext\",\n", "      \"vega-embed\": \"https://cdn.jsdelivr.net/npm//vega-embed@6?noext\",\n", "    };\n", "\n", "    function maybeLoadScript(lib, version) {\n", "      var key = `${lib.replace(\"-\", \"\")}_version`;\n", "      return (VEGA_DEBUG[key] == version) ?\n", "        Promise.resolve(paths[lib]) :\n", "        new Promise(function(resolve, reject) {\n", "          var s = document.createElement('script');\n", "          document.getElementsByTagName(\"head\")[0].appendChild(s);\n", "          s.async = true;\n", "          s.onload = () => {\n", "            VEGA_DEBUG[key] = version;\n", "            return resolve(paths[lib]);\n", "          };\n", "          s.onerror = () => reject(`Error loading script: ${paths[lib]}`);\n", "          s.src = paths[lib];\n", "        });\n", "    }\n", "\n", "    function showError(err) {\n", "      outputDiv.innerHTML = `<div class=\"error\" style=\"color:red;\">${err}</div>`;\n", "      throw err;\n", "    }\n", "\n", "    function displayChart(vegaEmbed) {\n", "      vegaEmbed(outputDiv, spec, embedOpt)\n", "        .catch(err => showError(`Javascript Error: ${err.message}<br>This usually means there's a typo in your chart specification. See the javascript console for the full traceback.`));\n", "    }\n", "\n", "    if(typeof define === \"function\" && define.amd) {\n", "      requirejs.config({paths});\n", "      require([\"vega-embed\"], displayChart, err => showError(`Error loading script: ${err.message}`));\n", "    } else {\n", "      maybeLoadScript(\"vega\", \"5\")\n", "        .then(() => maybeLoadScript(\"vega-lite\", \"4.17.0\"))\n", "        .then(() => maybeLoadScript(\"vega-embed\", \"6\"))\n", "        .catch(showError)\n", "        .then(() => displayChart(vegaEmbed));\n", "    }\n", "  })({\"config\": {\"view\": {\"continuousWidth\": 400, \"continuousHeight\": 300}}, \"data\": {\"name\": \"data-c2bdb2c588da4eeb8056fc1ba73b45df\"}, \"mark\": \"point\", \"encoding\": {\"color\": {\"field\": \"group\", \"type\": \"nominal\"}, \"tooltip\": [{\"field\": \"region\", \"type\": \"ordinal\"}, {\"field\": \"floating_outside_rate\", \"type\": \"quantitative\"}, {\"field\": \"a14-\", \"type\": \"quantitative\"}], \"x\": {\"field\": \"floating_outside_rate\", \"type\": \"quantitative\"}, \"y\": {\"field\": \"a15_59\", \"scale\": {\"domain\": [0.5, 0.7]}, \"type\": \"quantitative\"}}, \"title\": \"\\u6d41\\u52a8\\u4eba\\u53e3\\u6bd4\\u4f8b\\u4e0e15\\u5c81\\u523059\\u5c81\\u4e4b\\u95f4\\u4eba\\u53e3\\u5e74\\u9f84\\u7ed3\\u6784\\u6bd4\\u4f8b\\u7684\\u5173\\u7cfb\", \"$schema\": \"https://vega.github.io/schema/vega-lite/v4.17.0.json\", \"datasets\": {\"data-c2bdb2c588da4eeb8056fc1ba73b45df\": [{\"region\": \"\\u5317\\u4eac\", \"a14-\": 0.1184, \"a15_59\": 0.6853, \"a60+\": 0.1963, \"a65+\": 0.133, \"population\": 21893095, \"gdp\": 36102.6, \"edu_college\": 0.4198, \"edu_senior\": 0.17593, \"edu_junior\": 0.23289, \"edu_primary\": 0.10503, \"urba_2000\": 0.7754, \"urba_2005\": 0.8362, \"urba_2010\": 0.8593, \"urba_2015\": 0.8579, \"urba_2019\": 0.8516, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 1.0, \"urba_over_70\": 1.0, \"floating_pop_inside\": 4991158.0, \"floating_pop_outside\": 8418418.0, \"gdp_avg\": 16.490404851392643, \"floating_inside_rate\": 0.22797863892702241, \"floating_outside_rate\": 0.3845238875545006, \"floating_rate\": 0.612502526481523, \"group\": 1}, {\"region\": \"\\u5929\\u6d25\", \"a14-\": 0.13470000000000001, \"a15_59\": 0.6487, \"a60+\": 0.21660000000000001, \"a65+\": 0.1475, \"population\": 13866009, \"gdp\": 14083.7, \"edu_college\": 0.2694, \"edu_senior\": 0.17719, \"edu_junior\": 0.32294, \"edu_primary\": 0.16123, \"urba_2000\": 0.7199, \"urba_2005\": 0.7507, \"urba_2010\": 0.796, \"urba_2015\": 0.8881, \"urba_2019\": 0.9415, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 1.0, \"urba_over_70\": 1.0, \"floating_pop_inside\": 2944879.0, \"floating_pop_outside\": 3534816.0, \"gdp_avg\": 10.156996147918266, \"floating_inside_rate\": 0.21238115451966028, \"floating_outside_rate\": 0.25492670601901385, \"floating_rate\": 0.4673078605386741, \"group\": 1}, {\"region\": \"\\u5185\\u8499\\u53e4\", \"a14-\": 0.1404, \"a15_59\": 0.6617000000000001, \"a60+\": 0.1978, \"a65+\": 0.1305, \"population\": 24049155, \"gdp\": 17359.8, \"edu_college\": 0.18688, \"edu_senior\": 0.14814, \"edu_junior\": 0.33861, \"edu_primary\": 0.23627, \"urba_2000\": 0.4268, \"urba_2005\": 0.4719, \"urba_2010\": 0.555, \"urba_2015\": 0.6205, \"urba_2019\": 0.6663, \"urba_over_40\": 1.0, \"urba_over_50\": 0.65, \"urba_over_60\": 0.3, \"urba_over_70\": null, \"floating_pop_inside\": 9776541.0, \"floating_pop_outside\": 1686420.0, \"gdp_avg\": 7.218465679979192, \"floating_inside_rate\": 0.40652326453881643, \"floating_outside_rate\": 0.07012387753332705, \"floating_rate\": 0.4766471420721435, \"group\": 1}, {\"region\": \"\\u8fbd\\u5b81\", \"a14-\": 0.1112, \"a15_59\": 0.6315999999999999, \"a60+\": 0.2572, \"a65+\": 0.17420000000000002, \"population\": 42591407, \"gdp\": 25115.0, \"edu_college\": 0.18216, \"edu_senior\": 0.1467, \"edu_junior\": 0.42799, \"edu_primary\": 0.18888, \"urba_2000\": 0.5424, \"urba_2005\": 0.5871, \"urba_2010\": 0.621, \"urba_2015\": 0.6805, \"urba_2019\": 0.693, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 0.6, \"urba_over_70\": null, \"floating_pop_inside\": 12822813.0, \"floating_pop_outside\": 2847308.0, \"gdp_avg\": 5.896729356698641, \"floating_inside_rate\": 0.3010657290565677, \"floating_outside_rate\": 0.06685170086069239, \"floating_rate\": 0.36791742991726006, \"group\": 1}, {\"region\": \"\\u5409\\u6797\", \"a14-\": 0.11710000000000001, \"a15_59\": 0.6523, \"a60+\": 0.2306, \"a65+\": 0.1561, \"population\": 24073453, \"gdp\": 12311.3, \"edu_college\": 0.16738, \"edu_senior\": 0.1708, \"edu_junior\": 0.38234, \"edu_primary\": 0.22318, \"urba_2000\": 0.4968, \"urba_2005\": 0.525, \"urba_2010\": 0.5333, \"urba_2015\": 0.5829, \"urba_2019\": 0.6405, \"urba_over_40\": 1.0, \"urba_over_50\": 0.75, \"urba_over_60\": 0.15, \"urba_over_70\": null, \"floating_pop_inside\": 9349212.0, \"floating_pop_outside\": 1001471.0, \"gdp_avg\": 5.114056550175831, \"floating_inside_rate\": 0.3883619022165204, \"floating_outside_rate\": 0.041600637847840106, \"floating_rate\": 0.4299625400643605, \"group\": 1}, {\"region\": \"\\u9ed1\\u9f99\\u6c5f\", \"a14-\": 0.1032, \"a15_59\": 0.6646, \"a60+\": 0.2322, \"a65+\": 0.1561, \"population\": 31850088, \"gdp\": 13698.5, \"edu_college\": 0.14793, \"edu_senior\": 0.15525, \"edu_junior\": 0.42793, \"edu_primary\": 0.21863, \"urba_2000\": 0.5154, \"urba_2005\": 0.5309, \"urba_2010\": 0.5567, \"urba_2015\": 0.635, \"urba_2019\": 0.7017, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 0.35, \"urba_over_70\": 0.05, \"floating_pop_inside\": 10720408.0, \"floating_pop_outside\": 829176.0, \"gdp_avg\": 4.300930031967258, \"floating_inside_rate\": 0.3365895880727237, \"floating_outside_rate\": 0.026033711429619914, \"floating_rate\": 0.3626232995023436, \"group\": 1}, {\"region\": \"\\u4e0a\\u6d77\", \"a14-\": 0.098, \"a15_59\": 0.6681999999999999, \"a60+\": 0.23379999999999998, \"a65+\": 0.1628, \"population\": 24870895, \"gdp\": 38700.6, \"edu_college\": 0.33872, \"edu_senior\": 0.1902, \"edu_junior\": 0.28935, \"edu_primary\": 0.11929, \"urba_2000\": 0.8831, \"urba_2005\": 0.891, \"urba_2010\": 0.8927, \"urba_2015\": 0.8609, \"urba_2019\": 0.8642, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 1.0, \"urba_over_70\": 1.0, \"floating_pop_inside\": 4654606.0, \"floating_pop_outside\": 10479652.0, \"gdp_avg\": 15.560598040400235, \"floating_inside_rate\": 0.18715072376768105, \"floating_outside_rate\": 0.4213620780434319, \"floating_rate\": 0.608512801811113, \"group\": 1}, {\"region\": \"\\u6d59\\u6c5f\", \"a14-\": 0.13449999999999998, \"a15_59\": 0.6786, \"a60+\": 0.187, \"a65+\": 0.13269999999999998, \"population\": 64567588, \"gdp\": 64613.3, \"edu_college\": 0.1699, \"edu_senior\": 0.14555, \"edu_junior\": 0.32706, \"edu_primary\": 0.26384, \"urba_2000\": 0.4867, \"urba_2005\": 0.5602, \"urba_2010\": 0.6161, \"urba_2015\": 0.609, \"urba_2019\": 0.6424, \"urba_over_40\": 1.0, \"urba_over_50\": 0.75, \"urba_over_60\": 0.5, \"urba_over_70\": null, \"floating_pop_inside\": 13921361.0, \"floating_pop_outside\": 16186454.0, \"gdp_avg\": 10.007079713121698, \"floating_inside_rate\": 0.21560912264525042, \"floating_outside_rate\": 0.2506900830800742, \"floating_rate\": 0.46629920572532463, \"group\": 1}, {\"region\": \"\\u9655\\u897f\", \"a14-\": 0.17329999999999998, \"a15_59\": 0.6346, \"a60+\": 0.192, \"a65+\": 0.1332, \"population\": 39528999, \"gdp\": 26181.9, \"edu_college\": 0.18397, \"edu_senior\": 0.15581, \"edu_junior\": 0.33979, \"edu_primary\": 0.21686, \"urba_2000\": 0.3226, \"urba_2005\": 0.3724, \"urba_2010\": 0.4576, \"urba_2015\": 0.5317, \"urba_2019\": 0.5842, \"urba_over_40\": 0.65, \"urba_over_50\": 0.35, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 11333383.0, \"floating_pop_outside\": 1933712.0, \"gdp_avg\": 6.623466483429039, \"floating_inside_rate\": 0.286710599476602, \"floating_outside_rate\": 0.04891882033238434, \"floating_rate\": 0.3356294198089863, \"group\": 2}, {\"region\": \"\\u8d35\\u5dde\", \"a14-\": 0.2397, \"a15_59\": 0.6065, \"a60+\": 0.15380000000000002, \"a65+\": 0.11560000000000001, \"population\": 38562148, \"gdp\": 17826.6, \"edu_college\": 0.10952, \"edu_senior\": 0.09951, \"edu_junior\": 0.30464, \"edu_primary\": 0.31921, \"urba_2000\": 0.2387, \"urba_2005\": 0.2686, \"urba_2010\": 0.338, \"urba_2015\": 0.3999, \"urba_2019\": 0.4615, \"urba_over_40\": 0.2, \"urba_over_50\": null, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 10548217.0, \"floating_pop_outside\": 1146546.0, \"gdp_avg\": 4.622823396663485, \"floating_inside_rate\": 0.27353810788755856, \"floating_outside_rate\": 0.02973242050728087, \"floating_rate\": 0.3032705283948394, \"group\": 2}, {\"region\": \"\\u56db\\u5ddd\", \"a14-\": 0.161, \"a15_59\": 0.6219, \"a60+\": 0.21710000000000002, \"a65+\": 0.1693, \"population\": 83674866, \"gdp\": 48598.8, \"edu_college\": 0.13267, \"edu_senior\": 0.13301, \"edu_junior\": 0.31443, \"edu_primary\": 0.31317, \"urba_2000\": 0.2669, \"urba_2005\": 0.33, \"urba_2010\": 0.4017, \"urba_2015\": 0.4773, \"urba_2019\": 0.5395, \"urba_over_40\": 0.5, \"urba_over_50\": 0.15, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 25233163.0, \"floating_pop_outside\": 2590041.0, \"gdp_avg\": 5.808052324816391, \"floating_inside_rate\": 0.3015620365618512, \"floating_outside_rate\": 0.030953631882720913, \"floating_rate\": 0.3325156684445721, \"group\": 2}, {\"region\": \"\\u91cd\\u5e86\", \"a14-\": 0.1591, \"a15_59\": 0.6222, \"a60+\": 0.2187, \"a65+\": 0.17079999999999998, \"population\": 32054159, \"gdp\": 25002.8, \"edu_college\": 0.15412, \"edu_senior\": 0.15956, \"edu_junior\": 0.30582, \"edu_primary\": 0.29894, \"urba_2000\": 0.3309, \"urba_2005\": 0.4521, \"urba_2010\": 0.53, \"urba_2015\": 0.5987, \"urba_2019\": 0.6546, \"urba_over_40\": 0.75, \"urba_over_50\": 0.55, \"urba_over_60\": 0.2, \"urba_over_70\": null, \"floating_pop_inside\": 10902860.0, \"floating_pop_outside\": 2193575.0, \"gdp_avg\": 7.8001734501909725, \"floating_inside_rate\": 0.3401387008780982, \"floating_outside_rate\": 0.06843339736350594, \"floating_rate\": 0.4085720982416042, \"group\": 2}, {\"region\": \"\\u5e7f\\u897f\", \"a14-\": 0.23629999999999998, \"a15_59\": 0.5969, \"a60+\": 0.16690000000000002, \"a65+\": 0.122, \"population\": 50126804, \"gdp\": 22156.7, \"edu_college\": 0.10806, \"edu_senior\": 0.12962, \"edu_junior\": 0.36388, \"edu_primary\": 0.27855, \"urba_2000\": 0.2815, \"urba_2005\": 0.3363, \"urba_2010\": 0.4, \"urba_2015\": 0.4691, \"urba_2019\": 0.5086, \"urba_over_40\": 0.45, \"urba_over_50\": 0.1, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 11879397.0, \"floating_pop_outside\": 1359384.0, \"gdp_avg\": 4.420130196211991, \"floating_inside_rate\": 0.23698692220633097, \"floating_outside_rate\": 0.02711890428921022, \"floating_rate\": 0.2641058264955412, \"group\": 2}, {\"region\": \"\\u6e56\\u5357\", \"a14-\": 0.19519999999999998, \"a15_59\": 0.606, \"a60+\": 0.19879999999999998, \"a65+\": 0.1481, \"population\": 66444864, \"gdp\": 41781.5, \"edu_college\": 0.12239, \"edu_senior\": 0.17776, \"edu_junior\": 0.35636, \"edu_primary\": 0.25214, \"urba_2000\": 0.2975, \"urba_2005\": 0.3701, \"urba_2010\": 0.433, \"urba_2015\": 0.5218, \"urba_2019\": 0.5962, \"urba_over_40\": 0.65, \"urba_over_50\": 0.3, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 15998284.0, \"floating_pop_outside\": 1577563.0, \"gdp_avg\": 6.288145912978315, \"floating_inside_rate\": 0.24077532915109887, \"floating_outside_rate\": 0.023742437037721983, \"floating_rate\": 0.26451776618882084, \"group\": 2}, {\"region\": \"\\u6e56\\u5317\", \"a14-\": 0.1631, \"a15_59\": 0.6325999999999999, \"a60+\": 0.20420000000000002, \"a65+\": 0.1459, \"population\": 57752557, \"gdp\": 43443.5, \"edu_college\": 0.15502, \"edu_senior\": 0.17428, \"edu_junior\": 0.3428, \"edu_primary\": 0.2352, \"urba_2000\": 0.4022, \"urba_2005\": 0.432, \"urba_2010\": 0.497, \"urba_2015\": 0.5687, \"urba_2019\": 0.6099, \"urba_over_40\": 1.0, \"urba_over_50\": 0.45, \"urba_over_60\": 0.1, \"urba_over_70\": null, \"floating_pop_inside\": 16226947.0, \"floating_pop_outside\": 2249614.0, \"gdp_avg\": 7.522350915129178, \"floating_inside_rate\": 0.2809736545517803, \"floating_outside_rate\": 0.038952630270552346, \"floating_rate\": 0.31992628482233265, \"group\": 2}, {\"region\": \"\\u6cb3\\u5357\", \"a14-\": 0.2314, \"a15_59\": 0.5879, \"a60+\": 0.1808, \"a65+\": 0.1349, \"population\": 99365519, \"gdp\": 54997.1, \"edu_college\": 0.11744, \"edu_senior\": 0.15239, \"edu_junior\": 0.37518, \"edu_primary\": 0.24557, \"urba_2000\": 0.232, \"urba_2005\": 0.3065, \"urba_2010\": 0.385, \"urba_2015\": 0.4578, \"urba_2019\": 0.518, \"urba_over_40\": 0.45, \"urba_over_50\": 0.1, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 24365959.0, \"floating_pop_outside\": 1273646.0, \"gdp_avg\": 5.534827428416088, \"floating_inside_rate\": 0.2452154353463398, \"floating_outside_rate\": 0.012817786419452004, \"floating_rate\": 0.2580332217657918, \"group\": 2}, {\"region\": \"\\u5c71\\u4e1c\", \"a14-\": 0.18780000000000002, \"a15_59\": 0.6032, \"a60+\": 0.209, \"a65+\": 0.15130000000000002, \"population\": 101527453, \"gdp\": 73129.0, \"edu_college\": 0.14384, \"edu_senior\": 0.14334, \"edu_junior\": 0.35778, \"edu_primary\": 0.23693, \"urba_2000\": 0.38, \"urba_2005\": 0.45, \"urba_2010\": 0.497, \"urba_2015\": 0.569, \"urba_2019\": 0.6129, \"urba_over_40\": 0.75, \"urba_over_50\": 0.45, \"urba_over_60\": 0.15, \"urba_over_70\": null, \"floating_pop_inside\": 23897755.0, \"floating_pop_outside\": 4129007.0, \"gdp_avg\": 7.202879402480431, \"floating_inside_rate\": 0.2353821975618752, \"floating_outside_rate\": 0.04066887209314706, \"floating_rate\": 0.2760510696550223, \"group\": 2}, {\"region\": \"\\u6c5f\\u897f\", \"a14-\": 0.21960000000000002, \"a15_59\": 0.6117, \"a60+\": 0.16870000000000002, \"a65+\": 0.1189, \"population\": 45188635, \"gdp\": 25691.5, \"edu_college\": 0.11897, \"edu_senior\": 0.15145, \"edu_junior\": 0.35501, \"edu_primary\": 0.27514, \"urba_2000\": 0.2767, \"urba_2005\": 0.37, \"urba_2010\": 0.4406, \"urba_2015\": 0.5255, \"urba_2019\": 0.5932, \"urba_over_40\": 0.6, \"urba_over_50\": 0.3, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 12241920.0, \"floating_pop_outside\": 1279014.0, \"gdp_avg\": 5.685389700308495, \"floating_inside_rate\": 0.27090705439542484, \"floating_outside_rate\": 0.028303886585642608, \"floating_rate\": 0.2992109409810675, \"group\": 2}, {\"region\": \"\\u5b89\\u5fbd\", \"a14-\": 0.1924, \"a15_59\": 0.6196, \"a60+\": 0.18789999999999998, \"a65+\": 0.1501, \"population\": 61027171, \"gdp\": 38680.6, \"edu_college\": 0.1328, \"edu_senior\": 0.13294, \"edu_junior\": 0.33724, \"edu_primary\": 0.26875, \"urba_2000\": 0.2781, \"urba_2005\": 0.3551, \"urba_2010\": 0.4301, \"urba_2015\": 0.5162, \"urba_2019\": 0.5832, \"urba_over_40\": 0.6, \"urba_over_50\": 0.25, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 16549409.0, \"floating_pop_outside\": 1550509.0, \"gdp_avg\": 6.338258740520677, \"floating_inside_rate\": 0.27118099575679167, \"floating_outside_rate\": 0.025406863444481148, \"floating_rate\": 0.29658785920127284, \"group\": 2}, {\"region\": \"\\u6c5f\\u82cf\", \"a14-\": 0.1521, \"a15_59\": 0.6295000000000001, \"a60+\": 0.2184, \"a65+\": 0.162, \"population\": 84748016, \"gdp\": 102719.0, \"edu_college\": 0.18663, \"edu_senior\": 0.16191, \"edu_junior\": 0.33308, \"edu_primary\": 0.22742, \"urba_2000\": 0.4149, \"urba_2005\": 0.505, \"urba_2010\": 0.6058, \"urba_2015\": 0.6381, \"urba_2019\": 0.6728, \"urba_over_40\": 1.0, \"urba_over_50\": 0.75, \"urba_over_60\": 0.5, \"urba_over_70\": null, \"floating_pop_inside\": 19671338.0, \"floating_pop_outside\": 10308610.0, \"gdp_avg\": 12.120519729925006, \"floating_inside_rate\": 0.23211561672429004, \"floating_outside_rate\": 0.12163836378187308, \"floating_rate\": 0.35375398050616313, \"group\": 2}, {\"region\": \"\\u5c71\\u897f\", \"a14-\": 0.1635, \"a15_59\": 0.6472, \"a60+\": 0.1892, \"a65+\": 0.129, \"population\": 34915616, \"gdp\": 17651.9, \"edu_college\": 0.17358, \"edu_senior\": 0.16485, \"edu_junior\": 0.3895, \"edu_primary\": 0.19506, \"urba_2000\": 0.3491, \"urba_2005\": 0.4212, \"urba_2010\": 0.4804, \"urba_2015\": 0.5729, \"urba_2019\": 0.6351, \"urba_over_40\": 0.75, \"urba_over_50\": 0.45, \"urba_over_60\": 0.15, \"urba_over_70\": null, \"floating_pop_inside\": 11270656.0, \"floating_pop_outside\": 1620518.0, \"gdp_avg\": 5.055588880345115, \"floating_inside_rate\": 0.3227969971946077, \"floating_outside_rate\": 0.046412413288082904, \"floating_rate\": 0.3692094104826906, \"group\": 2}, {\"region\": \"\\u6cb3\\u5317\", \"a14-\": 0.2022, \"a15_59\": 0.5992000000000001, \"a60+\": 0.1985, \"a65+\": 0.1392, \"population\": 74610235, \"gdp\": 36206.9, \"edu_college\": 0.12418, \"edu_senior\": 0.13861, \"edu_junior\": 0.3995, \"edu_primary\": 0.24664, \"urba_2000\": 0.2608, \"urba_2005\": 0.3769, \"urba_2010\": 0.445, \"urba_2015\": 0.5189, \"urba_2019\": 0.5874, \"urba_over_40\": 0.65, \"urba_over_50\": 0.25, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 16620369.0, \"floating_pop_outside\": 3155272.0, \"gdp_avg\": 4.852806052681646, \"floating_inside_rate\": 0.22276258746537925, \"floating_outside_rate\": 0.04229006918420777, \"floating_rate\": 0.265052656649587, \"group\": 2}, {\"region\": \"\\u5e7f\\u4e1c\", \"a14-\": 0.1885, \"a15_59\": 0.688, \"a60+\": 0.1235, \"a65+\": 0.0858, \"population\": 126012510, \"gdp\": 110760.9, \"edu_college\": 0.15699, \"edu_senior\": 0.18224, \"edu_junior\": 0.35484, \"edu_primary\": 0.20676, \"urba_2000\": 0.55, \"urba_2005\": 0.6068, \"urba_2010\": 0.6618, \"urba_2015\": 0.6383, \"urba_2019\": 0.6587, \"urba_over_40\": 1.0, \"urba_over_50\": 1.0, \"urba_over_60\": 0.75, \"urba_over_70\": null, \"floating_pop_inside\": 31012976.0, \"floating_pop_outside\": 29622110.0, \"gdp_avg\": 8.789674929893865, \"floating_inside_rate\": 0.24611029492230574, \"floating_outside_rate\": 0.2350727717430595, \"floating_rate\": 0.4811830666653652, \"group\": 3}, {\"region\": \"\\u798f\\u5efa\", \"a14-\": 0.1932, \"a15_59\": 0.647, \"a60+\": 0.1598, \"a65+\": 0.111, \"population\": 41540086, \"gdp\": 43903.9, \"edu_college\": 0.14148, \"edu_senior\": 0.14212, \"edu_junior\": 0.32218, \"edu_primary\": 0.28031, \"urba_2000\": 0.4157, \"urba_2005\": 0.494, \"urba_2010\": 0.5711, \"urba_2015\": 0.6032, \"urba_2019\": 0.6386, \"urba_over_40\": 1.0, \"urba_over_50\": 0.7, \"urba_over_60\": 0.25, \"urba_over_70\": null, \"floating_pop_inside\": 11574735.0, \"floating_pop_outside\": 4889876.0, \"gdp_avg\": 10.569044079494684, \"floating_inside_rate\": 0.2786401308846592, \"floating_outside_rate\": 0.11771463352290604, \"floating_rate\": 0.3963547644075653, \"group\": 3}, {\"region\": \"\\u6d77\\u5357\", \"a14-\": 0.1997, \"a15_59\": 0.6537999999999999, \"a60+\": 0.1465, \"a65+\": 0.1043, \"population\": 10081232, \"gdp\": 5532.4, \"edu_college\": 0.13919, \"edu_senior\": 0.15561, \"edu_junior\": 0.40174, \"edu_primary\": 0.19701, \"urba_2000\": 0.4011, \"urba_2005\": 0.4517, \"urba_2010\": 0.4983, \"urba_2015\": 0.5312, \"urba_2019\": 0.5628, \"urba_over_40\": 1.0, \"urba_over_50\": 0.4, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 2410018.0, \"floating_pop_outside\": 1088143.0, \"gdp_avg\": 5.48782132977398, \"floating_inside_rate\": 0.23905986887316946, \"floating_outside_rate\": 0.10793750208307873, \"floating_rate\": 0.3469973709562482, \"group\": 3}, {\"region\": \"\\u4e91\\u5357\", \"a14-\": 0.1957, \"a15_59\": 0.6552, \"a60+\": 0.1491, \"a65+\": 0.1075, \"population\": 47209277, \"gdp\": 24521.9, \"edu_college\": 0.11601, \"edu_senior\": 0.10338, \"edu_junior\": 0.29241, \"edu_primary\": 0.35667, \"urba_2000\": 0.2336, \"urba_2005\": 0.2951, \"urba_2010\": 0.347, \"urba_2015\": 0.4407, \"urba_2019\": 0.504, \"urba_over_40\": 0.35, \"urba_over_50\": 0.05, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 9978920.0, \"floating_pop_outside\": 2230394.0, \"gdp_avg\": 5.194296875167141, \"floating_inside_rate\": 0.21137625132450133, \"floating_outside_rate\": 0.04724482435941563, \"floating_rate\": 0.25862107568391696, \"group\": 3}, {\"region\": \"\\u897f\\u85cf\", \"a14-\": 0.24530000000000002, \"a15_59\": 0.6695, \"a60+\": 0.0852, \"a65+\": 0.0567, \"population\": 3648100, \"gdp\": 1902.7, \"edu_college\": 0.11019, \"edu_senior\": 0.07051, \"edu_junior\": 0.15757, \"edu_primary\": 0.32108, \"urba_2000\": 0.1893, \"urba_2005\": 0.2071, \"urba_2010\": 0.2267, \"urba_2015\": 0.2727, \"urba_2019\": 0.3075, \"urba_over_40\": null, \"urba_over_50\": null, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 624011.0, \"floating_pop_outside\": 407121.0, \"gdp_avg\": 5.215591677859708, \"floating_inside_rate\": 0.17105095803294865, \"floating_outside_rate\": 0.11159809215756147, \"floating_rate\": 0.2826490501905101, \"group\": 3}, {\"region\": \"\\u7518\\u8083\", \"a14-\": 0.19399999999999998, \"a15_59\": 0.6357, \"a60+\": 0.1703, \"a65+\": 0.1258, \"population\": 25019831, \"gdp\": 9016.7, \"edu_college\": 0.14506, \"edu_senior\": 0.12937, \"edu_junior\": 0.27423, \"edu_primary\": 0.29808, \"urba_2000\": 0.2401, \"urba_2005\": 0.3002, \"urba_2010\": 0.3613, \"urba_2015\": 0.4451, \"urba_2019\": 0.5118, \"urba_over_40\": 0.35, \"urba_over_50\": 0.1, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 6586817.0, \"floating_pop_outside\": 765648.0, \"gdp_avg\": 3.6038213047881897, \"floating_inside_rate\": 0.26326384858474866, \"floating_outside_rate\": 0.030601645550683378, \"floating_rate\": 0.293865494135432, \"group\": 3}, {\"region\": \"\\u9752\\u6d77\", \"a14-\": 0.20809999999999998, \"a15_59\": 0.6704000000000001, \"a60+\": 0.12140000000000001, \"a65+\": 0.0868, \"population\": 5923957, \"gdp\": 3005.9, \"edu_college\": 0.1488, \"edu_senior\": 0.10568, \"edu_junior\": 0.24344, \"edu_primary\": 0.32725, \"urba_2000\": 0.3476, \"urba_2005\": 0.3923, \"urba_2010\": 0.4476, \"urba_2015\": 0.513, \"urba_2019\": 0.5712, \"urba_over_40\": 0.65, \"urba_over_50\": 0.3, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 1653356.0, \"floating_pop_outside\": 417304.0, \"gdp_avg\": 5.074142165447858, \"floating_inside_rate\": 0.2790965565752756, \"floating_outside_rate\": 0.07044345527828781, \"floating_rate\": 0.3495400118535634, \"group\": 3}, {\"region\": \"\\u5b81\\u590f\", \"a14-\": 0.20379999999999998, \"a15_59\": 0.6609, \"a60+\": 0.1352, \"a65+\": 0.0962, \"population\": 7202654, \"gdp\": 3920.6, \"edu_college\": 0.1734, \"edu_senior\": 0.13432, \"edu_junior\": 0.29717, \"edu_primary\": 0.26111, \"urba_2000\": 0.3243, \"urba_2005\": 0.4228, \"urba_2010\": 0.4787, \"urba_2015\": 0.5395, \"urba_2019\": 0.5802, \"urba_over_40\": 0.75, \"urba_over_50\": 0.35, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 2687551.0, \"floating_pop_outside\": 675119.0, \"gdp_avg\": 5.443271327485674, \"floating_inside_rate\": 0.3731334310935941, \"floating_outside_rate\": 0.09373197712954141, \"floating_rate\": 0.46686540822313555, \"group\": 3}, {\"region\": \"\\u65b0\\u7586\", \"a14-\": 0.22460000000000002, \"a15_59\": 0.6626000000000001, \"a60+\": 0.1128, \"a65+\": 0.0776, \"population\": 25852345, \"gdp\": 13797.6, \"edu_college\": 0.16536, \"edu_senior\": 0.13208, \"edu_junior\": 0.31559, \"edu_primary\": 0.28405, \"urba_2000\": 0.3382, \"urba_2005\": 0.3716, \"urba_2010\": 0.4302, \"urba_2015\": 0.4675, \"urba_2019\": 0.5115, \"urba_over_40\": 0.5, \"urba_over_50\": 0.1, \"urba_over_60\": null, \"urba_over_70\": null, \"floating_pop_inside\": 5476334.0, \"floating_pop_outside\": 3390712.0, \"gdp_avg\": 5.337078706012936, \"floating_inside_rate\": 0.21183122846302724, \"floating_outside_rate\": 0.13115684476591968, \"floating_rate\": 0.3429880732289469, \"group\": 3}]}}, {\"mode\": \"vega-lite\"});\n", "</script>"], "text/plain": ["alt.Chart(...)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["alt.Chart(gdf.reset_index()).mark_point().encode(\n", "    alt.Y(\"a15_59\",scale=alt.Scale(domain=[0.5,0.7])),\n", "    x=\"floating_outside_rate\",\n", "    color=\"group:N\",\n", "    tooltip=[\"region:O\",\"floating_outside_rate:Q\",\"a14-:Q\"]\n", ").properties(\n", "    title=\"流动人口比例与15岁到59岁之间人口年龄结构比例的关系\"\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "6EECDF0AD77E4A1C917E17D164AFE722", "jupyter": {}, "mdEditEnable": false, "notebookId": "6421a6ec1b0fc2b8b6bdfa53", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["上图的横坐标为流动人口比例，纵坐标为年龄在14岁到59岁的人口比例。  \n", "\n", "因为第2组和第3组的区别主要在于15岁到59岁之间的人口年龄比例，所以我们重点看这两组的区别。可以发现：  \n", "- 15岁到59岁之间人口年龄比例更高的第3组，其流动人口比例也普遍更高。  \n", "\n", "## 结束"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "384px"}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 1}
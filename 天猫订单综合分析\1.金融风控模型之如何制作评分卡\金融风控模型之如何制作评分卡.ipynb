{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<img src=\"msb.png\" width = \"800\" height = \"400\" div align=center />"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <center> 金融风控模型之如何制作评分卡"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##  一、课前准备\n", "### 1.1 熟悉Python的数据分析库numpy、pandas和scikit算法库\n", "\n", "### 1. 2 熟悉逻辑回归和随机森林算法\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 二、课堂主题\n", "\n", "- 在银行借贷场景中，评分卡是一种以分数形式来衡量一个客户的信用风险大小的手段，它衡量向别人借钱的人（受信人，需要融资的公司）不能如期履行合同中的还本付息责任，并让借钱给别人的人（授信人，银行等金融机构）， 造成经济损失的可能性。一般来说，评分卡打出的分数越高，客户的信用越好，风险越小。 \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 这些”借钱的人“，可能是个人，有可能是有需求的公司和企业。对于企业来说，我们按照融资主体的融资用途，分 \n", "别使用企业融资模型，现金流融资模型，项目融资模型等模型。而对于个人来说，我们有”四张卡“来评判个人的信用程度：A卡，B卡，C卡和F卡。而众人常说的“评分卡”其实是指A卡，又称为申请者评级模型，主要应用于相关融资类业务中新用户的主体评级，即判断金融机构是否应该借钱给一个新用户，如果这个人的风险太高，我们可以拒 绝贷款。 "]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["![image.png](attachment:image.png)"]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": [" ![image.png](attachment:image.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 三、课堂目标\n", "-  能够使用RF算法对缺失值进行补充\n", "\n", "-  能够掌握样本不平衡问题\n", "\n", "-  熟练掌握评分卡的分箱操作"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 四、知识要点"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 4.1 探索数据"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.1.1 导库/获取数据"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%matplotlib inline \n", "import numpy as np \n", "import pandas as pd "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SeriousDlqin2yrs</th>\n", "      <th>RevolvingUtilizationOfUnsecuredLines</th>\n", "      <th>age</th>\n", "      <th>NumberOfTime30-59DaysPastDueNotWorse</th>\n", "      <th>DebtRatio</th>\n", "      <th>MonthlyIncome</th>\n", "      <th>NumberOfOpenCreditLinesAndLoans</th>\n", "      <th>NumberOfTimes90DaysLate</th>\n", "      <th>NumberRealEstateLoansOrLines</th>\n", "      <th>NumberOfTime60-89DaysPastDueNotWorse</th>\n", "      <th>NumberOfDependents</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>0.766127</td>\n", "      <td>45</td>\n", "      <td>2</td>\n", "      <td>0.802982</td>\n", "      <td>9120.0</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>0.957151</td>\n", "      <td>40</td>\n", "      <td>0</td>\n", "      <td>0.121876</td>\n", "      <td>2600.0</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>0.658180</td>\n", "      <td>38</td>\n", "      <td>1</td>\n", "      <td>0.085113</td>\n", "      <td>3042.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>0.233810</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>0.036050</td>\n", "      <td>3300.0</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0</td>\n", "      <td>0.907239</td>\n", "      <td>49</td>\n", "      <td>1</td>\n", "      <td>0.024926</td>\n", "      <td>63588.0</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   SeriousDlqin2yrs  RevolvingUtilizationOfUnsecuredLines  age  \\\n", "1                 1                              0.766127   45   \n", "2                 0                              0.957151   40   \n", "3                 0                              0.658180   38   \n", "4                 0                              0.233810   30   \n", "5                 0                              0.907239   49   \n", "\n", "   NumberOfTime30-59DaysPastDueNotWorse  DebtRatio  MonthlyIncome  \\\n", "1                                     2   0.802982         9120.0   \n", "2                                     0   0.121876         2600.0   \n", "3                                     1   0.085113         3042.0   \n", "4                                     0   0.036050         3300.0   \n", "5                                     1   0.024926        63588.0   \n", "\n", "   NumberOfOpenCreditLinesAndLoans  NumberOfTimes90DaysLate  \\\n", "1                               13                        0   \n", "2                                4                        0   \n", "3                                2                        1   \n", "4                                5                        0   \n", "5                                7                        0   \n", "\n", "   NumberRealEstateLoansOrLines  NumberOfTime60-89DaysPastDueNotWorse  \\\n", "1                             6                                     0   \n", "2                             0                                     0   \n", "3                             0                                     0   \n", "4                             0                                     0   \n", "5                             1                                     0   \n", "\n", "   NumberOfDependents  \n", "1                 2.0  \n", "2                 1.0  \n", "3                 0.0  \n", "4                 0.0  \n", "5                 0.0  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_csv(r\"Acard.csv\",index_col=0)\n", "#观察数据类型 \n", "data.head() "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(150000, 11)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["#观察数据结构 \n", "data.shape"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 150000 entries, 1 to 150000\n", "Data columns (total 11 columns):\n", " #   Column                                Non-Null Count   Dtype  \n", "---  ------                                --------------   -----  \n", " 0   SeriousDlqin2yrs                      150000 non-null  int64  \n", " 1   RevolvingUtilizationOfUnsecuredLines  150000 non-null  float64\n", " 2   age                                   150000 non-null  int64  \n", " 3   NumberOfTime30-59DaysPastDueNotWorse  150000 non-null  int64  \n", " 4   DebtRatio                             150000 non-null  float64\n", " 5   MonthlyIncome                         120269 non-null  float64\n", " 6   NumberOfOpenCreditLinesAndLoans       150000 non-null  int64  \n", " 7   NumberOfTimes90DaysLate               150000 non-null  int64  \n", " 8   NumberRealEstateLoansOrLines          150000 non-null  int64  \n", " 9   NumberOfTime60-89DaysPastDueNotWorse  150000 non-null  int64  \n", " 10  NumberOfDependents                    146076 non-null  float64\n", "dtypes: float64(4), int64(7)\n", "memory usage: 13.7 MB\n"]}], "source": ["data.info() # 每列的缺失值情况"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.1.2 去重复值"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["data.drop_duplicates(inplace=True) "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 149391 entries, 0 to 149390\n", "Data columns (total 11 columns):\n", " #   Column                                Non-Null Count   Dtype  \n", "---  ------                                --------------   -----  \n", " 0   SeriousDlqin2yrs                      149391 non-null  int64  \n", " 1   RevolvingUtilizationOfUnsecuredLines  149391 non-null  float64\n", " 2   age                                   149391 non-null  int64  \n", " 3   NumberOfTime30-59DaysPastDueNotWorse  149391 non-null  int64  \n", " 4   DebtRatio                             149391 non-null  float64\n", " 5   MonthlyIncome                         120170 non-null  float64\n", " 6   NumberOfOpenCreditLinesAndLoans       149391 non-null  int64  \n", " 7   NumberOfTimes90DaysLate               149391 non-null  int64  \n", " 8   NumberRealEstateLoansOrLines          149391 non-null  int64  \n", " 9   NumberOfTime60-89DaysPastDueNotWorse  149391 non-null  int64  \n", " 10  NumberOfDependents                    145563 non-null  float64\n", "dtypes: float64(4), int64(7)\n", "memory usage: 12.5 MB\n"]}], "source": ["data.index = range(data.shape[0]) \n", "  \n", "data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.1.3 填补缺失值"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["SeriousDlqin2yrs                        0.000000\n", "RevolvingUtilizationOfUnsecuredLines    0.000000\n", "age                                     0.000000\n", "NumberOfTime30-59DaysPastDueNotWorse    0.000000\n", "DebtRatio                               0.000000\n", "MonthlyIncome                           0.195601\n", "NumberOfOpenCreditLinesAndLoans         0.000000\n", "NumberOfTimes90DaysLate                 0.000000\n", "NumberRealEstateLoansOrLines            0.000000\n", "NumberOfTime60-89DaysPastDueNotWorse    0.000000\n", "NumberOfDependents                      0.025624\n", "dtype: float64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["data.isnull().sum()/data.shape[0]  # data.isnull().mean() "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["SeriousDlqin2yrs                        0.000000\n", "RevolvingUtilizationOfUnsecuredLines    0.000000\n", "age                                     0.000000\n", "NumberOfTime30-59DaysPastDueNotWorse    0.000000\n", "DebtRatio                               0.000000\n", "MonthlyIncome                           0.195601\n", "NumberOfOpenCreditLinesAndLoans         0.000000\n", "NumberOfTimes90DaysLate                 0.000000\n", "NumberRealEstateLoansOrLines            0.000000\n", "NumberOfTime60-89DaysPastDueNotWorse    0.000000\n", "NumberOfDependents                      0.000000\n", "dtype: float64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"NumberOfDependents\"].fillna(int(data[\"NumberOfDependents\"].mean()),inplace=True) \n", "  \n", "data.isnull().mean() "]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def fill_missing_rf(X, y, to_fill):\n", "    \"\"\"\n", "    X：要填补的特征矩阵\n", "    y：完整的，没有缺失值的标签\n", "    to_fill：字符串，要填补的那一列的名称/MonthlyIncome\n", "    \"\"\"\n", "    # 构建新特征矩阵和新标签\n", "    df = X.copy() \n", "    fill = df.loc[:, to_fill]\n", "    df = pd.concat([df.loc[:, df.columns != to_fill], pd.DataFrame(y)], axis=1)\n", "\n", "    #找出训练集和测试集\n", "    Ytrain = fill[fill.notnull()]\n", "    Ytest = fill[fill.isnull()]\n", "    Xtrain = df.iloc[Ytrain.index, :]\n", "    Xtest = df.iloc[Ytest.index, :]\n", "\n", "    from sklearn.ensemble import RandomForestRegressor as rfr\n", "    \n", "    #用随机森林回归来填补缺失值\n", "    rfr = rfr(n_estimators=100)\n", "    rfr = rfr.fit(Xtrain, Ytrain)\n", "    Ypredict = rfr.predict(Xtest)\n", "    \n", "    return Ypredict"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["X = data.iloc[:,1:] \n", "y = data[\"SeriousDlqin2yrs\"] \n", "\n", "y_pred = fill_missing_rf(X,y,\"MonthlyIncome\") \n", "  \n", "#确认我们的结果合理之后，我们就可以将数据覆盖了 \n", "data.loc[data.loc[:,\"MonthlyIncome\"].isnull(),\"MonthlyIncome\"] = y_pred"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["(29221,)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["y_pred.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.2 描述性统计"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2.1 处理异常值"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "from matplotlib import pyplot as plt\n", "\n", "x1=data['age']\n", "fig,axes = plt.subplots()\n", "axes.boxplot(x1)\n", "axes.set_xticklabels(['age'])\n", "\n", "data = data[data['age']>0]\n", "data = data[data['age']<100]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SeriousDlqin2yrs</th>\n", "      <th>RevolvingUtilizationOfUnsecuredLines</th>\n", "      <th>age</th>\n", "      <th>NumberOfTime30-59DaysPastDueNotWorse</th>\n", "      <th>DebtRatio</th>\n", "      <th>MonthlyIncome</th>\n", "      <th>NumberOfOpenCreditLinesAndLoans</th>\n", "      <th>NumberOfTimes90DaysLate</th>\n", "      <th>NumberRealEstateLoansOrLines</th>\n", "      <th>NumberOfTime60-89DaysPastDueNotWorse</th>\n", "      <th>NumberOfDependents</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>149377.000000</td>\n", "      <td>149377.000000</td>\n", "      <td>149377.000000</td>\n", "      <td>149377.000000</td>\n", "      <td>149377.000000</td>\n", "      <td>1.493770e+05</td>\n", "      <td>149377.000000</td>\n", "      <td>149377.000000</td>\n", "      <td>149377.000000</td>\n", "      <td>149377.000000</td>\n", "      <td>149377.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.066998</td>\n", "      <td>6.071638</td>\n", "      <td>52.302115</td>\n", "      <td>0.393903</td>\n", "      <td>354.434785</td>\n", "      <td>5.423788e+03</td>\n", "      <td>8.481058</td>\n", "      <td>0.238142</td>\n", "      <td>1.022447</td>\n", "      <td>0.212523</td>\n", "      <td>0.740442</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.250020</td>\n", "      <td>250.275393</td>\n", "      <td>14.718202</td>\n", "      <td>3.853130</td>\n", "      <td>2041.927928</td>\n", "      <td>1.322580e+04</td>\n", "      <td>5.136575</td>\n", "      <td>3.826343</td>\n", "      <td>1.130212</td>\n", "      <td>3.810701</td>\n", "      <td>1.108299</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>21.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1%</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>24.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10%</th>\n", "      <td>0.000000</td>\n", "      <td>0.003199</td>\n", "      <td>33.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.035075</td>\n", "      <td>1.800000e-01</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.000000</td>\n", "      <td>0.030136</td>\n", "      <td>41.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.177446</td>\n", "      <td>1.800000e+03</td>\n", "      <td>5.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.000000</td>\n", "      <td>0.154247</td>\n", "      <td>52.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.368232</td>\n", "      <td>4.419000e+03</td>\n", "      <td>8.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>0.000000</td>\n", "      <td>0.556524</td>\n", "      <td>63.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.875062</td>\n", "      <td>7.416000e+03</td>\n", "      <td>11.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90%</th>\n", "      <td>0.000000</td>\n", "      <td>0.978007</td>\n", "      <td>72.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1275.000000</td>\n", "      <td>1.080000e+04</td>\n", "      <td>15.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99%</th>\n", "      <td>1.000000</td>\n", "      <td>1.093930</td>\n", "      <td>87.000000</td>\n", "      <td>4.000000</td>\n", "      <td>4985.240000</td>\n", "      <td>2.325000e+04</td>\n", "      <td>24.000000</td>\n", "      <td>3.000000</td>\n", "      <td>4.000000</td>\n", "      <td>2.000000</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.000000</td>\n", "      <td>50708.000000</td>\n", "      <td>99.000000</td>\n", "      <td>98.000000</td>\n", "      <td>329664.000000</td>\n", "      <td>3.008750e+06</td>\n", "      <td>58.000000</td>\n", "      <td>98.000000</td>\n", "      <td>54.000000</td>\n", "      <td>98.000000</td>\n", "      <td>20.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       SeriousDlqin2yrs  RevolvingUtilizationOfUnsecuredLines            age  \\\n", "count     149377.000000                         149377.000000  149377.000000   \n", "mean           0.066998                              6.071638      52.302115   \n", "std            0.250020                            250.275393      14.718202   \n", "min            0.000000                              0.000000      21.000000   \n", "1%             0.000000                              0.000000      24.000000   \n", "10%            0.000000                              0.003199      33.000000   \n", "25%            0.000000                              0.030136      41.000000   \n", "50%            0.000000                              0.154247      52.000000   \n", "75%            0.000000                              0.556524      63.000000   \n", "90%            0.000000                              0.978007      72.000000   \n", "99%            1.000000                              1.093930      87.000000   \n", "max            1.000000                          50708.000000      99.000000   \n", "\n", "       NumberOfTime30-59DaysPastDueNotWorse      DebtRatio  MonthlyIncome  \\\n", "count                         149377.000000  149377.000000   1.493770e+05   \n", "mean                               0.393903     354.434785   5.423788e+03   \n", "std                                3.853130    2041.927928   1.322580e+04   \n", "min                                0.000000       0.000000   0.000000e+00   \n", "1%                                 0.000000       0.000000   0.000000e+00   \n", "10%                                0.000000       0.035075   1.800000e-01   \n", "25%                                0.000000       0.177446   1.800000e+03   \n", "50%                                0.000000       0.368232   4.419000e+03   \n", "75%                                0.000000       0.875062   7.416000e+03   \n", "90%                                1.000000    1275.000000   1.080000e+04   \n", "99%                                4.000000    4985.240000   2.325000e+04   \n", "max                               98.000000  329664.000000   3.008750e+06   \n", "\n", "       NumberOfOpenCreditLinesAndLoans  NumberOfTimes90DaysLate  \\\n", "count                    149377.000000            149377.000000   \n", "mean                          8.481058                 0.238142   \n", "std                           5.136575                 3.826343   \n", "min                           0.000000                 0.000000   \n", "1%                            0.000000                 0.000000   \n", "10%                           3.000000                 0.000000   \n", "25%                           5.000000                 0.000000   \n", "50%                           8.000000                 0.000000   \n", "75%                          11.000000                 0.000000   \n", "90%                          15.000000                 0.000000   \n", "99%                          24.000000                 3.000000   \n", "max                          58.000000                98.000000   \n", "\n", "       NumberRealEstateLoansOrLines  NumberOfTime60-89DaysPastDueNotWorse  \\\n", "count                 149377.000000                         149377.000000   \n", "mean                       1.022447                              0.212523   \n", "std                        1.130212                              3.810701   \n", "min                        0.000000                              0.000000   \n", "1%                         0.000000                              0.000000   \n", "10%                        0.000000                              0.000000   \n", "25%                        0.000000                              0.000000   \n", "50%                        1.000000                              0.000000   \n", "75%                        2.000000                              0.000000   \n", "90%                        2.000000                              0.000000   \n", "99%                        4.000000                              2.000000   \n", "max                       54.000000                             98.000000   \n", "\n", "       NumberOfDependents  \n", "count       149377.000000  \n", "mean             0.740442  \n", "std              1.108299  \n", "min              0.000000  \n", "1%               0.000000  \n", "10%              0.000000  \n", "25%              0.000000  \n", "50%              0.000000  \n", "75%              1.000000  \n", "90%              2.000000  \n", "99%              4.000000  \n", "max             20.000000  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe([0.01,0.1,0.25,.5,.75,.9,.99])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["0     141094\n", "1       5232\n", "2       1555\n", "3        667\n", "4        291\n", "98       220\n", "5        131\n", "6         80\n", "7         38\n", "8         21\n", "9         19\n", "10         8\n", "96         5\n", "11         5\n", "13         4\n", "15         2\n", "14         2\n", "12         2\n", "17         1\n", "Name: NumberOfTimes90DaysLate, dtype: int64"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["data.loc[:,\"NumberOfTimes90DaysLate\"].value_counts()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 149152 entries, 0 to 149151\n", "Data columns (total 11 columns):\n", " #   Column                                Non-Null Count   Dtype  \n", "---  ------                                --------------   -----  \n", " 0   SeriousDlqin2yrs                      149152 non-null  int64  \n", " 1   RevolvingUtilizationOfUnsecuredLines  149152 non-null  float64\n", " 2   age                                   149152 non-null  int64  \n", " 3   NumberOfTime30-59DaysPastDueNotWorse  149152 non-null  int64  \n", " 4   DebtRatio                             149152 non-null  float64\n", " 5   MonthlyIncome                         149152 non-null  float64\n", " 6   NumberOfOpenCreditLinesAndLoans       149152 non-null  int64  \n", " 7   NumberOfTimes90DaysLate               149152 non-null  int64  \n", " 8   NumberRealEstateLoansOrLines          149152 non-null  int64  \n", " 9   NumberOfTime60-89DaysPastDueNotWorse  149152 non-null  int64  \n", " 10  NumberOfDependents                    149152 non-null  float64\n", "dtypes: float64(4), int64(7)\n", "memory usage: 12.5 MB\n"]}], "source": ["data.describe([0.01,0.1,0.25,.5,.75,.9,.99])\n", "(data[\"age\"] == 0).sum() \n", "\n", "data = data[data[\"age\"] != 0] \n", "\n", "data[data.loc[:,\"NumberOfTimes90DaysLate\"] > 90].count() \n", "  \n", "data = data[data.loc[:,\"NumberOfTimes90DaysLate\"] < 90] \n", "\n", "data.index = range(data.shape[0]) \n", "data.info() "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2.2 处理样本不均衡问题"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    139280\n", "1      9872\n", "Name: SeriousDlqin2yrs, dtype: int64"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["#探索标签的分布 \n", "X = data.iloc[:,1:] \n", "y = data.iloc[:,0] \n", "y.value_counts() \n", "  "]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    139280\n", "1      9872\n", "Name: SeriousDlqin2yrs, dtype: int64"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["y.value_counts()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["SeriousDlqin2yrs\n", "0    139280\n", "1      9872\n", "Name: SeriousDlqin2yrs, dtype: int64"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["data['SeriousDlqin2yrs'].groupby(data['SeriousDlqin2yrs']).count()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["样本个数：149152; 1占6.62%; 0占 93.38%\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#探索标签的分布 \n", "X = data.iloc[:,1:] \n", "y = data.iloc[:,0] \n", "y.value_counts() \n", "  \n", "n_sample = X.shape[0] \n", "  \n", "n_1_sample = y.value_counts()[1] \n", "n_0_sample = y.value_counts()[0] \n", "\n", "grouped = data['SeriousDlqin2yrs'].groupby(data['SeriousDlqin2yrs']).count()\n", "grouped.plot(kind='bar')\n", "\n", "\n", "print('样本个数：{}; 1占{:.2%}; 0占 {:.2%}'.format(n_sample,n_1_sample/n_sample,n_0_sample/n_sample)) \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from imblearn.over_sampling import SMOTE  #conda install -c glemaitre imbalanced-learn\n", "import imblearn \n", "  \n", "from imblearn.over_sampling import SMOTE \n", "  \n", "sm = SMOTE(random_state=42) #实例化 \n", "X,y = sm.fit_sample(X,y) "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n_sample_ = X.shape[0] \n", "  \n", "pd.Series(y).value_counts() \n", "  \n", "n_1_sample = pd.Series(y).value_counts()[1] \n", "n_0_sample = pd.Series(y).value_counts()[0] \n", "  \n", "print('样本个数：{}; 1占{:.2%}; 0占{:.2%}'.format(n_sample_,n_1_sample/n_sample_,n_0_sample/n_sample_))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2.3 训练集和测试集"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split \n", "\n", "X = pd.DataFrame(X) \n", "y = pd.DataFrame(y) \n", "  \n", "X_train, X_vali, Y_train, Y_vali = train_test_split(X,y,test_size=0.3,random_state=420)\n", "model_data = pd.concat([Y_train, X_train], axis=1) \n", "model_data.index = range(model_data.shape[0]) \n", "model_data.columns = data.columns \n", "  \n", "vali_data = pd.concat([Y_vali, X_vali], axis=1) \n", "vali_data.index = range(vali_data.shape[0]) \n", "vali_data.columns = data.columns \n", "  \n", "model_data.to_csv(r\"model_data.csv\") \n", "  \n", "vali_data.to_csv(r\"C:vali_data.csv\") "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.3 分箱处理"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3.1 等频分箱"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_data[\"qcut\"], updown = pd.qcut(model_data[\"age\"], retbins=True, q=20) "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_data[\"qcut\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["updown"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_data[model_data[\"SeriousDlqin2yrs\"] == 0].groupby(by=\"qcut\").count() [\"SeriousDlqin2yrs\"] "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[*zip([7,8,9],[\"a\",\"b\",\"c\"])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[*zip(updown,updown[1:],coount_y0,coount_y1)] "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#retbins 默认为False，为True是返回值是元组\n", "#q：分组个数\n", "  \n", "model_data[\"qcut\"], updown = pd.qcut(model_data[\"age\"], retbins=True, q=20) \n", "\n", "coount_y0 = model_data[model_data[\"SeriousDlqin2yrs\"] == 0].groupby(by=\"qcut\").count() [\"SeriousDlqin2yrs\"] \n", "coount_y1 = model_data[model_data[\"SeriousDlqin2yrs\"] == 1].groupby(by=\"qcut\").count() [\"SeriousDlqin2yrs\"] \n", "  \n", "#num_bins值分别为每个区间的上界，下界，0出现的次数，1出现的次数 \n", "num_bins = [*zip(updown,updown[1:],coount_y0,coount_y1)] \n", "  \n", "#注意zip会按照最短列来进行结合 \n", "num_bins"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3.2 封装WOE和IV函数"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 为了衡量特征上的信息量以及特征对预测函数的贡献，银行业定义了概念Information value(IV)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- IV = (good% - bad%) * WOE"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 银行业中用来衡量违约概率的指标，中文叫做证据权重(weight of Evidence)，本质其实就是优质客户 比上坏客户的比例的对数。WOE是对一个箱子来说的，WOE越大，代表了这个箱子里的优质客户越多。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- WOE = ln(good% / bad%)"]}, {"attachments": {"image.png": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABBkAAAE7CAYAAACYI1loAAABPmlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSCwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAysDGIMYgwsCdmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsishOX32QsuivPv4lapWKOZfhVTPQrgSkktTgbSf4A4KbmgqISBgTEByFYuLykAsVuAbJEioKOA7BkgdjqEvQbEToKwD4DVhAQ5A9lXgGyB5IzEFCD7CZCtk4Qkno7EhtoLAhw+wUbmQeamBJxKOihJrSgB0c75BZVFmekZJQqOwBBKVfDMS9bTUTAyMDJgYACFN0T1xSeQcHx3BiGWXcTAYHwOyPiBEMsERtfaQgYGpVsIMaV4Bga5QwwM66MKEosS4Q5g/MZSnGZsBGGrcDIwCAX////Fm4FB6inQnhX////6+v///xCgrCQDw6OHAOwDXly3m8WqAAAAOGVYSWZNTQAqAAAACAABh2kABAAAAAEAAAAaAAAAAAACoAIABAAAAAEAAAQZoAMABAAAAAEAAAE7AAAAAADOzaMAAEAASURBVHgB7N0FvDVV9T7wQbFFsVBR9LW7u7FbMbFFsDuxUBG7uwu7uxu7FbsVRUBQFLH7f7/zc73//Q5zzj3nBtx41udz7zlnYs+eZ+/Ze61nrb1mu/8uSBcJAkEgCASBIBAEgkAQCAJBIAgEgSAQBILAMhE43jLPz+lBIAgEgSAQBIJAEAgCQSAIBIEgEASCQBDoEQjJkI4QBIJAEAgCQSAIBIEgEASCQBAIAkEgCKwIAiEZVgTGFBIEgkAQCAJBIAgEgSAQBIJAEAgCQSAIhGRIHwgCQSAIBIEgEASCQBAIAkEgCASBIBAEVgSBkAwrAmMKCQJBIAgEgSAQBIJAEAgCQSAIBIEgEARCMqQPBIEgEASCQBAIAkEgCASBIBAEgkAQCAIrgkBIhhWBMYUEgSAQBIJAEAgCQSAIBIEgEASCQBAIAiEZ0geCQBAIAkEgCASBIBAEgkAQCAJBIAgEgRVBYPsVKWVCIQcdfOiEPdkcBIJAEAgCQSAIBIEgEASCQBAIAkEgCKw1BLbssvOyqrSqJIOanXGn0yyrgjk5CKxnBA474sg8A+u5AVP3IBAEgkAQCAJBIAgEgSCwiRBgvyxXslxiuQjm/CAQBIJAEAgCQSAIBIEgEASCQBAIAkGgRyAkQzpCEAgCQSAIBIEgEASCQBAIAkEgCASBILAiCIRkWBEYU0gQCAJBIAgEgSAQBIJAEAgCQSAIBIEgEJIhfSAIBIEgEASCQBAIAkEgCASBIBAEgkAQWBEEQjKsCIwpJAgEgSAQBIJAEAgCQSAIBIEgEASCQBAIyZA+EASCQBAIAkEgCASBIBAEgkAQCAJBIAisCAIhGVYExhQSBIJAEAgCQSAIBIEgEASCQBAIAkEgCIRk2CR94G9/+1v397//vb/bf//7353f//znP2e6e8c5/j//+c9Mx+egIBAEgkAQCAJBIAgEgSAQBIJAENicCIRk2ATtjiC4+tWv3l3/+tfv7/YnP/lJ//vGN75x969//WsqAv/973+7W93qVt01rnGN7tBDD516bHYGgSAQBIJAECgEFptfFttf5ayFzyLbV7Mu5uqVkj/84Q8rVdRc5Rx55JFTj//rX/86dX+783e/+91W50i7vb7//ve/r6+r8jlPXVelAik0CASBILCOEQjJsI4bb6lVP895ztOd61zn6ighX/rSl6YW841vfKP79a9/3V30ohftznzmM089NjuDQBAIAkEgCBQCn/70p7t73OMe3de//vXatM2nfc94xjO6P/3pT9tsn/aDIf6Upzyl++Mf/zjtsBXfx3i++c1v3r3uda+bOQoQiXLAAQfMVJf3vve93d3udrdtsDA/f+9739t6/tFHH731e/vlH//4R/uz//6Sl7yke8QjHtEddthhx9g374avfvWr3ac+9amZTnv0ox/dPeABD+gjHzkp2uuLprzZzW7WveIVr5gJw7e85S29c+TVr371Ma6t3N12263bd999u9/85jfH2L/cDX/+85+7u971rt373ve+RYvSj9/4xjdOvKcnP/nJ3fOe97zuqKOOWrSsHBAEgkAQ2CgIbL9RbiT3MR8CN7zhDbtnPvOZ3Uc+8pHuCle4wsSTP/CBD/T7bnSjG008JjuCQBAIAkEgCAwROOEJT9h961vf6k5xilMMd/W/RQeIkDvpSU86un9s47Oe9aze8Dv5yU/e3ete9xo7ZJttDFBG6oMe9KBuu+2222bfPD9OcIITdDznn/3sZ3vj1u8Shigj/IxnPGN3vOP9f9/Nz3/+846B/uAHP7gTQchw3XHHHXtj81e/+lW3yy67dKc85Sn7Yn784x93Jz7xibt3vetd3e1ud7t+28UvfvGepLnwhS/c3fe+9+2v/bGPfawnO+o6vP2vfOUre+LlHOc4R1Wp23777TtOAnVWr5JvfvOb3Zvf/Oa+TRyzmDjfvZ3oRCfqXvSiF3XnPve5tznlt7/9bYcMuMtd7tLBxD2c5Sxn6XH44Ac/2D396U/vEA9XucpV+r7AuaHNZ2mLgw46qL8PkZhD+c53vtNHYh5xxBE9psP9w9+weOlLX9rXrW07y0B/+ctfdqc61am2toVz9Rv73vrWt3YXu9jFujOd6UzDIrf+PuSQQzq47r777lu3tV9ERHzmM5/p7njHO7ab8z0IBIEgsKERWHyG2dC3v3lv7lrXulb3/Oc/v5/4/vKXv4wqeSZGXpgddtih23XXXTcvWLnzIBAEgkAQmBmBl73sZd0NbnCDDslAdt5559FzGblnOMMZtjHMkRKM6jFhYH/oQx/qHvrQh3bXve51u/e///3dSU5yku74xz/+2OH9NgTDD3/4ww4pcfe7333icXYgBXikeeKV20oZppe+9KX7stp917ve9fr6tPVgoLoeA5+h/pCHPGTrKYx8c6uIjNOc5jRbtw+/uOZtbnOb7lGPelS/ZBFeohtuetObdqIECLKCV/8HP/hBX6/Tnva0PR7OVfb5z3/+bYq9yEUu0hvMjltMOBnoCZe5zGV6Z0Rh0J6nnC9+8Yu9Uf6YxzymvzYCAaGClLjJTW7SXfKSl+xP+cpXvtL3ifvc5z49edCWM/YdyQDDsShKJAPRVm294F4ETFsmosC9tG1kvyiaa1/72j2ZU+ROe94s3/VzhFBdF5Hy/e9/v7vsZS/bn454Oe95z9shhJ74xCf2JFXtm6X8HBMEgkAQWI8IhGRYj622AnVGHPAsfPSjH+29FBS2oVCCEA1CREtZHB6T30EgCASBIBAEWgR4tnmCh/MKAsGcUvMJQ9RxvMyEYfbCF76we9zjHtdd9apXbYvsiYKXv/zlfdh5kRC8z5ZPtJEQljP84he/6B75yEf25wtlJ3VNRiWjXQQB4qEVUQcHH3xwxzsv0q+VoXHa7ivj0nmM3J122ql7/etf33vNX/CCF/RGcGv88uTLc9QSDJYSiBYYyhWveMXu8pe/fB/1YOki8VsCZ8a1TwLrd7zjHd273/3ubp999jmGMd0f9L9/ixEMjOGnPe1pPdFi2YWIi2ly5StfuXdYtMfsv//+PUFwvvOdbyv28L3a1a62TeSBpR7VNs7/2c9+1uOC5IGrqEsiggSuFUmBZFC2pZ8lokUsEfFniedQprXh8Nj2tz6jjv4QVtqhrbO2Q4h84Qtf6E/T9vqyyAnRJdrV/WzZsqWD1d5779299rWv7c561rO2l8n3IBAEgsCGQiAkw4ZqzvluxuSNZPjwhz98DGVQSUIdSU3y/Y/8CwJBIAgEgSAwAQFeXETCne98504YeQlj8mQnO1lnWYDoBd5u3nhGsuMJI+ypT31qd+pTn7pO6z9/+tOfdggGnmgGsgiGC1zgAr2xvc2BCz/MW4cffnh3qUtdarir/41YsORCvoThcoHb3/72ffnC+xcTxjcPuDqXKFvEwulOd7reqER01LIAdUJcWOcvYkLiZWQIYVirk+uW179ICcYsg//AAw/c5g1P8jfc4ha36DGt6zvWun/GeF239s3y+apXvao/Xxvuueee2xjwY+d/7Wtf60RCilThjBDRIGoDOQAXzgzLPC53ucv1S1u0/f3vf/+tRblHeCEe4EEQUYgZnn795TnPeU6/XVQFkgDuokOUJTrhSU96Ur/fP8a/Pif/gWiaIn+2HrCEL/qndrTkZ7/99uux/fa3v91HgxRpAWt/9fsOd7hDf6WW/Kr20Gfe9KY39bgtoTo5JQgEgSCwbhAIybBummrlKyq8z2RNUZDUqvWqUIgk6+IpOOc5z7nyF0+JQSAIBIEgsOEQYOQxMBln9aYB4fyM6Otc5zp94r+6abkEEA4885OEIf7xj3+8DzPnuRctoCxGn4iJIVEwqZzhdsaq8PjKiaCujH1vYbJkQoh7GYbOrVc+M/blGXjnO9/Z18tyjFpa4ZzHP/7x3a1vfev+ntrzEQCf//zneyOVUS1xZUVwMJhd31KIIhmQKsgKyQ2VIwrg7Gc/+9bbcD4sGOIljkN0iPCYJPJDSL7I8NdWba4BZM/nPve57m1ve1t/enntRT0iEOB/iUtcYmvRdAY5EdQTMfGwhz2s1yOQHHUfllAw9t07rCUDlZ/APSN69BPODh5+9eb1R1wgI9plEO6fLmLphBwRrq0d2mPkRXCcJRZFMLz4xS/ul5K4z7Y96iYQHXQh96q8oYiUUUfkyZe//OV+6Yg2afudthWVYCkNGRJYyLQiIHxKdlr61fB6+R0EgkAQ2CgIhGTYKC25hPsw4Vo3i/G31rVNWsQbZGJMwsclAJtTgkAQCAKbFAE5AhjDjMmKZBBGfqUrXWkbT/w0eBi68gAw5BiTvP8lIh14iHn3GWxC2YfLHurYaZ9ezcwzjRRAEjBOiegAc6NrtJ5ohq8lAN7OdNvb3raP1HBcEQx1LYY0730lTRY2j0yoV0jz0MMCPjzxvP2MbgkfeblL5GIw/yITnFtGc+2HDYJmXrGkQESFZSXyOJj/EQ8wZCyXMKotu7B0ApmDFGCwW35SuQu2LJBJ/oglLjBF1CAe6BASNiIz4HTPe96ze+ADH9hHLTgeESPaxLZWyhhHIrUEgvu3bMN9v/3tb+/boN2vDMY9aZedtHk46DTqMotYNoPQGDseQYUQkeCSWC7hvi3pQEaJxBBpUiQbzEXywJHIK2IbMkYfiASBIBAENiICIRk2YqvOcU8SVnmdlCUTLclgEuSVsW40EgSCQBAIAkFgFgQYrhL78W4zrhmEEhV+97vf7T3UCIQSBIEQ93Ybw4xHmYFdxm8d7y0OIu8Ycda680LzyjNYhagPDXHn8UQzLodkAPKC8S754r777ttJSmh9f7vGv67rswxa5XhDRCuiDizpOP3pT99v5qVGHjz72c/uoxMYrC1h4SAREIgS+JS0Bi2j35ufRCt4C5TIAN9LGOPua16BEcOfIY4EEJEhAsFSlMJPGyBKYHHve9+7J3rq/seWT8IOCaEcfxe84AX79rcUwhtA3Id+0eJmWcdSchLAWR6PsXqIOCAtydDioy+4L2QI/WaSICv0C/kfkFBDEku/E5mhTUWSIGP8RhzBVDsdtJCjAZlEPAf6DWIBQVNJH/XBSBAIAkFgoyLw/9+1tFHvMPc1FQFMuhA/oYImRWK9IaUIwTBUjPoD8i8IBIEgEASCwAgCDDwEw1AszWMU81Dz9vtjnN3pTnfqczIcffTR/TZREJI/Do1IrxlktDMSZeonjGIRBvaVgdxe1/V42BmKYwa5ZQsMU8sDGMoVbWAbEmBWkX9CbgAh/v54tkUZMLAlt/SGhQtd6ELbFOcaoiLa6IFtDlj4wQu/11579R5yhnsr7qe8/u32xb4PcZJE0zIDhA+BJxLHKz+9CaHeDFIkw7B8ugJHBYwd47547UViMKgZ6IgMeQ3qjRCOEaFRkQDDMqf9FvEhcaf8Ea7dSi1pmUQy2P69732vX/oBO38IBX/126fjCqexstyn3CDXvOY1+9eHImMsMZVsUgSOMhAU+qk/vz0TynTP9dcudWnvI9+DQBAIAhsBgUQybIRWXOY98BhZb8lbIiw1CR+XCWhODwJBIAhsQgQYqvL4DEX4fOUbEIEwFOeJWvBWANF1jMXWqOWBlhQR6S2Pg2UF5YkWPt96xBnfQv1FQ/heXmjJFtucBlUHpIDlD67h1c6EUcibb1mBVyS2EQZ13iyfIgJFWfiTs6AV11vMky2PAO+3CIAyeqsMRjoMGOzLEW+lYPTCG0YMbjjIU9DKGAbaFRnD6480srRCzgaREsTSCkSAevLqy89ARDFom2kkg+UH2rYEcaFfiBZBjOgzyB2JIYt0Qu6QMWLAdhj6018qmkCEjSUbiKB2aYUyChfnjgmSQOSCMvQzBMOYqKu+W28HGTsm24JAEAgCGw2B/z+Cb7Q7y/3MjIBQRkrMJz7xid5r8slPfrI729nO1oc8zlxIDgwCQSAIBIFNjQBDyisXS7yqkXi1IgK7DP7aX58MOkanN0aQJzzhCb0nmDfcdoke73KXu/R5DkQGFMHg2NYQ9ZtYL4+0mEUY+uc///n7aAhLHEp23XXXftmHvAEV9l77Zv2UqLBIF/fRivtolw+0+9rvSP8yXustHPaL/ICDcghywDWG1+l3TvmHyBDOj+Bx/yJNLH14z3ve093vfvfbugRkrAh5LCyPqDYQ0SC/hASfhNGPvCjyxjICRIHIScsHRFJOEssPqlzHiLBQVuWDkCzUfjk6EE+kSIa2f/Q7mn9DsgTZgwQYS5Y5PLYppv9qqQv8RcHAXX/VV4eC9IKte3DcYuUOz8/vIBAEgsB6RCAkw3pstRWus4maN0NG7be+9a298rLHHnus8FVSXBAIAkEgCGxkBHiJGZHeFiDJnbXzpLL9C5nn/eX1bqWOe/3rX98bzoyyWr7gdZWi7RAQlUyxPXf4fV4DzisT5VBQL68WZCiS8rKL8lsqyVAEg/IYmCXW9CMJhiSD7a0gDmD5ohe9qN9chAJjGs5IndqmfOfPQzJ4S4NkjpZyWO4hGaQlDV7lyXiX50LeCJEDY8IwlyND5AdR3kUucpH+1ZwHLSy/9CpJSwqURywh8aYQSxaQUdPaCpnQRrPIGzUUBAnCAlGAWCiM51mG4FxiqcNQYDxNLCl1TTlI5J94+MMfvg0xUueK8LBcQh0lAkVqRYJAEAgCGx2BkAwbvYVnvD9vmUAyyDRtYi9PxIyn57AgEASCQBDY5AjwtDNMeaxFHliXzhtOGFgiFCR5lIvAmvYSeYAY05YttEsf7BfGPkzaWOetxCdDWJQFg14CR69S9FYB9fPmhPLCL/davOUl8HE912mlPPG2Md4Z5MgI+SwIQoFhjoTxCSsGLAIG4cAoVu4s8uQnP7l773vf2xNARQK05zGcvRXEchHHjYk29TaRU5/61H3SQyST5S7uw3ZJGpEMJYxyRIZ7skxluSIXAnJAPQh8OE2m5bkYkjDqKMJmLOGnek4SyzfcZxFmcH/ta197jCU5lrVYHiJqQztKDhmSYRKq2R4EgsBGQiCJHzdSay7jXihTEkth9SlZNXEuo8icGgSCQBAIApsIAWSAZIde8ScioDzcLQTWzzPq5QCaRVaTYGDE83rLmXDjG9+4jwRow933X3hlJc/8SkhFHLjej370oz43ABKmhKHczruWkMhjwfvPeCdICctAEDK+w0b+g5e+9KW9Yc1Q3nfffftjF/uHBJKLoCUYkBR1LWXLqUAnGOaTqLK9flGCTm+++PKXv9ztueee/ZskLMEUHSLvQVu+JQ6SdCJcJJxeroge8GrMEvWctCTHMUiwYbSIpSLySNR9V1lInLEcHrVf30YqyAmBjNAmiId6bWUdh8TQrpbOWHpjOUkkCASBILAZEEgkwyZoZcpL+4qwSbdMMYwEgSAQBIJAEFgqAnIZlIyFm8uBwDAWjn+Vq1xlYpK+KqP9HHqh233zfpeETw6B5z73uX1UAEOQ4WuulMhPdEAbrj9v+cPjEfnemoG04MWH04EHHthHHrgWQmOXXXbZehrS3zINyzlKRAXwmHtt5y1vecv+jQ7KqeUcYyH/de7wUzsoh8GvXiIPLD1AapS4HuKiIilq+/BT/gSGtqUWSBQGuHwNohZaQbSIuLDEwLKB4RszHDskAdrzF2t/UR1ySkwSxAFiBNlBRItYluNtFfI6SEpqiYuoA32jJX2GZbpHb+Agn/3sZ/s3kyARiLdzlIjSERmqL7n2YYcd1kczcOZEgkAQCAIbGYH/o8c38h3m3oJAEAgCQSAIBIFjHQGeXTI0DhnGvMeT3gIwqaIM7GFZk45ttzNc5Qgo4Z2Xg0COiHbZhldakvbYOmeez7E6yl+AYJDIkXG7x0Leo6te9ap90kVvSZB7YkjKCLMfbpOgGREg+sLrPBEE3rwxPG5SfYdGPM//3nvv3RMOSICb3/zmW09llEsC3crwfPvcB0++pS0iVBjtDPY2mSIyA7mCWIC5uo959SuvgiUl7Z+2r/7U1qf9zoC3dGOScLg8//nP7yMUECr77bdf/6pubysRySLhpWiMW9ziFn10iFd7jskQA3Uee8uHfmCpiZwixDIORANiq10aM3aNbAsCQSAIrHcEEsmw3lsw9Q8CQSAIBIEgsAYRqKR6Q6Obx7e8vqpd+xm104ShOWbMTTvHPnkXJA7kneaV92mpwDD/A5JB+Hy9gpHRqk6M5R/84Af9Zdo3HtR1heF7laNEjr47b5jLAQkgWvCAAw7oDd16lec+++zTL32QD8kSA4kYK3Tf0oLWsOb9t9/rOR3D044guPe9792TNiIxGPPqLBpjuFxFVITXTnotpiTPQxEdoc3G9pXBPYnMUJ9DDjmkfw0mMkESTdEqCBB1ssxDsse99tqrX1IgGag3U3hVJsIE5updy0q8kaStv0iJFguGvjbdeeedexwqEqPNATG8P7/V/53vfGffXgiVO97xjv1hIjuQPX67f0tQ3vWud/VJLyW/bJdhFBHiRP1RvarNLJfwW929wlRyyi0LUSwl17/+9XsS5olPfGKn7VcyWqaukc8gEASCwFpAICTDWmiF1CEIBIEgEASCwAZDoAxGhmtrpLlNbxjw5gaGMwORtJ7vfsPgn3IYiRJISn7IMPTmA0azyATiGMsO6rdtDGvJJt/3vvf1SwsY07L9D4Wn2RsCSnibRR4wSute2uUEdZz8BbvvvnvvtZY80pIB3n3C4OTdZzTLwWBZxBALUQ1C/b0S0ZKDepsDQ7o83sgJxq8cAO2yCp5xuRMY60gOmCIiGPZDg5tBK4pAHgVLNOaRyssw9OIr4+CDD+5e85rX9AQLskQ7un91llBalAMioeqjDs9+9rP7pJKIFQSCttRGcBZx4J7KcHcN30UblPjNePeqTX/E/WnbMREVUu2A7HjDG94wuhxCGfKJiLR5zGMe0y9XQTbUqzOV3ZIMfiN6vKHD8gl5NLS/ZSGIFgk2W9l1IYpHvT/2sY/1ZMszn/nMqYkq23PzPQgEgSCwnhDYbsGDsO3Lm1ew9gcdfGh3xp1Os4IlpqggsL4QOOyII/MMrK8mS22DQBBYIQQYcoxrCfImJXCUfJG3e7fddutfAzjt0oxxkQgtgTDt+JXaJ7pBtAAjmfd5LJqhrsXYlvBQpIL7FtVgiQCje0gu1Dk+kSdeVSlRYpEtcjaISEBsIBus7W8N7/b89jvcf/azn/VGPIO3FSTBLGW05/gux4JIhdbQt+QAqcEwF0UhuWGJKAP7YSEx5Ng1ESFve9vbemKAUU5EjIjQWCwPRF3Hp+UZoj4e8YhH9ERFu08EhrdkUHWRN5asTGuH9lxRKUgT9a88DvaLwED0SHLaimvo7+Twww/voyKG+NuHiEC+eIOH5Shj2DguEgSCQBA4rhBgv2zZZedlXT4kw7Lgy8lBYDoCIRmm45O9QSAIbFwERBBIxMczPUkYvYyuWqIw6TjbJdjjuR8z3Kadt9x9jEdGdrvEY5YyEQPz5p2Ypdz1cIxXN+6www7HSlXhLAplbLmNthvbvpyKIY5Of/rTTyQHROnkNZXLQTjnBoEgcFwjEJLhuG6BXD8ILIJASIZFAMruIBAEgkAQCAJBIAgEgSAQBNYMAitBMuTtEmumOVORIBAEgkAQCAJBIAgEgSAQBIJAEAgC6xuBkAzru/1S+yAQBIJAEAgCQSAIBIEgEASCQBAIAmsGgZAMa6YpUpEgEASCQBAIAkEgCASBIBAEgkAQCALrG4GQDOu7/VL7IBAEgkAQCAJBIAgEgSAQBIJAEAgCawaBkAxrpilSkSAQBIJAEAgCQSAIBIEgEASCQBAIAusbgZAM67v9UvsgEASCQBAIAkEgCASBIBAEgkAQCAJrBoGQDGumKVKRIBAEgkAQCAJBIAgEgSAQBIJAEAgC6xuBkAzru/1S+yAQBIJAEAgCQSAIBIEgEASCQBAIAmsGgZAMa6YpUpEgEASCQBAIAkEgCASBIBAEgkAQCALrG4GQDOu7/VL7IBAEgkAQCAJBIAgEgSAQBIJAEAgCawaBkAxrpilSkSAQBIJAEAgCQSAIBIEgEASCQBAIAusbgZAM67v9UvsgEASCQBAIAkEgCASBIBAEgkAQCAJrBoGQDGumKVKRIBAEgkAQCAJBIAgEgSAQBIJAEAgC6xuBkAzru/1S+yAQBIJAEAgCQSAIBIEgEASCQBAIAmsGgZAMa6YpUpEgEASCQBAIAkEgCASBIBAEgkAQCALrG4GQDOu7/VL7IBAEgkAQCAJBIAgEgSAQBIJAEAgCawaB7Ve7JocdceRqXyLlB4E1jUCegTXdPKlcEAgCQSAIBIEgEASCQBAIAiuIwKqTDFt22XkFq5uigsD6QuCggw/t8gysrzZLbYNAEAgCQSAIBIEgEASCwGZFgP2yXMlyieUimPODQBAIAkEgCASBIBAEgkAQCAJBIAgEgR6BkAzpCEEgCASBIBAEgkAQCAJBIAgEgSAQBILAiiAQkmFFYEwhQSAIBIEgEASCQBAIAkEgCASBIBAEgkBIhk3WB/7yl790//3vf5d11/OU8c9//rP7xz/+sazr5eQgsBER+Ne//jX1thbbP/Xk7FxRBIyZv/vd71a0TIUddthhSy7zN7/5zaLnLmXsNWavlvzpT39araKPlXLh+be//e1YudZyL/Kzn/2s++Uvf7ncYpZ9/nL6+LwXp5tsZFnNZ3MpuE2bo6btW8q1NvM5f/3rX1dl/hli6jpLEePiUuaapVwr56wvBEIyrK/2WlJtKcivec1ruutc5zrdla985e7qV796d//7338uBWTeMj796U93t7nNbborXOEK3VWucpVuzz337N7xjncsqf45KQhsRAQ++clPdne+8527r371q6O3Z99TnvKUbh7DjAH0hCc8oTv66KNHy1zKxmlG1XIJy7H6UFa+8Y1vdM997nO7O97xjt1PfvKTscOWte23v/1td8ghh0ws4+tf/3r3jGc8ozv00P9LfLTddtt1N7jBDfptddILX/jC7mlPe1qnrKUI7O5617t2e+yxR/ejH/1o7iLe/OY3d3vttVfHmByTP/7xj911r3vd7vnPf343q3Hy73//u7v5zW/evfGNb+z+85//bFPsfvvt1730pS9dVt+6973v3d3vfvfbius2F1gHP4488sjuRje6UffqV796IqYHH3xwj9PYc6vP6FtE+z/wgQ/svvvd7y75zhlyj370o7vDDz/8GGXoF7e4xS26N73pTdvsQ5Zp56Fo71//+tfDzd2BBx448V6PcfBgw3L7+KC4qT///Oc/d3e6052697znPVOPs9Nz87rXvW6iYfT4xz++e9azntX9/ve/X7SsY/OAb33rW9297nWv7he/+MWSL/v3v/+9+/KXvzx6/j777NO9853v7GYlCIxfz3nOczplDsX8doc73KH7zGc+M9w18fdqzF8TLzZlh2fme9/73pQjFt/lOXvqU5/affvb3554sPnloQ99aPepT33qGONte9JRRx3Vj+XGi8WINHN/jd0+6/iWcKx5rb2G7/r93nvv3RWBPTa3jY018Nptt926D3/4w8Mi83uTI7Dqb5fY5Piuidt/29ve1ivs5z73ubub3vSmvSLxwQ9+sDdwXvSiF3XnOMc5Fq3nPGV84AMf6BWfE57whN01rnGN7rSnPW33oQ99qHviE5/YK/b3uc99Fr1eDggCGx2BE53oRL0Cf4pTnGL0VhnbDOGTnvSko/vHNlJqKNk77LBDd9/73nfskG22HXHEEd0rX/nKXtFhSI/JQx7ykF7BQRYO5clPfnJ31rOetVfaJ50/PKd+M0AoMwcddFCvNP/0pz/tFTsGd6vk3uMe9+hJ0jOe8Yx1aseggMu811QAXB/84Af3ZbzqVa/qTn7yk28tt764PkP7cpe7XLfzzv//DUnnOte56pC+jhTpu93tbv02Ct3xjjc7b49I4eU1PsJwkqjL9tsfc6pG4qojI5PBNJQvfelL3R/+8IeepBo7f3i83wxKfe4Nb3hDP3af7nSn23oYkvqe97xnx4h+3OMet3X7tC8U7VOf+tT9IfBBGN3kJjfpznCGM2xzGuPpghe84Fz4bVPAsfTjBCc4Qe9RZBTc7GY36/weCgMeGeMeERKtfPOb3+yQPxe/+MX7vquNLnWpS3UXuMAFekOeMdFizhh87Wtf2+200069IafvwtOzwwDw+8c//nFfJkISSXCWs5ylv+Txj3/8nky49KUv3Vahdy487GEP6y572cv27ansEvrB9a53vW3aTB9TX6TfvM/bcvo4MuYFL3hBt2XLlm1w1o+MGXDYcccdq+qdscy+qu+Zz3zmrfuGX371q1/1RKZnaEwYZtoYGXFsylvf+tbuEpe4RHf2s5999LIXu9jFup///Oe98f7e9763mzR3jJ3MsFT+u9/97p4oNFdc7WpX2+ZQ/YdOqE9Ow69OQi7Akq43FNsY6qc85SmHuyb+Xon5y3OhH4zVaeKFmx1f+9rXumc+85ndqU51qs780I7/SJXrX//6/Z8x1TzE6B5rL+P2W97ylo6u/ZKXvKSjfw9F2ebVBz3oQf08cpe73GV4SP/7JCc5Sf8sexYW0weMKa53pStdqR9v1e/FL35xd/vb376/lvmOw+8xj3lMP6+3F3RP2rPGbAQSXeL85z9/f5g5mi1AJ6ixzbOCdIX52HjYlp/vmw+BY2oumw+DdXvHJuHznve8Uwcdk8bznve8XoEz8DBsCOOfEWLfs5/97KkYzFMGJprhQRmh+Nbgi1jgCTQY8ZS1BsPUi2dnENhgCFDibnzjG29VgiYpcyZ8hkpruDICL3rRi44i8pGPfKRD8D3ykY/snzVkA4WkPX94IiXq+9//fq9I8JCNCUWKN7z1hNRxH//4x/tn/TKXuUx3oQtdqDYf45Pi9/73v7/jCWZ48hAythhCJzvZyXpPPs/v3e9+9z56wfjA+KHsUNYc14qyGGBDY7U9hoHDm8uYOtOZzrR1FwOtFD5jqOiuoZRRzvgrgWNtt02dEAQUfcq2uovYotzNItqKIDxqXB6e99nPfrbbf//9Ox7wodIMGwpv6wnnNazrf/7zn+/b3zg/q3H40Y9+tL8OL25r7KrXhS984d5gONvZzrZNNRm6vJ+77777Ntv9EEEnigKODGgKqzkL6Vyi3Sn1lO0nPelJU/trnbOan/qMuYsiTblvpdpAnxojpxxbivZFLnKR3ouovylHG/hjvNhG/NY3/RYV4/lG+lXZCAIKvt9IBFFPDDGKvX4r2qbIP6HO5lYRUIyh6qtV5/6CC/+MH494xCP6KAqEHkKJ1DihjW53u9v125BC+p8+NWsf6k/837/l9HHEBrJm+OwbN6561at2dAqRTksRz9slL3nJrfeMjPMcXf7yl++LO/GJT9yd73zn64mcxz72sT2hVPuWcr1Zz0Ei8P7f8pa3HCWJtZHoJHqU5xvxV+Iehgb9D3/4w+5zn/tc/8ejbtxDrJz+9KfvTnOa09SpWz/13S0LhuykOcmBxkxE4w1veMN+3GIoj/WN6n/t2IvosL0lturiKzV/8eTTdd1DPYt1jUmfxiiYmsPMZcZxov+3JAMDHPHlOLqt+fXpT396b8AjpFspTDw7Nd+0++s7XVwZyMJJUs9mO98ZM8yNw3vUdxHFIsZEnxkXkHHGAX3YH/Kj7svcbA6z33Nh3K9nzucPfvCDXl9RN+1HxzduwMy1jZXqYaxHRBobFyNCJt1ntm88BEIyrLM2NWBgRoXKYhVN4tMe6Je//OW9QmLSahVZAw3PkYnKwFEDzhgc85Txne98p1eYDKxFMCjTYGRS2n9BYfniF7/Ye7PGrpVtQWCjI8DTxoPPQGyFgeH5LqOAcU8B4FkhlGCkIBLPkqdWKAK8FZQrxg1hhCqvDBbbPH8HLXgB9913Xz97Rd3nUFGxraQMIUb0UIxDV7ziFacSDM5hjE8zCChElGAKdol7YlwLgd5ll11qc/9pPPM3SRi+PC0wgFc79o2dw+NG8aKgkQoX5cmp9qBUve997+t43YmxjpdI+QxT9RdqSlmlVKqD8FMKYtsGzqWUUaop+sZzf2PCK8UIp8AxJimWpbw73rZdd921vz6CQf9wTcbHF77whd5T2c4PvOxI41bxr+vqb+YTkQbmBf2uFOU6hvGJFIJViTpWuO+tb33r2tx/ws59Vl+fRJDxgim3lOltClmFHzDlzWeUMapbQV4h1PQBeLZSyne7bfi9fZZcR/+guOuDyqWk2070kXe96109eaDvUdA95wydkrbvTMNH+XBE4pBpxyIoGEoMU32i7ss5CL7ykNe1W+Om6uVzpfs4gqSVqle7bZbviAh46H/GETpOOwZoAxEBDHDi+TIGIF3Pec5z9uMAYogO4/l6wAMe0OtcDPDVFNcWSaI+dDREyFBudatbdQctjOFtH3G/DGORKLe97W37UzyX7lsZnkvGrLG6xVh/afuJvtsupWGw65c1BioYKW2u0X/ascixCAh9xrhRRrPoHc9ZEWnagU7ZlrmS85c2Q8jNI+ZJ4zYidWyeq7Lgo11Kt3Wf8HQOMgoBVFI4GtemCRw4HozJxlXtD0PLKLSlyCTYEvVE1hIYmncQFO0YP3xmEAzauG1n51fbaTMkL0KvttlP3K+6nOc85+l/mw/1M9GFxPjx+te/vo+mE42H5ERwKm+MSOpPyr9NhUBIhnXS3JhjoW5C5EwoBi5KdikDk26D4WIgxPwPxTaKMm/ZNIV9njJMaPIxjK3Rq21Y9EgQ2IwIUBAoCkLseUVKKOsmcx6Cil4oT4XjCcWGh5nh3ApvSxEMjBkRDBRACuVQGMmMzGEI9fC49rd6tAphu2/e75RexvCY13tYFg8ro5dC7f7mqQPDDUEjP0VrXAyvUb95i5EgFD3X+cpXvtIbznIIFN5wpUQiSwmvDcKUsTomyrHGeazejFdtLhR+2lKJtlyRGR/72Md6g3WoMOoDxnlRC8ql4I8RWXC0bpZCzaPeir4BKwokzBkgbTh6e2z7nWGMJGoV3dpPaaWoVrhtbR9+qvuxqZR61kTnIHuGirW+6TniBVxMkGAInTI6hsdX1FJFGyBuPHuWnXiuEI7Ov9a1rjU89Ri/1XWsL9EH6AdFLgyNjGMU9L8NoiAYCYhC3m39hvFd/X3See32le7jbdlL/c64Y6DRjUTG6FcMXZ7twkZ/81e/EZmk7cP1jDH6GFK1tn2p9Zr1PBGfwtm1B4JOeLoxouqqHP0ToWyJiPv1jPkT4WXcd3xLQky6NkJSZFIZkeYnZGrlbNh/gZTWJxiNNRbwkuuvwz6PjBCppt5w9EmcX/22lsq6Rm1bzfkLmedZH4qcFrOOu+25w7HCPhE3+pa5xrNdESKl77Z9qsoSYYLo37JAWhUOsDOfiA7Utshsc5gyqyzjBzyJ8RrGlr8Myd26ziyfrq8P1TKr9py2z2l383dFPhu/PF/6azk9RHOYO82l2trYVs9RW26+bx4EQjKs4bam5PFGUQJ8eqh5yOrhHRu82ttxvsGUp2YY9uk4ExWZlkRoKWWYvMsjqHwDJE/X29/+9t6AGmPnHRcJAhsdARM1pc6kTqkgJm4h0Ly9LdlX67rHyILCiTFIERVqTclkWPJsULAZ2mNKUZ076yfjZhZDfZbyRExRZOURmGSAKwc2QvetGeYJnkdRocCWV+ra1772LNXqj2mXAbhnop1KbDMelvhtrJsmpUC2x4hAY7SI2hhTdCmOogqGJIBQan+E15WBwcvK+6ruvNOFk22MAhhSWinblHn1Me6bU1qPnTEa4cErRykX5TaLMEaU12I3PE97a8NpYm47LgRJQCGuMHP9TvuIghEGXMscqm5lOCF83BMD0PP3qEc9qvcsym9UUTA8jdqIEQB3ZCFlnNFK9B1kQ5EOe+yxR/+9ruVcbWaed6xxQn9kMBfxyBuvLXmCXY9MwpLRYpkKxZ/HFpmAjNqyYOgQBg0ZMzbkDlGu8Ya+oN+VrGQfV6ZwdP2b4Tb2fHkG6RDueaxfMcLgZEkWo43hI39GOxYaz9w3gox4ltv97rWeJYYWQtFSlcXIsr6wZf5DEOuX+gW55jWvOYqDOvIcaxvjXZsvZloV3Kv8WIhB44z+qR8S+2BXkTawFrnA2135G5DjHFT6Y41VyE/97xWveEXvAFNWtR2yowxv21tZ7flLRBbHHAxbYShLSu65Heu/7bGzfDdHI8W1Rd1rkQFjkQxIHeNLa8SLEOBgKHJTfxT5xSEhkkF0i+vQEYy7nmVOh3p+Z6nn2DH6ebXV2P7aRp9AYhdeiHIE5zA/j2PMJ+Z4JBXsLZfzHM5ynbpePjcGAiEZ1mg7MsgpfQZ0DzXFxIRiAppVTBQmomKgh+dVFISBYpIstwxsLSXMZGSCNrjXIDXpmtkeBDYqAjyOQmIPOOCArZEMQnYp7a0BO+3+RQKYsCkhliGUgugcyiPDyHNGgfFsD8PBp5U9to/SuVIkg/KFgDL+KRxCTMeEsgonClcp+2PHjW2zpt0yBmMfQ4QhP0ayjp1bOQ2EjPPeFRGrLEZ5Gw5bCl+VI9RVuwyXdtR+n8rRRtoaGcKYGIo6My4RR21OiPY4EQmMTWSCyAqEhW3ChF1fjgTe3FKuGQGUUv1uzLNHgRXhwlhRHsOWN5Vy2yrCbR3cQ5EZFOaxch1v/pkU7VHlqe9xIfoiQwNu+ogQcyIkXf+EQ0vm82brm9brS6TGAHCce5Swj/BoEoRNkUjIHnkQlItgakUIMqNBnyvD0n4kgKUTnnGkPV0ASce4K8+m/Cbay5/+RCaNI4gFRANjRj3kAuH5RvYREY36Trt+nLeasa8vMmgYoQRm8sqMyXL7eJsbRlmzGiaWoqj/2PGIC6Tdlv8RKgxAZJKoS7hpA9ctoqXWnIs0IXBCyDACea5XU9S/7Qd+ez4QVKWzuT4CBeGDJBsjGIrkKn1L23FW6VP6OULN3KDvFoHAkIRN/dYn9Lf67boVjWcs8d2cpp8g5hjZxu1pYkwxvsFxtecvxLt79ny0S360ufuf9KxMq//YPktRtIVxoaSe0Un6dzuu0pFFFYtgaCN9x/qy8mFvHJon6qjqtZRPOoDxp55NUdUIKuRbex/K9pwYSx/+8If3OWNEABoHJ93LUuqTc9YPAiEZ1mhbUUQ81Bh7SulSBpNiUicZGaUU1mA4BsVyy+CNMZmYtE3qJjHKmcE/EgQ2GwI8dF4fK4RX6CPD1GvmrOc3aTNySxB8DL92GyWY9866eYZbPcPO4ZESqigCgqJHgaJced6EA48Z655vivw0I9wxbWRS1W+pn+rMu0Vx5/Uoz0+VByP3jCyY97rOdR5jw/gphNw2RrCQ0sVIWtjWOm314QkjMGLEUI5bRb/fufCP4kxRZMjw8FToeu2vT+MfcgBpXCHata8+hbFT3ie9Pk+oLQODglchy0glRq0oFso+w6C8tMqFBZzb/lLXQ8ggtSm3IhjKqEaYuK8iWur49lP/ImPeujoOdv7WosAEXsLGkQOMfUZ2a2i39S6DTb8cRpo4zjMnao9QzEsQgpRsWeSHoq14WwvL2u+cyjYPP33TUg5El3ZRZ5EttRSy5vEiG6qc9tMzoA/yTDMIGZ7uCVkhIsLyoFbUgTHDqEE0qoexwtsHJslK9PEq29hj+Y7lHNPGKPcBD88dwmjYz7WL51o/tQ+5yYB2X4g446r17e2yFtdjMDHircMnjOJjQ5AfdL96FhEM7s9rAqsu2stYJAJmTHi84UJ31Ce9ZtAz3UYwFcE0dv7YNstrOIvqrRzwg7k+aUzSl/UrYtwlCI1WB1UPRAdis42aWY35S/8lIgeK8PObYYx0m3d+ce6YKAtR1UrpztPGT8dzwBnLEX8iwuC2WMQMY7/6Rl1TWytr1rFWhIo5q8jRKmfs09ilbHo8cobI/2F82rJA3NHn6ffGU33E3IXkpt8Yj6ZFuo1dL9s2DgIhGdZoW5r4sOhC9HipPNAmmHnIhgoBrdDK4a3WGsNpk/dyyyhPnQHKJOk3pRbLGQkCmw0BSs3Y2nMeSgptPW9w2XfffXt4EA0maoaz/S972cuOQdLxojBOvUe7FBSkgmfcvjGCgTIipJFQzCd5Gowf08aIvoA5/4kCoGi296sIxoD743lslTPjByNgkvHuXPV0P4wCyqVyCDwY4AwoniYe5SGx4ThGiDLGvO7wp6BLAFeJ1ZxTYjtvn7DsVpmt/T4Zcu5LW1DKxsgKxzH6GEIwGhMRCzzoRSIImadgSjqpD1HiEVc8Y+pMLNOhDI6J3AQV7t/u51n3N03kd6CkTuo7zqV0Wg40TWZVjKeVsdR9vJAiPRhtDCL9hyAJRHbw/M8q8hdpC2K+Y7RWMlP3iAQaCnKRTMNQdIvjyote16hP56uv9q5jbBsK40p0kDnYUg8Jmhkb+/8vrNn+lmRzPuOBh9Ur7xbLHbFSfbzqjdjQj3mjKyqH8QarNrpKvf2RdnuV45lCEFgqoj2MDe5fBBnMPJMM0Ro7jR9IN2TOpOe5yl6JT+Sw8VA9jTVyeZgT1NNYZaxAjIg+OWDBsBfVqp2M960B39alHV88p0TUgfKX6uSxTAgpNxSEm7mt/uyHa40/ngtRWa5r7CItEbSa85drTXu27F8N8SyQdh4bu46IZXO8iETRc9pKhBhBfiEdavmK4/xGSogwMpfV3O75t3TBeGseRXroU+ZO340fotkqcbG21Leq7LG61TbXQBqoo7KqXRGQsHVtjgPkxyTyvMrK5+ZCICTDGm1v7D1lh4eD8sM4N+FQZCmQk7J0t7djsjXRGHTGxARKpnmhVqIM1zB5erVeeQ8o8sfFwK8ukSBwXCBg6UIpse31KfyUaCGyY6GmlADrZxnQvJ1DJZHSjbTj2TBG8JKXh4ay3BqXDB3KD6+o76WgMuCtJx8Thve0MWLsnHab66lPeZXsY6xQVChDlKAyMhnDxj6hrbaXMIxEFVC+xpR+x/IS8/5RhERztMLjL3yYEsQbDMv21ZXOR9IgMRhuxqYyWtpyht/VlyLIUK03KAyP8dtYK2pDO4o60IY1/g6Pb+97uE+yL/UqQ9h+ESG8guYIhkmF7utTpQyKZBjDTVSFdrc+udb01zU/8YlPdActrME3do9JLR2AmXYpZbc91r2oDwV1mlia0RrM045djX2MN55ffZ0hSuCsbREkXmc5y3wFM1FGyAmklj7pGWOEOd91hlKRDBT1ScIQocQXyaaepD599/y3b8OoZZDD9jMGGWcQi/qiaEPjAbKK0VD3r0yif6m7PjtNVqqPt9fQp/xtWSDIyoMv6kteKnpQhW87h66ij08zoI1jyAplMHLr+Wiv6Tvs4Kltjg1hgGtLhqNxEhnpLUGtUwnRq73oUYgSx8JgMfGciowg5hpE6L4LBDYyatpYU+W2/VIb77pAzLRi7BYpYZyvqJp2v++MZ5EKCFDt085hqz1/DetybP32PBFj8iRBApizibH2k5/8ZD9W6u81R1hqJTqkHAKi6RANogr0g4oG0f8lYkRCISXNNXR/bea7CCP6gygkEQaiUfS76huT6ljbOUfoKMY25AaZNiaKrKqxs/SRKiufmweBkAxruK1NchRiyg+FEdkgvAnTSUkwwVgX2Srvw9vB3mOvTRRDpbmYVh7SaTJPGYwGSgtyQ/1bMQgaPDGpBuAK9W2PyfcgsFERoOC2hi0DlVjfyLAug394/54bilyF+lMQGRuWQdhuvbQQWAYoQqKd0MfGBorrmLd+eF2/KTo8IK1XbOy4adtEY/GgULbGDFHnHrSgYFE2YTEm6iyLNe/L0FhmnPLiM+R8ThLXZ4gbUxl7jKxSihmTxibhn8ZFxovw1TJYjGuEUmiNKYE9jxJjgNd6klDyEQyi0Sh5DAWKoiUcY2LfmFA0KaG8VYwQIdSWN1AqGaAIFko8AoLBwhtKLL1AJox5oYW+7jEh3Fp5vHDG/0ntZv6haE7arx76EBJrMdH+x5W4Rwo3j2rb15H6ni34Vij9pDqqPyJM7gKKuDL1Ic9PReyM4cDwI5Pun8fdUh19rQRxIAR5SFoq3zNkfmWEGAvGPKn0CPOvcaSIC7/lSjFvVwi1vkt/cB/TSLSV6uN1f+3n0JCBk3FubLnK8Ni2HN8ZV9pDxIrnV0SAZ2koxgDjsfty3GLlDs+f9zfM27nB9bYsjEfD6xqPEILILHVEllQy2EnXRACISDLfeKYt0xOVwuiHo/6HXCLISONp/daH2n7Z5qRxLgOWkSqqCnkxSfRXkSLqb7mXyClEsOdiNeevSfWxXbuupNDLEbblKPAMwr0dT9rrGZfNQYx9JDGCAO5FGsDWuIMUM+dXBAmCQb9EOtSxbbmIozGnAKO/5rP2+Hm+m+s8L6JoRFVUeTU/ioqq+VG/84ewWyySbZ465Nj1hcD266u6m7O2Jhosvj/KBiXCg2xQEu44Fn5dSFGcKB7Wmg5Z+wqLtJ5smsxTBo8W5RZzyggaCkXZ/bRJeIbH5HcQ2IgIMMQYXAxuxmAZFzye9jGQedfKICkMkHaEIsJosN6RksxgFelkGRUCAsmwmAyV1sWOp5hSINpkVIudYz8yoJRfxu2YgduWw7ijtAr/nUcYsdYVU+Z4jScZu1Wm4xAIFFvhx8ZUJA4CgreN8P5QEBkjlHJSXnbhpaVE2q48ytYkhZWCjtBAMJSS6DzLNtqEnbaVaF/K2VB4p4z5FHakFKwQxAia8nIxBgklU78xXxywQELoV/VWg7bcNqFbu913pLQ/9zhJeMfGyq3jKcOwQzRNE23QJrubduxq7EPCML7ghvypJQ5FaJkrFyMZzMn6ebu0yDvjPXNexeoe4aBd9l9YnqCtGAn6GzKw9Vq7R8aC/CKUdAZZS9ojDugB+pC+J5JJWYgCUSPGCgYoQmrsmWdsV99u8bRNbooiGayzN2aJzpgkK9nHJ12j3e6ZJzW+tPvUdZogUTzXiCOYeW7GiFgGPIyNt4jFWd+2Mu3aK7GPHsdgU2/Lac0dxrKK8hi7highkR+1RNXYgRQ1VhpzjWk1Nlm2wRit35bKtPgwKo0p5i9Gtb5nHnJ9xIcElcivoUOL3ofQRLD6joD74Ac/2I8Nqzl/VV8xdjLqS8xps0Rx1PHTPj3P7gsJ3s4Nlq61iSDbMvRT7SZCz3GEAa9tRCOJUlE/Y4I/87C2Mo4jCLcsEFCTSD/t144VytZOyitSwLaSIgjq97RP/cHxctbAll6grcfmR1jstdde/fjj+mPj0LRrZd/GQCAkwzprR4oEZdpgxKhovZZjtyLRUy25wIqWEs5bZkIxOZQ3xOBRSV1a78c8ZSgPyUABwmy3gx2PjNd7UWDa7WP1zrYgsNEQMCkzLDwDlFwexDI0PceURwkPbW+fP5E/FDqGD+WiFZ641qhp963Ed15MQumZR5AgY0bAPGUsdiyShtEN08pPsNg59osAQ/QwHihkjL9WaoxkwNfYWMYLcrS2OYdRihQa8xxR6izBEMVQ3uL2OvN+r6gL5yEa9BdeMAQJIngoDCoJhLWFPBLD+xweP/ztnvdYiHJgBIyRwgwxxgaigTFcuLXlUEaN9xRwwjOqj9/udrfbpj72l0HQnn9sfRd9YE51z/IPwFr7ew4Z7q0Hd6xOIgUREQyH1pgpxVpZliTwQiMzkAzm1TOd6Ux9lKG28hxL8izKCJaeeYaEcycJBZ+RopxWLN+BaUtcIHzknmAo8IaOEQfmZaQGcsEzpX2FV0+qw0r38fYe6jsDpRW6iygDnvGhTFoa6jh1RYhUtn+GkXYYLhPjQUb06fOeLYTfWiAZ3LelEggC/VOdOHLog/rsmLNIO+qDbZSEZRIVRcXYR4DWfGNcoQfWb+vr27GfXmfcQWpbXmX8LUFYIgpdU/9rxXNvnJT0uBXP/GrOX/o8QQSXIey353UlxhtjH7J7tKsJAABAAElEQVQHMdcSDPqMudOYMiYik5Hd+leRDPIGWZokCW/l/UEAEXgjMT3TIlAQ5EMip67jnofjtXmOVN+vY31q7xabdl99p9O7Pt2l+oucQaIa3GPNLRyexopJUZlVXj43DwIhGdZpW1MGLKNYTCh5JhghbQZ4gxTl3OTKsCl2WzmUo1KmMOalNM5TBuaW0k85UT9KkvMNqgZHTKxQvUgQ2GwIUKZE+DAIKMiMjaF4fhh2FJB6FofHtL9XU0FzHUnXjDWe4XmEAbqawktrrSpP0KRw1EnXN64J1+VVnvfcSWWObbcEA8EwpnAhjhidYyJJ2iwigoEXjWFgHKfwt4qnsZchacydZe328JplcOiTYwapvmGc5/WsuaItg7HKkJYojLfT2mP9SOJK5JioN0YO0ceQPoxsnvhJ0XkMQIo9MrtV6tvrzvudZ9D1ER+MVPVEFtTyBETNmAewvY5lLIhDmJUwZEQUUOILHwa+cHHC4C8Swm/9Wbix9tJvYEba8/sNzT/lumbb7vIIGEMkI23HEPM9AwDBRiYRB4wG4xQvuQgP+SAmyWr3cYag+2/FmzFEXBSmtQ+WQ8Kg9vnkkaf3IBu0MxyMxe6hFcY8g1j/cpwIJ+PxJFmNPjm8Fn0MuVDRV/YjvxAMnhlRcJw4+k4ryL9Wx6t9+jup6K3aPvwcvhrTmIKoQW62fa7Oc319WKQM735dp/YPP1d7/hK1QpAeDPoSYyyjfqniGdMeogsY2jWOKU9/ZZQj+DjtGPBtNIhjWtLHb2KekPuFI0Efh11FQhgvRa4Z80sf/7+zjvlf1MOQdEaykCH5YJv6GaumiTFB2yKIK9rQPSJY9BH9ogRpYswwHo4RgXVcPjcHAiEZNkE7W/clqzUm0ppRIsyJsT8tFLaFZp4yKDeUREx3re2jAFAMKZDtYN9eI9+DwEZHoA1PL+94e8+UFc8Jg9Gx5SFojzm2vlMmeVWQhkMlqerguR56QRi+lj+sprhmKTtLuQ4F3d+YlOe0PseOqW3Tjpk2zvE6TsqLwSs2tlyirulT32FwCIOm/PlO2WWcllBwtZv9COTFFP6hojmpzav8+hw7jqItdFr0TUXblYLLIKI0M0QqkgfBwGNufuLFQ0y0ESN1LYY5419uDZ7+4dKiOm7WT6HbliJQmPVlnj4efMo0IosHcTGCwbUYpFv+F2VUODKCGROeozaiwFIiyxwZWAzeWkpUCReH1/M8MSYZxEOjjELP0GmJe2XycutD7RiiPqKLkAfaZVL/ZNioNwJP/pJpBOOkMmCy3D6uDG3C815vWhF9KTJHf9duDBz9hD6D0Bnz1CqHuP9KQMrpghwT3Un0txLECqMJeSMKh0F5wMKSo10HCQ/r+JXuk1WuT88R0ouRKFphqK/xKntm1I9h2hqviC9EleULs8qQ0BmeZwxpl8EOx37HIxa1xRve8IZlGfLDay/lt74smmzYTytx4bxletbgjJgUtYGkqfFNWfCzTZ+h/3IkiOh1veprw2u2mNcYgNhEjNTz7nkWUWLpyzTST9nyahSpXPOTqApR0PKYIMVa8QwNk/7W/hrL/EYsIWI9k0UwI+s9R+1xnlfRFKJg/CFznBPZnAiEZNgE7U5pwWZbGyw/g4GLwjJkoilCk9bOzloGOJVLiaEsGtwMOCa6GjA3AeS5xSCwKAKMAVKKQJ0goR/v2rwEw5jCV2VO+6Tk8JAMjW6eZgYGj8QkUUfeWc94GbE8hLy1jKNJHulJ5c26fTWXYhSOrfLX1ovxrH20m7W+k4yP9pyV/s5DyUtEoSeWzvlDOlDoeD6N96LJrJWWDJJ3ftpbidx32xfb7/PWn5JcXv3hPINskKvA0oESXnzeN2t4eWQREZb5DZVTSzcoy0g4If/DPENV3iyfjH85CxjoFR7uPInWkAzyciAZZpEt/yMYHFv9B37tdvtEd2gbbcHoYIAgSipqwTFDgRc8x0TEgXoyumcVxMo0QU7RExhP+joSyWs8l0voTLvmpH3qgEwgnjW5CJAy8oCop/B9fYFugRxipI2NDa0RpCwe7uE227WZiMvy8BfR4Le+NjYmr2SfVAcGnHq4P0QcomgYUeC4Ev2XfjZckiUKQ93mEeTlPM+9MPzh8Yhyz7YcA/NIPTfznOPYSfOXfQzelRLjjTbxZ9zlTGujaUTdWM5iuQinnLnTn34rEoZBLlHuUOq+4Wi8ozMjwarfO96Y4Zk0liJEPQdt9ESViUz2fJvLHe/a+g7CFCE1JtNIqLY/1DiOpDDeGKPH7sc1kIL6HmIMAdsu9RurQ7ZtXASOt3FvLXc2RMBERDGlpNWAMTxmsd/zlOEalCyehhAMiyGb/ZsNAUYOGRqzjI9Sctv9Q4OrP7n5RyEYU5ybQ0a/Wo/O6ON9KRHSzqPHq94qUrW/PilRlBmKv5B6nzzNlPhpYf88kiKrLKPiiUNyrOQYQWEbKr9V58U+W6VveKwyKVY8ZNY1a0Ne6XlksXohn6ZhzvjmzaXQlvB0w08fYSBTQoVRWyJH0bNPyKtz4D3sc8px323dKtSYN47hP/xDXpBhn+PB5BluQ9eHfRcJXfsp1byDRNQFAxKJU89Hv+N//+AiVN4zstx18nChKA+JBB5EbVzec88FxZ1BxdAgY9Eb/6tiH2Xi+xBj2O677769B5RBbH4UjcNItnQGzsNzqsylfiKjGJutePZIrdNu94koQfQgznhrGQrak7fSWnxkkHXZi0nbj8aOXayPt+c4Vlg2Ag0pgxgi1qsjQJFrImOMJ94Oo/48zq0gQUv01/b6+prf2tSaeB7jlnBFAOkDDCtj7FBWqk96bj0Xnl39EkkrSsK1x65b9RBmrw8xOvVT4zEZEgzaZLH+1RqVVf60T8/oWFtbKiEyqWTsmNpXnys5f1WZ0z7NT6I9KjLBsfTbSSKvBOGZN962Y7R8LHssLFHybJgX2jlB5BCyqiUN2mtU2xZGoo61fy01QHpJbmzsEMGFMEAeixI2drZijBZtoI+rh0gnxLKxGuFi/NKnSBuBUWUYE1oM1K3mQ8cgGMwpoho8iyU1V7TjvIgXY5zoi8jmRSCRDJu37XPnQSAIHIcIlCJMURuu2+fNYgxSCkqRGHtlW1t95VCWKcNyIlA6KRgUjVLMeVuQjPWbYkOZ5QWjzEhAJxyTQnK3u93tGJ7k9nq+884wRmrNJwVFPYXtus4kQTxSaHhoeX5I69medN6s28vwpSBNMwjHypOngVLXJi8r5dwn5ZIByjBU9rxvRdBGQ6HwigDRbogXkWZjoo0QMgzkVhlE9gh5RSzoLxTQKkOIteORDJYYWDNNUWxDq11L32gVyuqf2qpC+ts6KUdYdEsGuDcElbavNxTwxOsfImOEtcOM8srwk2CPAmwdvCg6BIKoC/1vjHSCv9Bw9Rk+M23dFvvuueKNHIu0YRDo1yXuj/FJgS9lehh+Xcf6LDxaLHk/EXb6vPDtMlC0AYPMWmzlixYQAShyQDvCZ8x7XtdTH9epPA+13afxg1GiP/ms/sKoJtq7RDQSY8AyJwRRvVWDsaOvWzrBq+5P+1lGwZM7SZbTx6tM9+6ZEK0AJ59jyyG0F4OGUSssHTlqmZd+VNKSDLY5jqfd8iL4SNwqEoth1hK8jhVZhnQwzmlH2f9ds2Ql+qRnw3In0TzqoC6IEx5t/UY7W37iWdIfjNvuSRva5/lCRohG1Z/GRD3bPjl2jL7b3tvYMe02167+3m7X/kgfpJX5xRKh1ihtj63vynHecuYvY9ysAk+efv1enhdS/X6sDKSNca2iI7SBCANjq7Zwf/6GDjxRUnKhGePGpPDTNsZOETOW68itYEz3qU/UWGVsR7iJaPCn34ucMF6aPyrXhDFMBIF+YT4TsWD+0D9EF9T8jLTw9hsEArKkza+gf9WzLArLs+HZcb5rISCNpd44QvTPEvMOcrLIktqez82FQEiGzdXeudsgEATWCAIUNMbK2JsJeGn9UWxN6pSXxZIUVnnt+ux5bpVRecCCAcfbPathjsjg1ZhX3DPjhcLGayziiSGzUkKh5ikv79A85fLwDL2ApZy33l+5M5YilLby4tf5yCBGD+wRJG1uhTqGMUSJG4aoUlIlYOTFVG+kwFAofAgdhgxP15iRrP3bENwyQhl2Y31PG+on7b0wUESyKL8MaSHs1gmrPyPN/btHxpI/BhwjsUgJ547Vzz0dtECwUKqXQzAoZwwj28dE/Sj1SDARP4i4loAangM3RiKDT5swjpEAvI9jr8FEUiH2KPAMH+uYiTJ48LV5EQTDazGASUWCtPv1Yx5XItlzvSVA+dpGuSITLLfQxvrFcM2/c/Vz5MKrX/3q/l5EXwyfD8e1stQ+rgwGaUXbeCZcd5b2RpTq4wyvtk8q09hYBiQjsELHESrah7imqK7heAwrhApDz+dwbFyJPom0Yax5/rV7CXLFGGkeQEQintwLfOvP2MSD7Bk1biAch1IEw9AAHh6n744ROcPj6jfjemwph3EAScLI1N9EKI0lj61yfC53/mrLmuW7djUO6DdIfd9bA3usDG2BWNBXEDsMf8/DYlFVohkmiXHXs4oANsaI0BE1ZDsy1fNXY6kyjEf6ObLAuKrPOl992gSl+oSxkpOhorXkV0IuMPxrTLFPZB5CUbu1OTfMLfWGC3MfXaRIKOOb+0JMKlcZwzlRf45sbgS2W1DCtn0/0AricdDBh3Zbdtl5BUtMUUFgfSGQZ2B9tdexWVseHoohBWGSUA4ZZ7N4+Xm9GGpDJXlS2WtlOwOIh3slhfIL11Y5W075FHrhrsKZxwzuecrmWZpkRFPyeZonZf4fu443VTAO27wCY8ctto3nkQFdnnPGi3BihuwYjjChCFM2N4OU53JSArfCQN+zNlobMxw8ky15U8ethU+GQ7X3LPVhdEwbr6qM5fRxOLdh11Xmcj7VB6ky1o+Vy+M+zMi/nOutxXP1Rfkdyrgcq6OIHYbiYlFzdS4Ds8ib2jb8ZMB7FhYbJ47L+csz25I7w3tofy/Wl9pjZ/mOtPBMIRhFEyAwkEuz1meWa8xyjHkYCdWOVQg0c9Fi5BSS0/Mzz1gyS51yzHGLwErYLyEZjts2zNU3OAIr8ZBucIhye0EgCASBIBAEgkAQCAJBIAisEQRWwn5J4sc10pipRhAIAkEgCASBIBAEgkAQCAJBIAgEgfWOQEiG9d6CqX8QCAJBIAgEgSAQBIJAEAgCQSAIBIE1gkBIhjXSEKlGEAgCQSAIBIEgEASCQBAIAkEgCASB9Y5ASIb13oKpfxAIAkEgCASBIBAEgkAQCAJBIAgEgTWCQEiGNdIQqUYQCAJBIAgEgSAQBIJAEAgCQSAIBIH1jkBIhvXegql/EAgCQSAIBIEgEASCQBAIAkEgCASBNYJASIY10hCpRhAIAkEgCASBIBAEgkAQCAJBIAgEgfWOQEiG9d6CqX8QCAJBIAgEgSAQBIJAEAgCQSAIBIE1gkBIhjXSEKlGEAgCQSAIBIEgEASCQBAIAkEgCASB9Y5ASIb13oKpfxAIAkEgCASBIBAEgkAQCAJBIAgEgTWCQEiGNdIQqUYQCAJBIAgEgSAQBIJAEAgCQSAIBIH1jkBIhvXegql/EAgCQSAIBIEgEASCQBAIAkEgCASBNYJASIY10hCpRhAIAkEgCASBIBAEgkAQCAJBIAgEgfWOQEiG9d6CqX8QCAJBIAgEgSAQBIJAEAgCQSAIBIE1gkBIhjXSEKlGEAgCQSAIBIEgEASCQBAIAkEgCASB9Y5ASIb13oKpfxAIAkEgCASBIBAEgkAQCAJBIAgEgTWCQEiGNdIQqUYQCAJBIAgEgSAQBIJAEAgCQSAIBIH1jkBIhvXegql/EAgCQSAIBIEgEASCQBAIAkEgCASBNYJASIY10hCpRhAIAkEgCASBIBAEgkAQCAJBIAgEgfWOQEiG9d6CqX8QCAJBIAgEgSAQBIJAEAgCQSAIBIE1gkBIhjXSEKlGEAgCQSAIBIEgEASCQBAIAkEgCASB9Y5ASIb13oKpfxAIAkEgCASBIBAEgkAQCAJBIAgEgTWCQEiGNdIQqUYQCAJBIAgEgSAQBIJAEAgCQSAIBIH1jkBIhvXegql/EAgCQSAIBIEgEASCQBAIAkEgCASBNYJASIY10hCpRhAIAkEgCASBIBAEgkAQCAJBIAgEgfWOwParfQMHHXzoal8i5QeBNY1AnoE13TypXBAIAkEgCASBIBAEgkAQCAIriMCqkwxn3Ok0K1jdFBUE1hcChx1xZJdnYH21WWobBIJAEAgCQSAIBIEgEAQ2KwLsl+VKlkssF8GcHwSCQBAIAkEgCASBIBAEgkAQCAJBIAj0CIRkSEcIAkEgCASBIBAEgkAQCAJBIAgEgSAQBFYEgZAMKwLj/xXyl7/8pfvvf/+7rBLnKeOf//xn949//GNZ11vJk+ep+6TrzlPGv//97w4GkSAQBIJAEAgCQSAIBIEgEASCQBBYGwisek6GtXGbq1cLpMIb3vCG7s1vfnN35JFHdjvssEN3oQtdqLvvfe/b7bLLLjNdeN4yPve5z3Uve9nLup/+9Kfd8Y9//O485zlPd93rXrfbbbfdZrreSh40b93Hrj1vGd/4xje6F77whd2Pf/zj/v4vdalLdVe5ylV6DMbKz7YgEASCwHpG4K9//WtPKJ/ylKdc0m389re/7U572tNOPRdhe4ITnGDqMe3OeY9vz53l+5/+9Kfu5Cc/+SyHHqfHmL+2226747QOS7m49kPUn/jEJ17K6cfqOT//+c+77bfffqpO9bvf/a472clO1p3oRCcardvvf//77lSnOtXovpXY6Bk9yUlOshJFLbsMbUs3PN7xluZH/M9//rPkc5dd+RQQBILAhkHg+PsuyGrdzVFH/7Hb4WQnXa3i10S573znO7vnPve53ZnOdKbeyKfIfeYzn+k+9rGPdZe97GVnmtTmKePDH/5wt88++3QUsF133bW7xCUu0R144IHdxz/+8V4JZXAfmzJP3SfVa54y3v/+93ePeMQjuqOOOqq79rWv3V3hClfoKCBvectbupOe9KQ9wTPpOsfF9j/9+a8b/hk4LnDNNYPAZkLgIx/5SHf/+9+/H+ON+fPKa17zmu4Vr3hFd4ELXGB0TjKf3OxmN+v++Mc/dhe5yEV6A2XaNRint771rftDzne+821jZD/pSU/qCeBznvOcEw2+aWXXvnvd617dpz/96X5MR94vRb761a92z372s7srX/nK3a9+9avuFKc4xTZ1rTIPP/zw7uijj+6dBLVt1k+E9xe+8IXubGc727ogReq+fvOb33S3u93tOgalNmSUjgnc3va2t3XnOte5uhOe8ITbHPKSl7ykN/7PcIYz9FGcD33oQ3td6HSnO902x83y41//+lf3hCc8oa8LsqAVjgWOG6STPjwmr371q3vdCOlz0YtedJtDDjvssO7mN79594tf/KI/f1j+Ngcv4cef//zn7m53u1uPz7nPfe6pJdzjHvfon7Pznve8o5g/+clP7twv59FSCaCDDz6423PPPfvnT9vOIwgK/QJhos2nEWiO/frXv963+TzXyLFBIAisfQTYLzuecmlzb91dIhkKiSV8Ukxe9KIXdec///m75z//+VsVqqtd7Wrdgx70oH7f0572tKklz1PG3/72t055Bv1XvvKVvVKjcJMWBfF1r3tdd5Ob3KQz4R8bMk/dJ9VnnjJEiiB0KEPPe97zthIKd7zjHXtcKHsUPeROJAgEgSCwURD40Ic+1Bn/pxkwjDTe3jHZfffdeyL2cY97XD93DI/58pe/3P3hD3/oyetJZbTnfOtb3+oOPfTQPoLPfNdGSVzzmtfs7ne/+3UMncc85jHtaVO/t55mhu/Pfvaz7kY3ulF3+tOffpvzvv3tb/eG4mJeWvPFYx/72I4B6P7U+Zvf/GZ3gxvc4BiG0xvf+MbO9Z/xjGf08/k2F1zkB0PrrW99a489YmS9iKgV9/zZz362d5BMimJBKCGotMP1r3/9bW5PWyCoGPX0EqTOJS95yR5DuOhT1TfsE/WJgLDM05/IAqSAtvL7Jz/5SW+AIxt+/etfb41cMOerxzQnykEHHdS3wdWvfvVt6ujHd77znc7zccQRR3Q77rjjMfYPNzDyX/rSl3ZnOctZtonu0S9/+ctf9vVuo4oQNvbpBxe72MWmGt2HHHJI3w89k2MiIoKjil6zmBgTxoiILVu29Dgj2K51rWv1ER5tWe9973tHnwPH6A+e3fe85z29LnWa0xzzDXGiaDl8jEva+IEPfGCvg7bXyPcgEASCwLhGElxmQuBVr3pVJ4cAhrwN0WPkIh6++MUvdhj0M57xjBPLm6eM733ve/2EfPnLX34rwaBgysH1rne97rWvfW2vTFHMjg2Zp+6T6jNPGbxFFBpEiiUpJZSbe97znv2kh3wJyVDI5DMIBIH1jgDDiNFzuctdrl8WNnY/DCieXtEFV7rSlY5xCGPuOte5Tvf9739/6z7L7kSCkS996Uu959I4Os1zWSeLnDPvPPWpT91qRNa+C17wgh0DjKHTCiOS4WK+HJPXv/71fY4dnleGC681UkUURwnCANHsHvfbb7+JId0MXAQHjzVCWl1++MMf9ksMRVgMZaeddurJiGlz9fCc+o2UgdlaJBhE+SFQHvCABxwjlL9IhUtf+tJTIzDqOHNu5Uzi5XbP/hjtiALi984779z/ftazntUTOy9+8Yv78pEPvPfaFZnDA45MUCZiige/+i5D+za3uU13pzvdqffIF/FVdSns208kw93vfvfuzGc+c7u5/45kIHBoy0AMjJFViAKOo2F0B/1DBCXHDm//UkQ0yMUvfvGt19XXPZeltyAN4GT5xxOf+MSeAKp9w+uJnvDM0zeHYvtXvvKVnsBB4pQgBkR90E3vete71uatn6Jl1RFBh0SidynL96997Wt9uynPs6nvwHuI09bC8iUIBIFNjUBIhmU0P8+ISVUo5lDkCEAKfP7zn5/K8M5ThonJMoy///3vw8tt3UZZOrZknrpPqtM8ZVAiyIUvfOH+s/1HcaEgmqwpLZZORIJAEAgC6wEBnsv3ve99vbd4aPTw7hKKPM/kmPCk8pC+4AUv6Bj5jACGX2tQMeDMVcZR3kpReAxxnk6EuOV37bjJcyzSjNHYCkOfoXLjG9+4Pw8BYh5shVGirLe//e1bNzN43CfD7pa3vOXW7fVFXc1v8gsRyzbGxPnIgyFOdSzv7sMf/vB+acRTnvKUrV5chhMMxwwyyyjMIbOs2T/ggAP6SIpaEtDeOyKF0Vj7qk6r+cnwfdSjHtUTO+6hFdEDvNJycjzzmc9sd81sGJaB72RtL5rS/XGsKBvetpMikhAIjGVGqDm5IhDa+k1qP+UoWztbMkAmHSvahacd6eGYG97whv3x+jddqCJ/kAzIK+H/JZ4rSy/9DZdXOGaphrP20Nf8IbZEZPheQn/zDDLeCXINRiInznGOc/T37n62LBBjnte99967dyCd9axnrSK2frqWcQMhUULvPPvZz97jjwCAhQge44J70gYIMe1qjGj7r76rXp5PEbHwVb52ENUhugKp9K53vavjIIoEgSAQBKYhEJJhGjpT9pkAhc1ZK2pCGEqFCDpmkiylDBNCGx5nwjKJGPSFMyIiZhXXnzR5K2M4AbXlLqXu7fm+z1tGTYaT6kzBUSaFyoQYCQJBIAisBwSueMUr9p7MlhRQb2QBj64oBLl4ZhXzzic+8Yk+ymA4Xgp1NpaKWuDxZ2wZM8u4r2tILszof/nLX75NiPkHP/jB3hC6y13u0s+Bxtw2dLzOH34yOBk3LZHRHuPe/fHiThN1n2TEM7Ye/ehHd/C81a1u1edZ4IHlhV+q0djWRcTI05/+9N6wFiExXO+u/h/4wAe6H/zgB304uuiTIf5teSvxndEoamBsucztb3/7vg/AZDGRX4GXfssgAqXmXeczhBnyFXGAYBKhwKNOX5AbCWl1jWtcY+rl1HXY153AaJYDosiBxbDjpECs8fQjnp7znOf019UG2ts9cT5IEi06Qb6QEsY/Yk476uuLXavOm/aJmHvkIx/ZE1wibRAd+p+cXdX/4Omvft/hDnfoi2yfi8Jce7zpTW/qHSdj11VnfdAYUfLgBz+4j+oUrVCkDrLP0hhLSsfIhTpXhBL9Ur8hRY4gO0qnRTKsBFZ1zXwGgSCwcREIybDEtsXcm1QnKVc8I8SkOUmWW8bjH//4rQkfKWaUn5oUJl3Tdh4lXizKJo8BFp+CSTGrid8kbTKiMIzJcuuuzHnLKOVH/YdKDGWBN49IXhYJAkEgCKwXBBgVNfa2dbY0gEIvPHtMLKUYi14TBVCRAAxexzEULKnjETXWl6Fgm3kMKfHJT36yH5fNDerDmJf07853vnN/eaS2dfXC2BkwY1EBY/U0ZiurxvCxY2zjSTWPTRPz7pjwuqrrwx72sK0h8yI/GE5C8QkieixPEo9z67EfK982S0wYa/JQDAkG+7Ujb6/IEOHmDFz5KaotHLNawqAWwl86CYIKJvIoWDLBeCzjVR0QTETiaJhLwAwrxqllFpY58IgjoAjPtTmWZ5ueIYLhqle9ak9M2K9sZIOoBfesLhXBoP+pSxnSQvUZ5EgC5RDefcawtqCfkElt3e9c+Ed/EbkoKWr7/IjgsCyGJ/9Tn/pUH+3gHttjePcdZ4lFPQvu2fOCFGixqush1Dhy1HGsn7oX96WPyANymctcpl/u0PYt2HkGkW5kSA655yIgfNbSkrH+VvVqP5XvzWZFMNhnm3pXPYwriJyWWHRvnFWiPXwndXz/43//YB4JAkEgCMyCQEiGWVAaOaYmxklZr2uAH1vaUMUttwyTP4KAJ4pSYL0rRrudSOta9cnTY12iiUwEBkOf4uTPvVAaKQOUCSGnk2S5dVfuvGVQkikfEg7xTEngRUzoCJeaGE3akSAQBILAekaAkSKSwFjXLjuoezKGM6oRwRINl6FU++uTl5Y3lREqdw/CQHSCMZMR9u53v7vj/WQ4E2SDPAjWZtc8VmUhGCyhEOKtLEYjwwSxW4ZRHVufxuUiMuTMGZZZx/lkIKvLNDHPjYmweaHlJZYqugf1ZOya1xhbktQNZd999+33D7cPfzOw1dFbFKYJzzqM733ve/cRI+blWfNdTCt32j6RG7zn2tXczoAmt7jFLXqDWVuVkW8743z//ffvQ+Fve9vb9v2CYe1cegBDlFhyQhBLFSGoj4iOUDZPeytyGbi2cPsiGU596lP387Y2QnZwCiBgRBOUjmQe11b+kEGk5vS2/PZ79TlOnVbv8SwwtBnJnh331+5XRukJrdGMcCjxfI0RDbW//bRsxLM0drznzjKcws796osiUBE9IjGQM0ghQp9z/6JCiKVJtiETRWMMxbMndwMs/InacG8tCULvM55wesl34fn2LCAUKkeJbchFoi1ELIzdj/ar9hnWJb+DQBAIAi0CIRlaNOb4XpEKJuoxqe0G5Emy3DJqfa4BnyIglJHH4CEPecikS/Ysu6gHobdbFsIiTfaSfgmBtZbPdyIcspKCjRW23Lorc94ykCCUSOuIhT1SWCktJmwsv6zSPDEr/XqqsfvPtiAQBILAaiFgTC8jwZg3tjyAUeLVlN7y4PixueZHP/pRb1zwhnrzDuHJZ0yZPxC1vMrlVbXfnGBcHZIB5hbebsaO1wiWwcqg9vrHCqdWxlAYjmSxsZlh528pUh585yLP3afcSMjyloApw7S9BmOKB3qaIPVFKCBBhtiMnccbLhSfAYmccc4sbwwYK2uWbdpM1AJdAGkieoQR2eYhaMspoxupwKAcE0a+PBqkxQeGMBOxMBT9SSRk+yYKxj6SgmhffVsCUASZviPSRLSH9iJFPFS0Rb9xCf8YzRwmlauhLaLupyUZ2v2cIJKpIm/Gnq06lkEPc5EBCJ5h34Ahwsvzov/Tvfz2elY6i34liqNdgqJNEAuiQSrpo/YdE3oRssen8+SY0P9bsg7p4xkvEkUOj1bgLFJFZANdkG44SeClDZ1TfciymQMWcpVwYEWCQBAIAoVASIZCYs5PA61BfVJovomTTFOqVqIM18BIU0RFI1AITC5jDLRjKROiAWpyoAjd9KY37f94qCiStvE8TJOVqPtSyhDNgFzgGaNAa4O99tqrT64pTJYUeTGt/tkXBIJAEFirCFD4jcWE0WKcG0oZYsbESUYQb7rxsUgExgyjSOQDI8Yr94z/3/3ud/vQbtdAWpTXtb2meYPBPkz4xmO92GuTP/rRj/bh2pPmpbqOeXOYoLD21ecsJAQCBQnDkG0Jhipj7LPwHNtnm9f+MdjHDNZJ5/BuMzwZ47BfbRGNYsmLCBckQ70BgUHtFdfzkhyWFJTHv97+ICqAaIdydLT3xcCdJuZtx1SyxSq/jH7n+m6pwZjnflrZw32ibBjU+izSQ3RDSREY9JAxsZ3jBbFUr8WsPtKeg3CpPtZurzLpWgg4b9AQRSRyAVnnuXWPzkdQVC4Sv5GKdMux57DK9akNHN8umUJqDJ8RGLf4tmX4rj4SuSJCkAxDEQ2FwCCiNoi2Lz2S7unZRRRVRFR/UP4FgSCwqREIybCM5jdxSFJk8DbQtyLcjFAypsk8ZZg8sPIUzvIiVdkmN+v8ZFH+xS9+MZGJrkmhzms/JY4cvpO83T/8Pk/dh+fW76WUwSM3lsjK5IdgGPP61fXyGQSCQBBYywgIyRdRtscee/QJ6YQ4j5EI0/L9uD/GgnmpDE3bhF5b6iBBnnB1pDNDjiHF4CEiGYaZ7K2dtz5fIsMhycCDKdHkpLlFeHaF5QsdL4Osv1jzTzSGaAQEyDSxvKMM07HjECc8xeZloeNtMmRGoig4dULi1H3K7t8aasNyeZsZ7jzwk+rfnsMLTjcQig7fxz3ucRPn5Pa8lfguPwEiQB0qp5J+gHRB4PA2L0b2VD3kM2B8Iih4wRH5SC/h/8pwraFUJAN9ZUxEdVhGUW+OqGWTFf3pHPpNSzaVM2dSHxPN2OYP0JeQCPQZb6PyrDDw9fsy5scIg7a+2tmfPlLRBMg4y228RruiApxD/0IITKqfYzickBXKgGE9b/a1oq7u31KSxcQ9DB1Zng19ryWAjjrqqGMcV2XDXcSHtvVsjYkkr3Q1UsQL0qZysoz1g7Fysi0IBIHNhUBIhmW0txB9uRCEog0njHo9kdcGTZN5yhAai1GWobvNJlzlm5RM/NOUpTp2JT7nqfuk681ThonzHe94R6+cyLrcCnKFciPkcFYFqj0/34NAEAgCxzUCPP6MWcaQfAxEDoIhqWw7Q3mSmAsYiJaWMewl6GMQCNFm4DG8GeOMUeVYakckXkQoDBPrWtNdGeeH11QWYlc49yQD3LIM4/Kk/cpUDwaSOXUxKcN0eJx15+6RUX+f+9znGEsFGIOiMRig3gJQeYcYqcQSC+cNSR1RHMgP89WYDD3H3vaA0GGcIXlqGcDYuSu9TTto63r7VZW/68IrSt2b9q/Q/No39glj5NNTn/rUnmRgZArLZ5TW0pSxtqoInLE24jHnDKilQK4rEnPLQnj+EFtlm+8PWogAEXWjTSY5EJBKLcmgjgz+Itguf/nL9/vdi0hIUiTDsK37nf/7N9Ql3BMSYOxVp8Nj23J8lxtCBIcIE/2FDqefDIWDiiPJPThuWrnuYTg2IAIt0WmXyeh/kxxeolXkDpl2nbaOnDyOlfyySIZ2f74HgSAQBAqBkAyFxBI+d999937tIaXQxF0KFFb4a1/7Ws9+18SJ1aeokWKEfZ+nDJMHksHEu9tuu20zuZi8eVt4TYaTjuushsxT95W4fxMvxdinUMMK36MwwoWMvX99Ne49ZQaBIBAEVhIBpABPIg9kGXHKZ4yMEcfG+0oON6wHA4ChzdC6613v2i+nY2SIcrNmmzAYCU+oCDnGn+V2vKxDEpeBOkl4yc1905ZMWGrgNZzThFFlLP/6178+7bDeKDXfDoVRjaSRf4JhOk3g4nqWhuy88859mTz8cGBA1ts0lIF0Occ5zjExDLzC0M1xcJBATzQKo7d0gml1Wel96gtD7SopYzkkKvQecTALySDBMrKp5ln1tPSGgSmhJnLAdWBm+aL+ZDmCsHn33hri2tVbK0QkSAzZ6ij0IdE1lpQwquWUUg5jFrEj8oH3H2E2yRBGJrRRBOo3FMtAPReIAsSC9ifDSIDhee3vIk5qqUe7zz1OE1i6JqLHPSK4WmKkzqUnIlPUUYTNNENefTyvcmAou55FThd/JcgNWHpd5h4LEVKt0CvnEf1BhIi3cESCQBAIAtMQCMkwDZ1F9gmF9CowWYJNGpLmYPGFFvKYtAkYhasJOyQUuVI+5ilDeKGJnKFtUnU95wutpdSYbHhnji2Zp+4rcf8U7xvd6EZ9NnMKiWRilBIeIxMqb1kbGnts4ZDrBIEgEASWg4DINwaFCAaGwnKl9ZwzvmSftx7fEokivttrMIAYpLysEt2Zv2YV0QeIDGPzGBnCM2vOEpExbbkEz6vxu16rx3ttbr31rW+9TX3sH4viYPQw3GpuXaz+og/VicEoggEGSOphgj1kThnqY2UyLhmEQvSRFrzl5mI5DJabU2Dseottk2xSe2gXOTkQ8ggmxryIgVpCMa0cxIk+qd+IbikpI195ck0wUCX9QzLADmHj3CInYCFHFPIHyTTJm658/V6bK6MVUTjOXSxPVHvO2Hf9S1tX5ALDm5GPLJkk9ItWOJC0bRslUPsrD1f9bj/1DVEHRR5qG5hZgtQKEoau5DnyrIrKmUQyiKiAtWMROqKXkBNewynqqCVPRI64vmhP57mH5QgcOdKKWFtOWTk3CASBjYtASIZltq2JlucJ689DQAz6jP1pnp32svOUgQU3kVgXu//C66eIid+aQUy1ienYlHnqPqle85RR78MWzcG7QUyYwlKx9JEgEASCwHpDgGHbrvFu688wb73Jta+8qvV72ifPPgIceSARniiHlsxguItMY5hIBDmPMNIYG0hwRMNQrOFGMiCBJxEADB9GqqR4N7vZzboXvvCFPYEuQo2BbzlHGf+84AgZxID18UVszGI8t3VTH8tFGIC87JMMr8US2bku7Mz/T3jCE/pIO/kEeOIXE0YlosP8LVpiuVJvV0LMMHotDRCSL7qD0Blaj/+k64mqMae2BjiDVsSBtq6+Y0mDRI7Eay3b9rXcR+SAPmWJJ5lmlDrX9apsxzOKESb0nnnb1/mttE4f25Enk9rcfkSW+raCULnEJS6xzX3aTwcbEgbtee4JqaCvaRekC12tXltZxyIxEBEcOI7zGtFJ44LIHeUiDREnCCSiX3N0IblgKhJEdIslFCulIxkvOHcQDRVVhIyDQz2nfWXyLwgEgU2NQEiGZTa/Cdvkxfg14VIGMfHtROkSWHgZeMdk1jKcq1yKEW+THAUmQomJxpTQsWut9LZZ675S9+96sDZhmmQpzcIgbY8EgSAQBNYjArLLTxJjXRnS7TGU+rFM8O0xvvO080jf85737I0qhjBvZxl+jmHsmFsYXYyvNpzd/laGhtdwrmuPbb+PhYbbrzyGNjKhIigqATESwXbr2RmbBGnB0GPUey0iA7qS+fUHLPwbeqBre/vJ+ysajkEtQnCawdmeN/zudZ5yNjDmGIe8xpNyBwzPZZgjVJAoMChP9/C4WX7zZL/iFa/oDVPGnrK8VYTegZBXz1nnSYRHJcVELhAGq8hN5BbsXIMceOCB/euu6SCMaFEMxNsUSHtNSyskGGRgD/uYKAZkE6dDCe87I1r0p+ic6h+1f9gXa7vPxfqAZQnT3kTl/kSiyOdAGNQifZB0yBsYqZtnE+7T2s4zwglEROdImljGOWKt5Nvf/nYftQEz10ayiGYQjTIUy5tEVFRkRu23FEZfFMUi6aW+SWechWCYhhmstQNxDW2IdKr70M8qQWbVJZ9BIAhsbgRCMqxQ+5sUpimKs1xmnjJMWqUEzFL2ah8zT90n1WWeMig0lZ16UnnZHgSCQBDY7Ajw0jNGKl+N9dvICaQDQ0riYssMvO7xgIU3RchH4A0MDJQxEerdGiPt97HjF9tmmYaxnwwJC2TDnnvu2YflVzkMcyHkln+IaJNjAglRRq/jGLtksbrx+HMO8Bbz+E/zRvcFDv7xEDPiJI7kbYfhJDJlcGr/0+sw5R8QlWG5xzCB9Ng5Y9sY/tpMBGWb80kUBuNPJAuSYVZpdQvtTWDZbrcNKaD/ILEYm/BkbCNvxkR7Dt9OUsdZVqGe++yzT21a9LPyKiDcqg85SZ2rD0wqhAE/bQkG413+CCIiY7/99usJFPlKkHLyYGk3uggyR6SFVzgOZUiEqHMRN+2x8JW7xBITIqoD0SCaQb8YEiyWydarNdtykEGIGgTcm9/85v6Vq571WaTySqiL58nyH+JVoJbPICsqsue+971vP2549kQfqc9ieVdmqUOOCQJBYOMgEJJh47Rl7iQIBIEgEAQ2CAJD42R4W2UQMComCUKBESjXQ4lIMKQCI4IB+pSnPKV77GMf23tsGckyzTMgdl1I9shoYEC113Dd1ngvQ08o+ZjxxJgjY/sYQTy5DKmSliywjWFTInrPdYjIA/WTj4iR3UbzlWHcYljLSxjGEgMKTUe8MB6RDIwy3neRE4uRDaIovO2J51muJaHp0zzZVf/hJ1zloWCgz0MCDMvhSRf1MSQBGITwquR+2gK+IhEqcd9ipEhh2ba56/vNmL3kJS+5tY/oN0gC0SYMYEZ323eG9Z7nN7xh3eIsrwJBNrUElaUGLcmgH2hzUabO148QBxVtMake+joyAVHn2UBsEUsCkHF+WwokEoUhjphDPLRRMfV8OM8zoF6FCbLCb3W3/EBU5pb/LXtwvIgHkRNIHLgWkSKCVXTKQx/6UIdtIzARyeAYURaiDxAmnpXFHGFtW4toQgASCT6RQPq7cj1roiu0seXCyCHPRLu8ZptK5UcQCAKbEoGQDJuy2XPTQSAIBIEgsJYRKBKhrSMFnzHDiDvkkEP6XTvuuGN7yNbvjG9vk5AIsIwTO4X0877zmDK2LJ2oZHvyCPHeMhatG2eIIh2EyZcw1ssYsY3RTixZGPOsMuaEVZeR3x+88M/9Cenmya8Eia5/+OGH9+vXGUiML8e5bwnxRFogBxg9jHMJkCWhawkG5de1itjwm6dVOZYa7rGQYZ/RVMYew9B9ivBg7DFm2zX87hcWlgaIpBDqb1kAI3uaN7zuddIn41c4vLJaw3TS8WPb1QtZMrZEg9GHUCqx/MA9MpzLQF8sd1Rh2fZHxjFyioFca/9dQz+xXER0iWsw6OGN7JAAdOiNr3r5VB84V46Hdp+2k5uEIYswq/7sHAb0Pe5xj61t6TztWom26zfj3ZIUf0QZFd3Tb2j+6WveEoK8cE+M9pbcqEOVgZSyfMfbL0S2eD7r1ZmOa0kGv/fee+/uQhe6UL98wn3JWaE/Mehh2QqiT73lt4C5XB/aFLbe9DHE0zOLtEP8yMugbS0HktsEoaev2ubZElVimVCbx6vaWjv4jqzxJjMElmcRDiJREHOIMQSIfgEnYjmMsiNBIAgEAQiEZEg/CAJBIAgEgSCwxhBgEFLYW+OTMS0knWEpp4Iog/JSt9VnvDAiJIFrheHA82r9viSNw1wGjmWEMCQYda7VGiH2IxXa5G4Md4LsGFvjzojiLR8mQmSgyaeg/DL2eZaRJ+pfXl6GOMOKQcXgUqciJZw7rJ+6VJ3K++5+LYtwLEJh6PF3z9bZM5ztY8ARBhqShIHsnnmand/ef3/gEv/JK8QgbNt43qLG2nBSGfBjkFs7j1SBd4W/TzoHloxgBiocGZSIACHyY6/CdD+VG+KCF7xg/6YD+Q+8bhXOkyInvEWCIHKGou29UYpIYKodCCMcmVD9p9+48K/yd9Rvn/In+NN/4Y4IQbi18s1vfrNfFqDfiEpAgs3SNpZuemYY88MoGCQDIo0w1Iv8QMaUcY480fc8K624L4TfG9/4xn45DuwqgWslwtQ/9VNRBtrIsp/2WYORqANEBXLCcfJfiB4aPjva2ivCRbsYP0RwtM+0KCikCpISyeAZluNFuY798Y9/HJKhbcB8DwKbHIHtFgbTbd/Rs4KAHHTwod0ZdzrNCpaYooLA+kLgsCOOzDOwvpostQ0CawIB3lTG1dBLX5WTB4DRODSwav/Yp1f9MvTadftjx03b5k1KPO/lReX15I1nzIzVhafTWxSW4/GfVp+xfSI0GMJtndw7Y2/MIz1WxkbeRu2zjIahuJgwPPVFBilPuQiSlSJZFrv2auxH3iGthstyXAsuY9uXUw9LVBjsY8+GckUlTXpN5dh1PUuIABE/ByxE9niWnT8kDMbOtU20gb+xa0psiSwce0VnlSeSBxkyzB9SpsRK41fXzWcQCALHLgLsly27bPtK4XlrEJJhXsRyfBCYA4GQDHOAlUODQBAIAkEgCASBIBAEgkAQOE4RWAmS4XjH6R3k4kEgCASBIBAEgkAQCAJBIAgEgSAQBILAhkEgJMOGacrcSBAIAkEgCASBIBAEgkAQCAJBIAgEgeMWgZAMxy3+uXoQCAJBIAgEgSAQBIJAEAgCQSAIBIENg8D/Y+8+wCwpqreBtzliwqzoqpgD5hww5wQqZkHMOSdQEUHFrJgjYsAs5qyYsxgxK4qCoijmrN/8+u/Zr7a37507O3d2Z4b3PM/Mvbe7urrqreqqOu85dTokw7ppylQkCASBIBAEgkAQCAJBIAgEgSAQBILAtkUgJMO2xT93DwJBIAgEgSAQBIJAEAgCQSAIBIEgsG4QCMmwbpoyFQkCQSAIBIEgEASCQBAIAkEgCASBILBtEQjJsG3xz92DQBAIAkEgCASBIBAEgkAQCAJBIAisGwRCMqybpkxFgkAQCAJBIAgEgSAQBIJAEAgCQSAIbFsEQjJsW/xz9yAQBIJAEAgCQSAIBIEgEASCQBAIAusGgZAM66YpU5EgEASCQBAIAkEgCASBIBAEgkAQCALbFoGQDNsW/9w9CASBIBAEgkAQCAJBIAgEgSAQBILAukEgJMO6acpUJAgEgSAQBIJAEAgCQSAIBIEgEASCwLZFICTDtsU/dw8CQSAIBIEgEASCQBAIAkEgCASBILBuEAjJsG6aMhUJAkEgCASBIBAEgkAQCAJBIAgEgSCwbREIybBt8c/dg0AQCAJBIAgEgSAQBIJAEAgCQSAIrBsEQjKsm6ZMRYJAEAgCQSAIBIEgEASCQBAIAkEgCGxbBEIybFv8c/cgEASCQBAIAkEgCASBIBAEgkAQCALrBoGQDOumKVORIBAEgkAQCAJBIAgEgSAQBIJAEAgC2xaBkAzbFv/cPQgEgSAQBIJAEAgCQSAIBIEgEASCwLpBICTDumnKVCQIBIEgEASCQBAIAkEgCASBIBAEgsC2RSAkw7bFP3cPAkEgCASBIBAEgkAQCAJBIAgEgSCwbhA46UrX5Jhjj1vpWyT/ILCqEcgzsKqbJ4ULAkEgCASBIBAEgkAQCAJBYI4IrDjJsGGHc86xuMkqCKwtBI486uguz8DaarOUNggEgSAQBIJAEAgCQSAInFARoL8sV7JdYrkI5vogEASCQBAIAkEgCASBIBAEgkAQCAJBoEcgJEM6QhAIAkEgCASBIBAEgkAQCAJBIAgEgSAwFwRCMswFxmQSBIJAEAgCQSAIBIEgEASCQBAIAkEgCIRkSB8IAkEgCASBIBAEgkAQCAJBIAgEgSAQBOaCQEiGucCYTIJAEAgCQSAIBIEgEASCQBAIAkEgCASBkAzpA0EgCASBIBAEgkAQCAJBIAgEgSAQBILAXBAIyTAXGJNJEAgCQSAIBIEgEASCQBAIAkEgCASBIBCSIX0gCASBIBAEgkAQCAJBIAgEgSAQBIJAEJgLAiEZ5gJjMgkCQSAIBIEgEASCQBAIAkEgCASBIBAEQjJshT7wl7/8pfvvf/+77Dv97W9/6373u98tO5/lZjCP+sjjX//610xF+fe//93985//nCltEgWBIBAEgkAQCAJBIAgEgSAQBILAtkPgpNvu1uv7zkiF1772td0b3vCG7je/+U13utOdrrvUpS7VPexhD+vOc57zLLnyCIa73/3uvWL+5je/ecnXL/eCedTn+OOP7172spd1H/3oR7vjjjuuO8lJTtLtsMMO3V3vetfu5je/eXeiE51ok2J+5Stf6Z7//Od3P/jBD/q0V7ziFbtrX/va3c1udrNN0uVHEAgCQWAtIvDXv/61+/vf/96d4Qxn2KLi//rXv+7OcpazTL32H//4R3fyk598apr2JEL3ZCc7WXtort//9Kc/dac97Wnnmuc8MvviF7/YXfayl+1OetK1vSzS3v/5z3+6U57ylPOAZUXz+PGPf9zjPW1N9Nvf/rY7zWlO053iFKcYLYvzZzrTmUbPzeMgg8ipT33qeWS17Dy0rf554hMv3z5oTWddeapTnWqLy6WfMXxtv/32o3kcc8wx3TnOcY7RczkYBILA+kfgJPssyEpV8/g//LE7w+m3W6nsV3W+b33rW7tnP/vZ3bnPfe5u11137c585jN3hx12WPfBD36wu+pVr7qkSdFk8PjHP7770pe+1J3xjGfsbnvb2271ui+3PiZqZMLnPve57hKXuER3i1vcottuu+26b33rW93HP/7x7g9/+EN3tatdbWO93vWud3WPetSj+gnsxje+cXfNa16zsyA55JBD+gkfYbMW5IT8DKyF9kkZg8C2ROD9739/94AHPKAnGq5whSssuSivfvWru5e+9KXdJS95yX5uGGbwxz/+sSdwfV7mMpfpydphmvY3rzHzFbn4xS++CfG77777dt/73ve6C17wghMVvjavSd/vda979WP+Tjvt1M8Bk9JtzeOID/Pqpz71qe7Wt771JvVWZ/N3ibkKQT4PRa/ynOfnscce2+2222490aANlXVMjjrqqO5Nb3pTd+ELX3gzEupFL3pRfx0F0frj4Q9/eL+WOetZzzqW1dRjPBYtMy92sYttRi4xJNznPvfp+4F1wZi86lWv6h796Ef35dCHWzn66KP7tcRPfvKT/hlARsxT/vznP/fGHQQHnKbJnnvu2XnOLnKRi4xivt9++3Xq6/yWKvY//elPuzvf+c798wfP5cr1r3/93gi2YcOGLXoWPS/3v//9u3Od61zd+c9//k2Ko2122WWXDjGyJWPbJpnlRxAIAlsdgXnoL2ubst/qkM92w1/+8pfdgQce2CvTFoDFwF/vetfrHvSgB/Xnnvvc586U2c9+9rPuyU9+cnf44YfPlH4lEs2jPjwYLGp4LDzxiU/cWEws+K1udat+sXPTm960X4jwckDQWBzBrwgFnhxPfepTe+8GExqyJhIEgkAQWKsIvO997+t4M1A8JgklbZJ1/Y53vGNPvD7hCU/oXve6122WxRe+8IXu97//fUeJnpRHe9HXvva17he/+EXvgWe+ar0kbnSjG3X3u9/9+nHcnDSrtJZmls8f/vCHvSJ/9rOffZMsvvGNb/Rz5tZU3s2vFGllMt+Ym9y/9f6A7eUvf/mNJPgrXvGKPu3Tn/70UWJnk0ptgx+8UGD+iU98oieMJnmlIJTMy9oB6d/K17/+9V5h5tnBw1A/oigiLXi68EqsvsEDhNcmAoJXDux4FiAneHH6zRuRAn7AAQd01hPluQBz5eClOEnK24FCPJRvfvObvXcnYmUWb6CvfvWr3Qtf+MKOUt3iol8eeeSRfbnbfOTrHOMGLBiNJsnPf/7zfp3mmRwThhZtgoxYTHgYjHmiWPec7Wxn6575zGd2jC9DUuXQQw/tbnnLW25Ckk26l3Y19nzyk5/crP0nXTM8/sY3vrFv58Ll29/+dm8wuspVrtK94x3v6Ouw8847b7wMwv+6lgAAQABJREFUQWdtt/fee880Hm28MF+CQBBYkwiEZFiBZrMIMaHc7na320gwuA2lGFv/2c9+tsPynvOc55x6d4P0M57xjH6Sxgi//e1vn5p+pU7Ooz7qTFiJWuGZgXgwWSFSsPOf/vSn+0XxbW5zm40Eg2tMikgaXg4vf/nLQzK0QOZ7EAgCawqBX/3qV71lkweXbWBjgmC4733v21svr3Wta22WhDKHnLW4L2FdvMY1rtH/NO5y9TZuDrejVfr288Mf/nBv1X7Oc56zUYms88je05/+9N35zne+OtR/UiLNVaznY3LwwQf3iikFlXLKgw2p8oEPfGBjciQI5UMdEclbi2j4/Oc/373zne/s52VKJIKbFZ/ipTzmaFZnBDllmKgDRdtWFfPXthCKN1LpkY985GZW8doac+UrX3kzz4G2rKVk8yixXkEKqKt+4k/dHCN+s1b7bU2CjOJhYNsLgqC8FJAIX/7ylzsEjDx5ID7rWc/q21U+lFrz+j3ucY+OR0sRX1VmaYbCS4G13NbKoSCmCBzaPBADY31IGyNWht4dCBDP4AMf+MDubne72/A2M/1mTEJG1X2Re57LMoYgDS560Yv2xMuTnvSkngCqc8MbMKjwWPDMDMVxpA8Cx1/Je9/73r5NrC2RgbOIPmBNeqELXWiW5Juk4eGDYEI+eY78veUtb+nb+JWvfGX/XPGwQgT5I7bKInpg9djHPnaT/PIjCASB9YdASIYVaFMTsEl5bOHomC0CFGkkxDSxjYCV4SEPeUhvRUEyyHdryzzqY5HLulJeCW0dLF5IBYK0qCCXvvSl+8/2n0WNhZ/J2yJmteyVbMuY70EgCAQBCLBcUmKN46V8FDLf//73+68UHpbJMWFJZSF93vOe17uDU4IpeqWcuYZizlpI8UQw8KLjms3zwPa061znOpuMk5Rl1uQad+u+XMN5ViCCkRNcu4fzDQWH9bSNC0ThUU+K3R3ucIfKbuMn5e9DH/pQT4Y4ODauO+56eQ9xcm6lBI6UpT322KNXBhEmvBvMQZQm5D4MLnCBC2wkbihQlMUtUcyWUg/Ey2Me85ie2EHMtEK5U05Eh/ZuZahAt+fa70UyOOZeT3va03piiQIob9Z0xwlcWMkRCJRlBIY5+EpXulJ/vo2xMa395K2dy3NnUtof/ehH/T5/87s0vB0J8oclv7YuUF4RHG1beK5steSBglQYyqz4DK9DRCi//vzd7363NxT5XQIv/eYzn/lMfwi5dsQRR3S2NO244449bkgc3gie14c+9KG9B+eGBa+KobgXvD3vJdaN+qE8EC6wYJihyOvH+siDH/zg/rsxop5d3gNigo1JO44Mz0+7TloeIZ4Pnhu2s/ACuu51r9tn8+53v7sfd+50pzv1pJ02sw2rSJWWEBreN7+DQBBYPwiEZJhzW5pA7Zsz4JsMhlIuhtIsJixYJlOTbFlRFrtm7LwyTZrMpW8npOH186rP1a9+9WHWG3+znpFi7WtynFRmCx7lssA673nPuzGffAkCQSAIrCYEWHItrFuFTvkoDxbdN7nJTTpWzVnFvPGRj3ykVwaH4yOXf2MnQtcYyUXdGMnToZWXvOQlfWyg17zmNZtY4t/znvf0SpR558gF13FjbOs63ubRfqdwUm4mEb4UGfVfbA+5sm/Jnv+2LEv9XnMNpQ22N7jBDXrFUCA7ChSh8A3d0pd6ny1JT3F/wQte0JPvQ2WQ14i1hDgZiwnPDITTcM98e92LX/zi3s2+vGUQTDwUWMStD2wXkAd8pglDwZgCCUPeIEUuLKboM2wg1jw7cOANQfRR/Z7iztiAILrc5S7Xbymtcgk2iJizJfWggw6auvapaxb7tP6iSFPWedrop7aU2CZQddGX/NVvxBVpn4t6Zj33r3/963tDydi9peNhcJe73GXjac8Y5V1bFKmD7LPllGenPjK2luNdQskf227iGUcmfOc739l4H1/kgzRAYDzucY/bhNR03hiEVEHmuK8/ZCrvBG3N2wdBph7aB4HFg6W2VcgjEgSCwPpHICTDnNsY82+AnrQ4K0bZQLyYsJYsR1iYWDksPjHuBnsLTq60tRAwaZkg7O8bk3nWZyx/b9/g8qhs/ki547IkDRc1Fg+se8TkGAkCQSAIrFYEKB1DgkFZKU0W4AiBMbGVgmIwFIHvKvgdK6mxcOcFqyily7iJ1ChFxjHzEBKXm7Kx3Fxg7KegCvon6B6xl96+em7sFJhJQfiG5WFRl1eN2cPz9ZvSQdGYJubNrS2FFVJB3SlVrVAutcWGgbWZJwhPOnMXxWkllSckARd+W1UIgoqlXxwFWyZqi0OVG8FEWLlhLmgzTxLKqetYoFnEEVDEdkj9iJKob9jSwCJNESb6MLKhSIfdd999YwwF1+lTFGnpuOrDjAIuH8K6r6+x/rsXWaytrVfg+4hHPGLjWsV1+huvBaTQxz72sd7bARnRPmPqLZ2AqtW+6ux50U7KORTKtq0OyjjWTynU6kWhty4RcwBJaO1UoswbFvoJ0o0MY6moc5UHEeHNWTxDFiPf2vzVuwgGx7WXclc5bPFB5LTEonvpA2MGGXWyjiqvqrqXttNu1qlitLTXIuN41do2U/dVJqSPbUQIMPevuhqv9NG2jeo++QwCQWB9I/D/R8j1Xc+tVjuTERm6N1YBaoKwqFtJsTXBJGtiswDgCsu9zh+ig9eABYdFgjdXTJKVrA/XXHt/YYU1r8nfopkFBwHCtbaCUqnTPvvs01vYlLe2V0wqe44HgSAQBFYbApQUSp+tY5SvMRGjBvHLelqL9WE6HgmUOWSCuDbe3uMYyyVlRJwE15cFk/VRHITDFt5yVPNQ5cm1m+LMu0Fe5gyKBAWkLLOVtj4pZkVkUHaHeVY6n8gOls1porwrJRRvdWnfEtHeyxxECTI/tsId37VFgDvnO5wQOvVbgOKVEi7n5mjtSlljPCC3v/3t+zlTW/kroZzXlg6W8Hvf+959Otcij3gtkP3337//RK6UEomQYrmWN0t7K5R5yrtAmRWoUUwQ2ybgahsFIwBlE9lQaxzztj7ljxJK9J1pUn3OWqUMItJ7FvRtyq39/8iXofJa64J2K4OYDiXWRLXWqGOTPm0bcb+x9IgLcSsQC8RayZpKH1JPeLlvxU2gyDuO7CFiKPA2spVqbFuH/spTBRb+eG24viVBYGtLFDJAu3u+4YWI8RYY4lptIyDkUKy/EC/Dc+5tCxfvleobrkVKeE6RpPpTid/1/KuvdCUILSSn51+/qr5T5/MZBILA+kUgJMOc27asDcXiD7M3EZB2gB6mmcdvEy1PCEo5N0mTv8nIxGZvn++Ee2QFCRu770rVx6LUJGxiMllbmJRYWFjo+DPJCtTE0mQCx/rzbrBIr0mtrstnEAgCQWA1I0BhLSVhr7326r0AhuW1SBdUjwVRegrCUCgcyArW0HKD5x5OYaQcIGYpemVVdb05wDg6HDdZ6t/2trf1nhOU0FJYWWspG7XFb1gGvymOZLHtBBQ7f9tKxKig0LGkTyqr+AIIhNq+R3HzFg3W9DbAI+8PbvwC+1GWJ3ktzquu2kx7qgNioKz5bRyC9l6llOs3bbnbNMotjgZpFULKO4XaKyuHoj8h/qvNnZf+nve8Z59U++rbLNkIMn1HmVmyKz5VEQ9FNgzvMetv3hMMJBWrob2u6tOSDO15ZIAtQWIJTFuHWUMpP88ABM/wuYGh+Av6k3O2PYm1hcSrV0OKk9JuQXE//RA5UvEJJhFf1kGw9Ok6wS0Zi1qyTl/wjDMokVm2z7RYLPU7woL3FbLH2GNNhiRpSRjfeWjUOOceMDfOIDMFimzTL7UMSR8EgsDaQSAkw5zbysRmUrAAGRMTL5m00Bm7ZkuOWVywZhTLj632HnB/LFYmaMdYIqbJvOtjIYL1ZqnjDmyiwnQPhTcDIsKeSi6MMGWRESyzFkCORYJAEAgCawUBSq7FNmE9HxvDShEzBo4RDK7lsWA8LBKBazOlyB5pSozx1ViPUEYWEKRFWV37A//7x2uMpbxc2esci7W/afLBD36wV8wXUxrMh944ME0mkRD2nbsPInqSV8e0fOHJ2k54HDzsYQ/rFXW/KzaSiPfwMj/b00/gYh851+/y6OtPLPxTHq9hFAhza4jXifJE4fnCk6HegECh5lXhbQRLETEPyuJPkaYI84Qh2mEsECkFl0xqa/O0NGWVr/zr07XKqz9WGse2RJBilHqxCBAw9UpMeRWBMYlkcLwMLeXlo33Vq72mPAjk2R73myBzEASs/bBTFtsSPLfqqK+KqVXbIRAy1jz6WOsd8H+5bfpfG7i+3TKF1Bg+I7B1fGtKPYPexDHWFxiyePkgPMeEp4c36sA3EgSCwPpGICTDCrSviadeeTUcSLm3ES54KylFLozdA/Psb1aZV31Mhph2wZtYYVjepgX6YqEbY+a5MFqcT7t21rolXRAIAkFgayDg1bs8yCy+uayzlI+RCI5PE+OneaUUTWkt7FkJ7d+njLOus8q7X5EMPBmGyo3tFkhei/4hyWDP+5ELASDLMj4sU7nls/TaNlHKxzAdbwzlsXVjmtie0SqklZaiz2I9qRyVbimfFD6KdkXvp/hV+csLUXlh4o0ZFMdSqNSn3vREgVyuwjxruXlUINmVj2JL9AOeLwgclu4q42J5altvEUFQsIIj7pFejBPycK+hlCdDeQoMz4vpwRukgjsWjvUpPS+Z9m0Y1dcntS1LebuWgT0SwfrFVkpxRjwHvCFLmS+SbowYUAbt7A/hVt4EtsVQmhFN7dYKeXhm2jLIoxUGI2SFPHg01PPWpvFdXdUfjosJ0mNoiPJsWFe2BJBYJ8N0w7wLj+HxWX4PCQxrL3EpJmFbeYr3gXQaivHEWz/grL9GgkAQWN8IhGRYgfYV74CbHFe24YRTrzcae5XjChRlLlnOoz4mK25+Fjcmdq505ZY7LKSJ1H5Li5UKPlVpBNoySbO8zLqgqmvzGQSCQBDYFgiwfPMuQC74TuxtHhsDW6VsWFZWdq825sZPsRKgzxYHLtoUPEHg3IfiJZ/af08ZQSgMA+lSsndfCOQ3JvKyVQLJXAr4MJ293MbhSeelVw6KoTlxMRl6DEgv70lK6GL5TTqPGPDHW04bqIN5DkHDFZ/ng/ve61736rNgmS3l2RzGu4IFfdLrOCfddznHtYMy8r5oPWCus/CKUls4tH+55k+7D4y9otR+fCSDfO2V54VQ2yPH2qo8cMbaiAWfAsozsUSbCQhayn8dl7f5nfeNOAWItklbcqyXWkMNYovCXwSbt1YhjJ7ylKf0bekepVSPEXhVhuHaQZ0QFmPbS4ZpK4/61Bdgx8OEpwFSzLM5FAYmRI46SDctX3UYkgeIQARIu01G2/NInSbq5RkcixuCtNHfh+cKw/qs/G3bML54TuA+JkXSIPH0Ces5a76qL08iz5Y14bRxYyzvHAsCQWBtITA+SqytOqy60rJ82LvItdHEXwOpwfxLX/pSP+DWxGugLWvKpIl2W1dwHvWxoLY4s7ATAXnSBKWuJmITmU97OWvfpAmRmzAZex97fyL/gkAQCAKrCAHjHss3xaOUOMXjYj3mjcUKWMHhhtVgHfQKPQqcfeUW7LziykIoPYWRUFJsi7PQR0Kwsg5JW0rKJKHc+Zu2ZeLQQw/dLM9hfpQqVlj7tKcJ6+jWDgqnbLwszDV77733Zt4cymtLijgNRTIIWAwTr+uruX1aveZ1Dknk1ZLaFTlS2xvKO4VCPgvJwKMG2VTzqvIhUyiB6qYdtJW+Y7ui/mQ7gi0v5u12i6U5WfwQVnqxk1rSzHoGmVCvwYSXfHgo8hLhecP6jzArBXSIlTq2BNP73//+YZJeyfYKRkQBYqG2dQxjKGx2YXOgiL16c0tzauP2i/ZY+/2GN7xhf09Ejzqy1I+tb5AMMFFGJNa0N7ioi/JbR+qjnkN9jZHFX4m68sCwXWbPPfesw5t8IhnckxfMULzdS5sMz2lP7TOsh/LbkjWr6LPKJQ6DtkRUiSdTQVNnzSfpgkAQWJsIhGRYgXbDNBtE7T17yEMe0t3sZjfrB1cTtsHeRFTCwlSvj+T5sDUXLVWGxT6XWx+uqMXsm2jLOjS8L6ucSNoWfdxjLcwtUOCH+bcAMsGynrGyRYJAEAgCqxkBih8FyDaG1iK7pWWuAHqup3xxkxfAjmWRlXsoFCBvCWBlNbYu5ubcXs/7YPcFLwdj8dj2OmM5MhjRQNGcNHeZP2zdMI4T1utPfepTfTyDtjzOl7LXlmMlv1OmKE62jJhbWF+HQhkUtwDOvAgQNoJ2DhWw4XXz/s3zALGkXVie9QUEk/JT3GsdMe2+tjrok14/aO1RUkq+/HgZspojNKxZEPrnOte5+ngK+hNygucAz0ReOWJWTNv+qd+LEyKPVsSZ0OYtadGen/W7tyggB6ytCKVa27TkxDCvYWwDBiBEkxgKQ5kUX0s6JAtCwJqFINNgNowzZQ1km4HnyLMq6OYkkkH5tZO08hVDCxkpRsgee+yxiYeDtuJtwLtTX1aHoSDEtNskLw04Dc8hOB784AdvRj7I2zNgG0tLUg3vWb95qxDtYeuWdSBPFv13+LrYuiafQSAIrB8EQjKsUFuKRmzvHKsB10QiiI9XNU6zDK1QcZad7XLqg7U3yRIWt0lSliLn6/3YLCGsHcQEapFloo0EgSAQBFY7AlzRy314WFZKQ2v5rfNLUbSNpyzOFAXkNS+HlsygANp7T2mwD3opIk+edt5SJI+hCJ6HZLBtYBLBQPGhpHqTkLcSCI6HdOCRxq1cLAcYEViYKyiwLN1jXh7DMiz3NyXIlodSSCl4Q1E3ll5zIOWKZ8PwlX/Da8x3yBdu4jvuuOPw9JJ/e5sSfASapPTCEVFQ2xMQSdPiBtQNbbVhca/6Ok6hZbVu3dfVk8WZCDhZJITfvDoQZ/qUNw2Q9tr+QPMPfu7X9ktKMQLroQ996EzkSJPdZl+9haoV+Iwp25XG86W8rQj8yXAx7MfqPSQM2usoz0gFZIP7eoUkMqVeW1lpkRi8mPQF6XhzThoXKPGw4u2KOKm3x+hz7iVwtnJqM6QYQ8y0wJ/1qtIqyyyf+pLXn46Jday3aKhn9Tl9CHlorGilyBx4+7N+I/pOJAgEgfWPQEiGFWpjg69FH2UZc4v1NSi3E61bY/EXcyOVznWzpJN2JWQ59RGXYqlltyiBnQmUu6/f9h62i6OVqGfyDAJBIAjMC4Exy2jlLeDemCJtu4RAjosJi7YI+9ylLdp9Zyksxc/1tuJRhJxnuR4jNeo+FIVWXDeLTEpHqbBNjpt0WZnLI4LLNZLBfnZjPEEwcKtGyvO6QEzUtsJZyrElaVjYKe4UQPErKJrKMRQKHm8UdRIMcqiMDtNTzBEArmHtb7fJDNMu9lscDlsRlI/Sy7qN6OANgoBnES5lb7G8KLkbFgIekmpvdeF5ySLfehUcfvjh/eutrV0o0RXPo4JOtvdEdAkeaY0ztHDDGHnDwFIiP5Z8Fn1bdlqPFmlKOa307ee0c9Lp823MivZa32Fo26Z4DsQWVp4+nh8484zQ7yjTFPnyUugTD/7BrgKa8lxFuNkiS/TfEtsGEH7WL7x6kCyHLXjE7LwQUHQoSAbjRj0zdd5WGME1EXSIMSSlZ2sawVDXTvrUn5cqyuX5HAqjGq+aloCrdhCDbC3FIRvWLb+DQBDYMgRmW0VsWd65agEBE/G0heZaA2lr18eiuPVwWGt4pbxBIAgEgZVAwBYFc0vFp/HWCX/1ajnb7xC1rPDiQgiWx/o9KVghV+9WgWu/b0n5WdeLFB6S68gG2+a45ZewkNvyYQ+3QIKICHvSWyt6pZ3HJ8urOlI43QPBQYbB7ihi3NLVhYeCrRLiN3B7nyS2mNjrzrPkiCOO2CwA9KTrhscp/mIWUNApcCXeZoBk4ImxFLfzIhjko70JDNrjjvFA0X/0F0rzrrvu2hMlvFHGRHtq7zHhdq+clPhZpeIq8AhoyQxlRlBMEwr89ttvPzEJJRmZQMQ4EUMBgSJeCYKC96R2Q5Ygc5AqY7EaiqSpGynz8Jhz8H3nO9/Zey/4XUQDbwYGmCHB8vnPf75/W4W0rSA09EOkmJgt+lhLKLZpZ/0+fOZnvW4sHRKBtw+8hnUaS59jQSAIrH8EQjKs/zZODYNAEAgCQWCVILCY9ZCHAplmLad8s77W2yOkZ0mu1xhSQLlJ23rAYrvbbrt19773vXt3ZZZjVlUKVHuPocJRip69+WPKE2WOjJ0TUI4lty3fkCxghS0RgZ7LOuF1QeGjmFGyh5bxuma5nxRNW+8ofQgOHgPwQMiwyBNtgVTg4q5Otqdw8YenMlJOKaAtjq7zm1cEBX3S3nvpFhN715EtQxJAPAPtUtZ4bQFfnghIDTLJw6TuWf1s2B8pxfvss08fhLP6iACjYjDw8kBwjNW58l3qJ7KsSIy6tratIJtaggrJ05IMyi4YJMKHx4F+hDgob4vKb/gpj7e97W19LBFtWd4AyAnbSfwWuJsninQ77bRT359bN38BGUs8A/KsfoAc8Vsb8ErihVnbHlyDrOHpinjxV0SKrRy8U/S5oXgeeTK4Ly8LnhAIE8/ylhqyPPPD9h/ed9bfCBP1bwkGfSkSBILACReBkAwn3LZPzYNAEAgCQWArI9AqSXVrygVlhhL385//vD88yU2b8k3ZpYCWcuICsQ648iMWKFuvfOUrNyrL4gBJb08093176gUlbqO8KwOlo6QUPa7j5Spf53zKgzs5IqAV9ePSzZLPNZywdAtgJwYDBYnyRcmlMAmIR7G3b922Oso5rwseBitFMCg7DGFFdl5wW/fGBlsckA5lfUfUsNCyIFOgKPHqxoKsvfzBkFW6FYobJRBu0+IDtNcMvyMLeKmMbalRxsc//vEbL9EGlFlKeZFD0zwtXFjt1rY5S7468+aovf/SquNzn/vcnsRyD9s/uMwjHLRfq1hK34ryuEfFeGjP2RpDkRcU0Gf1Z9dQoG0pKsXddYiUXXbZZWMWznlNpm05/og8yrtnY8L/fVFWcRN4K6iTz7HnDL5iEvBuEfPBdgdtXa/OlF1LMvgtHTJC31AfbwHhialfDfuHINdIB7E2YH7ggQf2/U55kIFDPD2zSDvlscXI8yy+hr6I/NIvHfNseda8EWSx9i+CoW1/9dhSQXb44yEkHolxoF6FupztQltanlwXBILAtkcgJMO2b4OUIAgEgSAQBE4gCFh8Uxxbqyhlmhs0xdI2AhbkslK3sFBeKBHtvmfnKYyCMLLGc6Mei2VACeHSLm6Dew2VEKRCBWGUJ9KBUMJqb3V/4H//KFHIgmFgPAoaZV3+pSCyLCNPlJ9SBQPWX8qUPwoXpa5ICdcOy9fee7nfKcpFKMiLYsqrgdUcuUH59RYG++3t0W9FcDuKHZd6Cq7tKUNBYFAIt5RgkN9YGw7vU7/hpxy2n+g/3ibA22GaaF9KMAVVv6HgIgJsrxl7FabXi7KkU5gRL/DyhhGu+/pkEQTDe1KoSXmqtOcRCTxwiNcbsvgT3jbIhOo//cGFfxW/o377hJM//RfutpYM20x8CW2K/EEEvOY1r5mpbWzV9MzwyBn2c0RIvT5UvynyAxkAS+Ke3griWWlFvXjEICB8eo7UHx7lhYEIQyR464k2EoOh9YaA0c4L5BgCQn+WTvwLxMMsz06RJGMkS1vWpX5HdvJ82XffffsyCX6KcIwEgSBwwkPgRAvuTCvmz3TkUUd3G3aYvG/xhAd3anxCQyDPwAmtxVPfIDAdAdZUysQkK73I/pTXoYI1LVcKBhKh3bc/Lf3YOW9CopyWFZWFk5JDSRwrC6KAYkcBWmsiUOGYh4B6iNhfVuG1Vi/LObEUKvjgtPJTMgUDpZBSVBE8Lck07drVeI73BYJruC1HWeEydnw59bBFBbky9mzIl1fS2GtlJ93TVhBkHo8fnj3awlabWQgDeerTgsbOek94ISZ5PdQzP6lsW3IcCcO7RP5IlEgQCAJrC4F56C8hGdZWm6e0awyBeTyka6zKKW4QCAJBIAgEgSAQBIJAEAgCaxSBeegvJ16jdU+xg0AQCAJBIAgEgSAQBIJAEAgCQSAIBIFVhkBIhlXWIClOEAgCQSAIBIEgEASCQBAIAkEgCASBtYpASIa12nIpdxAIAkEgCASBIBAEgkAQCAJBIAgEgVWGQEiGVdYgKU4QCAJBIAgEgSAQBIJAEAgCQSAIBIG1ikBIhrXacil3EAgCQSAIBIEgEASCQBAIAkEgCASBVYZASIZV1iApThAIAkEgCASBIBAEgkAQCAJBIAgEgbWKQEiGtdpyKXcQCAJBIAgEgSAQBIJAEAgCQSAIBIFVhkBIhlXWIClOEAgCQSAIBIEgEASCQBAIAkEgCASBtYpASIa12nIpdxAIAkEgCASBIBAEgkAQCAJBIAgEgVWGQEiGVdYgKU4QCAJBIAgEgSAQBIJAEAgCQSAIBIG1ikBIhrXacil3EAgCQSAIBIEgEASCQBAIAkEgCASBVYZASIZV1iApThAIAkEgCASBIBAEgkAQCAJBIAgEgbWKQEiGtdpyKXcQCAJBIAgEgSAQBIJAEAgCQSAIBIFVhkBIhlXWIClOEAgCQSAIBIEgEASCQBAIAkEgCASBtYpASIa12nIpdxAIAkEgCASBIBAEgkAQCAJBIAgEgVWGQEiGVdYgKU4QCAJBIAgEgSAQBIJAEAgCQSAIBIG1ikBIhrXacil3EAgCQSAIBIEgEASCQBAIAkEgCASBVYZASIZV1iApThAIAkEgCASBIBAEgkAQCAJBIAgEgbWKwElXuuBHHnX0St8i+QeBVY1AnoFV3TwpXBAIAkEgCASBIBAEgkAQCAJzRGDFSYZznHX7ORY3WQWBtYXAMcce1+UZWFttltIGgSAQBIJAEAgCQSAIBIETKgL0l+VKtkssF8FcHwSCQBAIAkEgCASBIBAEgkAQCAJBIAj0CIRkSEcIAkEgCASBIBAEgkAQCAJBIAgEgSAQBOaCQEiGucA4PZO//OUv3X//+9/piRY5++c//3mRFFvv9DzqU6X9+9//3v3zn/+sn6Of//73vxdNM3phDgaBIBAEgkAQCAJBIAgEgSAQBILAVkVgxWMybNXarKKbIRXe8IY3dG9605u64447rttuu+26S17ykt2DHvSgbocddpippL///e+7l7/85d1HPvKR7o9//GN31rOetdtpp526+93vfv33mTKZU6J51GdYlK985Svdgx/84O4GN7hB94QnPGF4ujv88MO7F73oRd0PfvCD7iQnOUl3hStcobvWta7V3fjGN94sbQ4EgSAQBNYaAn/961+7f/zjH93pT3/6LSr6b37zm+7MZz7z1GuRuCc72cmmpmlPLjV9e+0s3//0pz91pz3taWdJukVp/vOf/3QnPvHy7Ce/+tWvurOc5SzLzmeLKrDMi7QfYv6UpzzlMnNa+ct/8pOfdCc96Umnrol++9vfdqc5zWm6U5ziFKMF+t3vfted8YxnHD03j4Oe0VOd6lTzyGrZeWhba6Hl9u+xgszjuXnVq17VXfjCF+6ueMUrLmnMGStPjgWBILD2ETjJPguyUtU4/g9/7LY7zalXKvtVne873vGO7vnPf353rnOdq7vVrW7VLwQ/9alP9YTBla985UUnxb/97W/d/e9//+6zn/1sd45znKPPg9X/M5/5TPfxj3+8u+pVr7rFC9MtAW659RneE2ny0Ic+tOOhseOOO/bkQZvmve99b/e4xz2uO/7447sb3vCG3dWudrXOguTNb35zd+pTn7onbNr0q/X7n/781xPsM7Ba2yTlCgKrBYEPfehD3UMe8pCeaLjc5S635GIdfPDB3Stf+cru4he/+OicQqHfdddde5IaQU1BmSaU0zvc4Q59kote9KLdiU50oo3Jn/rUp/aEr/F6ksK3MfGUL+a1T37yk/0YjnxfqsDsLW95S6/MUD5bQdjstdde3fbbb9/Pm+25Wb//61//6u51r3t1b3/727tLX/rS3ZnOdKZZL10V6X796193d77znTtKozac1OY///nPu7e+9a3dBS94we7kJz/5JmV/6Utf2iv/Zz/72XsvzEc/+tH9WgbxslSB5/7779+XZdheDAkML0gnfXhMXvOa13R77713Xw7t0coxxxzT3eY2t+l++tOf9tcP82/Tbsl365N73/vePT4XutCFpmZx3/vet3/OLnKRi4xi/rSnPa03nFDCt5QAOuqoo7q73/3u/fOnbZciK/3cKMsPf/jD7slPfnKHpLvmNa+5lOIlbRAIAqsMAfrLGU6/9Dm6rUY8GVo05vTdAPviF7+4u9jFLta94AUv2Lggu851rtM9/OEP78894xnPmHq3Qw45pB+wXWPQLrG4eu5zn9tZXFpMbQ2ZR32G5VT/Y489dni4/83zA0FjcXTggQduJBTudre7da7j3XC+852vQ9ZEgkAQCAJrFYEPfOADHUJ5mgJDSWPtHZPddtutJ17NEayIQ/niF7/Y8YhDNkzKo73mG9/4Rnf00Uf3HnjmntZL4vrXv37veUbReeITn9heNvV7a2mm+P74xz/ubnGLW3RnO9vZNrnum9/8Zq8oLmal/dKXvtSTFDe72c16b4M2E8ryLW95y+6BD3xg95SnPGUz8rpNO+m7OVYdkdvmmaEgMniGtATMMM22/K1sMP/0pz/dGycmebEglBBU2uGmN73pJkXWFvoMpV49v/zlL3eXv/zl+zUNa7o+VX3DOV6bCAjY+ONZwPvRXO435ZNhAdnwy1/+cqPngjleOXgpTpIjjzyy77vXve51N0vyrW99q/N8WEuc4Qxn2Oz88ABS42Uve1l3nvOcZxNLu375s5/9rC9361WEsHFOn7jMZS7TEy3DPOv3L37xi+7rX/9655kcEx4RDE3WMYuJMWGMiNiwYUOPszUgD9AhqfLud7+781yM9c2Vfm7UiUHtbW97W/ed73xntIrqdcABB3QPe9jDeu/e0UQ5GASCwLpBYHzlsm6qt20q8upXv7oTtwDD3lp8KMWIh89//vMdBp6HwiQxeZooWCRauclNbtK98IUv7Cwet5bMoz5tWT/4wQ92H/3oR/vF4Dvf+c72VP/9c5/7XL/AufWtb72RYHACHraK8HKwoA7JsBl0ORAEgsAaQYBiROm5ylWuMlEZpkCx9PIuuMY1rrFZzShzN7rRjTZZ1PN24/lFvvCFL/Su3sbNMcVjmKFxmVL69Kc/faMSWWkucYlL9N5zFJ1WKJHvete7+vmuPV7fX//61/cxdVheKaes1kgVltUSFmPEsjruu+++E93BKa6UWgSA8sDwdKc73SYKGVdtyheFz/Y6Yr796le/upkyXfevT95yr3jFK3pywfxNOR4Kw8E5z3nOjnV/MUJkeO28fisnQwRvwKErf5EKcJi2LaXS2cZZcZbkpZ/4o7TDm/itzn4/5znP6ZBRL3nJS/r8kQ+s9+71rGc9q8cZmSBPxBQLfvVdivYd73jHbo899ugt8kV8VVnG8EEy3Oc+9+nOfe5zb3baOonAoc0DMTDWNogC7Tf07kCo6FO8EYZrrs1uOuEAguuyl73sxvvq65TtWqcgDeBk+wcCjEJe54ZZ8p7wzFsvDsVxhAECx18JwpLXh77OE6eVeT03xiP9Dn5j+Lrnzjvv3BM/b3zjG9si9N+NR9auyBtESbX/ZglzIAgEgXWBQEiGFWhGE7BJecxdzKLniCOO6LdBcGOdJCbrsb2AJk8DvfgMW0vmUZ8qqwlQ3SyCb37zm3djJINFBbnUpS7Vf7b/LGSQMyZvixhbJyJBIAgEgdWIAMvle97znt5aPFyUlwJrwW7BPSYW4yykiGVKNaWCwtAqVBQ4c41xk/WaFx1PA5ZOhLZFfztOshzzTqM0tkLRp6jwBHAdAsQ81goFR16slSUUHvU0N93udrerwxs/lVVcoYqlY9vGmLgeOTDESZ0pbBTg73//+z2xwPLN3Z83H+WTwma+LFxsJ7QNAGFNYEIxt41ikmJnPrFFj9v+ta997e573/veZsX8xCc+0SvRCAvbRm5729tulmZeByi+j3/843tiZ0gWIFp4W4jJ8exnP3uTWw4V6E1ONj9aBU/b8xLkjcAwIm9WZ8dJEUnqTVlGYJiDywOhLd+w/Zpb9nlrZ1sGyKS0vF20lTaXxlqB6N/WPuX5g2RAXmnrEs+VdvQ33F4hzaz4VH71qT0QCf70DR4ZvpfYzuoZrD6HXIMRz4kLXOACfd3VZ8MCSed5fdSjHtW99rWv7c573vNWFhs/3cu4gZAosW48//nP3+Ovz8MCkWZcUCdtYCuSdjVGuHbez428r3SlK3XqWnEwfG+NaVXe+mzPX/3qV+8PG1cmtX1dl88gEATWPgIhGebchiZQbnf2mppQhlIuhtIsJsPrTfy2D5hAeDTMKso0bUCX33AxWXnPsz7y4tZrIfjYxz62n6TrPu1nlWVSmS145GWBxe0xEgSCQBBYjQhYVFNqS/mtMlKcWXR5IdhvPquYNz72sY/1XgbD8fFHP/pRP47zWuDSTtkyRpZyX/eoYMIs9q2L+fvf//5eWbjnPe/Zz2HG2NZ1vK4fflI4KTctkdGmUXd/rLjTxLg/tufftUgN8QF4P1DSbOnwV/MgZY+Ch4CGC4KFmNsISzhp69sf+N+/P/zhD/1WRoqa9mKBbxVnybiiC1bM8g/jMRK8zXO5392f1wCjQksIyPcud7lL3wfGAiYP7yu+Ais93FqpedYxijBFvjwOEEw8FFjUYSgWEkyvd73rtVls9l1Zh31dIkozUqjIgWHfHWbEsIFY0xaIp+c973l9kve97329Qq1O2lpQaN4J4oWUUP4Rc9ZK+vpi96rrpn0i5hBavGZ42iA6bCkRc6tIC3j6q993vetd+yzb56Iw1x4s/YitMVFm5IkxouQRj3hE78XJW6H6pufC1hhbSItcqHus1HNTbVjl4jWx8wKRORZTwzgEr1122aXvs4VNXZvPIBAE1jcCIRnm3L6Yf5PypMWZSYqYdGcVEy73Q5O0CYSCbt/dYsLCxIJj8YlpxupbcFr41kLApG1yqkXZMM951gezj3nnPokFtxgYk1oMKf9wUWPxwLpH7PGMBIEgEARWKwLG6xpr2zLaGkCR4J49JrYBjHmr8QIoT4Dvfve7vVWfVbQsosb2UqocMw8hJQQLNpabC5SHMi/o3z3ucY/+9qyN9tVzY6fAjLlpj5XTGC2vGrPH0jjGovrMZz5z0un+eBECw0TmzEc+8pE9cfK6172uV3wpLUOBCxKbpdV8Bh+khLmSBXiSmFcppLBgmac0iXlk7qyYDBRCacSpoNiPtemk/Jd7nELNhb/WFAgqln5xFLiu8ywoxdK9KHbka1/7Wo+5oM22wVBOeXPY5gAPBBSxHdKcynKOrOHBwJODIkzkjWyAjbdBKUt5MOinylKKNE9FCrk1i3wI8ocyzPoPUzKprfuTC/+sVyjggqK2WPPg4EHCks+rhLeDftWmscaQDrFUz4I66w9IgRaruh9CzVYHZRzrp+qiXhR67v76GO+ZlvyBHa8EpBsZkkPqXEq2z9paMmsAR/l7M1kRDO7hmHJXOYwrSADrvJV+btyfGDsEKB8br5yHLa8L24/asjsXCQJBYH0jEJJhzu1bE+ukqNk1yBqYZxWurVh713IXtcWAu2HrIjjMi6ucfYomNh4RFpj26vpTNotIiwOLC6TFJJlXfUzeLGcWfGUtmXRPi2aLEbEXWI2KUDHB77fffv2k5VqTeCQIBIEgsJYQoKTYPmBsa7cdVB2M2QgAirJAwaUo1fn6ZKVlTaWEsuhTko2xxkhKmHmC9ZNiTJANlG4xcWoeqrwQDOYZRLC8KI2HHnpoT+SWYlRp65PyUESGGDnDPCudTwqyskwTcR2mCUXGvMk9nMIsZk+R9q4zN9ruQfHiicBzgtcbIkfcC8Hm2vSuMceYK5ETriPKueeee/aEOM8HW1m0l4B+AkqOKan9hSv07/a3v31vDdau5nIKNLFVQ1m0VSn5jlPODzrooH6NcKc73anvF9K51rxPESUC8BHEUnkE6iNIFHkP99SLZeDePEqKZPDmDRhS9pEdjADWKgwItcYxb+tT/rQH0XemSfU57dUSCJ4Fijal2rOjfu15eda6oHXhL08W5z1fs7Yh71HP0lh6zx1jSWGnvtZUFGpED08M5AxSiPAsUn9eIURfdYxyzhtjKIwoYjfAwh+vDXVrSRB913jCaGW7kOdbP7Y2RMaQlXhu2rJqE20+NAhVGl4y1qvTxodKm88gEATWFwIhGebcnrWImeQGV8dNyLOKBZM/Yg+ixZLXGHnF1ph7qXRYd4ssrrgbFtwkTf6C7nCJtbfPd0LhryBh/YHBv3nUB1Gxzz779GVlCVlMkCD2K9pXzA3SAtYixgSO9RdlmmVmGFl5sXxzPggEgSCwLRGgZJSSYIwbG78pJSzptgNIPzZXUKgpF6yhZW23p54yRSlGzLIql1VVnc0BxtHhYp81mrWbssPluRRWruq2EdQWvzHcKI5ksbGYYudvOWLuMl8pP8VFuXk4lHhTws4LbtsCMpZQqs2BiHQKozQlFDT1NT+2Yt6k3DkvBgXMYFqKdZt2a3x3f14LSAHzKO8RSuQkI0Mp3UiFSdtDKPniaBBrhRLKLIV6bJ7Wn3g+tm+ioOwjKYj21bdZrBFk+g6vRR4mFYCziIfytqj7LvWTRw4DScVqaK+v+rQkQ3veekQwVeTN2LNVaSn0MOcZgOAZPjcwpMB7XvR//cxvr2e1RuEpwoujjCq2oGgTxAJvEM8X0b5jYh2E7PHpOjEmGItasg7p4xkvEkUMj6HM+7kZ5g8HfcOabEyQH0vx3B3LI8eCQBBYmwiEZJhzu5nYTAqTXPlNvGSxRdmkYrHi2I/J/Y9XApe9MbG44A1QCw5uglxM/bFYWaA5ttg7wOdRHwtCe4mVmwtniUUfsVgwOZtsKyAUbwbkAkuZBTVMWZcEy3zMYx7TX1cESP8j/4JAEAgCqxwBrunGXkJpMa4NpRQxY+AkJYjHgvGwSATKDKWI5wMlxiv3jPXf/va3e9du90BalNW1vad5ghKubK2wWPubJh/+8Id7d+0xS297nXlvGKCwPe/7NBJCPARKMVKlXMPVnbWYpwLFTrwEREsJazrPgwc84AH9JytvK+a+4fyH4KFIu9/xxx/fE/XetDGpHdr8VvI7bxRbXni4IBnqDQgUaltIZnktYls+WwrK4l9vf+AVQLTDWCBSCu40MU9LU8EWK/9S+l3ru60GY5b7aXkPz/GyoVDrs0gP3g0lRWBMIhkcZ2gR3LRei1nPXHsNwqW8iNrjdR9rKwQcAxAvIn0RWee5VUfXIygqFonfSEVrw7HnsPL1qQ2kb7cgUOaHzwiMW3zbPHxfiedmeA94W4sNn6VKB8PF6ltp8xkEgsD6QiAkwwq0p4nHPk+Dv4miFa6DhAveNOHxYB8rN7ShlCJuUTWJZChyYXit396LPXxH+Vi6Orbc+vBAIAiDMRGx2p99oCwGJSx0Y4GtkBMmtTErYF2bzyAQBILAakLAnnceZLvvvnsfkI51b0x5XczqxzJpXilFUx25XtvqIECeOQPJTJGjSFF4CE+GYSR7e+ftz7edYEgyHHbYYT05PGkuYaEst3yu46WQ9Tdr/vHGsF0CATJNbO8oxXSYDkmtHMhoyjahcCM3KHaUZiQGfEuBtUUPsUKps9Vh7M0XFDfzEysz5R1G4logXdTJHKtcrm9FvtzXy0Lfnlup7+ITIAJY4iuGkn6AGFF32yMXI3uqbOIZeIsIgoIVHHGP9OL+Lw/3Gkp5MsBsTGy54e1R65Paalnem65h9W/JpjLGTOpj2qZIJdfrS5Ra6xeBNz0rFHz9vpT5McLAtSX6qT/PQnkTION4iNqCU14B0iMXKMiTyicNgxGyQh4wrOfNuVaUVf0nxaJq06rD0BDl2bCubAkgRNgwXZvPSj03xiDrW55MxiFvc5nkNcMgZkzybOlviL/ylmrLmu9BIAisPwRCMqxAm3LpF2CJK9twwqnXG/FImCQmUW6AJlf7HdtJ1jUme7KYlalPNId/y62PBRoCYSjqgXjg0sq9t16pZiK1FcRipYJP1bW2i7iOC+KsC6q6Np9BIAgEgW2BAIs/pZgyZH8/EYNgbLFNeZ8kFBQKoq1klGDjqvGTJZ+CJwgcLwbKqHx4kRGENUJhuG/algAeZmMiL0QuD7NJBIJx2zg86bx8lYOCZE5cTEoxHaazt51Cg3imVCNsRO9XZ1bpIttF2fd2DIJkoPTVHDKcL+TBq48F1isGeQuoK+WJ0i3exYaFrRQUYZ4jrWhDShPipO7dnl+J78qmrSmOrQeMLSIUN1iUa/60+8MY+cRDg9LHiMAtHxlQwSXH2qo8cMbaCNGD/K+tQO4vNgD8hoEN5W1+533D6wbRNslggFRq1z/KSOEvgo1y67y68HwkRTKMEXh9goV/w76gTkiAei1jpfM5TNue811sCB4cSCqeBt4I4a0gQ2Fg0m7qIN20fNVhODYgAj0H7TYZJNc0g9VKPDfqxTNJbI/aAuIZXEyMNcYuazhtNY24WSyvnA8CQWBtIBCSYQXaiVLN5dKi0sRfCzD7CFlGsOc18bIKGHyJyZ4YfC93ucv12yFYUWwRKDFBsVgRE+zWkOXWx+Q4Jt4hjWTg6siqUmIitlD2yfXQfkSCfBHsjIxZpfoT+RcEgkAQWEUIWFgbx1kgS4lTPMpI6w5dRbaXu4LD1bH6pDjXa4C9BUFcB0rGT3/6037PtnQURsLCaSsa5c9Wg1bh7hMs/KOgThJWcnPXNDKbO7bXcE4Tc5axm7faNKGUmi/HhCKJYBgTincJS7d5FmlA6UNMTFJmKqBwXeuT8icOEM8In23elU5bwpRiPKaUVrp5fyKJYKhdBWWs1xuWKzriYBaSgeEC2VTzqnLaekPpFRRUO7iPvmN+1p/M0RRL7dDWWbsKwomIERiyVYytZ6xVxHeAq/gZ8tGOyBmeD6z/CLNJCjcyoW0/5RuKbZ+eC0QBYkF/I9Ms/MM8ijiprR7teXWcJrB0T0SPOvKCaYmRutY6T59RRmufsb5VaZXH82odKe96Fino/kqQG7BEuO2+4CE1lJV4btyj2sRWJe3smRt6fCAXlbWeadtpjIU8T+r6YXnzOwgEgfWFQEiGFWhPUX25XIoybNLxOiFWAFYD7ndtsCrubqVgWwgWIcFd0UKH5YsLqInMRG5xwVVTcKVyC12BKmyS5Tzqs0mGi/ywEL/FLW7RRze3QLFQtEjhEmzSsjhkZYsEgSAQBFYzAjzXKBTGcYrCcqV1z7dQF6DQfnzuyEVct/cwb5gzKNwC3Y3tLW/Tt995HyAyjMVjZAjLLDKYR8a07RIsr8Zr8yBhvTY3CqrYlsf5aV4cbdl8F99HvSiB4jMgs3161aQ4DHAfC4Q3zKd+U+YQQbZjIG8Q3EPhJm5biTmRJbcljYZp5/1bvbSHduFZoXwIJkoej4HaQjHtvrw09En9hndLSSn58rNlEZas1EgGhD4vQ9cWOcFzQABCHjpIpmnWdP1em5enYt2TF45rJ+3lr3SLfepfyIHyXKB4U66RJZPEeqIVSjKlvvUSqPMVR6t+t59ILV4H1Q+0DcyGr0y1drPW8xx5VnkeTSIZeFTAWlqEDu8l5IRtB7yOWvKE54j78+50nTosJvN4boYkAeIKgdAalHik8uZFYFVwUGVrya3FyprzQSAIrG0EQjKsUPuZqC1YDL4sDMSksddee021DFVxTMgCcgl0Va+edM5kZgFk4bc1Zbn1WWpZ6/3Y3CpZO4gJlCss1j4SBIJAEFjtCHBxb/d4t+WlmI8tuMuq2qad9J3LOQKbsk6h5uXQkhkUd55oFBOBIJcilDSedratjc03XKSRDEjfIseH+VN8KKmC4vHIs52Bgs4jjSXedg4YEVZwxIDgjfbHF7FBIeSpYfuCPxZ9YquDbSGth4M8EfDmq8tf/vIzB5wzR5uvba0w57C2Iv3hWQq4wJGUc14SixEMlErkC69F6Zcr9TYlxAyll7s5l3zrA3LQwisrh4rf2D0pgubQVgGn0PI40NbVd2xpYHkmXmvZtq+tIjwH9Kn73//+fRrXtmn6g//757j7Vd4OU4oRJqz/s5AjbX7D763RxjnkyTRlG5GlvK0gVHiPDuug7YeEQXudOiEVkA3aBemCRLClpxUkhrWbvi+d/jNpXLAdRr5IQ8QJAol4zhiqBOpUTp4gngVbIsbWRCv53NQzUZ/aV73aN18gs5CAFfeir0T+BYEgcIJCICTDCjW3Cd/kR1k2YVtMIg7aidatsfhIhDExuVmcmTSxwhZjAh7VwD52zUodm0d9hmUziU6qu/vBzgRq0rWI5hY5y0JqeJ/8DgJBIAhsCwREl58kxrZSpNs0tkuwmC8mLPiU3vvd7369UrX//vv31s5S/FxP2THnULrMI607+zD/oeI1nKuG6ev3mGu4c/KjaFP8y2OhAg4jERy3n52ySZAWFD0KP8snBVowP+M/JYrSxFOBRwYiAiHQEgzyEBBY8D7KGFfuxQQmyHxlZRUuizzywj0on7wwxBwQsNCc5XhZriflTzFHqCBRYLBY+kn5OM6S7dWbFFNzv7zgYO5EwHsN56zzIsKjgn8iFwiFleclcovlvNYXLN5eb23tQonmxUDqddrtPW2tEMeCgj3sY7wYkE2InxLWd1gicnjnVP+o88O+WMd9Dr0Q2nO+25Yw7c1T6scLtLab2sJKGUbSIW9gpGyeTbhPazvPSAU05Z3jTRe1PcDarQQ5xmsDZu6NZOHNMOYtYysOj4ryzKg8bIVBLPBiEfRSv9VPxwgG16zkc1NlmuWz7SezpE+aIBAE1g8CIRlWuC0NsNMWmrPc3iQ3baKbJY95pZlHfZZSFgucila9lOuSNggEgSCwnhGwF54yUvFp7N9GTiAdKFJcle0PZ9k/bOFNEazz4gxQUMaEq3erwLXfx9Ivdsw2jVIwhoQFsuHud7/7Ju7VFHMu5LZ/8CZ43OMe15MQlGIegOZRpAIFbZJ897vf7V/bh5DmXm5rYZEwY9cILIjsaeMMSOdtAaz+jlMiER+8Qh796EdvrNNYfnVM4GbxBxASyjQMAF3pFvuk+Gsz9a+YTa4RpBLJwJMFyTCrFMEgvfYm2rk97hhSQP9BYlGaeVBYg9i6OCbac/h2kkrHuq2ce++9dx1a9LPiKiDcqg+5SJkRHtNE/5i2BYPyLn4E4ZGx77779gQK0goph1DSbtYeyByeFjvttNNmtxwSIcpcxE2bGL5il9hiQlj9EQ1II/1iSLDYHluv1mzzQQYhahBwb3rTm/rg4Pr2JFnJ56btO+6v3tqlDfxZb9Hg1RMJAkHghIlASIYTZrun1kEgCASBILANEBgqJ8MiVKC5oet2mw6hQAkU66GE5xdSAcFAAT3ggAO6Jz3pSb3FFtEgTsGDHvSgbueFYI+sqhSo9h7u2xILpeix5o8pT6Xsj52jBLHkUqRKykJev1lhS3jquQ/hdaF83v5Ayabs1RsinC8FZ4gji7RXHvNgYFF2f+VgMWYtZ7nmKdFaiCtOhHxbodiKGSAvZAEsWftnFbjygKCgL4UEGObPks7rY0gCIBngVXvgtQV8kSJIDTLJw6TuUTi2be6c37XdpPqIfoMk4G1CAaZ0t32n8tySz4MPPriPS9UaUsRVIMimlqDikt+SDPqAYJC8RF2vHyEOyttiUnn0dWQCos6zgdgivEWRcX7bCsQT5dBDD+2JOcRDuw2jng/XlZJdmCArlFPZxZJCem3437YH6fVPnhNIHLgWkWIrB+8UZNZQYMKTQdZxDM0AADMXSURBVBpeFrxA9GXPyiRD1ko9NxU7pcYqn+rYbpdQD89OkZotXsO65XcQCALrE4GQDOuzXVOrIBAEgkAQWIUI1MK8LZpFO2WGEufViGTSe+cp32IUCARYyon09ntzoWYxpWyx5FewPW+IYL2lLNo3ThFFOrSKM4W+FE/5sWYTWxbGLKuUOXv5XdeK+nHppmBUcGL3F0PB/nUKEuVLOvUWEI+nhX3r3mpAOfdWAcH8EAxDqftVWQWgdL8f/OAHfZyCUrjs97cfHEEAW8qo102q9yQRh4DVWfwFLvOUQOVZqlB+kRu2GLSK6VLy0Ua8VMZe78gajlAqsf2AFwvFuRT0aW8FcV3h2PZHyjG8KMi1919a/YSVmneJe1DobVtBdsB/aI13TYnyaKuK8VDHfbLai03CUwRhVv3ZNRRoJE8p7tL7XoGy6zfl3ZYUf0Qe5d3TH2j+6WvaFnmhTpT2ltyopPKwnQcp5e0XYh/oQ/XqTOmGSrNYWZe85CV7zxf1ErPC1hEePbBsBdGn3OJbwNxWHG0KW4ESh3h6ZpFm4ozYCqRtedqIbYJI47nhmGeLV4k3gti60kq19zyem8qrCB/xVpAzrSDXPEOeTXgYs0jrkdOmz/cgEATWHwIhGdZfm6ZGQSAIBIEgsEoRoBBSBlrlkzLNJZ1iKaYCC3JZqdtqWKxTIoavX7ToZ3nloixII2JgKJQQLu2UOvcaKiFIhQrC6NqyViI7xva4U6LGXpFHQRNPQf6lILIsI0+Uv6y8FHGKFYWKwqVMRUq4dli+qo9ysdqXAmfvOTf+YQBA6XkSIEIok8gHMQZKWOwroKQ3OYkdoK6XuMQleg+M5WzTUyYKYdvGdd9ZP8facNK18KOQI1WQKPBGAEwTOMKQgqrfULwRARTGsVdhqk/FhoCRNx2If+B1q/rWJM8JHiFEjIehaHtvkCICmLKGE0o4MqH6T39w4V/F76jfPimy/vRfuCNCEG6taF+BCLU5yzrFd5a20Qc8M5T5YQBIJAMijSDNivxAxsCSIE+QNp6VVtQL4XfIIYf0wS9hVwFcKxAmEg+RIJ6DNhLos41BAiMxHRAVyAnp9GHeQ2PPzryeG/WQl1dWFgFWOLR19F09tZnzYsaIH9HWYZg+v4NAEFhfCJxoYdDd9F0+c6zfkUcd3Z3jrP8XOXqO2SarILBmEDjm2OPyDKyZ1kpBg8DKI8CaSrkas9K7+49//ONe6R4qWNNK5tW+FL3lWAm9CYnlvayoLJ6s8ZSZsbKwgNtvPW3/+7Qyb+k5WwNgRLHkpl6KzlLz48aujrw9KGXIm7F6LjXfbZnecs42mgo+OK0sFEV9Ud1ZynlstCTTtGtX4znkHdJquC1HWeEydnw59dAPp/UZXkmTXlM5dl/P0nbbbdd7/By24NnjWXb9GGEwdr3gm/4m3XNez417uw8PEHjPIghFAdDn8aaVWe6XNEEgCCwfAfrLhh3OuayMQjIsC75cHASmIxCSYTo+ORsEgkAQCAJBIAgEgSAQBILA6kFgHiTDiVdPdVKSIBAEgkAQCAJBIAgEgSAQBIJAEAgCQWAtIxCSYS23XsoeBIJAEAgCQSAIBIEgEASCQBAIAkFgFSEQkmEVNUaKEgSCQBAIAkEgCASBIBAEgkAQCAJBYC0jEJJhLbdeyh4EgkAQCAJBIAgEgSAQBIJAEAgCQWAVIRCSYRU1RooSBIJAEAgCQSAIBIEgEASCQBAIAkFgLSMQkmEtt17KHgSCQBAIAkEgCASBIBAEgkAQCAJBYBUhEJJhFTVGihIEgkAQCAJBIAgEgSAQBIJAEAgCQWAtIxCSYS23XsoeBIJAEAgCQSAIBIEgEASCQBAIAkFgFSEQkmEVNUaKEgSCQBAIAkEgCASBIBAEgkAQCAJBYC0jEJJhLbdeyh4EgkAQCAJBIAgEgSAQBIJAEAgCQWAVIRCSYRU1RooSBIJAEAgCQSAIBIEgEASCQBAIAkFgLSMQkmEtt17KHgSCQBAIAkEgCASBIBAEgkAQCAJBYBUhEJJhFTVGihIEgkAQCAJBIAgEgSAQBIJAEAgCQWAtIxCSYS23XsoeBIJAEAgCQSAIBIEgEASCQBAIAkFgFSEQkmEVNUaKEgSCQBAIAkEgCASBIBAEgkAQCAJBYC0jEJJhLbdeyh4EgkAQCAJBIAgEgSAQBIJAEAgCQWAVIRCSYRU1RooSBIJAEAgCQSAIBIEgEASCQBAIAkFgLSMQkmEtt17KHgSCQBAIAkEgCASBIBAEgkAQCAJBYBUhEJJhFTVGihIEgkAQCAJBIAgEgSAQBIJAEAgCQWAtIxCSYS23XsoeBIJAEAgCQSAIBIEgEASCQBAIAkFgFSEQkmEVNUaKEgSCQBAIAkEgCASBIBAEgkAQCAJBYC0jcNKVLvwxxx630rdI/kFgVSOQZ2BVN08KFwSCQBAIAkEgCASBIBAEgsAcEVhxkmHDDuecY3GTVRBYWwgcedTRXZ6BtdVmKW0QCAJBIAgEgSAQBIJAEDihIkB/Wa5ku8RyEcz1QSAIBIEgEASCQBAIAkEgCASBIBAEgkCPQEiGdIQgEASCQBAIAkEgCASBIBAEgkAQCAJBYC4IhGSYC4zrK5O//OUv3X//+9/1VanUJggEgSAQBIJAEAgCQSAIBIEgEARWHIEVj8mw4jXIDeaCAFLhta99bfeGN7yh+81vftOd7nSn6y51qUt1D3vYw7rznOc8M93jAx/4QPeqV71qYtoDDzywO9vZzjbxfE4EgSAQBOaBwF//+tfu73//e3eGM5xhi7L79a9/3Z3lLGeZeu0//vGP7uQnP/nUNO3Jf/7zn93JTnay9tBcv//pT3/qTnva084lz3/961/dSU/6/5cHS63rpEL85z//6eBwilOcYlKSJR//5S9/2Z31rGftTnzitWczgStMTnnKUy653lv7gh//+Md9n5i2Hvjtb3/bneY0p5nYvs6f6UxnWpGiM46c+tSnXpG8p2U6r2dj2j1e9rKXdRe5yEW6q1zlKssaQxYbIw477LDucpe7XLfddttNK07OBYEgEARmQuAk+yzITCm3INHxf/hjd4bTZ7DaAui2+iVvfetbu2c/+9nduc997m7XXXftznzmM3cmnA9+8IPdVa961ZkWBm9+85u7z372s/0i0iLfpN/+3frWtz7BTV55BrZ6V84Ng0D3/ve/v3vAAx7QEw1XuMIVlozIq1/96u6lL31pd8lLXrI74xnPuNn1f/zjH7ub3/zmnc/LXOYy3UlOcpLN0rQH/v3vf/fjqmMXv/jFuxOd6EQbT++7777d9773ve6CF7zgROVsY+IpX+51r3t1H//4x7uddtpp2ePsne50p+7YY4/tLnzhC/dluuc979l9+9vf7s5//vP3BPSUYmxy6hvf+EZP1lR9//a3v3U3vvGNO8omhakEuU2JWgzHSl+fyJDdd9+9e8tb3tJd9rKX7bbffvs6tSY+Ybzbbrv1RIN+MVb/o446qnvTm97Ut8WQ1HrRi17UX3OOc5yj9z58+MMf3s/hSJctEXhaEl7sYhfbjLD6yle+0t3nPvfp+9YlLnGJ0ewZGR796Ef3ZfFctHL00Ud3t7jFLbqf/OQn/XOFjJiX/PnPf+7ufve7931Vn11M9txzz/7ZndTn9ttvv059nT/VqU41NTtjzZOf/OTu9Kc/ff98TE38v5O///3vl0Qs/eAHP+ie+MQndgi1a1/72rPcYjTNG9/4xu5973tfp/2G9fJswtBakIHp7Gc/+2ge7UGE4VifbdMo87zIzzbf+q5OCMaWFH7CE57QIcXUs8qnfi1xWte3n9///vc76RjZIkHghI7APPSX/2+qOKGjuYrr/7nPfa67/OUvvywGe1r1TAK8DAzIFtZlZbre9a7XPehBD+rPPfe5z52WRX/OAG1AR0xUHotelARBIAgEgTkjYCGN6KQkTJKhtb5Nd8c73rE75JBDOovV173ude2p/vsXvvCFjqLAMrjYwtUFX/va17pf/OIXvaeYcbVdEN/oRjfq7ne/+3WUScrKrNJahVnDf/jDH3aI3KFyQNE3ti/F0q9en//85zs4EMoEpese97jHrMXr0yGZKdH3vve9O/U2LyBm5F+CGEFwI7XNP0VI1PlpnxSMn/3sZ91NbnKTUQWPlZn3yFLynHa/eZ9TNu34iU98oiehxjxdEFQs2dqVkt7K17/+9R5PBIs66pdINYSFNjv++OM36Wtf/OIXe49FJARPH/jwLODJyIPRbwqtNjrggAN6pbY8F8ztynLFK16xLcIm38vb4frXv/4mx/345je/2XnmECuLeRh99atf7V74whd2GzZs2GTdo58feeSRfZnbPOTpnGcWFowl0+TnP/95d/jhh2/s38O0+q02QUYsJoif73znO5sQe6791a9+NdHT6RWveEV3rWtdq0MKzfJc7rLLLh0jzhFHHDFaHIoxYuRRj3rUVAX5Lne5S3f/+9+/u8Md7tAhqJCGJR/72Md6o9A1r3nNnmSq49M+73a3u3U3velNe4JlLJ0+9ZKXvKR7yEMe0t3+9rffLInz2m1Inm2WcMIBY5KxAxGMGD7nOf/vbXbHHXdc35+RJvo0vA899NC+73tG9C9jUTsuKAdvXGTRK1/5yt47asJtczgIBIEZEQjJMCNQ2zLZM57xjH5RZgHJy2BLrRST6mAANqne7na324Qc4MFgcco7gRWiBvCxfAzQFrkmrRAMYwjlWBAIAlsDAYt7i8+rXe1qE61+lJ373ve+3Z3vfOd+sT8sF8XL4pn1vuRTn/pUd41rXKP/aUzkmo2EbReqlXb4+eEPf7hfSD/nOc/ZROmTjtXQwvZ85zvfJpdZHL/jHe/olfRNTvzvx8EHH9wrkhRKyiQXZ6SKhXIJZd4inELz1Kc+dSaFxrWU3R133HGjMuj3uc51riXPPVe+8pW7nXfeuXvc4x7XW+J32GGHvgw1h1F0n//85/dt9YhHPGImLKtuFFqkhDkHkUE5Hgpy3Ly19957z1z3YR7L/a2ciKpHPvKRm1mPS7mC0yRrbxEPPFTM0wgBVmj9zh8Fq2Io+a2d/LZuQG7xLqi8EQTlpYBE+PKXv9w9/elP36hcPutZz9r4PCDpbnOb2/TEEi+ZItOqzGO48FKgxGrnoSC7CBzaPKwdhoo2ogCxUlboygv5wZL/wAc+sKPgbqlYozDc1H0Rhp51ax5i+8pFL3rRXlF90pOe1K+76tzwnlVG/QzuxgbPoTGGhwkj0Ytf/OJeqfecS0MJRrp4bo010tquKq/Kb3if6173uv3z/vrXv354qr8HUtA2L+RMtdUwof5B8eaRok8iUUvke53rXKfjWVW41LlJn8qPUDEOjgnyR93e9ra3dbe61a028974wx/+0D/DSKHq52P5tMeQZ695zWt6b5grXelKfX2ct/6sNaq8PCMwVh/PDfyMNze4wQ16oszWXXiU7LXXXv0YirBEpkWCQBBYPgIhGZaP4YrnYAFlAsCuHnTQQf2izTET8ZhIgyFeTCyyuSxaiBhsx9zwHPvWt77VffrTn+5JiEl5/vSnP+3dzEzMkSAQBILASiJgYfvOd76zt+4OF8Q8qojF+jOf+czRYlj8smY+73nP6xerFv0W/+3inGJOQaYkIhh4e7EW8jygOFiQt3vALUx5hVHyWuHKzbMCSUwBQYC0i1tpkR3GYtbKEhY39aSEsTwOhaL2oQ99qCdDnLv0pS89TNL/dr28hziNJv7fwRYHh4a/HaMgUFAf//jHb0YsKxeljHJLGTQfVTyetu6UL2SQ2D9VPvMNZcDe8ElCaaCsIsEpDyzJQ+EhQYkmF7rQhUYtqcNrtvQ3Mucxj3lMTxYN97PzHuBtQQHUh1qZpFC2aVrly32e9rSn9UQVRVm+rNjlGYKYYrFVb4oy8oLyTBkrKcLB78K8zrWf8td3yhtoUtof/ehH/TYVz4I0lEnyyU9+sm/z2r5A4UNwaIsSzyrrO2V3uJ6ZBZvKp/1ERCh7ERnf/e53eyOJYyUwQ4h85jOf6Q8h7HgJsIYj12BHSUVgGQMe+tCH9ltWNix4VhCWcOOHe+ivRH15I9luoI30S+Ietp+WB4hngaLMsl/xKTxfnhf9vrZn+d6Wuc+s+dee531APFuT2qkuhbN6IDdLjEvw5klV1/Owse4bes/UNT6V29YCz+GYHHPMMf3hxz72sZsRDE7AF9m1FEF+IRmUH1kySfRLxIs66cPaQbtrW15Bb3/723uyB5n8nve8px8jbbtqx/RJeed4EAgCsyEQkmE2nLZpKoTCbW97236BagKzeProRz/aT4a8D+xxbffXWdBaGC8muy/sZXUdgsDCqM2jri23XmmmSS3s5YM5tpA2oFtQWCBPsgJMyzPngkAQCAJjCFhUG1NaBUw6ZAFvL9YoFshZxfj2kY98pFfeapFd17KQWbyz1pU7OYXR4rQVbsG2ilkAl6LgvAUsZQGpe+SCqzfFrXX1bvNov1MQH/zgB09c9Frgqz/FbZooe3kODNPxYkO4DEmEMZIaDsb2EsqbcR9e7XHnKdaImFK4fb7gBS/oL0XmWPwjKQ5b2CJhG0W7HY+yhqyheGrHobhOW1CWxXW44Q1vuPE+lZai/aUvfalXRKSdRMBU+uV+Kov6sdoOsTR/m0eHGI3dkws7EovyNSZImVve8pYbvQ3Ms5RX223garuA61lrFxNlLUW8TUtJpywXubCYss9IgawrUok3BNHv9Y1yYxd3BHHUbgmihFLWtT/jyPDZa8s1y3dEn5gQFF+eO9X3bSthLa+6OO6vfu+xxx599q2CWWXRBxl5EFslvI70UZbzUqQprrxNbnazm23y/BtbeBuVIIZIWd3reJEx9VuZEJljCjySwvNhXWgdV/Woa+vTveBLsVbfEl5DxiRbRggvCkRnux2DhxGCCkGy3PVbYVn3H/tElLUEWKWB33nPe976OfPnhg0bOh44JUU2FcmF9OUtgxSzvWf//fevpPkMAkFgTgiEZJgTkCudjUH66le/ev9n6wIWliXvKU95Sm8dEYSMm6KJw4RtElpMLIQN7BYnkxa9FQDHwmOaWEAQk7GJwsTIlc5i0mLTYN8O+NPyyrkgEASCwDQELJiHBIP0FBxj5ST3XVspyqLe5i9IXQWqs9CmeO+8YMGkIFEokBq1UHbMeGkLBLLXGEoBp7BRJgXpKwsba6MFvFgGNS629530nZIur+EWimF6Yyyr6TQxvk8SyihCGgnRKiFl4W2vszffHDOUYTuwWAuGZ34yf7CGsqRXOgE5S+zVRhrUPMP6+O53v7u7613vOurJQPm15USMB3lSsCinyJJSzM1B0lAcEE1jinTdf96fSAKeG9ziCdJLnViDuacj8luckVaEsqcdGRF4gfAOUacLXOACvQeENEUIwVWdWHRZy4uIkS+yoUgH2JT13PX6tH5KkZbWOoJSTgGXF2Hd138pZO5HpvUf5605KOAU1xZrfZhCZ+uE/f6CciIjqh+4Vr2l0yc8X1z9PX8IgRYnaQmCzjYH5Rvr9wwb6vS73/2uX3sgoXgLIB5b8keZNywooYg8MiSH1Lmedwo8hZt3SBF61k6FO8KRqOswFoTn37OE+Cih9JOhx1Odr0/X8qAaG6+kgQVPJyTWmHIuDYIA9giQahtjFjIUSVHXISu0N3KRRw4Rv4EUUdj/WMF/vCk8+8N4HtazvJ08E1WHxYqBILNG5hmmHY37xgV46AvaVP8yjngujFfwlNZ4xDtlucTKYmXM+SBwQkAgJMMabGUMuEnZAMn9UmRuf5hoi8UhQz6tiiZlMmkiqUnIhDdNauK0h5P7Y03Q3Aq5GprATPiitUeCQBAIAvNGAKFJQeNGS1EaE4ECeX5Z9NcYNUzHI4HihUxA3lJ4HePWS2ESJ8H1tRjmASEOAqt8jZeVJzdsFk/eDfKi4Fn8WshOsj5a7BaRQTEd5ll5+0R2lFLQHm+/K+8kQRxUYL82DSVsKBb4i729AelB2TQHWdCTd73rXb2VnwV+DHMuypQ98wZFgHXRmzaGIh9KO+WylA111z6UVVjYHqMPCFhp3hlTVIf5zvO3t3JQhvQVhIItL4RrvLJo/9ZaTjm3DdI2Q0qdOV06dantImVhRVSVRRfBJc6FfGHWCkWe4i4eQEsyULbLMs2ybc7WTsiGmt+5yOun/spQoT9Ok+rHiKJqF+m1teeFcq+NkS8twSAN5Z5Uf2tJLIr+rO3HYu9ek9IjLxADGxaIBWLdg/zhNaOeMHPv8uDhoeM4zxDy3ve+t4/5QVkfbuvoEzT/2oCsrvPMIIsQFfqEtkMg+psm6oKImOSVgtCiTE8bH/Q1ZFMrPAOQDLaDFRnmvHEJYcp7YimCoKr13/A6z+usom8wSOl7cC7RDvJZrB9Wep9IPdvUeDPp27aLWR/LF27V36RF4libFqmGTDL+e4Z3XiCaI0EgCGw5AiEZthy7bXYl64eFVEUbNpmztE2bbCYVtiaZsmQM05WL4NhWijatvbkGdYN5KyZkEZpNsFjqkAwtOvkeBILAPBAwJpZlUwCvsQU8BUIQPG95kJ6iNRQeWcgK41VZxlnSKHgUWItXSllZQF1vgU2RGI6/rGcCnlnE8uwq5dKCFskwVsYqj4U2GY6ndb4+KWKLWZor7bw/3ffIhe0frbcFjFlzW2WR8uDYpAU74sf8I79J0fEpbuaO9l7qw7Js7nEewa0dKJRtDIJ513tafu6vj4jdgRwoa365aA+vLaVcX2y32FQ6ihVrNWGhL6G4U0K9nWAo+qe5tvpQnXeNV5ESWHteWMGRbvqjMvPmqdhMRTwU2VD5LPWT9wRrccVqaK+vOrVKX51HBNhihDCatv5AVCg7hZtiOHwO5QdHHgWeJ+d5uPCwQQwiC/VbHj0Ub2IbinsKJoocKas24gwBwXuh2g5ZQQS2hD1c1dUYoW/rj8YABBEPK944++yzT3/NtH/KbKyy1hsTazOkwFKliL4ih5Z6/TA97KqPDs+Vh+vw+Njv8jSxbaTINOmUE3E8Nl6P5eOYtbE2ZITzTBJ4GYvEb0FIVf0Z7IxPCDt9DgFjm9rYNpU+o/wLAkFgZgRCMswM1bZPaDFr0cpd0qKA5YAVh6sbq0WJOA2zxGRgfTNpykd05TFxH7LYYndaGu6NxGIjEgSCQBCYNwKsUJR6wiur3O/b+5TSRNmdtGDlscCaXCQC93FKDOWXwkFpMN5yp0UWEIpAWUjb+9mjX27v7XFjdTtet+fqu4UuS/Yky2ylM25TsqfJNBICMWxhPRzfx66RjoJUwkNC7ANeF6UUCJrHMj8UBEulGZ6z8IeHT39jwgpfQfLqPOsmZVoZuMcjHGyTmNS2dd1Kf4oxwbuFNw1PBkoqoVDzaBHdf1YR76Cs/RRpSrA5n2ijscCm2pRM6zu2G0hXVvm6R326Xnn18Urj2JaINQul/uUvf3lPwLSeM0VgjJEMjnnOWLfLa0gfVK82PWWxFMb2eFtWhIC1jngV8FMWW1OMBepI8abc1nYICi9iwPpn2G+RQYhIiqn7uk58Ftghyyioys3LBukhH6IMtW1iklGnLTNseLSUR1B7znf3Hht3hukm/S6SZNL5Oo7IGCNu6jyFnQfPmPDaECNsKTKt386ajy0ivHiMa/oMTxdbsxDGyAPeCvV2ETjCwpxhPHniE5/Ykxqz3ivpgkAQmIxASIbJ2KyaM/aqWdyabC0suJJixFkgxiZVQa5qYptWiZpkTGIWjxYVNVnXdSxExEQyTZTLomVsMqqFYzHV0/LJuSAQBILAUhDgRm9s5G0gMJ74MWOK5mJxZQSqM/6VUqgM3J1teeBiy7W83Njdr0gG5O9QEeFuy0XaXuJyw606WQDzAKjxt47XZ7nQs8xS4MvyWOfrkzeG8pTiUseHn9ygW+WxPW/M95YGHm2ttZjleSi2fVAYW2G154I/rH+bxj0QzJPIEATBLBZZll352IJHeYc7Dz57yxE6XKPVleW7FSQQi+pS3cDbPJb63ZYRZD9lkmJL9C1WXzjAfBZlSl8RoA45weLKcwGJRtF1vfsMpTwZ4D5JxAnh7aGPkVJ669MxxFD7Nox6fib1Wxb9mutdr39SlLmoW5NoY88Wy34p80X8ja1j9Ht/FOnyJBDvw+srEX2tt4zr9cH2/sowFEonwkI+1ir1DA/Tqav6w3IoPJAQFazlykaZ9SpVJKStKwgIpJk3ptjSoqztugpxaT0nj2HsGOONZ5UXCqzE4RrzcFEmZJ4xyXOgf9geVN5SwzK3v+UP10nt2Ka1XUSZjGFjY2qbdrV851GirNoJoYcogqftWsYPW1DELvFcIu5aEbTTdpJIEAgC80EgJMN8cFzRXFgrTCTcGLl0LWZZMLEtRUQ+5vnAtXM46VYAsPZ1R8O8LSYEoLKg4GUxJDhYTUi5Hw+vz+8gEASCwJYgwOJvwY5c8J2wsI8ttlsFangvEeK9tUfke0qQ8YzF67AFd2rKGKuX+1j4y8f9iBgECIXhvmlW9t0He6HrnvKyAEbuTiIQLIgpkZPOy0s5jLmzeK2x5o0J650tJEOhIA+FQsUauFQxPyADWHiJOYbCXNZPJMOYK317H0QSkoM3A28J3gLwo0hTvCkWtlJQDil8regX5k+W5V122aU9tWLflc28SmFpvWoQHYKC6lPlmj+pENqMgYF3BiVSnuZ/ZH5tcxxr+/LomdTmLPgsu7YElVA44VfKfx2Xv3gKFGPbBEp5q/Ptp7VCq0yXe3qRdhRmhgbBqsUaIUUyTFJgh0SMOiErxhTvYdq2bPUdMQA/JBXDiC02nvehMK4gc9RBurG8WcMp3zxpkBueaUSYZ1Y/Q4bBzXFeDyVF/iAIhsIz6dnPfnbv6m8MQ+AtJsqKjPIMwLWIFnFKlA2x0o4jvCz8HnrBID49P3UcGcGyr7w8ABaL/bJYOZdzXhvMKjBQXvh6/vQZ3mkliD6CIC2BoXFcX0CWIZYXC8xZ1+YzCASByQiEZJiMzao5Y2FhkdYGw5ln4bxi0mTCvdPipyYkrC/rECtCu/gwyVvgmrxMhBYoXt2EwTfAt/tEpeWaKE8TeiQIBIEgMA8ELKxZri0MS+mSLwvh2Csb7aOuQG7D+yNCvfLOWGYfOCsk7y0Lb5ZyYhwmxj2WforaYf8L9liR5vsEC/+mWc0pYv6mbZmgHAzzrLzrkwJEEaDsTBNW3jYA4LS08zyHtEaI2zIyfHuF+aDmGbgTrtUU7zELa6sQVBkpHl4LSZmiGLWvCaw0CCft5P68PraWUFjsCddXzIn6JCmPDwr5YiQDYgV51XqYeEMThZcLunbV9vriQQcd1PdPWxEoqpT54fYSZA9CiSIpuGZLxFGEkQn1KszHPvaxfUBQhgGeMpQu1n8k3JjCrW7q2LadiP1DoWzbE0/xQyxQ+MmYB+TwWr+LKKw3wbRpEG6LideduieiRx0922MelpROmCgjRX9sf75nj4cGHGGinzHwiE/A86n6tzZvDSzlJTR8ZaWyF0HAW8P9rcGGHhqee21hrUZ46RgLKdJ1veMwsmZk9KmyOO6ZgdXQC8ZYpw7t8a1JLFTbGouRtyWIG313FkHsWM8WgSNPOBLPE8+UGleRdiX6o/mhyFVtGwkCQWD5CIRkWD6GK55DDXwrdSNBqZAYIi2zVHEZYw2xcDHJmoxb4ULJbZZ7cr2qzcRkkYGowB5zRUMwWGAZ2C2OWKAiQSAIBIHlIkBJo6xYzLfW0y3Nt4LduZ6ixKWd273F6pjySlnhGs0iyiI/5u49qSwW+LwcWO/HiGMKDq8JRAOlsFUQ2jyN2+I2GK8JS7NX3omi3pbH+VrAt9fP47vgeZSmMQ8BbzyCo3OtIoccEGugVYh4MyCj1cm80yqrY+WkaCEWKFfmp51HosDb/lJ58jxpiaixPOd5jPcBskpb86zQv5BWFB4Kp62O04T1WB9/xjOesYnCVQq+vAQYtB0HmQEzyhXrq2v1T+QErwGKoj7Eo0PsgGlbHz1L3P6HVlyeI/rRkLiYVoexc7Z6Un7Lc8EaQd+Y1N5DCzalm4fBmII+Ka5UlYOiSpkssokiCTfbmlrhXq8/ejY9/0iDMZIBWVNvAHG9trX9wZ8yjnmZtPcZ+94+E85TjHlYaecSRADjjzVVBfJ0riWNKu0YkclSP4ZfXbPUT+NOBd0dXtvGcBmeG/72TBMeS62Srz/POn4hKIzJYqJ4VpAO+pyxQtsjLHg1kdYbRjvr45EgEATmi0BIhvniuWZzE6DIQGxS46JJMOCC4IxNVMOKcqW1oLOodH3lwYLE+rHYomqYX34HgSAQBCYhwArV7slu09mfP7bgnnWhKi9WPcoz5YcSy9rekhmUNXvlWWDtuV6KyNNi12sL5TEU7sxIBtb7SQQDBYdS6dV79huXMi1wJY8xsRzKUgcL1lgKJ6t0eXlwr7atY0zBqwV/WzaL//Z1mBRoWHN5p5SWZbWuka/yD7fgOU/RvcIVrlBJezzUgUv9WHk2Jlz4Ym4xV5lTEAnIbYogRaeUcEGNWWwR24ioSQQDhRKZw1tvXiS4twHAHNlD8VUvZEFtT0BODZXJtn6+UywR+C0WlC0eBeWSLx2FsSL4l2LlOOEZgojTRym+9baJ9vr/S/n//2sv92z7OiMBUsye/+XO48OtnPChkI+J51VZW6E0VsC+9rh2H5IF7XnfkRlIBWSD++oj+m25z1d6RIb+oj9IZ/vC2FjDmNKSa0idlpwp6zvPCWRKSY1DrSJd56r/1qe2UIbWowD5xEuoJR7q+sU+kTa2cCy3Hdv7IAbb8rXn9EFK/yxSXi2U/fJEcB1CkkFrFkEMV0wW7WMctfWs+r48agxr+/gseSdNEAgCS0cgJMPSMVuXV1j0WEzzSMDA14Q5NhBL528o2HHBiCzcsMVIinp90DBtfgeBIBAEthSBaZY4+/1LkW7z5w4riNliQnkWDZ/LMgXNd1sy2iBtlHNKi/Nce8dIjboP5bCV1qrfHh9+n5SO4mWhTLkui3B5RHgLBpLB3nOu7wTBQKGnnPO6QEzY/qbslHQu7MN7UZBaK7Lv5oiyAldZYUPkNSbKN3ztJAXKvNAeL2Wy6jOWF2WLF5206lAWeXv+ESgs+hRQMQeQ3TxQeBQMy9zmTTGn/CMiWPonkRHtNdO+i+1hKwLPCfV0b28q4WHCxd1rOBcjGORPwd2wQNyT6j8IAB6HLLKtR4HtIte4xjX6OVu7VXyQCjg5vB/yzJZGCnG7FcO9eDFQiBkXSuTJko/IsQ2o9ZKRpu0ndU19TjsnjeeojVlR1/mEn+02iCfCek9h1edgzEKtH1tnIF+mtbPr4VdBUnltIvGKGNOfSmx1QSJS8HkqIFkOW9gWtXPjLWON40//abeSVh4+i6gTp6otW1nSi2xorxl+L7JheNzv4TM7lmZ4DLGiPSuQZnt+jPRoz6/0d6Trs571rE0IBvccElOzlgPuvIaQKsYHRJHnW9BexwVfFbshEgSCwMohEJJh5bBdkzlbkExbwM9SKYuGSRPvLNcnTRAIAkFgWyFgi4IxkKcAoYj7QzpY9LMiImNtY+OuT8llqbY/e0ws3ltlq/0+ln6xYyzhZeEeksDIBm7UrZWTNZHCveeee/YKOCJCDB4L7UkLeNve2nIiXii27baSxcrpfJvHtPTyX0xszUCctAq2a1g+tYPjiCBkCtd+dSucJuVty4o4BjxVjjjiiFGvi0nXDo9T/nntUdBrH7g03maAZKDsIBlmkSIYpC3lD5btced4tOiP+h+Fedddd+0VqdZyK10r+og+NCaCcypnkUdjaYbHygLNI6AlNJSbQjtNKPCTDBEIJ2QC4QYvfgICxZ565IQYEtoNUYLIQaqMxWpwfRE1vhNlHh5zHMaUU94LpIgGv3nkFMHCWwXhInaLPmd7RXkO9Rcu/Cvvhbvc5S4bY3E4xxNBHx3zEG3bWtrCsN2OgMgilX//Y4Z/2gL5hWhsXyNal3oGi+yrY1vzc7GA5kspC9wQKshGBJmxG8GExEDK6VdIRWRbBR9dSv5JGwSCwGwIhGSYDaekCgJBIAgEgVWMwGIL5FJkWTQnCeWbtbTeHiGdhWi9cpCyuP/++/eWMRbW3XbbrQ+4Zv89pYMFlMLT3sOCt1W2SyljeR9TdCheZOycOAesrm35htZOHg4l9kRzMSe8DSholCgK8dCKXdf4pJC0eKpD+7tNO+m78rtumtRr/yi2ZJonAWzHhCLobSBcqilPFEIW/1lEO/F+oKCP7bufJY9KI/YCAmdIBCBBtHVZ5LWvNkOKIDbINKt09dsh/vrUPvvs0wf1rD5H6eW6znsFuUHpbvtilXVLPxFwRWRUHqXs8iBpSS+W/pZkUH7BILnCs+zrm8iD8rio/NpP14v/5L6eNa8kJIgJ20n8RojxRJFOAGrPx9CzprwKXKtfyrdwQY74rQ14OvHsQcCVIGx4biJe/CFSEBx77bVXTyxQXGGuL/Ns4CXAswmZI0153FR+vCOQlowxnsPWe6e8G6rNfSpPux2BR4x+rq6kxpPKf+yTIi0Aq0CkbRyHNi0Mhn2sPT+P78ZWyn/rKTNLvrZMIHKMDxXnoSW0hnkg3RCI9UxrP30ECWiLCy8q4wXSUkwJbW/s90z6rk/DrIJEDvPP7yAQBGZDICTDbDglVRAIAkEgCKxiBCySh2LRTvmgdJWbcuu63KanfIvFQFlsF7D2HHtVHZdbC1xbwmrvNWuk9EgGljF74AXPbZVcZWiV7VLKeHuVa3tbDnlw/6aAtKJ+Yi5QMCrgHEVGrAQxGLiNWyBTTChVFuUs/PaYewsBZZrXBYvhNILBPVnIS9Hx24J7jPRwbpIo79h+e/lUXlzekTjiWijfUoKviUUgngLFFZaslvJYilCquMJrh7GyzpoXsoDny9g2HSQIBa9Eu1JmKeWlILZ70CtdfVY/aPsQSz6yS7tQckth1u+4gCPF5E8pY7mmjOu7+kRZ4yv/9lN53KfiPLTnWN8patzNfdYz4hqKsm1KVQ7XIVLamAXOUe5s9al98fIoj6H2XspJGaXMq5PPsecWtjwFbAfiuWKrg+e9XptZebYkg2PSUtJ5IahPvSlLoOryYqhrEVhIB94LcKec2s5Sr/U2BhgfeIfog8rOk8WffBGTnjd11ZbKIh+f3liDPCkpkqHGMt5S7esXpeMNwzPJWOCNXsYs0nrP9Af+98+4t/fee/eE03777TfRu0dfqvu21/uO0PIaVedbIslYJvaBMXFMjD0EqWIMQpIin4xhe+yxx9glo8dgjZDyNiHPPKk3tbQXKKexUH9oxxJt5J7Vfq4xVtoGZ0uVPFvvHSSWtowEgSCwPARCMiwPv1wdBIJAEAgCqwABC2BKXmvFtLgXgI8SaBsBi29ZlNsiUzQo7IKEtUIpEDyMizLL2Ng2MAtYCoYFq3sNFUYL8daVuhQJStPYfnSKiQXwMJAdhUr8APmXMscKTIlQfgtoGFDgKZL/r707TG1bCcMw2kJXceHC/ZlldGndS/aQ9RRKuo00j2HAV8iu4kyo6RxBmsSWxpozkpvvlSz1VXFUETZCiZbdrt95f/u5YqOvPuc/pgrJvSJvPL/3vQJuWwiMI6WjcG65ivuORl87i6HioWKqIrKjmh19bH0qFDuro89z3zLVZgXJewKGXndvu7i0Po1Jp2/3kZa2ye4EcV4QbZdre+nIeEdZ2w4ruAsBKkD3boPZ7UofHx9PxXLF2SjmaqNbuLadj4Bg+1oV1E3j7Jfz5wsSCoSauvtUR9ibOoOnMGFsk6cHX/8Z1wQZv/c9p77aJ7LviHZB05g6lb0wo+2ksa2wPTI2Dw8Pp32wbWG739R22+/YFiuSR/jRWQ95NvW6FZztf+dT/eqilwUQfe+I97i2w5gvzy6O2UUH2z4riNsvCxwK7PJqHAsW2rcKcjo7YXt2TvO0niOsqjDem1qnfJv32+vZLIVI52dftEz7StfRKOwpVCksvTblshf4tExttG3kWAHe703b98vTg5t/xjbTw72HNvVe9pap/rbdNM5dGLeft3a1VxBUYDi2zR7rbj2FIO3nX8+uq9FzjVtnHxUMF0QXxPU+WpAzzhRpPhMBArcJfH79z/vltkV/v9T3Hz8//ffvP7+f0RwE/lIB+8BfOrC6dXcC/UFfIXTpKH1X4u9I6rYYutaR7sBQiHDpKOG1Zcdz3bGnQnIcQa7A6O4B/dG+ty4FBRVhFZV/YupPgufn5//9od6FJAsq9gq4t6xjAUgFTwHEOBvkyPJPT0+nwmyEJBXKe3ZH2rqXeXLuegrj4oOX1qvCtIuL1vfOlmkczkOrS8vd8+MV2hWao1gd65rJ9rHx3Hu+9xGVa9tMBeberWr3XrMLZ44QYO/59zxW24VnR4vwwpgu1L13Z5SCjc5uOjr1vnP0dY+2+RHztT9cu9Du9jULGQpzC25NBAgcF5hRvwgZjnubk8CbBWbspG9+UQsQIECAAAECBAgQIEDgBoEZ9cvlK2DdsEIWIUCAAAECBAgQIECAAAECBNYVEDKsO/Z6ToAAAQIECBAgQIAAAQIEpgoIGaZyaowAAQIECBAgQIAAAQIECKwrIGRYd+z1nAABAgQIECBAgAABAgQITBUQMkzl1BgBAgQIECBAgAABAgQIEFhXQMiw7tjrOQECBAgQIECAAAECBAgQmCogZJjKqTECBAgQIECAAAECBAgQILCugJBh3bHXcwIECBAgQIAAAQIECBAgMFVAyDCVU2MECBAgQIAAAQIECBAgQGBdASHDumOv5wQIECBAgAABAgQIECBAYKqAkGEqp8YIECBAgAABAgQIECBAgMC6AkKGdcdezwkQIECAAAECBAgQIECAwFQBIcNUTo0RIECAAAECBAgQIECAAIF1BYQM6469nhMgQIAAAQIECBAgQIAAgakCQoapnBojQIAAAQIECBAgQIAAAQLrCggZ1h17PSdAgAABAgQIECBAgAABAlMFhAxTOTVGgAABAgQIECBAgAABAgTWFRAyrDv2ek6AAAECBAgQIECAAAECBKYKCBmmcmqMAAECBAgQIECAAAECBAisKyBkWHfs9ZwAAQIECBAgQIAAAQIECEwVEDJM5dQYAQIECBAgQIAAAQIECBBYV0DIsO7Y6zkBAgQIECBAgAABAgQIEJgqIGSYyqkxAgQIECBAgAABAgQIECCwroCQYd2x13MCBAgQIECAAAECBAgQIDBVQMgwlVNjBAgQIECAAAECBAgQIEBgXYEvH9317z9+fvRLaJ/AXQvYB+56eKwcAQIECBAgQIAAAQITBT6/vE4T29MUAQIECBAgQIAAAQIECBAgsKiAj0ssOvC6TYAAAQIECBAgQIAAAQIEZgsIGWaLao8AAQIECBAgQIAAAQIECCwqIGRYdOB1mwABAgQIECBAgAABAgQIzBYQMswW1R4BAgQIECBAgAABAgQIEFhU4BcJDq1Dp8s4VgAAAABJRU5ErkJggg=="}}, "cell_type": "markdown", "metadata": {}, "source": ["![image.png](attachment:image.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["columns = [\"min\",\"max\",\"count_0\",\"count_1\"] \n", "df = pd.DataFrame(num_bins,columns=columns)\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"total\"] = df.count_0 + df.count_1    # 一个箱子的标签0和1的总数\n", "df[\"percentage\"] = df.total / df.total.sum() #一个箱子的标签0和1的总数，占总样本的比例\n", "df[\"bad_rate\"] = df.count_1 / df.total  # 一个箱子，坏客户在count_0+count_1的比重\n", "df[\"good%\"] = df.count_0/df.count_0.sum() # 一个箱子的好客户占整个样本中好客户的比重\n", "df[\"bad%\"] = df.count_1/df.count_1.sum()  # 一个箱子的坏客户占整个样本中坏客户的比重\n", "df[\"woe\"] = np.log(df[\"good%\"] / df[\"bad%\"]) # 针对于箱子的重要度/证据权重\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rate = df[\"good%\"] - df[\"bad%\"]\n", "iv = np.sum(rate * df.woe)\n", "iv"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_woe(num_bins):\n", "    columns = [\"min\",\"max\",\"count_0\",\"count_1\"]  \n", "    df = pd.DataFrame(num_bins,columns=columns)\n", "    df[\"total\"] = df.count_0 + df.count_1 \n", "    df[\"percentage\"] = df.total / df.total.sum() \n", "    df[\"bad_rate\"] = df.count_1 / df.total \n", "    df[\"good%\"] = df.count_0/df.count_0.sum() \n", "    df[\"bad%\"] = df.count_1/df.count_1.sum() \n", "    df[\"woe\"] = np.log(df[\"good%\"] / df[\"bad%\"])\n", "    return df\n", "\n", "\n", "# 计算IV值 \n", "def get_iv(df):\n", "    rate = df[\"good%\"] - df[\"bad%\"]\n", "    iv = np.sum(rate * df.woe)\n", "    return iv "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3.3 用卡方检验来合并箱体画出IV曲线"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_bins_ = num_bins.copy()\n", "\n", "import matplotlib.pyplot as plt\n", "import scipy\n", "num_bins_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x1 = num_bins_[0][2:]\n", "x1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x2 = num_bins_[1][2:]\n", "x2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pv = scipy.stats.chi2_contingency([x1, x2])[1]\n", "pv"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_bins_ = num_bins.copy()\n", "\n", "import matplotlib.pyplot as plt\n", "import scipy\n", "\n", "IV = []\n", "axisx = []\n", "\n", "while len(num_bins_) > 2:\n", "    pvs = []\n", "    \n", "    for i in range(len(num_bins_) - 1):\n", "        x1 = num_bins_[i][2:]\n", "        x2 = num_bins_[i + 1][2:]\n", "        \n", "        pv = scipy.stats.chi2_contingency([x1, x2])[1]\n", "        pvs.append(pv)\n", "\n", "    \n", "    i = pvs.index(max(pvs))\n", "    num_bins_[i:i + 2] = [(num_bins_[i][0],num_bins_[i+1][1],num_bins_[i][2]+num_bins_[i+1][2],num_bins_[i][3]+num_bins_[i+1][3])]\n", "\n", "    bins_df = get_woe(num_bins_)\n", "    axisx.append(len(num_bins_))\n", "    IV.append(get_iv(bins_df))\n", "\n", "plt.figure()\n", "plt.plot(axisx, IV)\n", "plt.xticks(axisx)\n", "plt.xlabel(\"number of box\") \n", "plt.ylabel(\"IV\") \n", "plt.show() \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_bin(num_bins_,n):\n", "    while len(num_bins_) > n:\n", "        pvs = []\n", "        # 获取 num_bins_两两之间的卡方检验的置信度（或卡方值）\n", "        for i in range(len(num_bins_) - 1):\n", "            x1 = num_bins_[i][2:]\n", "            x2 = num_bins_[i + 1][2:]\n", "            # 0 返回 chi2 值，1 返回 p 值。 \n", "            pv = scipy.stats.chi2_contingency([x1, x2])[1]\n", "            # chi2 = scipy.stats.chi2_contingency([x1,x2])[0]       \n", "            pvs.append(pv)\n", "\n", "        # 通过 p 值进行处理。合并 p 值最大的两组\n", "        i = pvs.index(max(pvs))\n", "        num_bins_[i:i + 2] = [(num_bins_[i][0],num_bins_[i+1][1],num_bins_[i][2]+num_bins_[i+1][2],num_bins_[i][3]+num_bins_[i+1][3])]\n", "        \n", "    return num_bins_\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["afterbins = get_bin(num_bins, 6)\n", "\n", "afterbins"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3.4 用最佳分箱个数分箱，并验证分箱结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def graphforbestbin(DF, X, Y, n=5,q=20,graph=True):\n", "    DF = DF[[X,Y]].copy()\n", "    DF[\"qcut\"],bins = pd.qcut(DF[X],retbins=True,q=q,duplicates=\"drop\")\n", "    coount_y0 = DF.loc[DF[Y]==0].groupby(by=\"qcut\").count()[Y]\n", "    coount_y1 = DF.loc[DF[Y]==1].groupby(by=\"qcut\").count()[Y]\n", "    num_bins = [*zip(bins,bins[1:],coount_y0,coount_y1)]\n", "    # 确保每个箱中都有0和1\n", "    for i in range(q):\n", "        if 0 in num_bins[0][2:]:\n", "            num_bins[0:2] = [(num_bins[0][0],num_bins[1][1],num_bins[0][2]+num_bins[1][2],num_bins[0][3]+num_bins[1][3])]\n", "            continue\n", "        for i in range(len(num_bins)):\n", "            if 0 in num_bins[i][2:]:\n", "                num_bins[i-1:i+1] = [(num_bins[i-1][0],num_bins[i][1],num_bins[i-1][2]+num_bins[i][2],num_bins[i-1][3]+num_bins[i][3])]\n", "                break\n", "        else:\n", "            break\n", "    #计算WOE\n", "    def get_woe(num_bins):\n", "        columns = [\"min\",\"max\",\"count_0\",\"count_1\"]\n", "        df = pd.DataFrame(num_bins,columns=columns)\n", "        df[\"total\"] = df.count_0 + df.count_1\n", "        df[\"good%\"] = df.count_0/df.count_0.sum()\n", "        df[\"bad%\"] = df.count_1/df.count_1.sum()\n", "        df[\"woe\"] = np.log(df[\"good%\"] / df[\"bad%\"])\n", "        return df\n", "    #计算IV值\n", "    def get_iv(df):\n", "        rate = df[\"good%\"] - df[\"bad%\"]\n", "        iv = np.sum(rate * df.woe)\n", "        return iv\n", "    # 卡方检验，合并分箱\n", "    IV = []\n", "    axisx = []\n", "    while len(num_bins) > n:\n", "        global bins_df\n", "        pvs = []\n", "        for i in range(len(num_bins)-1):\n", "            x1 = num_bins[i][2:]\n", "            x2 = num_bins[i+1][2:]\n", "            pv = scipy.stats.chi2_contingency([x1,x2])[1]\n", "            pvs.append(pv)\n", "        i = pvs.index(max(pvs))\n", "        num_bins[i:i+2] = [(num_bins[i][0],num_bins[i+1][1],num_bins[i][2]+num_bins[i+1][2],num_bins[i][3]+num_bins[i+1][3])]\n", "        bins_df = pd.DataFrame(get_woe(num_bins))\n", "        axisx.append(len(num_bins))\n", "        IV.append(get_iv(bins_df))\n", "        \n", "    if graph:\n", "        plt.figure()\n", "        plt.plot(axisx,IV)\n", "        plt.xticks(axisx)\n", "        plt.xlabel(\"number of box\")\n", "        plt.ylabel(\"IV\")\n", "        plt.show()\n", "        \n", "    return bins_df\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_data.columns "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in model_data.columns[1:-1]:\n", "    print(i)\n", "\n", "    graphforbestbin(model_data,i ,\"SeriousDlqin2yrs\",n=2,q = 20)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 根据图像观察手动把特征进行最佳分箱操作\n", "# 特征名称：分箱的个数/箱子的转折点\n", "auto_bins = {'RevolvingUtilizationOfUnsecuredLines':5\n", "            ,'age':6\n", "            ,'DebtRatio':4\n", "            ,'MonthlyIncome':3\n", "            ,'NumberOfOpenCreditLinesAndLoans':7\n", "            }\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 手动处理对于不能分箱的特征\n", "hand_bins = {'NumberOfTime30-59DaysPastDueNotWorse':[0,1,2,13]\n", "            ,'NumberOfTimes90DaysLate':[0,1,2,17]\n", "            ,'NumberRealEstateLoansOrLines':[0,1,2,4,54]\n", "            ,'NumberOfTime60-89DaysPastDueNotWorse':[0,1,2,8]\n", "            ,'NumberOfDependents':[0,1,2,3]\n", "            }\n", "#用np.-inf , np.inf\n", "hand_bins = {k:[-np.inf,*v[:-1],np.inf] for k,v in hand_bins.items()}\n", "\n", "bins_of_col = {}\n", "for col in auto_bins:\n", "    bins_df = graphforbestbin(model_data,col,'SeriousDlqin2yrs',n = auto_bins[col],q=20,graph=False)\n", "    bins_list = sorted(set(bins_df['min']).union(bins_df['max']))\n", "    bins_list[0],bins_list[-1] = -np.inf,np.inf\n", "    bins_of_col[col] = bins_list\n", "\n", "bins_of_col.update(hand_bins)\n", "bins_of_col \n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.4 计算各箱的WOE并映射到数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = model_data.copy() \n", "data = data[[\"age\",\"SeriousDlqin2yrs\"]].copy() \n", "  \n", "data[\"cut\"] = pd.cut(data[\"age\"],[-np.inf, 36.0, 52.0, 56.0, 61.0, 74.0, np.inf]) \n", "# 不同的年龄段/箱子对于的年龄和标签 \n", "data \n", "  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.groupby(\"cut\")[\"SeriousDlqin2yrs\"].value_counts() \n", "  \n", "#使用unstack()来将分支状结构变成表状结构 \n", "data.groupby(\"cut\")[\"SeriousDlqin2yrs\"].value_counts().unstack() \n", "  \n", "bins_df = data.groupby(\"cut\")[\"SeriousDlqin2yrs\"].value_counts().unstack() \n", "  \n", "bins_df[\"woe\"] = np.log((bins_df[0]/bins_df[0].sum())/(bins_df[1]/bins_df[1].sum())) "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bins_df # 在年龄的箱子中属于0的个数和属于1的个数以及WOE"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 对所有的特征进行计算箱子的WOE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df:数据表\n", "# col：列\n", "# bins：箱子的个数\n", "def get_woe(df,col,y,bins):\n", "    \n", "    df = df[[col,y]].copy()\n", "    df[\"cut\"] = pd.cut(df[col],bins) \n", "    bins_df = df.groupby(\"cut\")[y].value_counts().unstack()\n", "    woe = bins_df[\"woe\"] = np.log((bins_df[0]/bins_df[0].sum())/(bins_df[1]/bins_df[1].sum()))\n", "    iv = np.sum((bins_df[0]/bins_df[0].sum()-bins_df[1]/bins_df[1].sum())*bins_df['woe'])\n", "    return woe"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 所有的WOE\n", "woeall = {}\n", "\n", "for col in bins_of_col:\n", "    woeall[col] = get_woe(model_data,col,\"SeriousDlqin2yrs\",bins_of_col[col])\n", "woeall   "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_woe = pd.DataFrame(index=model_data.index)\n", "\n", "for col in bins_of_col:\n", "    model_woe[col] = pd.cut(model_data[col],bins_of_col[col]).map(woeall[col])\n", "\n", "model_woe[\"SeriousDlqin2yrs\"] = model_data[\"SeriousDlqin2yrs\"]\n", "\n", "model_woe  #这就是建模数据\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.5 建模与模型验证"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["woeall_vali = {}\n", "for col in bins_of_col:\n", "    woeall_vali[col] = get_woe(vali_data,col,\"SeriousDlqin2yrs\",bins_of_col[col])\n", "    \n", "# 测试数据    \n", "vali_woe = pd.DataFrame(index=vali_data.index)\n", "for col in bins_of_col:\n", "    vali_woe[col] = pd.cut(vali_data[col],bins_of_col[col]).map(woeall_vali[col])\n", "\n", "vali_woe[\"SeriousDlqin2yrs\"] = vali_data[\"SeriousDlqin2yrs\"]\n", "\n", "\n", "vali_x = vali_woe.iloc[:,:-1]\n", "vali_y = vali_woe.iloc[:,-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.linear_model import LogisticRegression as LR\n", "# 训练集\n", "x = model_woe.iloc[:,:-1]\n", "y = model_woe.iloc[:,-1]\n", "lr = LR().fit(x,y)\n", "lr.score(vali_x,vali_y)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- C是正则化强度的倒数，C越小，损失函数就越小，模型对损失函数的惩罚越重\n", "- solver：默认是liblinear，针对小数据量是个不错的选择，用于求解使模型最优化参数的算法，即最优化问题的算法\n", "- max_iter：所有分类的实际迭代次数，对于liblinear求解器，会给出最大的迭代次数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c_1 = np.linspace(0.01,1,20) \n", "c_2 = np.linspace(0.01,0.2,20)\n", "\n", "score = []\n", "for i in  c_1:\n", "    lr = LR(solver=\"liblinear\",C = i).fit(x,y)\n", "    score.append(lr.score(vali_x,vali_y))\n", "\n", "\n", "plt.figure()\n", "plt.plot(c_1,score)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')\n", "score = []\n", "for i in [1,2,3,4,5,6]:\n", "    lr = LR(solver=\"liblinear\" ,C = 0.025 , max_iter=i).fit(x,y)\n", "    score.append(lr.score(vali_x , vali_y))\n", "    \n", "plt.figure()\n", "plt.plot([1,2,3,4,5,6],score)\n", "plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import scikitplot as skplt  #pip install scikit-plot \n", "  \n", "vali_proba_df = pd.DataFrame(lr.predict_proba(vali_x))\n", "\n", "skplt.metrics.plot_roc(vali_y, vali_proba_df, plot_micro=False,figsize=(6,6),plot_macro=False) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.6 制作评分卡"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 评分卡中的分数，由以下公式计算：Score = A - B *log(odds)\n", "- A与B是常数，A叫做“补偿”，B叫做“刻度"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- log(odds)代表了一个人违约的可能性,逻辑回归的结果取对数几率形式会得到θX，即参数*特征矩阵，所以log(odds)其实就是参数。两个常数可以通过两个假设的分值带入公式求出，这两个假设分别是： \n", "    - 某个特定的违约概率下的预期分值 \n", "    - 指定的违约概率翻倍的分数（PDO）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 例如，假设对数几率为1/60时,设定的特定分数为600，PDO=20，那么对数几率为1/30时的分数就是620。带入以上线性表达式，可以得到：\n", "    - 600 = A - B*log(1/60)\n", "    - 620 = A - B*log(1/30)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["B = 20/np.log(2) \n", "A = 600 + B*np.log(1/60)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["(481.8621880878296, 28.85390081777927)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["A , B"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'lr' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/var/folders/b5/b821qsb14tb041gdvx21p4800000gn/T/ipykernel_1600/2924192439.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mlr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mintercept_\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'lr' is not defined"]}], "source": ["lr.intercept_"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'lr' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/var/folders/b5/b821qsb14tb041gdvx21p4800000gn/T/ipykernel_1600/4088408111.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mbase_score\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mA\u001b[0m \u001b[0;34m-\u001b[0m \u001b[0mB\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0mlr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mintercept_\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0mbase_score\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'lr' is not defined"]}], "source": ["base_score = A - B*lr.intercept_ \n", "base_score"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'woeall' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/var/folders/b5/b821qsb14tb041gdvx21p4800000gn/T/ipykernel_1600/4163286257.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mwoeall\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'woeall' is not defined"]}], "source": ["woeall"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'lr' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/var/folders/b5/b821qsb14tb041gdvx21p4800000gn/T/ipykernel_1600/2221464025.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mlr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcoef_\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0mB\u001b[0m \u001b[0;31m# log(odds)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'lr' is not defined"]}], "source": ["lr.coef_[0][1]*B # log(odds)"]}, {"cell_type": "code", "execution_count": 279, "metadata": {}, "outputs": [{"data": {"text/plain": ["cut\n", "(-inf, 36.0]   -11.113083\n", "(36.0, 52.0]    -6.284292\n", "(52.0, 56.0]     0.357473\n", "(56.0, 61.0]     6.219509\n", "(61.0, 74.0]    21.197762\n", "(74.0, inf]     36.554287\n", "dtype: float64"]}, "execution_count": 279, "metadata": {}, "output_type": "execute_result"}], "source": ["score_age = woeall[\"age\"] * (-B*lr.coef_[0][0]) \n", "score_age "]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'base_score' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/var/folders/b5/b821qsb14tb041gdvx21p4800000gn/T/ipykernel_1600/1259551147.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0mfile\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m\"ScoreData.csv\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[0;32mwith\u001b[0m \u001b[0mopen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfile\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\"w\"\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mfdata\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 3\u001b[0;31m     \u001b[0mfdata\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwrite\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"base_score,{}\\n\"\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mformat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mbase_score\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      4\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mi\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mcol\u001b[0m \u001b[0;32min\u001b[0m \u001b[0menumerate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcolumns\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m     \u001b[0mscore\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mwoeall\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mcol\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m*\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m-\u001b[0m\u001b[0mB\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0mlr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcoef_\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'base_score' is not defined"]}], "source": ["file = \"ScoreData.csv\" \n", "with open(file,\"w\") as fdata:\n", "    fdata.write(\"base_score,{}\\n\".format(base_score))\n", "for i,col in enumerate(x.columns):\n", "    score = woeall[col] * (-B*lr.coef_[0][i])\n", "    score.name = \"Score\"\n", "    score.index.name = col\n", "    score.to_csv(file,header=True,mode=\"a\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 五、总结\n", "- 至此，我们评分卡的内容就全部结束了。其实大家可以发现，真正建模的部分不多，更多是我们如何处理数 据，如何利用统计和机器学习的方法将数据调整成我们希望的样子，所以除了算法，更加重要的是我们能够达成数据目的的工程能力。写出属于自己的分箱函数和评分卡模型。"]}, {"attachments": {"%E7%BB%93%E6%9D%9F%E8%AF%AD.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["![%E7%BB%93%E6%9D%9F%E8%AF%AD.png](attachment:%E7%BB%93%E6%9D%9F%E8%AF%AD.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "245.38601684570312px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 2}
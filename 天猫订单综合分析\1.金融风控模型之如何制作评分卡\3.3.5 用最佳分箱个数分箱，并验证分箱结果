3.3.5 用最佳分箱个数分箱，并验证分箱结果
将合并箱体的部分定义为函数，并实现分箱

# 目的是为了传入最优的分箱 ，也就是6是最好的
def get_bin(num_bins_,n):
    while len(num_bins_) > n:
        pvs = []
        # 获取 num_bins_两两之间的卡方检验的置信度（或卡方值）
        for i in range(len(num_bins_) - 1):
            x1 = num_bins_[i][2:]
            x2 = num_bins_[i + 1][2:]
            # 0 返回 chi2 值，1 返回 p 值。 
            pv = scipy.stats.chi2_contingency([x1, x2])[1]
            # chi2 = scipy.stats.chi2_contingency([x1,x2])[0]       
            pvs.append(pv)

        # 通过 p 值进行处理。合并 p 值最大的两组
        i = pvs.index(max(pvs))
        num_bins_[i:i + 2] = [(num_bins_[i][0],num_bins_[i+1][1],num_bins_[i][2]+num_bins_[i+1][2],num_bins_[i][3]+num_bins_[i+1][3])]
        
    return num_bins_


afterbins = get_bin(num_bins, 6)

afterbins
------------------------------------------------
(21.0, 36.0, 14736, 24981),
 (36.0, 52.0, 34430, 46384),
 (52.0, 56.0, 9168, 8976),
 (56.0, 61.0, 11187, 8326),
 (61.0, 74.0, 20343, 7414),
 (74.0, 99.0, 7703, 1344)]


bins_df = get_woe(num_bins)

bins_df

min	max	count_0	count_1	total	percentage	bad_rate	good%	bad%	woe
0	21.0	28.0	4232	7616	11848	0.060761	0.642809	0.043375	0.078173	-0.589033
1	28.0	31.0	3534	5850	9384	0.048125	0.623402	0.036221	0.060046	-0.505468
2	31.0	34.0	4045	6796	10841	0.055597	0.626879	0.041459	0.069756	-0.520309
------------------------------------------------














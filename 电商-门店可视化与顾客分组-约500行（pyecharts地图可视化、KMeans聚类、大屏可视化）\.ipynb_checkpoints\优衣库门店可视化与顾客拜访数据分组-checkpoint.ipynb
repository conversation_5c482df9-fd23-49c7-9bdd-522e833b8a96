{"cells": [{"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Table of Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#项目背景\" data-toc-modified-id=\"项目背景-1\">项目背景</a></span><ul class=\"toc-item\"><li><span><a href=\"#1-引言\" data-toc-modified-id=\"1-引言-1.1\">1 引言</a></span></li><li><span><a href=\"#2-数据说明\" data-toc-modified-id=\"2-数据说明-1.2\">2 数据说明</a></span></li></ul></li><li><span><a href=\"#一、数据导入及预处理\" data-toc-modified-id=\"一、数据导入及预处理-2\">一、数据导入及预处理</a></span><ul class=\"toc-item\"><li><span><a href=\"#1-数据导入\" data-toc-modified-id=\"1-数据导入-2.1\">1 数据导入</a></span></li><li><span><a href=\"#2-数据观察\" data-toc-modified-id=\"2-数据观察-2.2\">2 数据观察</a></span><ul class=\"toc-item\"><li><span><a href=\"#2.1-查看数据形状\" data-toc-modified-id=\"2.1-查看数据形状-2.2.1\">2.1 查看数据形状</a></span></li><li><span><a href=\"#2.2-检查缺失值\" data-toc-modified-id=\"2.2-检查缺失值-2.2.2\">2.2 检查缺失值</a></span></li><li><span><a href=\"#2.3-有无重复值\" data-toc-modified-id=\"2.3-有无重复值-2.2.3\">2.3 有无重复值</a></span></li></ul></li><li><span><a href=\"#3-数据预处理\" data-toc-modified-id=\"3-数据预处理-2.3\">3 数据预处理</a></span><ul class=\"toc-item\"><li><span><a href=\"#3.1-获取详细地址\" data-toc-modified-id=\"3.1-获取详细地址-2.3.1\">3.1 获取详细地址</a></span></li><li><span><a href=\"#3.2-批量获取经纬度\" data-toc-modified-id=\"3.2-批量获取经纬度-2.3.2\">3.2 批量获取经纬度</a></span><ul class=\"toc-item\"><li><span><a href=\"#3.2.1-安装geopy包\" data-toc-modified-id=\"3.2.1-安装geopy包-2.3.2.1\">3.2.1 安装geopy包</a></span></li><li><span><a href=\"#3.2.2-批量获取经纬度\" data-toc-modified-id=\"3.2.2-批量获取经纬度-2.3.2.2\">3.2.2 批量获取经纬度</a></span></li></ul></li></ul></li></ul></li><li><span><a href=\"#二、优衣库门店可视化\" data-toc-modified-id=\"二、优衣库门店可视化-3\">二、优衣库门店可视化</a></span><ul class=\"toc-item\"><li><span><a href=\"#1-数据获取\" data-toc-modified-id=\"1-数据获取-3.1\">1 数据获取</a></span><ul class=\"toc-item\"><li><span><a href=\"#1.1-读取地点数据\" data-toc-modified-id=\"1.1-读取地点数据-3.1.1\">1.1 读取地点数据</a></span></li><li><span><a href=\"#1.2-筛选优衣库门店\" data-toc-modified-id=\"1.2-筛选优衣库门店-3.1.2\">1.2 筛选优衣库门店</a></span></li></ul></li><li><span><a href=\"#2-门店可视化\" data-toc-modified-id=\"2-门店可视化-3.2\">2 门店可视化</a></span><ul class=\"toc-item\"><li><span><a href=\"#2.1-带涟漪效果的散点图\" data-toc-modified-id=\"2.1-带涟漪效果的散点图-3.2.1\">2.1 带涟漪效果的散点图</a></span></li><li><span><a href=\"#2.2-热力图\" data-toc-modified-id=\"2.2-热力图-3.2.2\">2.2 热力图</a></span></li></ul></li></ul></li><li><span><a href=\"#三、拜访数据分组\" data-toc-modified-id=\"三、拜访数据分组-4\">三、拜访数据分组</a></span><ul class=\"toc-item\"><li><span><a href=\"#1-数据获取\" data-toc-modified-id=\"1-数据获取-4.1\">1 数据获取</a></span></li><li><span><a href=\"#2-拜访数据分组\" data-toc-modified-id=\"2-拜访数据分组-4.2\">2 拜访数据分组</a></span><ul class=\"toc-item\"><li><span><a href=\"#2.1-KMeans分组\" data-toc-modified-id=\"2.1-KMeans分组-4.2.1\">2.1 KMeans分组</a></span></li><li><span><a href=\"#2.2-输出分组结果\" data-toc-modified-id=\"2.2-输出分组结果-4.2.2\">2.2 输出分组结果</a></span><ul class=\"toc-item\"><li><span><a href=\"#2.2.1-分组结果\" data-toc-modified-id=\"2.2.1-分组结果-4.2.2.1\">2.2.1 分组结果</a></span></li><li><span><a href=\"#2.2.2-中心点坐标\" data-toc-modified-id=\"2.2.2-中心点坐标-4.2.2.2\">2.2.2 中心点坐标</a></span></li></ul></li></ul></li><li><span><a href=\"#3-K值调整\" data-toc-modified-id=\"3-K值调整-4.3\">3 K值调整</a></span><ul class=\"toc-item\"><li><span><a href=\"#3.1-The-Elbow-Method\" data-toc-modified-id=\"3.1-The-Elbow-Method-4.3.1\">3.1 The Elbow Method</a></span></li><li><span><a href=\"#3.2-分组结果可视化\" data-toc-modified-id=\"3.2-分组结果可视化-4.3.2\">3.2 分组结果可视化</a></span></li><li><span><a href=\"#3.3-聚类中心地址\" data-toc-modified-id=\"3.3-聚类中心地址-4.3.3\">3.3 聚类中心地址</a></span></li></ul></li><li><span><a href=\"#大屏展示\" data-toc-modified-id=\"大屏展示-4.4\">大屏展示</a></span></li></ul></li><li><span><a href=\"#总结与展望\" data-toc-modified-id=\"总结与展望-5\">总结与展望</a></span><ul class=\"toc-item\"><li><span><a href=\"#1-项目总结\" data-toc-modified-id=\"1-项目总结-5.1\">1 项目总结</a></span></li><li><span><a href=\"#2-后期展望\" data-toc-modified-id=\"2-后期展望-5.2\">2 后期展望</a></span></li></ul></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"id": "286B6FBE1B0D40EB83705953BC98DE12", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["# 项目背景"]}, {"cell_type": "markdown", "metadata": {"id": "1BEE7BBF1BE14042A80BE831E126606B", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 1 引言"]}, {"cell_type": "markdown", "metadata": {"id": "220D3D9059A74A72AD616D2C07E5DD86", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["在正文开始之前，大家可以先来考虑这样两个问题：\n", "\n", "1. 一家连锁企业在开设线下门店时会以哪些因素为参考呢？\n", "\n", "1. 在不同层次的城市应该分别开设多少家门店，门店位置如何选取，才能促其利润最大化呢？"]}, {"cell_type": "markdown", "metadata": {"id": "46AFB893A08D45F78F86F3785A26F719", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["\n", "![Image Name](https://cdn.kesci.com/upload/image/qfg69uh7s8.jpg)\n"]}, {"cell_type": "markdown", "metadata": {"id": "D8516DA5041A449E9D9B83969E0DA95F", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["这其实就是一个数据分析的过程，如果我们拥有相应城市的人口、GDP、人群分布、用户行为等数据，我们就可以使用尽可能科学的方法帮助我们计算出相应的商业中心。\n", "\n", "那么，什么才是最科学的方法呢？下面就让我们一起来探索吧。"]}, {"cell_type": "markdown", "metadata": {"id": "B61452DD3F0443A4AE611A739EA90FE7", "jupyter": {}, "mdEditEnable": false, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2 数据说明"]}, {"cell_type": "markdown", "metadata": {"id": "3A9DB5DA61304B2EA4AE5D2B3D6F5D56", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["该数据集是一份统计自在上海市热门地点活动的80人的拜访数据样例，包含顾客姓名、剩余拜访次数、地理位置等信息。\n", "\n", "分为以下三个csv文件：\n", "\n", "* uniqlo.csv\n", "\n", "* uniqlo1.csv\n", "\n", "* uniqlo2.csv"]}, {"cell_type": "markdown", "metadata": {"id": "F54CEF28A5D7475F830379949407446A", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["其中***uniqlo2.csv***是在***uniqlo.csv***的基础上添加了详细地址、经纬度等信息，具体方法分两步：\n", "\n", "1. 使用百度地图，根据地址信息查询其对应的详细地址；\n", "\n", "1. 使用[经纬度查询工具](http://map.yanue.net/)，根据详细地址匹配对应位置的经纬度。"]}, {"cell_type": "markdown", "metadata": {"id": "61367210642B4CD5A58DEF5D2A79872D", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["为了说明地名数据的一般处理方法，下面我们使用不含详细地址及经纬度数据的这一份csv文件进行分析；大家也大家根据自己的情况考虑略过这部分操作，直接使用最后一份数据集进行后续的分析。"]}, {"cell_type": "markdown", "metadata": {"id": "78C6E1BDF9C7444AB4574096C72E07F0", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["# 一、数据导入及预处理"]}, {"cell_type": "markdown", "metadata": {"id": "FC71C69501DA47BE8A0C4BECC404AC60", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 1 数据导入"]}, {"cell_type": "code", "execution_count": 57, "metadata": {"id": "AD1CFBF4B4FC47E0839B550266F40CC2", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>姓名</th>\n", "      <th>剩余拜访次数</th>\n", "      <th>地址</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>余杭</td>\n", "      <td>1</td>\n", "      <td>优衣库南京西路店</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>沈一苇</td>\n", "      <td>2</td>\n", "      <td>优衣库浦东商场成山路店</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>杨舒琦</td>\n", "      <td>1</td>\n", "      <td>优衣库大拇指广场店</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>罗丹</td>\n", "      <td>1</td>\n", "      <td>优衣库宝山万达广场店</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>刘远</td>\n", "      <td>2</td>\n", "      <td>优衣库光启城店</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    姓名  剩余拜访次数           地址\n", "0   余杭       1     优衣库南京西路店\n", "1  沈一苇       2  优衣库浦东商场成山路店\n", "2  杨舒琦       1    优衣库大拇指广场店\n", "3   罗丹       1   优衣库宝山万达广场店\n", "4   刘远       2      优衣库光启城店"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["#读取不含经纬度信息的拜访数据\n", "\n", "import pandas as pd\n", "\n", "data = pd.read_csv('./uniqlo.csv')\n", "data.head()"]}, {"cell_type": "markdown", "metadata": {"id": "971BCB6018D9431F8B0E6E7A56C5F325", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2 数据观察"]}, {"cell_type": "markdown", "metadata": {"id": "26CC60990D6640AB8A9C6EC679C2DDC5", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 2.1 查看数据形状"]}, {"cell_type": "code", "execution_count": 58, "metadata": {"id": "73FFAC488069463FA8791C8E0B92E159", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["(80, 3)"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["#查看数据形状\n", "\n", "data.shape"]}, {"cell_type": "markdown", "metadata": {"id": "D61678255B8C486A8984F8B90D285C32", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 2.2 检查缺失值"]}, {"cell_type": "code", "execution_count": 59, "metadata": {"id": "9D7500A365CC4D3995DABCFAD44C2973", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["姓名        0\n", "剩余拜访次数    0\n", "地址        0\n", "dtype: int64"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["#检查各列有无缺失值\n", "\n", "data.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {"id": "F193CDFFFD894C538CD394EF8A5E219A", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 2.3 有无重复值"]}, {"cell_type": "code", "execution_count": 60, "metadata": {"id": "57FA78839BDF46F3838B8734338AB793", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["姓名数目为： 80\n", "剩余拜访次数枚举值为： [1 2 3]\n", "地址数目为： 73\n"]}], "source": ["#检查各列独立元素的数目\n", "\n", "print('姓名数目为：',data['姓名'].nunique())\n", "print('剩余拜访次数枚举值为：',data['剩余拜访次数'].unique())\n", "print('地址数目为：',data['地址'].nunique())"]}, {"cell_type": "markdown", "metadata": {"id": "EFCE956C11654FC28453967E2CE32546", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 3 数据预处理"]}, {"cell_type": "markdown", "metadata": {"id": "F5518577AAEB4152852200C4D3E8D45C", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["我们可以使用百度地图查询数据集中地址所对应的详细地址，继而使用***Python***支持的***geopy***工具包完成详细地址到经纬度的转换。"]}, {"cell_type": "markdown", "metadata": {"id": "8D7001FAC2B14AAAACE18E559148716F", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 3.1 获取详细地址"]}, {"cell_type": "markdown", "metadata": {"id": "687AA5476A664C7E83FFB47036618BD8", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["由于数据量不大，我们此处使用百度地图手动处理；后续可以考虑调用适合的API来完成这一转换。"]}, {"cell_type": "code", "execution_count": 61, "metadata": {"id": "CFD3166B1BD24E1FA496F31D032E2145", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>姓名</th>\n", "      <th>剩余拜访次数</th>\n", "      <th>地址</th>\n", "      <th>详细地址</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>余杭</td>\n", "      <td>1</td>\n", "      <td>优衣库南京西路店</td>\n", "      <td>上海市黄浦区南京西路</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>沈一苇</td>\n", "      <td>2</td>\n", "      <td>优衣库浦东商场成山路店</td>\n", "      <td>上海市浦东新区成山路</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>杨舒琦</td>\n", "      <td>1</td>\n", "      <td>优衣库大拇指广场店</td>\n", "      <td>上海市浦东新区芳甸路</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>罗丹</td>\n", "      <td>1</td>\n", "      <td>优衣库宝山万达广场店</td>\n", "      <td>上海市宝山区一二八纪念路</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>刘远</td>\n", "      <td>2</td>\n", "      <td>优衣库光启城店</td>\n", "      <td>上海市徐汇区宜山路</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    姓名  剩余拜访次数           地址          详细地址\n", "0   余杭       1     优衣库南京西路店    上海市黄浦区南京西路\n", "1  沈一苇       2  优衣库浦东商场成山路店    上海市浦东新区成山路\n", "2  杨舒琦       1    优衣库大拇指广场店    上海市浦东新区芳甸路\n", "3   罗丹       1   优衣库宝山万达广场店  上海市宝山区一二八纪念路\n", "4   刘远       2      优衣库光启城店     上海市徐汇区宜山路"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["#读取含详细地址的数据\n", "\n", "data = pd.read_csv('./uniqlo1.csv')\n", "data.head()"]}, {"cell_type": "markdown", "metadata": {"id": "FD530B04C87A4AD1BA5B20E28B0A46C4", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 3.2 批量获取经纬度"]}, {"cell_type": "markdown", "metadata": {"id": "84DABE7278AB4B4F9AA134142501D37A", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["#### 3.2.1 安装geopy包"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "6E72109B69704D86BD5A90BDF7E08BD4", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple/\n", "Requirement already satisfied: geopy in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (2.3.0)\n", "Requirement already satisfied: geographiclib<3,>=1.52 in c:\\users\\<USER>\\envs\\jv\\lib\\site-packages (from geopy) (2.0)\n", "------------------------------------------------------------\n", "geopy包安装完成！\n"]}], "source": ["#安装geopy包,并指定清华源下载\n", "\n", "!pip install geopy -i https://pypi.tuna.tsinghua.edu.cn/simple/\n", "print('-'*60)\n", "print('geopy包安装完成！')"]}, {"cell_type": "markdown", "metadata": {"id": "64CAB3354A0D40FA88D0B7DE51E616D6", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["#### 3.2.2 批量获取经纬度\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"id": "46458B802235499D8BA55D48212F2DFB", "jupyter": {}, "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["# #根据详细地址获取经纬度\n", "\n", "# # 速度较慢，可直接读取经纬度解析完成的数据uniqlo2.csv\n", "\n", "# import warnings \n", "# warnings.filterwarnings('ignore')\n", "\n", "# from geopy.geocoders import Nominatim\n", "# geolocator = Nominatim(user_agent='Mozilla/5.0(Windows NT 10.0;WOW64)ApplewebKit/537.36(KHTML,like Gecko)Chrome/55.0.2883.75 Safari/537.36')\n", "\n", "# from geopy.extra.rate_limiter import RateLimiter\n", "# geocode = RateLimiter(geolocator.geocode, min_delay_seconds=1)\n", "\n", "# #获取location\n", "# data['location'] = data['详细地址'].apply(geocode)\n", "\n", "# #获取经度\n", "# data['经度'] = data['location'].apply(lambda loc: loc.longitude if loc else None)\n", "\n", "# #获取纬度\n", "# data['纬度'] = data['location'].apply(lambda loc: loc.latitude if loc else None)\n", "\n", "# print('经纬度解析完成！')"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"id": "AD9E30E64F5E4F20AF8886A09638539E", "jupyter": {}, "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["# data.head()"]}, {"cell_type": "markdown", "metadata": {"id": "EF9EFC43D05342EE8553B04A7AFDD16A", "jupyter": {}, "mdEditEnable": false, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["# 二、优衣库门店可视化"]}, {"cell_type": "markdown", "metadata": {"id": "3EB7011DBD70427784C62D57F36AF221", "jupyter": {}, "mdEditEnable": false, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 1 数据获取"]}, {"cell_type": "markdown", "metadata": {"id": "E702883DF90E465C89EFD5B4C983DBCA", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["前面我们已经得到了带经纬度信息的优衣库门店数据，由于该数据中包含一些其他地点，因此我们需要对其进行相应的筛选；考虑到前一步得到的经纬度数据只到路道级别，不能保持原有数据的精确度，因此我们转而使用经经纬度解析工具解析后的这一份地点数据进行如下操作。"]}, {"cell_type": "markdown", "metadata": {"id": "AEB5DA2EB95D424E9D88F09BC7FFBB55", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 1.1 读取地点数据"]}, {"cell_type": "code", "execution_count": 62, "metadata": {"id": "488564BB27194B30958E60B4B8979ACB", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>姓名</th>\n", "      <th>剩余拜访次数</th>\n", "      <th>地址</th>\n", "      <th>详细地址</th>\n", "      <th>经度</th>\n", "      <th>纬度</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>余杭</td>\n", "      <td>1</td>\n", "      <td>优衣库南京西路店</td>\n", "      <td>上海市静安区南京西路969号</td>\n", "      <td>121.465285</td>\n", "      <td>31.235748</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>沈一苇</td>\n", "      <td>2</td>\n", "      <td>优衣库浦东商场成山路店</td>\n", "      <td>上海市浦东新区成山路500号</td>\n", "      <td>121.513909</td>\n", "      <td>31.177723</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>杨舒琦</td>\n", "      <td>1</td>\n", "      <td>优衣库大拇指广场店</td>\n", "      <td>上海市浦东新区芳甸路199弄</td>\n", "      <td>121.566506</td>\n", "      <td>31.233415</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>罗丹</td>\n", "      <td>1</td>\n", "      <td>优衣库宝山万达广场店</td>\n", "      <td>上海市宝山区一二八纪念路988弄</td>\n", "      <td>121.452854</td>\n", "      <td>31.330385</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>刘远</td>\n", "      <td>2</td>\n", "      <td>优衣库光启城店</td>\n", "      <td>上海市徐汇区宜山路455号</td>\n", "      <td>121.434446</td>\n", "      <td>31.190694</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    姓名  剩余拜访次数           地址              详细地址          经度         纬度\n", "0   余杭       1     优衣库南京西路店    上海市静安区南京西路969号  121.465285  31.235748\n", "1  沈一苇       2  优衣库浦东商场成山路店    上海市浦东新区成山路500号  121.513909  31.177723\n", "2  杨舒琦       1    优衣库大拇指广场店    上海市浦东新区芳甸路199弄  121.566506  31.233415\n", "3   罗丹       1   优衣库宝山万达广场店  上海市宝山区一二八纪念路988弄  121.452854  31.330385\n", "4   刘远       2      优衣库光启城店     上海市徐汇区宜山路455号  121.434446  31.190694"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["#读取含经纬度的地点数据\n", "\n", "data2 = pd.read_csv('./uniqlo2.csv')\n", "data2.head()"]}, {"cell_type": "markdown", "metadata": {"id": "CD00EDAA381B48418EB0521A618087B6", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 1.2 筛选优衣库门店"]}, {"cell_type": "code", "execution_count": 63, "metadata": {"id": "F9790779387C4509850E68AFE06D8E2D", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>姓名</th>\n", "      <th>剩余拜访次数</th>\n", "      <th>地址</th>\n", "      <th>详细地址</th>\n", "      <th>经度</th>\n", "      <th>纬度</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>余杭</td>\n", "      <td>1</td>\n", "      <td>优衣库南京西路店</td>\n", "      <td>上海市静安区南京西路969号</td>\n", "      <td>121.465285</td>\n", "      <td>31.235748</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>沈一苇</td>\n", "      <td>2</td>\n", "      <td>优衣库浦东商场成山路店</td>\n", "      <td>上海市浦东新区成山路500号</td>\n", "      <td>121.513909</td>\n", "      <td>31.177723</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>杨舒琦</td>\n", "      <td>1</td>\n", "      <td>优衣库大拇指广场店</td>\n", "      <td>上海市浦东新区芳甸路199弄</td>\n", "      <td>121.566506</td>\n", "      <td>31.233415</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>罗丹</td>\n", "      <td>1</td>\n", "      <td>优衣库宝山万达广场店</td>\n", "      <td>上海市宝山区一二八纪念路988弄</td>\n", "      <td>121.452854</td>\n", "      <td>31.330385</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>刘远</td>\n", "      <td>2</td>\n", "      <td>优衣库光启城店</td>\n", "      <td>上海市徐汇区宜山路455号</td>\n", "      <td>121.434446</td>\n", "      <td>31.190694</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    姓名  剩余拜访次数           地址              详细地址          经度         纬度\n", "0   余杭       1     优衣库南京西路店    上海市静安区南京西路969号  121.465285  31.235748\n", "1  沈一苇       2  优衣库浦东商场成山路店    上海市浦东新区成山路500号  121.513909  31.177723\n", "2  杨舒琦       1    优衣库大拇指广场店    上海市浦东新区芳甸路199弄  121.566506  31.233415\n", "3   罗丹       1   优衣库宝山万达广场店  上海市宝山区一二八纪念路988弄  121.452854  31.330385\n", "4   刘远       2      优衣库光启城店     上海市徐汇区宜山路455号  121.434446  31.190694"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["#根据地址字段筛选出含'优衣库'的数据行\n", "\n", "uniqlo = data2[data2['地址'].str.contains('优衣库')]\n", "uniqlo.head()"]}, {"cell_type": "code", "execution_count": 64, "metadata": {"id": "12E102125B4B45B4A861A1A8498233D5", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["(44, 6)"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["#一共筛选出了多少条数据\n", "\n", "uniqlo.shape"]}, {"cell_type": "code", "execution_count": 65, "metadata": {"id": "E07E2A910C504630BF76321A83AB1430", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["40"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["#去重后的门店数\n", "\n", "uniqlo['地址'].nunique()"]}, {"cell_type": "code", "execution_count": 66, "metadata": {"id": "44FC3F779C584553899F25E5411D73EA", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["array(['优衣库南京西路店', '优衣库浦东商场成山路店', '优衣库大拇指广场店', '优衣库宝山万达广场店', '优衣库光启城店',\n", "       '优衣库西郊百联店', '优衣库日月光店', '优衣库曹安公路店', '优衣库安亭嘉亭荟店', '优衣库太平洋不夜城店',\n", "       '优衣库浦东中房金谊广场', '优衣库南京东路第一百货店', '优衣库淮海路太平洋店', '优衣库百联南桥店',\n", "       '优衣库开元地中海店', '优衣库金山百联店', '优衣库我格广场店', '优衣库江桥万达店', '优衣库大悦城店',\n", "       '优衣库成山路巴黎春天店', '优衣库宝山巴黎春天店', '优衣库莲花国际广场店', '优衣库七宝凯德店', '优衣库金桥店',\n", "       '优衣库塘桥巴黎春天店', '优衣库永新城店', '优衣库百联中环店', '优衣库龙之梦店', '优衣库陕西路巴黎春天店',\n", "       '优衣库大宁国际店', '优衣库新梅联合广场店', '优衣库五角场店', '优衣库正大广场店', '优衣库港汇广场店',\n", "       '优衣库南京东路中联店', '优衣库莘庄仲盛店', '优衣库五角场又一城店', '优衣库上海长泰广场店', '优衣库正大乐城店',\n", "       '优衣库闵行浦江镇店'], dtype=object)"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["#40个门店分别是哪些\n", "\n", "uniqlo['地址'].unique()"]}, {"cell_type": "code", "execution_count": 67, "metadata": {"id": "5204007D9C6C4A398E353A951B7281DA", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>地址</th>\n", "      <th>剩余拜访次数</th>\n", "      <th>经度</th>\n", "      <th>纬度</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>优衣库七宝凯德店</td>\n", "      <td>2</td>\n", "      <td>121.348318</td>\n", "      <td>31.171320</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>优衣库上海长泰广场店</td>\n", "      <td>3</td>\n", "      <td>121.607512</td>\n", "      <td>31.210366</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>优衣库五角场又一城店</td>\n", "      <td>2</td>\n", "      <td>121.521831</td>\n", "      <td>31.307534</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>优衣库五角场店</td>\n", "      <td>1</td>\n", "      <td>121.520314</td>\n", "      <td>31.306632</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>优衣库光启城店</td>\n", "      <td>2</td>\n", "      <td>121.434446</td>\n", "      <td>31.190694</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           地址  剩余拜访次数          经度         纬度\n", "0    优衣库七宝凯德店       2  121.348318  31.171320\n", "1  优衣库上海长泰广场店       3  121.607512  31.210366\n", "2  优衣库五角场又一城店       2  121.521831  31.307534\n", "3     优衣库五角场店       1  121.520314  31.306632\n", "4     优衣库光启城店       2  121.434446  31.190694"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["#获取40个门店的名称及经纬度数据\n", "\n", "import numpy as np\n", "\n", "uniqlo = uniqlo.groupby('地址',as_index=False).agg({'剩余拜访次数':sum,'经度':np.mean,'纬度':np.mean})\n", "uniqlo.head()"]}, {"cell_type": "markdown", "metadata": {"id": "FF7283D0DBA34B78819B103A422B1E78", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2 门店可视化"]}, {"cell_type": "markdown", "metadata": {}, "source": ["需安装包：\n", "\n", "pip install pyecharts -i https://pypi.tuna.tsinghua.edu.cn/simple/\n", "\n", " pip install echarts-countries-pypkg -i https://pypi.tuna.tsinghua.edu.cn/simple/\n", " \n", " pip install echarts-china-provinces-pypkg -i https://pypi.tuna.tsinghua.edu.cn/simple/\n", " \n", " pip install echarts-china-cities-pypkg -i https://pypi.tuna.tsinghua.edu.cn/simple/\n", " \n", " pip install echarts-china-counties-pypkg -i https://pypi.tuna.tsinghua.edu.cn/simple/\n", " \n", " pip install echarts-china-misc-pypkg -i https://pypi.tuna.tsinghua.edu.cn/simple/\n", " \n", " pip install echarts-united-kingdom-pypkg -i https://pypi.tuna.tsinghua.edu.cn/simple/"]}, {"cell_type": "markdown", "metadata": {"id": "A6D6549E1DDB429B883D22BB153BD730", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 2.1 带涟漪效果的散点图"]}, {"cell_type": "code", "execution_count": 68, "metadata": {"id": "B163AE0C4706489B9FDD87DD5B2F2030", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min', '上海':'https://assets.pyecharts.org/assets/v5/maps/shanghai'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"********************************\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts', '上海'], function(echarts) {\n", "                var chart_******************************** = echarts.init(\n", "                    document.getElementById('********************************'), 'white', {renderer: 'canvas'});\n", "                var option_******************************** = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"effectScatter\",\n", "            \"coordinateSystem\": \"geo\",\n", "            \"showEffectOn\": \"render\",\n", "            \"rippleEffect\": {\n", "                \"show\": true,\n", "                \"brushType\": \"stroke\",\n", "                \"scale\": 2.5,\n", "                \"period\": 4\n", "            },\n", "            \"symbolSize\": 9,\n", "            \"data\": [\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e03\\u5b9d\\u51ef\\u5fb7\\u5e97\",\n", "                    \"value\": [\n", "                        121.348318,\n", "                        31.17132,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e0a\\u6d77\\u957f\\u6cf0\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.607512,\n", "                        31.210366,\n", "                        3\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e94\\u89d2\\u573a\\u53c8\\u4e00\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.521831,\n", "                        31.307534000000004,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e94\\u89d2\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.52031399999998,\n", "                        31.306632,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5149\\u542f\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.434446,\n", "                        31.190694,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5357\\u4eac\\u4e1c\\u8def\\u4e2d\\u8054\\u5e97\",\n", "                    \"value\": [\n", "                        121.489976,\n", "                        31.242919,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5357\\u4eac\\u4e1c\\u8def\\u7b2c\\u4e00\\u767e\\u8d27\\u5e97\",\n", "                    \"value\": [\n", "                        121.481317,\n", "                        31.240769,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5357\\u4eac\\u897f\\u8def\\u5e97\",\n", "                    \"value\": [\n", "                        121.465285,\n", "                        31.235748,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5858\\u6865\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.526688,\n", "                        31.214227,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5927\\u5b81\\u56fd\\u9645\\u5e97\",\n", "                    \"value\": [\n", "                        121.459213,\n", "                        31.281012,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5927\\u60a6\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.478725,\n", "                        31.248893,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5927\\u62c7\\u6307\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.566506,\n", "                        31.233415,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u592a\\u5e73\\u6d0b\\u4e0d\\u591c\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.46328,\n", "                        31.251464,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5b89\\u4ead\\u5609\\u4ead\\u835f\\u5e97\",\n", "                    \"value\": [\n", "                        121.16916100000002,\n", "                        31.293966,\n", "                        3\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5b9d\\u5c71\\u4e07\\u8fbe\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.452854,\n", "                        31.330385,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5b9d\\u5c71\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.417753,\n", "                        31.276786,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5f00\\u5143\\u5730\\u4e2d\\u6d77\\u5e97\",\n", "                    \"value\": [\n", "                        121.225779,\n", "                        31.043855,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6210\\u5c71\\u8def\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.542118,\n", "                        31.184962,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6211\\u683c\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.429781,\n", "                        31.244328000000003,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u65b0\\u6885\\u8054\\u5408\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.52288,\n", "                        31.235271,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u65e5\\u6708\\u5149\\u5e97\",\n", "                    \"value\": [\n", "                        121.47510800000002,\n", "                        31.21155,\n", "                        4\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u66f9\\u5b89\\u516c\\u8def\\u5e97\",\n", "                    \"value\": [\n", "                        121.370842,\n", "                        31.259728000000003,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6b63\\u5927\\u4e50\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.464675,\n", "                        31.192402,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6b63\\u5927\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.505676,\n", "                        31.242789,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6c38\\u65b0\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.44902,\n", "                        31.198699,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6c5f\\u6865\\u4e07\\u8fbe\\u5e97\",\n", "                    \"value\": [\n", "                        121.33073,\n", "                        31.247020000000003,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6d66\\u4e1c\\u4e2d\\u623f\\u91d1\\u8c0a\\u5e7f\\u573a\",\n", "                    \"value\": [\n", "                        121.519196,\n", "                        31.147252,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6d66\\u4e1c\\u5546\\u573a\\u6210\\u5c71\\u8def\\u5e97\",\n", "                    \"value\": [\n", "                        121.513909,\n", "                        31.177723,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6dee\\u6d77\\u8def\\u592a\\u5e73\\u6d0b\\u5e97\",\n", "                    \"value\": [\n", "                        121.467549,\n", "                        31.223576,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6e2f\\u6c47\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.44325,\n", "                        31.201186,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u767e\\u8054\\u4e2d\\u73af\\u5e97\",\n", "                    \"value\": [\n", "                        121.389893,\n", "                        31.250845,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u767e\\u8054\\u5357\\u6865\\u5e97\",\n", "                    \"value\": [\n", "                        121.490514,\n", "                        30.92182,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u8398\\u5e84\\u4ef2\\u76db\\u5e97\",\n", "                    \"value\": [\n", "                        121.39373700000002,\n", "                        31.112857,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u83b2\\u82b1\\u56fd\\u9645\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.409081,\n", "                        31.139156,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u897f\\u90ca\\u767e\\u8054\\u5e97\",\n", "                    \"value\": [\n", "                        121.377053,\n", "                        31.214539,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u91d1\\u5c71\\u767e\\u8054\\u5e97\",\n", "                    \"value\": [\n", "                        121.354894,\n", "                        30.737391,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u91d1\\u6865\\u5e97\",\n", "                    \"value\": [\n", "                        121.585552,\n", "                        31.261473,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u95f5\\u884c\\u6d66\\u6c5f\\u9547\\u5e97\",\n", "                    \"value\": [\n", "                        121.510835,\n", "                        31.100538,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u9655\\u897f\\u8def\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.447881,\n", "                        31.24915,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u9f99\\u4e4b\\u68a6\\u5e97\",\n", "                    \"value\": [\n", "                        121.484808,\n", "                        31.276663,\n", "                        1\n", "                    ]\n", "                }\n", "            ],\n", "            \"label\": {\n", "                \"show\": false,\n", "                \"margin\": 8\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"\"\n", "            ],\n", "            \"selected\": {\n", "                \"\": true\n", "            },\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"formatter\": function (params) {        return params.name + ' : ' + params.value[2];    },\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u4e0a\\u6d77\\u5e02\\u4f18\\u8863\\u5e93\\u95e8\\u5e97\\u53ef\\u89c6\\u5316\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ],\n", "    \"visualMap\": {\n", "        \"show\": true,\n", "        \"type\": \"piecewise\",\n", "        \"min\": 0,\n", "        \"max\": 100,\n", "        \"inRange\": {\n", "            \"color\": [\n", "                \"#50a3ba\",\n", "                \"#eac763\",\n", "                \"#d94e5d\"\n", "            ]\n", "        },\n", "        \"calculable\": true,\n", "        \"inverse\": false,\n", "        \"splitNumber\": 5,\n", "        \"hoverLink\": true,\n", "        \"orient\": \"vertical\",\n", "        \"padding\": 5,\n", "        \"showLabel\": true,\n", "        \"itemWidth\": 20,\n", "        \"itemHeight\": 14,\n", "        \"borderWidth\": 0,\n", "        \"pieces\": [\n", "            {\n", "                \"min\": 0,\n", "                \"max\": 1,\n", "                \"label\": \"1\",\n", "                \"color\": \"#50A3BA\"\n", "            },\n", "            {\n", "                \"min\": 1,\n", "                \"max\": 2,\n", "                \"label\": \"2\",\n", "                \"color\": \"#DD675E\"\n", "            },\n", "            {\n", "                \"min\": 2,\n", "                \"max\": 3,\n", "                \"label\": \"3\",\n", "                \"color\": \"#E2C568\"\n", "            },\n", "            {\n", "                \"min\": 3,\n", "                \"label\": \"4\",\n", "                \"color\": \"#3700A4\"\n", "            }\n", "        ]\n", "    },\n", "    \"geo\": {\n", "        \"map\": \"\\u4e0a\\u6d77\",\n", "        \"roam\": true,\n", "        \"aspectScale\": 0.75,\n", "        \"nameProperty\": \"name\",\n", "        \"selectedMode\": false,\n", "        \"emphasis\": {}\n", "    }\n", "};\n", "                chart_********************************.setOption(option_********************************);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x228ca2a0100>"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["#使用pyecharts中的Geo类绘制散点图\n", "\n", "from pyecharts.charts import Geo\n", "from pyecharts import options as opts\n", "from pyecharts.globals import GeoType\n", "\n", "city = '上海'\n", "\n", "#实例化一个Geo类\n", "geo = Geo()\n", "#以上海市地图为底景\n", "geo.add_schema(maptype=city)\n", "# #添加地点坐标至坐标库中\n", "for i in range(40):\n", "    geo.add_coordinate(uniqlo.iloc[i]['地址'],uniqlo.iloc[i]['经度'],uniqlo.iloc[i]['纬度'])\n", "\n", "data_pair = [(uniqlo.iloc[i]['地址'],int(uniqlo.iloc[i]['剩余拜访次数'])) for i in range(40)]\n", "    \n", "# 将数据添加到地图上\n", "geo.add('',data_pair,type_=GeoType.EFFECT_SCATTER, symbol_size=9)\n", "# 设置样式\n", "geo.set_series_opts(label_opts=opts.LabelOpts(is_show=False))\n", "#自定义分级\n", "pieces = [\n", "        {'min': 0, 'max': 1, 'label': '1', 'color': '#50A3BA'},\n", "        {'min': 1, 'max': 2, 'label': '2', 'color': '#DD675E'},\n", "        {'min': 2, 'max': 3, 'label': '3', 'color': '#E2C568'},\n", "        {'min': 3, 'label': '4', 'color': '#3700A4'}\n", "]\n", "#是否自定义分段\n", "geo.set_global_opts(\n", "        visualmap_opts=opts.VisualMapOpts(is_piecewise=True, pieces=pieces),\n", "        title_opts=opts.TitleOpts(title='上海市优衣库门店可视化'),\n", "    )\n", "    \n", "geo.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "118EB660E6DE48D98F9724BEC8E7B6FE", "jupyter": {}, "mdEditEnable": false, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 2.2 热力图"]}, {"cell_type": "markdown", "metadata": {"id": "2661FFB3939543EF957CD127820DA282", "jupyter": {}, "mdEditEnable": false, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["绘制热力图只需将GeoType参数为设置HEATMAP即可，具体展示如下："]}, {"cell_type": "code", "execution_count": 69, "metadata": {"id": "D89CED04498F4D7988319447F9F7A72B", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min', '上海':'https://assets.pyecharts.org/assets/v5/maps/shanghai'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"8c01bd8603524aa5a0ad6df2339f25da\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts', '上海'], function(echarts) {\n", "                var chart_8c01bd8603524aa5a0ad6df2339f25da = echarts.init(\n", "                    document.getElementById('8c01bd8603524aa5a0ad6df2339f25da'), 'white', {renderer: 'canvas'});\n", "                var option_8c01bd8603524aa5a0ad6df2339f25da = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"heatmap\",\n", "            \"coordinateSystem\": \"geo\",\n", "            \"data\": [\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e03\\u5b9d\\u51ef\\u5fb7\\u5e97\",\n", "                    \"value\": [\n", "                        121.348318,\n", "                        31.17132,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e0a\\u6d77\\u957f\\u6cf0\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.607512,\n", "                        31.210366,\n", "                        3\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e94\\u89d2\\u573a\\u53c8\\u4e00\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.521831,\n", "                        31.307534000000004,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e94\\u89d2\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.52031399999998,\n", "                        31.306632,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5149\\u542f\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.434446,\n", "                        31.190694,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5357\\u4eac\\u4e1c\\u8def\\u4e2d\\u8054\\u5e97\",\n", "                    \"value\": [\n", "                        121.489976,\n", "                        31.242919,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5357\\u4eac\\u4e1c\\u8def\\u7b2c\\u4e00\\u767e\\u8d27\\u5e97\",\n", "                    \"value\": [\n", "                        121.481317,\n", "                        31.240769,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5357\\u4eac\\u897f\\u8def\\u5e97\",\n", "                    \"value\": [\n", "                        121.465285,\n", "                        31.235748,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5858\\u6865\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.526688,\n", "                        31.214227,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5927\\u5b81\\u56fd\\u9645\\u5e97\",\n", "                    \"value\": [\n", "                        121.459213,\n", "                        31.281012,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5927\\u60a6\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.478725,\n", "                        31.248893,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5927\\u62c7\\u6307\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.566506,\n", "                        31.233415,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u592a\\u5e73\\u6d0b\\u4e0d\\u591c\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.46328,\n", "                        31.251464,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5b89\\u4ead\\u5609\\u4ead\\u835f\\u5e97\",\n", "                    \"value\": [\n", "                        121.16916100000002,\n", "                        31.293966,\n", "                        3\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5b9d\\u5c71\\u4e07\\u8fbe\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.452854,\n", "                        31.330385,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5b9d\\u5c71\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.417753,\n", "                        31.276786,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5f00\\u5143\\u5730\\u4e2d\\u6d77\\u5e97\",\n", "                    \"value\": [\n", "                        121.225779,\n", "                        31.043855,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6210\\u5c71\\u8def\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.542118,\n", "                        31.184962,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6211\\u683c\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.429781,\n", "                        31.244328000000003,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u65b0\\u6885\\u8054\\u5408\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.52288,\n", "                        31.235271,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u65e5\\u6708\\u5149\\u5e97\",\n", "                    \"value\": [\n", "                        121.47510800000002,\n", "                        31.21155,\n", "                        4\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u66f9\\u5b89\\u516c\\u8def\\u5e97\",\n", "                    \"value\": [\n", "                        121.370842,\n", "                        31.259728000000003,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6b63\\u5927\\u4e50\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.464675,\n", "                        31.192402,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6b63\\u5927\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.505676,\n", "                        31.242789,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6c38\\u65b0\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.44902,\n", "                        31.198699,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6c5f\\u6865\\u4e07\\u8fbe\\u5e97\",\n", "                    \"value\": [\n", "                        121.33073,\n", "                        31.247020000000003,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6d66\\u4e1c\\u4e2d\\u623f\\u91d1\\u8c0a\\u5e7f\\u573a\",\n", "                    \"value\": [\n", "                        121.519196,\n", "                        31.147252,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6d66\\u4e1c\\u5546\\u573a\\u6210\\u5c71\\u8def\\u5e97\",\n", "                    \"value\": [\n", "                        121.513909,\n", "                        31.177723,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6dee\\u6d77\\u8def\\u592a\\u5e73\\u6d0b\\u5e97\",\n", "                    \"value\": [\n", "                        121.467549,\n", "                        31.223576,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6e2f\\u6c47\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.44325,\n", "                        31.201186,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u767e\\u8054\\u4e2d\\u73af\\u5e97\",\n", "                    \"value\": [\n", "                        121.389893,\n", "                        31.250845,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u767e\\u8054\\u5357\\u6865\\u5e97\",\n", "                    \"value\": [\n", "                        121.490514,\n", "                        30.92182,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u8398\\u5e84\\u4ef2\\u76db\\u5e97\",\n", "                    \"value\": [\n", "                        121.39373700000002,\n", "                        31.112857,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u83b2\\u82b1\\u56fd\\u9645\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.409081,\n", "                        31.139156,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u897f\\u90ca\\u767e\\u8054\\u5e97\",\n", "                    \"value\": [\n", "                        121.377053,\n", "                        31.214539,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u91d1\\u5c71\\u767e\\u8054\\u5e97\",\n", "                    \"value\": [\n", "                        121.354894,\n", "                        30.737391,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u91d1\\u6865\\u5e97\",\n", "                    \"value\": [\n", "                        121.585552,\n", "                        31.261473,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u95f5\\u884c\\u6d66\\u6c5f\\u9547\\u5e97\",\n", "                    \"value\": [\n", "                        121.510835,\n", "                        31.100538,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u9655\\u897f\\u8def\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.447881,\n", "                        31.24915,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u9f99\\u4e4b\\u68a6\\u5e97\",\n", "                    \"value\": [\n", "                        121.484808,\n", "                        31.276663,\n", "                        1\n", "                    ]\n", "                }\n", "            ],\n", "            \"pointSize\": 20,\n", "            \"blurSize\": 20,\n", "            \"label\": {\n", "                \"show\": false,\n", "                \"margin\": 8\n", "            },\n", "            \"rippleEffect\": {\n", "                \"show\": true,\n", "                \"brushType\": \"stroke\",\n", "                \"scale\": 2.5,\n", "                \"period\": 4\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"\"\n", "            ],\n", "            \"selected\": {\n", "                \"\": true\n", "            },\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"formatter\": function (params) {        return params.name + ' : ' + params.value[2];    },\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u4e0a\\u6d77\\u5e02\\u4f18\\u8863\\u5e93\\u95e8\\u5e97\\u53ef\\u89c6\\u5316\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ],\n", "    \"visualMap\": {\n", "        \"show\": true,\n", "        \"type\": \"piecewise\",\n", "        \"min\": 0,\n", "        \"max\": 100,\n", "        \"inRange\": {\n", "            \"color\": [\n", "                \"#50a3ba\",\n", "                \"#eac763\",\n", "                \"#d94e5d\"\n", "            ]\n", "        },\n", "        \"calculable\": true,\n", "        \"inverse\": false,\n", "        \"splitNumber\": 5,\n", "        \"hoverLink\": true,\n", "        \"orient\": \"vertical\",\n", "        \"padding\": 5,\n", "        \"showLabel\": true,\n", "        \"itemWidth\": 20,\n", "        \"itemHeight\": 14,\n", "        \"borderWidth\": 0,\n", "        \"pieces\": [\n", "            {\n", "                \"min\": 0,\n", "                \"max\": 1,\n", "                \"label\": \"1\",\n", "                \"color\": \"#50A3BA\"\n", "            },\n", "            {\n", "                \"min\": 1,\n", "                \"max\": 2,\n", "                \"label\": \"2\",\n", "                \"color\": \"#E2C568\"\n", "            },\n", "            {\n", "                \"min\": 2,\n", "                \"max\": 3,\n", "                \"label\": \"3\",\n", "                \"color\": \"#DD675E\"\n", "            },\n", "            {\n", "                \"min\": 3,\n", "                \"label\": \"4\",\n", "                \"color\": \"#DD0200\"\n", "            }\n", "        ]\n", "    },\n", "    \"geo\": {\n", "        \"map\": \"\\u4e0a\\u6d77\",\n", "        \"roam\": true,\n", "        \"aspectScale\": 0.75,\n", "        \"nameProperty\": \"name\",\n", "        \"selectedMode\": false,\n", "        \"emphasis\": {}\n", "    }\n", "};\n", "                chart_8c01bd8603524aa5a0ad6df2339f25da.setOption(option_8c01bd8603524aa5a0ad6df2339f25da);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x228ca3074f0>"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["city = '上海'\n", "\n", "#实例化一个Geo类\n", "geo2 = Geo()\n", "#以上海市地图为底景\n", "geo2.add_schema(maptype=city)\n", "# #添加地点坐标至坐标库中\n", "for i in range(40):\n", "    geo2.add_coordinate(uniqlo.iloc[i]['地址'],uniqlo.iloc[i]['经度'],uniqlo.iloc[i]['纬度'])\n", "\n", "data_pair = [(uniqlo.iloc[i]['地址'],int(uniqlo.iloc[i]['剩余拜访次数'])) for i in range(40)]\n", "    \n", "# 将数据添加到地图上\n", "geo2.add('',data_pair,type_=GeoType.HEATMAP, symbol_size=5)\n", "# 设置样式\n", "geo2.set_series_opts(label_opts=opts.LabelOpts(is_show=False))\n", "\n", "#自定义分级\n", "pieces = [\n", "        {'min': 0, 'max': 1, 'label': '1', 'color': '#50A3BA'},\n", "        {'min': 1, 'max': 2, 'label': '2', 'color': '#E2C568'},\n", "        {'min': 2, 'max': 3, 'label': '3', 'color': '#DD675E'},\n", "        {'min': 3, 'label': '4', 'color': '#DD0200'}\n", "]\n", "#是否自定义分段\n", "geo2.set_global_opts(\n", "        visualmap_opts=opts.VisualMapOpts(is_piecewise=True, pieces=pieces),\n", "        title_opts=opts.TitleOpts(title='上海市优衣库门店可视化'),\n", "    )\n", "    \n", "geo2.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "663D0BD2C97342749B9FA34A5AEC1275", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["# 三、拜访数据分组"]}, {"cell_type": "markdown", "metadata": {"id": "DF007530737B4EC0A7F8CE7D72BDD0FE", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["由于我们事先不知道将要把数据点分为哪些组，所以这是一个典型的无监督学习问题，以下我们将采用最常用的KMeans算法对拜访数据进行分组。"]}, {"cell_type": "markdown", "metadata": {"id": "2CB8913DCF834C49B43E2FDA5539AE0C", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 1 数据获取"]}, {"cell_type": "code", "execution_count": 70, "metadata": {"id": "F201833C80DB4C02851B13664B9104F3", "jupyter": {}, "mdEditEnable": false, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>地址</th>\n", "      <th>经度</th>\n", "      <th>纬度</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>优衣库南京西路店</td>\n", "      <td>121.465285</td>\n", "      <td>31.235748</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>优衣库浦东商场成山路店</td>\n", "      <td>121.513909</td>\n", "      <td>31.177723</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>优衣库大拇指广场店</td>\n", "      <td>121.566506</td>\n", "      <td>31.233415</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>优衣库宝山万达广场店</td>\n", "      <td>121.452854</td>\n", "      <td>31.330385</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>优衣库光启城店</td>\n", "      <td>121.434446</td>\n", "      <td>31.190694</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            地址          经度         纬度\n", "0     优衣库南京西路店  121.465285  31.235748\n", "1  优衣库浦东商场成山路店  121.513909  31.177723\n", "2    优衣库大拇指广场店  121.566506  31.233415\n", "3   优衣库宝山万达广场店  121.452854  31.330385\n", "4      优衣库光启城店  121.434446  31.190694"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["#拜访地点数据获取\n", "\n", "visit = data2[['地址','经度','纬度']]\n", "visit.head()"]}, {"cell_type": "markdown", "metadata": {"id": "B5532458960249E0824FF9C055B28AA5", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2 拜访数据分组"]}, {"cell_type": "markdown", "metadata": {"id": "8C6436FCDADE4711B63906949E984A25", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 2.1 KMean<PERSON>分组"]}, {"cell_type": "markdown", "metadata": {"id": "8A12C4ED6C794460912219AFA84BA032", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["这里简要介绍一下KMeans算法的步骤：\n", "\n", "1. 随机初始化k个聚类中心；\n", "\n", "1. 将n个待分类数据分配到距离它最近的聚类中心；\n", "\n", "1. 根据步骤2结果，重新计算新的聚类中心；\n", "\n", "1. 若达到最大迭代步数或两次迭代差小于设定的阈值则算法结束，否则重复步骤2。"]}, {"cell_type": "code", "execution_count": 71, "metadata": {"id": "DA1D444BD8124BE3A4D0E3B8AC82477B", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["array([1, 0, 0, 4, 1, 3, 1, 3, 7, 1, 0, 1, 1, 5, 6, 2, 1, 3, 1, 0, 1, 3,\n", "       3, 0, 0, 1, 3, 4, 1, 4, 0, 4, 1, 1, 1, 3, 4, 0, 1, 0, 1, 0, 1, 1,\n", "       4, 0, 1, 4, 1, 1, 1, 1, 1, 1, 1, 4, 2, 4, 1, 4, 4, 4, 1, 1, 1, 4,\n", "       1, 1, 4, 1, 1, 1, 1, 1, 3, 3, 1, 4, 1, 1])"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["#使用KMeans算法将拜访数据分为8组\n", "\n", "from sklearn.cluster import KMeans\n", "\n", "kmeans = KMeans(n_clusters=8,\n", "init='k-means++',)\n", "\n", "X = visit[['经度','纬度']]\n", "y = [0,1,2,3,4,5,6,7]\n", "kmeans.fit(X,y)\n", "y_pred = kmeans.predict(X)\n", "\n", "y_pred"]}, {"cell_type": "markdown", "metadata": {"id": "2B9561E0CFBD42E99955DF09EC11084C", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 2.2 输出分组结果"]}, {"cell_type": "markdown", "metadata": {"id": "DA1644C2808D4CD89988BF88650EAD1F", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["#### 2.2.1 分组结果"]}, {"cell_type": "code", "execution_count": 72, "metadata": {"id": "BBA3B30BB72049D6810EE006899CCF8E", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_27036\\1956377192.py:4: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  visit_result['分组'] = y_pred\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>地址</th>\n", "      <th>经度</th>\n", "      <th>纬度</th>\n", "      <th>分组</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>优衣库南京西路店</td>\n", "      <td>121.465285</td>\n", "      <td>31.235748</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>优衣库浦东商场成山路店</td>\n", "      <td>121.513909</td>\n", "      <td>31.177723</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>优衣库大拇指广场店</td>\n", "      <td>121.566506</td>\n", "      <td>31.233415</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>优衣库宝山万达广场店</td>\n", "      <td>121.452854</td>\n", "      <td>31.330385</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>优衣库光启城店</td>\n", "      <td>121.434446</td>\n", "      <td>31.190694</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            地址          经度         纬度  分组\n", "0     优衣库南京西路店  121.465285  31.235748   1\n", "1  优衣库浦东商场成山路店  121.513909  31.177723   0\n", "2    优衣库大拇指广场店  121.566506  31.233415   0\n", "3   优衣库宝山万达广场店  121.452854  31.330385   4\n", "4      优衣库光启城店  121.434446  31.190694   1"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["#将各点分组结果添加到数据中\n", "\n", "visit_result = visit\n", "visit_result['分组'] = y_pred\n", "\n", "visit_result.head()"]}, {"cell_type": "markdown", "metadata": {"id": "7E8DDCF5326A4F6D8F1DB740C6E0DEDF", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["#### 2.2.2 中心点坐标"]}, {"cell_type": "code", "execution_count": 73, "metadata": {"id": "AC0B21066C344F3687EABB660BFC0C5A", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["array([[121.54159609,  31.19906845],\n", "       [121.4622371 ,  31.2227638 ],\n", "       [121.2775555 ,  30.7676925 ],\n", "       [121.37352322,  31.18662689],\n", "       [121.49704807,  31.29351427],\n", "       [121.490514  ,  30.92182   ],\n", "       [121.225779  ,  31.043855  ],\n", "       [121.169161  ,  31.293966  ]])"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["#中心点坐标\n", "\n", "center = kmeans.cluster_centers_\n", "center"]}, {"cell_type": "code", "execution_count": 74, "metadata": {"scrolled": true}, "outputs": [], "source": ["#获取中心点详细地址\n", "import time\n", "from geopy.geocoders import Nominatim\n", "geolocator = Nominatim(user_agent='Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Safari/537.36 SE 2.X MetaSr 1.0',\n", "                      timeout=5)"]}, {"cell_type": "code", "execution_count": 75, "metadata": {"id": "FD5B315FD5444FDC8C29EDD1C408136E", "jupyter": {}, "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["# 注意：建议访问速度不要太快，否则报timeout错误\n", "# for x,y in center:\n", "#     print(x,y)\n", "#     location = geolocator.reverse((y,x))\n", "#     print(location.address)\n", "#     time.sleep(1.5)"]}, {"cell_type": "markdown", "metadata": {"id": "F89EC8EDAD5A4BE78B70F79DD08021A9", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 3 K值调整"]}, {"cell_type": "markdown", "metadata": {"id": "9C76F80752BF4AAD83F249C2DE1F0EC2", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 3.1 The Elbow Method"]}, {"cell_type": "markdown", "metadata": {"id": "A915E429CE104E22992DD7C551BD22F7", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["由于这里没有事先对数据分成几组作出规定，因此我们有必要对K值进行调整，来找到一个能够使我们比较满意的分组结果；事实上，KMeans算法中K值的选取一直是业界比较关心的一个问题，这里我们采用最常用的 ***The Elbow Method(“肘方法”)*** 来选取一个恰当的K值。"]}, {"cell_type": "code", "execution_count": 76, "metadata": {"id": "EDB7317693E740218F5F382113C6E96F", "jupyter": {}, "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["[1.1199952338001689,\n", " 0.558270281163914,\n", " 0.3855777565436488,\n", " 0.30733776648440747,\n", " 0.257769677168049,\n", " 0.2082964850213846,\n", " 0.16012632717998687,\n", " 0.12601703009602078,\n", " 0.10061820700843141,\n", " 0.08507746547618386,\n", " 0.07031261552800207,\n", " 0.055099457469065846,\n", " 0.04722299535086234,\n", " 0.04170369855419209,\n", " 0.033916789825823454,\n", " 0.028029108223692173,\n", " 0.025511937873070177,\n", " 0.022317368448707943,\n", " 0.02019623645458845,\n", " 0.018476438160232884]"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["#对不同的K值分别计算误差和\n", "\n", "from sklearn.cluster import KMeans\n", "\n", "inertia = []\n", "for i in range(1,21):\n", "    \n", "    kmeans = KMeans(n_clusters=i,\n", "    init='k-means++')\n", "    \n", "    X = visit[['经度','纬度']]\n", "    kmeans.fit(X)\n", "    inertia.append(kmeans.inertia_)\n", "    \n", "inertia"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"id": "AA16674AC4B6468A81397FC93528E495", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#绘制误差和随K值的变化曲线图\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "plt.style.use('fivethirtyeight')\n", "plt.figure(figsize=(12,6))\n", "plt.plot(range(1,11),inertia[0:10])\n", "\n", "plt.title('The Elbow Method')\n", "plt.xlabel('K')\n", "plt.ylabel('inertia')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "37648A1829F14B838AA367946FA20E29", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["这里可以看到，当K<=10时，K=3是一个比较好的分组数目；为了解误差后续的变化趋势，我们看下K<=20时的结果。"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"id": "92FF332A0A864B5F8C709F9D40612845", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#绘制误差和随K值的变化曲线图\n", "\n", "plt.figure(figsize=(12,6))\n", "plt.plot(range(1,21),inertia)\n", "\n", "plt.title('The Elbow Method')\n", "plt.xlabel('K')\n", "plt.ylabel('inertia')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "C69E246A6F7F4B8D84F994C3B5CABF36", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["可以看到，K<=20时，拐点出现在K=9附近，但此处误差下降的速度并不明显，下面我们按照K=3来进行分组。"]}, {"cell_type": "markdown", "metadata": {"id": "68B52E05D23742BD893FBBB5CA483FE1", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 3.2 分组结果可视化"]}, {"cell_type": "code", "execution_count": 77, "metadata": {"id": "DF569357E2A44D7984B25D0076E47E20", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["array([0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 2, 1, 2, 0, 1, 0, 0, 0, 1,\n", "       1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0])"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["#使用KMeans算法将拜访数据分为3组\n", "\n", "from sklearn.cluster import KMeans\n", "\n", "kmeans = KMeans(n_clusters=3,\n", "init='k-means++',)\n", "\n", "X = visit[['经度','纬度']]\n", "kmeans.fit(X)\n", "y_pred = kmeans.predict(X)\n", "\n", "y_pred"]}, {"cell_type": "code", "execution_count": 78, "metadata": {"id": "A8644F8C5ECE4D3C845AB91F3AEA2DFB", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min', '上海':'https://assets.pyecharts.org/assets/v5/maps/shanghai'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"5449904cfd1e42fc9ebf829e27828592\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts', '上海'], function(echarts) {\n", "                var chart_5449904cfd1e42fc9ebf829e27828592 = echarts.init(\n", "                    document.getElementById('5449904cfd1e42fc9ebf829e27828592'), 'white', {renderer: 'canvas'});\n", "                var option_5449904cfd1e42fc9ebf829e27828592 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"effectScatter\",\n", "            \"coordinateSystem\": \"geo\",\n", "            \"showEffectOn\": \"render\",\n", "            \"rippleEffect\": {\n", "                \"show\": true,\n", "                \"brushType\": \"stroke\",\n", "                \"scale\": 2.5,\n", "                \"period\": 4\n", "            },\n", "            \"symbolSize\": 9,\n", "            \"data\": [\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5357\\u4eac\\u897f\\u8def\\u5e97\",\n", "                    \"value\": [\n", "                        121.465285,\n", "                        31.235748,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6d66\\u4e1c\\u5546\\u573a\\u6210\\u5c71\\u8def\\u5e97\",\n", "                    \"value\": [\n", "                        121.513909,\n", "                        31.177723,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5927\\u62c7\\u6307\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.566506,\n", "                        31.233415,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5b9d\\u5c71\\u4e07\\u8fbe\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.452854,\n", "                        31.330385,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5149\\u542f\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.434446,\n", "                        31.190694,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u897f\\u90ca\\u767e\\u8054\\u5e97\",\n", "                    \"value\": [\n", "                        121.377053,\n", "                        31.214539,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u65e5\\u6708\\u5149\\u5e97\",\n", "                    \"value\": [\n", "                        121.475108,\n", "                        31.21155,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u66f9\\u5b89\\u516c\\u8def\\u5e97\",\n", "                    \"value\": [\n", "                        121.370842,\n", "                        31.259728000000003,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5b89\\u4ead\\u5609\\u4ead\\u835f\\u5e97\",\n", "                    \"value\": [\n", "                        121.16916100000002,\n", "                        31.293966,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u592a\\u5e73\\u6d0b\\u4e0d\\u591c\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.46328,\n", "                        31.251464,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6d66\\u4e1c\\u4e2d\\u623f\\u91d1\\u8c0a\\u5e7f\\u573a\",\n", "                    \"value\": [\n", "                        121.519196,\n", "                        31.147252,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5357\\u4eac\\u4e1c\\u8def\\u7b2c\\u4e00\\u767e\\u8d27\\u5e97\",\n", "                    \"value\": [\n", "                        121.481317,\n", "                        31.240769,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6dee\\u6d77\\u8def\\u592a\\u5e73\\u6d0b\\u5e97\",\n", "                    \"value\": [\n", "                        121.467549,\n", "                        31.223576,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u767e\\u8054\\u5357\\u6865\\u5e97\",\n", "                    \"value\": [\n", "                        121.490514,\n", "                        30.92182,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5f00\\u5143\\u5730\\u4e2d\\u6d77\\u5e97\",\n", "                    \"value\": [\n", "                        121.225779,\n", "                        31.043855,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u91d1\\u5c71\\u767e\\u8054\\u5e97\",\n", "                    \"value\": [\n", "                        121.354894,\n", "                        30.737391,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6211\\u683c\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.429781,\n", "                        31.244328000000003,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6c5f\\u6865\\u4e07\\u8fbe\\u5e97\",\n", "                    \"value\": [\n", "                        121.33073,\n", "                        31.247020000000003,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5927\\u60a6\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.478725,\n", "                        31.248893,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6210\\u5c71\\u8def\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.542118,\n", "                        31.184962,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5b9d\\u5c71\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.417753,\n", "                        31.276786,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u83b2\\u82b1\\u56fd\\u9645\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.409081,\n", "                        31.139156,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e03\\u5b9d\\u51ef\\u5fb7\\u5e97\",\n", "                    \"value\": [\n", "                        121.348318,\n", "                        31.17132,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u91d1\\u6865\\u5e97\",\n", "                    \"value\": [\n", "                        121.585552,\n", "                        31.261473,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5858\\u6865\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.526688,\n", "                        31.214227,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6c38\\u65b0\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.44902,\n", "                        31.198699,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u767e\\u8054\\u4e2d\\u73af\\u5e97\",\n", "                    \"value\": [\n", "                        121.389893,\n", "                        31.250845,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u9f99\\u4e4b\\u68a6\\u5e97\",\n", "                    \"value\": [\n", "                        121.484808,\n", "                        31.276663,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u9655\\u897f\\u8def\\u5df4\\u9ece\\u6625\\u5929\\u5e97\",\n", "                    \"value\": [\n", "                        121.447881,\n", "                        31.24915,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5927\\u5b81\\u56fd\\u9645\\u5e97\",\n", "                    \"value\": [\n", "                        121.459213,\n", "                        31.281012,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u65b0\\u6885\\u8054\\u5408\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.52288,\n", "                        31.235271,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e94\\u89d2\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.52031399999998,\n", "                        31.306632,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6b63\\u5927\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.505676,\n", "                        31.242789,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6e2f\\u6c47\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.44325,\n", "                        31.201186,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u5357\\u4eac\\u4e1c\\u8def\\u4e2d\\u8054\\u5e97\",\n", "                    \"value\": [\n", "                        121.489976,\n", "                        31.242919,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u8398\\u5e84\\u4ef2\\u76db\\u5e97\",\n", "                    \"value\": [\n", "                        121.39373700000002,\n", "                        31.112857,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e94\\u89d2\\u573a\\u53c8\\u4e00\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.521831,\n", "                        31.307534000000004,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e0a\\u6d77\\u957f\\u6cf0\\u5e7f\\u573a\\u5e97\",\n", "                    \"value\": [\n", "                        121.607512,\n", "                        31.210366,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u6b63\\u5927\\u4e50\\u57ce\\u5e97\",\n", "                    \"value\": [\n", "                        121.464675,\n", "                        31.192402,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u95f5\\u884c\\u6d66\\u6c5f\\u9547\\u5e97\",\n", "                    \"value\": [\n", "                        121.510835,\n", "                        31.100538,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c71\\u4e1c\\u4e2d\\u8def145\\u53f7\",\n", "                    \"value\": [\n", "                        121.491114,\n", "                        31.239373,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e1c\\u65b9\\u8def1630\\u53f7\",\n", "                    \"value\": [\n", "                        121.531632,\n", "                        31.213081,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5236\\u9020\\u5c40\\u8def639\\u53f7\",\n", "                    \"value\": [\n", "                        121.492472,\n", "                        31.208031,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u745e\\u91d1\\u4e8c\\u8def197\\u53f7\",\n", "                    \"value\": [\n", "                        121.472605,\n", "                        31.217703000000004,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u63a7\\u6c5f\\u8def1665\\u53f7\",\n", "                    \"value\": [\n", "                        121.523675,\n", "                        31.280067,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e1c\\u65b9\\u8def1678\\u53f7\",\n", "                    \"value\": [\n", "                        121.530729,\n", "                        31.211445,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6dee\\u6d77\\u897f\\u8def241\\u53f7\",\n", "                    \"value\": [\n", "                        121.43348600000002,\n", "                        31.204657,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6b66\\u8fdb\\u8def85\\u53f7\",\n", "                    \"value\": [\n", "                        121.49498,\n", "                        31.259005,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5b9c\\u5c71\\u8def600\\u53f7\",\n", "                    \"value\": [\n", "                        121.430603,\n", "                        31.184901,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u533b\\u5b66\\u9662\\u8def138\\u53f7\",\n", "                    \"value\": [\n", "                        121.458553,\n", "                        31.204393,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e4c\\u9c81\\u6728\\u9f50\\u4e2d\\u8def12\\u53f7\",\n", "                    \"value\": [\n", "                        121.45042,\n", "                        31.222696000000003,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u67ab\\u6797\\u8def138\\u53f7\",\n", "                    \"value\": [\n", "                        121.461124,\n", "                        31.20416,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c7e\\u9633\\u8def83\\u53f7\",\n", "                    \"value\": [\n", "                        121.459801,\n", "                        31.216084,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u65b9\\u659c\\u8def419\\u53f7\",\n", "                    \"value\": [\n", "                        121.489711,\n", "                        31.220091,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u96f6\\u9675\\u8def399\\u53f7\",\n", "                    \"value\": [\n", "                        121.459427,\n", "                        31.196451,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5ef6\\u957f\\u4e2d\\u8def301\\u53f7\",\n", "                    \"value\": [\n", "                        121.460247,\n", "                        31.277936,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u65b0\\u6751\\u8def389\\u53f7\",\n", "                    \"value\": [\n", "                        121.200217,\n", "                        30.797994,\n", "                        2\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u653f\\u6c11\\u8def507\\u53f7\",\n", "                    \"value\": [\n", "                        121.50893899999998,\n", "                        31.306621000000003,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u51e4\\u9633\\u8def415\\u53f7\",\n", "                    \"value\": [\n", "                        121.47413799999998,\n", "                        31.23909,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u957f\\u6d77\\u8def174\\u53f7\",\n", "                    \"value\": [\n", "                        121.53438,\n", "                        31.314777000000003,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u957f\\u6d77\\u8def225\\u53f7\",\n", "                    \"value\": [\n", "                        121.535568,\n", "                        31.315785,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u7518\\u6cb3\\u8def110\\u53f7\",\n", "                    \"value\": [\n", "                        121.494791,\n", "                        31.294773,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u9752\\u6d77\\u8def44\\u53f7\",\n", "                    \"value\": [\n", "                        121.47057,\n", "                        31.236615000000004,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5b9b\\u5e73\\u5357\\u8def725\\u53f7\",\n", "                    \"value\": [\n", "                        121.456107,\n", "                        31.193764,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u666e\\u5b89\\u8def185\\u53f7\",\n", "                    \"value\": [\n", "                        121.484847,\n", "                        31.228823,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u82b7\\u6c5f\\u4e2d\\u8def274\\u53f7\",\n", "                    \"value\": [\n", "                        121.477667,\n", "                        31.265602,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5ef6\\u5b89\\u897f\\u8def221\\u53f7\",\n", "                    \"value\": [\n", "                        121.448122,\n", "                        31.225464,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u957f\\u4e50\\u8def536\\u53f7\",\n", "                    \"value\": [\n", "                        121.46268700000002,\n", "                        31.226189,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c34\\u7535\\u8def56\\u53f7\",\n", "                    \"value\": [\n", "                        121.477515,\n", "                        31.279301,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5317\\u4eac\\u897f\\u8def1400\\u5f0424\\u53f7\",\n", "                    \"value\": [\n", "                        121.454787,\n", "                        31.235549,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6b66\\u5937\\u8def196\\u53f7\",\n", "                    \"value\": [\n", "                        121.43331,\n", "                        31.219685,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5eb7\\u5b9a\\u8def380\\u53f73\\u697c\",\n", "                    \"value\": [\n", "                        121.455435,\n", "                        31.240979,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u8861\\u5c71\\u8def910\\u53f7\",\n", "                    \"value\": [\n", "                        121.445945,\n", "                        31.201999,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u65e5\\u6708\\u5149\\u5e97\",\n", "                    \"value\": [\n", "                        121.475108,\n", "                        31.21155,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u4e03\\u5b9d\\u51ef\\u5fb7\\u5e97\",\n", "                    \"value\": [\n", "                        121.348318,\n", "                        31.17132,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u8398\\u5e84\\u4ef2\\u76db\\u5e97\",\n", "                    \"value\": [\n", "                        121.39373700000002,\n", "                        31.112857,\n", "                        1\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f18\\u8863\\u5e93\\u65e5\\u6708\\u5149\\u5e97\",\n", "                    \"value\": [\n", "                        121.475108,\n", "                        31.21155,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u653f\\u6c11\\u8def507\\u53f7\",\n", "                    \"value\": [\n", "                        121.50893899999998,\n", "                        31.306621000000003,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5eb7\\u5b9a\\u8def380\\u53f73\\u697c\",\n", "                    \"value\": [\n", "                        121.455435,\n", "                        31.240979,\n", "                        0\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u666e\\u5b89\\u8def185\\u53f7\",\n", "                    \"value\": [\n", "                        121.484847,\n", "                        31.228823,\n", "                        0\n", "                    ]\n", "                }\n", "            ],\n", "            \"label\": {\n", "                \"show\": false,\n", "                \"margin\": 8\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"\"\n", "            ],\n", "            \"selected\": {\n", "                \"\": true\n", "            },\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"formatter\": function (params) {        return params.name + ' : ' + params.value[2];    },\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u4e0a\\u6d77\\u5e02\\u70ed\\u95e8\\u5730\\u70b9\\u5206\\u7ec4\\u53ef\\u89c6\\u5316\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ],\n", "    \"visualMap\": {\n", "        \"show\": true,\n", "        \"type\": \"piecewise\",\n", "        \"min\": 0,\n", "        \"max\": 100,\n", "        \"inRange\": {\n", "            \"color\": [\n", "                \"#50a3ba\",\n", "                \"#eac763\",\n", "                \"#d94e5d\"\n", "            ]\n", "        },\n", "        \"calculable\": true,\n", "        \"inverse\": false,\n", "        \"splitNumber\": 5,\n", "        \"hoverLink\": true,\n", "        \"orient\": \"vertical\",\n", "        \"padding\": 5,\n", "        \"showLabel\": true,\n", "        \"itemWidth\": 20,\n", "        \"itemHeight\": 14,\n", "        \"borderWidth\": 0,\n", "        \"pieces\": [\n", "            {\n", "                \"min\": 0,\n", "                \"max\": 0,\n", "                \"label\": \"1\",\n", "                \"color\": \"#50A3BA\"\n", "            },\n", "            {\n", "                \"min\": 0,\n", "                \"max\": 1,\n", "                \"label\": \"2\",\n", "                \"color\": \"#DD0200\"\n", "            },\n", "            {\n", "                \"min\": 1,\n", "                \"max\": 2,\n", "                \"label\": \"3\",\n", "                \"color\": \"#E2C568\"\n", "            }\n", "        ]\n", "    },\n", "    \"geo\": {\n", "        \"map\": \"\\u4e0a\\u6d77\",\n", "        \"roam\": true,\n", "        \"aspectScale\": 0.75,\n", "        \"nameProperty\": \"name\",\n", "        \"selectedMode\": false,\n", "        \"emphasis\": {}\n", "    }\n", "};\n", "                chart_5449904cfd1e42fc9ebf829e27828592.setOption(option_5449904cfd1e42fc9ebf829e27828592);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x228c9a045e0>"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["#K=3时拜访数据分组可视化\n", "\n", "from pyecharts.charts import Geo\n", "from pyecharts import options as opts\n", "from pyecharts.globals import GeoType\n", "\n", "city = '上海'\n", "\n", "#实例化一个Geo类\n", "geo3 = Geo()\n", "#以上海市地图为底景\n", "geo3.add_schema(maptype=city)\n", "# #添加地点坐标至坐标库中\n", "for i in range(80):\n", "    geo3.add_coordinate(visit.iloc[i]['地址'],visit.iloc[i]['经度'],visit.iloc[i]['纬度'])\n", "\n", "data_pair = [(visit.iloc[i]['地址'],int(y_pred[i])) for i in range(80)]\n", "    \n", "# 将数据添加到地图上\n", "geo3.add('',data_pair,type_=GeoType.EFFECT_SCATTER, symbol_size=9)\n", "# 设置样式\n", "geo3.set_series_opts(label_opts=opts.LabelOpts(is_show=False))\n", "#自定义分级\n", "pieces = [\n", "        {'min': 0, 'max': 0, 'label': '1', 'color': '#50A3BA'},\n", "        {'min': 0, 'max': 1, 'label': '2', 'color': '#DD0200'},\n", "        {'min': 1, 'max': 2, 'label': '3', 'color': '#E2C568'}\n", "]\n", "#是否自定义分段\n", "geo3.set_global_opts(\n", "        visualmap_opts=opts.VisualMapOpts(is_piecewise=True, pieces=pieces),\n", "        title_opts=opts.TitleOpts(title='上海市热门地点分组可视化'),\n", "    )\n", "    \n", "geo3.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "2CCF464E86E94CE58FF3948A8CB83E7D", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 3.3 聚类中心地址"]}, {"cell_type": "code", "execution_count": 79, "metadata": {"id": "82255DA8EC944F1D89EEBA483249D7B0", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["array([[121.48337518,  31.23489423],\n", "       [121.34151355,  31.18340573],\n", "       [121.34854167,  30.81906833]])"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["#获取聚类中心坐标\n", "\n", "center_ = kmeans.cluster_centers_\n", "center_"]}, {"cell_type": "code", "execution_count": 56, "metadata": {"id": "EC0A048107E948C782AAA8F174B77C48", "jupyter": {}, "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["# 可以到 https://lbs.amap.com/tools/picker 输入经纬度121.48337518,31.23489423查看地图位置\n", "\n", "#获取聚类中心点详细地址\n", "\n", "# from geopy.geocoders import Nominatim\n", "# geolocator = Nominatim(user_agent='Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.81 Safari/537.36 SE 2.X MetaSr 1.0',\n", "#                       timeout=5)\n", "\n", "# for i in range(3):\n", "#     location = geolocator.reverse((center_[i][1],center_[i][0]))\n", "#     print(location.address)\n", "#     time.sleep(1.5)\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 大屏展示"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/plain": ["'C:\\\\Users\\\\<USER>\\\\cx_xiangmu\\\\Python数据分析\\\\上架\\\\2301-Python数据分析与可视化项目\\\\电商-优衣库门店可视化与顾客分组-约500行（pyecharts地图可视化、KMeans聚类）\\\\test.html'"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["from pyecharts.charts import Page\n", "page = Page(layout=Page.DraggablePageLayout, page_title=\"大屏展示\")\n", "\n", "page.add(\n", "    geo,\n", "    geo2,\n", "    geo3)\n", "# 先保存到test.html 然后打开，拖拽图片自定义布局， 之后记得点击左上角“save config”对布局文件进行保存。\n", "# 会生成一个chart_config.json的文件，这其中包含了每个图表ID对应的布局位置\n", "page.render('test.html')"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<!DOCTYPE html>\\n<html>\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <title>大屏展示</title>\\n            <script type=\"text/javascript\" src=\"https://assets.pyecharts.org/assets/v5/echarts.min.js\"></script>\\n        <script type=\"text/javascript\" src=\"https://assets.pyecharts.org/assets/v5/maps/shanghai.js\"></script>\\n        <script type=\"text/javascript\" src=\"https://assets.pyecharts.org/assets/v5/jquery.min.js\"></script>\\n        <script type=\"text/javascript\" src=\"https://assets.pyecharts.org/assets/v5/jquery-ui.min.js\"></script>\\n        <script type=\"text/javascript\" src=\"https://assets.pyecharts.org/assets/v5/ResizeSensor.js\"></script>\\n\\n            <link rel=\"stylesheet\"  href=\"https://assets.pyecharts.org/assets/v5/jquery-ui.css\">\\n\\n</head>\\n<body >\\n    <style>.box {  } </style>\\n        \\n    <div class=\"box\">\\n                <div id=\"********************************\" class=\"chart-container\" style=\"position: absolute; width: 900px; height: 500px; top: 31px; left: 8px;\"></div>\\n    <script>\\n        var chart_******************************** = echarts.init(\\n            document.getElementById(\\'********************************\\'), \\'white\\', {renderer: \\'canvas\\'});\\n        var option_******************************** = {\\n    \"animation\": true,\\n    \"animationThreshold\": 2000,\\n    \"animationDuration\": 1000,\\n    \"animationEasing\": \"cubicOut\",\\n    \"animationDelay\": 0,\\n    \"animationDurationUpdate\": 300,\\n    \"animationEasingUpdate\": \"cubicOut\",\\n    \"animationDelayUpdate\": 0,\\n    \"aria\": {\\n        \"enabled\": false\\n    },\\n    \"color\": [\\n        \"#5470c6\",\\n        \"#91cc75\",\\n        \"#fac858\",\\n        \"#ee6666\",\\n        \"#73c0de\",\\n        \"#3ba272\",\\n        \"#fc8452\",\\n        \"#9a60b4\",\\n        \"#ea7ccc\"\\n    ],\\n    \"series\": [\\n        {\\n            \"type\": \"effectScatter\",\\n            \"coordinateSystem\": \"geo\",\\n            \"showEffectOn\": \"render\",\\n            \"rippleEffect\": {\\n                \"show\": true,\\n                \"brushType\": \"stroke\",\\n                \"scale\": 2.5,\\n                \"period\": 4\\n            },\\n            \"symbolSize\": 9,\\n            \"data\": [\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e03\\\\u5b9d\\\\u51ef\\\\u5fb7\\\\u5e97\",\\n                    \"value\": [\\n                        121.348318,\\n                        31.17132,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e0a\\\\u6d77\\\\u957f\\\\u6cf0\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.607512,\\n                        31.210366,\\n                        3\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e94\\\\u89d2\\\\u573a\\\\u53c8\\\\u4e00\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.521831,\\n                        31.307534000000004,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e94\\\\u89d2\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.52031399999998,\\n                        31.306632,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5149\\\\u542f\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.434446,\\n                        31.190694,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5357\\\\u4eac\\\\u4e1c\\\\u8def\\\\u4e2d\\\\u8054\\\\u5e97\",\\n                    \"value\": [\\n                        121.489976,\\n                        31.242919,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5357\\\\u4eac\\\\u4e1c\\\\u8def\\\\u7b2c\\\\u4e00\\\\u767e\\\\u8d27\\\\u5e97\",\\n                    \"value\": [\\n                        121.481317,\\n                        31.240769,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5357\\\\u4eac\\\\u897f\\\\u8def\\\\u5e97\",\\n                    \"value\": [\\n                        121.465285,\\n                        31.235748,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5858\\\\u6865\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.526688,\\n                        31.214227,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5927\\\\u5b81\\\\u56fd\\\\u9645\\\\u5e97\",\\n                    \"value\": [\\n                        121.459213,\\n                        31.281012,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5927\\\\u60a6\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.478725,\\n                        31.248893,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5927\\\\u62c7\\\\u6307\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.566506,\\n                        31.233415,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u592a\\\\u5e73\\\\u6d0b\\\\u4e0d\\\\u591c\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.46328,\\n                        31.251464,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5b89\\\\u4ead\\\\u5609\\\\u4ead\\\\u835f\\\\u5e97\",\\n                    \"value\": [\\n                        121.16916100000002,\\n                        31.293966,\\n                        3\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5b9d\\\\u5c71\\\\u4e07\\\\u8fbe\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.452854,\\n                        31.330385,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5b9d\\\\u5c71\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.417753,\\n                        31.276786,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5f00\\\\u5143\\\\u5730\\\\u4e2d\\\\u6d77\\\\u5e97\",\\n                    \"value\": [\\n                        121.225779,\\n                        31.043855,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6210\\\\u5c71\\\\u8def\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.542118,\\n                        31.184962,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6211\\\\u683c\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.429781,\\n                        31.244328000000003,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u65b0\\\\u6885\\\\u8054\\\\u5408\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.52288,\\n                        31.235271,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u65e5\\\\u6708\\\\u5149\\\\u5e97\",\\n                    \"value\": [\\n                        121.47510800000002,\\n                        31.21155,\\n                        4\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u66f9\\\\u5b89\\\\u516c\\\\u8def\\\\u5e97\",\\n                    \"value\": [\\n                        121.370842,\\n                        31.259728000000003,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6b63\\\\u5927\\\\u4e50\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.464675,\\n                        31.192402,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6b63\\\\u5927\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.505676,\\n                        31.242789,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6c38\\\\u65b0\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.44902,\\n                        31.198699,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6c5f\\\\u6865\\\\u4e07\\\\u8fbe\\\\u5e97\",\\n                    \"value\": [\\n                        121.33073,\\n                        31.247020000000003,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6d66\\\\u4e1c\\\\u4e2d\\\\u623f\\\\u91d1\\\\u8c0a\\\\u5e7f\\\\u573a\",\\n                    \"value\": [\\n                        121.519196,\\n                        31.147252,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6d66\\\\u4e1c\\\\u5546\\\\u573a\\\\u6210\\\\u5c71\\\\u8def\\\\u5e97\",\\n                    \"value\": [\\n                        121.513909,\\n                        31.177723,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6dee\\\\u6d77\\\\u8def\\\\u592a\\\\u5e73\\\\u6d0b\\\\u5e97\",\\n                    \"value\": [\\n                        121.467549,\\n                        31.223576,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6e2f\\\\u6c47\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.44325,\\n                        31.201186,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u767e\\\\u8054\\\\u4e2d\\\\u73af\\\\u5e97\",\\n                    \"value\": [\\n                        121.389893,\\n                        31.250845,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u767e\\\\u8054\\\\u5357\\\\u6865\\\\u5e97\",\\n                    \"value\": [\\n                        121.490514,\\n                        30.92182,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u8398\\\\u5e84\\\\u4ef2\\\\u76db\\\\u5e97\",\\n                    \"value\": [\\n                        121.39373700000002,\\n                        31.112857,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u83b2\\\\u82b1\\\\u56fd\\\\u9645\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.409081,\\n                        31.139156,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u897f\\\\u90ca\\\\u767e\\\\u8054\\\\u5e97\",\\n                    \"value\": [\\n                        121.377053,\\n                        31.214539,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u91d1\\\\u5c71\\\\u767e\\\\u8054\\\\u5e97\",\\n                    \"value\": [\\n                        121.354894,\\n                        30.737391,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u91d1\\\\u6865\\\\u5e97\",\\n                    \"value\": [\\n                        121.585552,\\n                        31.261473,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u95f5\\\\u884c\\\\u6d66\\\\u6c5f\\\\u9547\\\\u5e97\",\\n                    \"value\": [\\n                        121.510835,\\n                        31.100538,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u9655\\\\u897f\\\\u8def\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.447881,\\n                        31.24915,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u9f99\\\\u4e4b\\\\u68a6\\\\u5e97\",\\n                    \"value\": [\\n                        121.484808,\\n                        31.276663,\\n                        1\\n                    ]\\n                }\\n            ],\\n            \"label\": {\\n                \"show\": false,\\n                \"margin\": 8\\n            }\\n        }\\n    ],\\n    \"legend\": [\\n        {\\n            \"data\": [\\n                \"\"\\n            ],\\n            \"selected\": {\\n                \"\": true\\n            },\\n            \"show\": true,\\n            \"padding\": 5,\\n            \"itemGap\": 10,\\n            \"itemWidth\": 25,\\n            \"itemHeight\": 14,\\n            \"backgroundColor\": \"transparent\",\\n            \"borderColor\": \"#ccc\",\\n            \"borderWidth\": 1,\\n            \"borderRadius\": 0,\\n            \"pageButtonItemGap\": 5,\\n            \"pageButtonPosition\": \"end\",\\n            \"pageFormatter\": \"{current}/{total}\",\\n            \"pageIconColor\": \"#2f4554\",\\n            \"pageIconInactiveColor\": \"#aaa\",\\n            \"pageIconSize\": 15,\\n            \"animationDurationUpdate\": 800,\\n            \"selector\": false,\\n            \"selectorPosition\": \"auto\",\\n            \"selectorItemGap\": 7,\\n            \"selectorButtonGap\": 10\\n        }\\n    ],\\n    \"tooltip\": {\\n        \"show\": true,\\n        \"trigger\": \"item\",\\n        \"triggerOn\": \"mousemove|click\",\\n        \"axisPointer\": {\\n            \"type\": \"line\"\\n        },\\n        \"showContent\": true,\\n        \"alwaysShowContent\": false,\\n        \"showDelay\": 0,\\n        \"hideDelay\": 100,\\n        \"enterable\": false,\\n        \"confine\": false,\\n        \"appendToBody\": false,\\n        \"transitionDuration\": 0.4,\\n        \"formatter\": function (params) {        return params.name + \\' : \\' + params.value[2];    },\\n        \"textStyle\": {\\n            \"fontSize\": 14\\n        },\\n        \"borderWidth\": 0,\\n        \"padding\": 5,\\n        \"order\": \"seriesAsc\"\\n    },\\n    \"title\": [\\n        {\\n            \"show\": true,\\n            \"text\": \"\\\\u4e0a\\\\u6d77\\\\u5e02\\\\u4f18\\\\u8863\\\\u5e93\\\\u95e8\\\\u5e97\\\\u53ef\\\\u89c6\\\\u5316\",\\n            \"target\": \"blank\",\\n            \"subtarget\": \"blank\",\\n            \"padding\": 5,\\n            \"itemGap\": 10,\\n            \"textAlign\": \"auto\",\\n            \"textVerticalAlign\": \"auto\",\\n            \"triggerEvent\": false\\n        }\\n    ],\\n    \"visualMap\": {\\n        \"show\": true,\\n        \"type\": \"piecewise\",\\n        \"min\": 0,\\n        \"max\": 100,\\n        \"inRange\": {\\n            \"color\": [\\n                \"#50a3ba\",\\n                \"#eac763\",\\n                \"#d94e5d\"\\n            ]\\n        },\\n        \"calculable\": true,\\n        \"inverse\": false,\\n        \"splitNumber\": 5,\\n        \"hoverLink\": true,\\n        \"orient\": \"vertical\",\\n        \"padding\": 5,\\n        \"showLabel\": true,\\n        \"itemWidth\": 20,\\n        \"itemHeight\": 14,\\n        \"borderWidth\": 0,\\n        \"pieces\": [\\n            {\\n                \"min\": 0,\\n                \"max\": 1,\\n                \"label\": \"1\",\\n                \"color\": \"#50A3BA\"\\n            },\\n            {\\n                \"min\": 1,\\n                \"max\": 2,\\n                \"label\": \"2\",\\n                \"color\": \"#DD675E\"\\n            },\\n            {\\n                \"min\": 2,\\n                \"max\": 3,\\n                \"label\": \"3\",\\n                \"color\": \"#E2C568\"\\n            },\\n            {\\n                \"min\": 3,\\n                \"label\": \"4\",\\n                \"color\": \"#3700A4\"\\n            }\\n        ]\\n    },\\n    \"geo\": {\\n        \"map\": \"\\\\u4e0a\\\\u6d77\",\\n        \"roam\": true,\\n        \"aspectScale\": 0.75,\\n        \"nameProperty\": \"name\",\\n        \"selectedMode\": false,\\n        \"emphasis\": {}\\n    }\\n};\\n        chart_********************************.setOption(option_********************************);\\n    </script>\\n<br/>                <div id=\"8c01bd8603524aa5a0ad6df2339f25da\" class=\"chart-container\" style=\"position: absolute; width: 900px; height: 500px; top: 33px; left: 919px;\"></div>\\n    <script>\\n        var chart_8c01bd8603524aa5a0ad6df2339f25da = echarts.init(\\n            document.getElementById(\\'8c01bd8603524aa5a0ad6df2339f25da\\'), \\'white\\', {renderer: \\'canvas\\'});\\n        var option_8c01bd8603524aa5a0ad6df2339f25da = {\\n    \"animation\": true,\\n    \"animationThreshold\": 2000,\\n    \"animationDuration\": 1000,\\n    \"animationEasing\": \"cubicOut\",\\n    \"animationDelay\": 0,\\n    \"animationDurationUpdate\": 300,\\n    \"animationEasingUpdate\": \"cubicOut\",\\n    \"animationDelayUpdate\": 0,\\n    \"aria\": {\\n        \"enabled\": false\\n    },\\n    \"color\": [\\n        \"#5470c6\",\\n        \"#91cc75\",\\n        \"#fac858\",\\n        \"#ee6666\",\\n        \"#73c0de\",\\n        \"#3ba272\",\\n        \"#fc8452\",\\n        \"#9a60b4\",\\n        \"#ea7ccc\"\\n    ],\\n    \"series\": [\\n        {\\n            \"type\": \"heatmap\",\\n            \"coordinateSystem\": \"geo\",\\n            \"data\": [\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e03\\\\u5b9d\\\\u51ef\\\\u5fb7\\\\u5e97\",\\n                    \"value\": [\\n                        121.348318,\\n                        31.17132,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e0a\\\\u6d77\\\\u957f\\\\u6cf0\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.607512,\\n                        31.210366,\\n                        3\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e94\\\\u89d2\\\\u573a\\\\u53c8\\\\u4e00\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.521831,\\n                        31.307534000000004,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e94\\\\u89d2\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.52031399999998,\\n                        31.306632,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5149\\\\u542f\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.434446,\\n                        31.190694,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5357\\\\u4eac\\\\u4e1c\\\\u8def\\\\u4e2d\\\\u8054\\\\u5e97\",\\n                    \"value\": [\\n                        121.489976,\\n                        31.242919,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5357\\\\u4eac\\\\u4e1c\\\\u8def\\\\u7b2c\\\\u4e00\\\\u767e\\\\u8d27\\\\u5e97\",\\n                    \"value\": [\\n                        121.481317,\\n                        31.240769,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5357\\\\u4eac\\\\u897f\\\\u8def\\\\u5e97\",\\n                    \"value\": [\\n                        121.465285,\\n                        31.235748,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5858\\\\u6865\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.526688,\\n                        31.214227,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5927\\\\u5b81\\\\u56fd\\\\u9645\\\\u5e97\",\\n                    \"value\": [\\n                        121.459213,\\n                        31.281012,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5927\\\\u60a6\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.478725,\\n                        31.248893,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5927\\\\u62c7\\\\u6307\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.566506,\\n                        31.233415,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u592a\\\\u5e73\\\\u6d0b\\\\u4e0d\\\\u591c\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.46328,\\n                        31.251464,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5b89\\\\u4ead\\\\u5609\\\\u4ead\\\\u835f\\\\u5e97\",\\n                    \"value\": [\\n                        121.16916100000002,\\n                        31.293966,\\n                        3\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5b9d\\\\u5c71\\\\u4e07\\\\u8fbe\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.452854,\\n                        31.330385,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5b9d\\\\u5c71\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.417753,\\n                        31.276786,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5f00\\\\u5143\\\\u5730\\\\u4e2d\\\\u6d77\\\\u5e97\",\\n                    \"value\": [\\n                        121.225779,\\n                        31.043855,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6210\\\\u5c71\\\\u8def\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.542118,\\n                        31.184962,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6211\\\\u683c\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.429781,\\n                        31.244328000000003,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u65b0\\\\u6885\\\\u8054\\\\u5408\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.52288,\\n                        31.235271,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u65e5\\\\u6708\\\\u5149\\\\u5e97\",\\n                    \"value\": [\\n                        121.47510800000002,\\n                        31.21155,\\n                        4\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u66f9\\\\u5b89\\\\u516c\\\\u8def\\\\u5e97\",\\n                    \"value\": [\\n                        121.370842,\\n                        31.259728000000003,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6b63\\\\u5927\\\\u4e50\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.464675,\\n                        31.192402,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6b63\\\\u5927\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.505676,\\n                        31.242789,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6c38\\\\u65b0\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.44902,\\n                        31.198699,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6c5f\\\\u6865\\\\u4e07\\\\u8fbe\\\\u5e97\",\\n                    \"value\": [\\n                        121.33073,\\n                        31.247020000000003,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6d66\\\\u4e1c\\\\u4e2d\\\\u623f\\\\u91d1\\\\u8c0a\\\\u5e7f\\\\u573a\",\\n                    \"value\": [\\n                        121.519196,\\n                        31.147252,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6d66\\\\u4e1c\\\\u5546\\\\u573a\\\\u6210\\\\u5c71\\\\u8def\\\\u5e97\",\\n                    \"value\": [\\n                        121.513909,\\n                        31.177723,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6dee\\\\u6d77\\\\u8def\\\\u592a\\\\u5e73\\\\u6d0b\\\\u5e97\",\\n                    \"value\": [\\n                        121.467549,\\n                        31.223576,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6e2f\\\\u6c47\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.44325,\\n                        31.201186,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u767e\\\\u8054\\\\u4e2d\\\\u73af\\\\u5e97\",\\n                    \"value\": [\\n                        121.389893,\\n                        31.250845,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u767e\\\\u8054\\\\u5357\\\\u6865\\\\u5e97\",\\n                    \"value\": [\\n                        121.490514,\\n                        30.92182,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u8398\\\\u5e84\\\\u4ef2\\\\u76db\\\\u5e97\",\\n                    \"value\": [\\n                        121.39373700000002,\\n                        31.112857,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u83b2\\\\u82b1\\\\u56fd\\\\u9645\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.409081,\\n                        31.139156,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u897f\\\\u90ca\\\\u767e\\\\u8054\\\\u5e97\",\\n                    \"value\": [\\n                        121.377053,\\n                        31.214539,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u91d1\\\\u5c71\\\\u767e\\\\u8054\\\\u5e97\",\\n                    \"value\": [\\n                        121.354894,\\n                        30.737391,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u91d1\\\\u6865\\\\u5e97\",\\n                    \"value\": [\\n                        121.585552,\\n                        31.261473,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u95f5\\\\u884c\\\\u6d66\\\\u6c5f\\\\u9547\\\\u5e97\",\\n                    \"value\": [\\n                        121.510835,\\n                        31.100538,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u9655\\\\u897f\\\\u8def\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.447881,\\n                        31.24915,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u9f99\\\\u4e4b\\\\u68a6\\\\u5e97\",\\n                    \"value\": [\\n                        121.484808,\\n                        31.276663,\\n                        1\\n                    ]\\n                }\\n            ],\\n            \"pointSize\": 20,\\n            \"blurSize\": 20,\\n            \"label\": {\\n                \"show\": false,\\n                \"margin\": 8\\n            },\\n            \"rippleEffect\": {\\n                \"show\": true,\\n                \"brushType\": \"stroke\",\\n                \"scale\": 2.5,\\n                \"period\": 4\\n            }\\n        }\\n    ],\\n    \"legend\": [\\n        {\\n            \"data\": [\\n                \"\"\\n            ],\\n            \"selected\": {\\n                \"\": true\\n            },\\n            \"show\": true,\\n            \"padding\": 5,\\n            \"itemGap\": 10,\\n            \"itemWidth\": 25,\\n            \"itemHeight\": 14,\\n            \"backgroundColor\": \"transparent\",\\n            \"borderColor\": \"#ccc\",\\n            \"borderWidth\": 1,\\n            \"borderRadius\": 0,\\n            \"pageButtonItemGap\": 5,\\n            \"pageButtonPosition\": \"end\",\\n            \"pageFormatter\": \"{current}/{total}\",\\n            \"pageIconColor\": \"#2f4554\",\\n            \"pageIconInactiveColor\": \"#aaa\",\\n            \"pageIconSize\": 15,\\n            \"animationDurationUpdate\": 800,\\n            \"selector\": false,\\n            \"selectorPosition\": \"auto\",\\n            \"selectorItemGap\": 7,\\n            \"selectorButtonGap\": 10\\n        }\\n    ],\\n    \"tooltip\": {\\n        \"show\": true,\\n        \"trigger\": \"item\",\\n        \"triggerOn\": \"mousemove|click\",\\n        \"axisPointer\": {\\n            \"type\": \"line\"\\n        },\\n        \"showContent\": true,\\n        \"alwaysShowContent\": false,\\n        \"showDelay\": 0,\\n        \"hideDelay\": 100,\\n        \"enterable\": false,\\n        \"confine\": false,\\n        \"appendToBody\": false,\\n        \"transitionDuration\": 0.4,\\n        \"formatter\": function (params) {        return params.name + \\' : \\' + params.value[2];    },\\n        \"textStyle\": {\\n            \"fontSize\": 14\\n        },\\n        \"borderWidth\": 0,\\n        \"padding\": 5,\\n        \"order\": \"seriesAsc\"\\n    },\\n    \"title\": [\\n        {\\n            \"show\": true,\\n            \"text\": \"\\\\u4e0a\\\\u6d77\\\\u5e02\\\\u4f18\\\\u8863\\\\u5e93\\\\u95e8\\\\u5e97\\\\u53ef\\\\u89c6\\\\u5316\",\\n            \"target\": \"blank\",\\n            \"subtarget\": \"blank\",\\n            \"padding\": 5,\\n            \"itemGap\": 10,\\n            \"textAlign\": \"auto\",\\n            \"textVerticalAlign\": \"auto\",\\n            \"triggerEvent\": false\\n        }\\n    ],\\n    \"visualMap\": {\\n        \"show\": true,\\n        \"type\": \"piecewise\",\\n        \"min\": 0,\\n        \"max\": 100,\\n        \"inRange\": {\\n            \"color\": [\\n                \"#50a3ba\",\\n                \"#eac763\",\\n                \"#d94e5d\"\\n            ]\\n        },\\n        \"calculable\": true,\\n        \"inverse\": false,\\n        \"splitNumber\": 5,\\n        \"hoverLink\": true,\\n        \"orient\": \"vertical\",\\n        \"padding\": 5,\\n        \"showLabel\": true,\\n        \"itemWidth\": 20,\\n        \"itemHeight\": 14,\\n        \"borderWidth\": 0,\\n        \"pieces\": [\\n            {\\n                \"min\": 0,\\n                \"max\": 1,\\n                \"label\": \"1\",\\n                \"color\": \"#50A3BA\"\\n            },\\n            {\\n                \"min\": 1,\\n                \"max\": 2,\\n                \"label\": \"2\",\\n                \"color\": \"#E2C568\"\\n            },\\n            {\\n                \"min\": 2,\\n                \"max\": 3,\\n                \"label\": \"3\",\\n                \"color\": \"#DD675E\"\\n            },\\n            {\\n                \"min\": 3,\\n                \"label\": \"4\",\\n                \"color\": \"#DD0200\"\\n            }\\n        ]\\n    },\\n    \"geo\": {\\n        \"map\": \"\\\\u4e0a\\\\u6d77\",\\n        \"roam\": true,\\n        \"aspectScale\": 0.75,\\n        \"nameProperty\": \"name\",\\n        \"selectedMode\": false,\\n        \"emphasis\": {}\\n    }\\n};\\n        chart_8c01bd8603524aa5a0ad6df2339f25da.setOption(option_8c01bd8603524aa5a0ad6df2339f25da);\\n    </script>\\n<br/>                <div id=\"5449904cfd1e42fc9ebf829e27828592\" class=\"chart-container\" style=\"position: absolute; width: 900px; height: 500px; top: 557px; left: 9px;\"></div>\\n    <script>\\n        var chart_5449904cfd1e42fc9ebf829e27828592 = echarts.init(\\n            document.getElementById(\\'5449904cfd1e42fc9ebf829e27828592\\'), \\'white\\', {renderer: \\'canvas\\'});\\n        var option_5449904cfd1e42fc9ebf829e27828592 = {\\n    \"animation\": true,\\n    \"animationThreshold\": 2000,\\n    \"animationDuration\": 1000,\\n    \"animationEasing\": \"cubicOut\",\\n    \"animationDelay\": 0,\\n    \"animationDurationUpdate\": 300,\\n    \"animationEasingUpdate\": \"cubicOut\",\\n    \"animationDelayUpdate\": 0,\\n    \"aria\": {\\n        \"enabled\": false\\n    },\\n    \"color\": [\\n        \"#5470c6\",\\n        \"#91cc75\",\\n        \"#fac858\",\\n        \"#ee6666\",\\n        \"#73c0de\",\\n        \"#3ba272\",\\n        \"#fc8452\",\\n        \"#9a60b4\",\\n        \"#ea7ccc\"\\n    ],\\n    \"series\": [\\n        {\\n            \"type\": \"effectScatter\",\\n            \"coordinateSystem\": \"geo\",\\n            \"showEffectOn\": \"render\",\\n            \"rippleEffect\": {\\n                \"show\": true,\\n                \"brushType\": \"stroke\",\\n                \"scale\": 2.5,\\n                \"period\": 4\\n            },\\n            \"symbolSize\": 9,\\n            \"data\": [\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5357\\\\u4eac\\\\u897f\\\\u8def\\\\u5e97\",\\n                    \"value\": [\\n                        121.465285,\\n                        31.235748,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6d66\\\\u4e1c\\\\u5546\\\\u573a\\\\u6210\\\\u5c71\\\\u8def\\\\u5e97\",\\n                    \"value\": [\\n                        121.513909,\\n                        31.177723,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5927\\\\u62c7\\\\u6307\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.566506,\\n                        31.233415,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5b9d\\\\u5c71\\\\u4e07\\\\u8fbe\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.452854,\\n                        31.330385,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5149\\\\u542f\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.434446,\\n                        31.190694,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u897f\\\\u90ca\\\\u767e\\\\u8054\\\\u5e97\",\\n                    \"value\": [\\n                        121.377053,\\n                        31.214539,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u65e5\\\\u6708\\\\u5149\\\\u5e97\",\\n                    \"value\": [\\n                        121.475108,\\n                        31.21155,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u66f9\\\\u5b89\\\\u516c\\\\u8def\\\\u5e97\",\\n                    \"value\": [\\n                        121.370842,\\n                        31.259728000000003,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5b89\\\\u4ead\\\\u5609\\\\u4ead\\\\u835f\\\\u5e97\",\\n                    \"value\": [\\n                        121.16916100000002,\\n                        31.293966,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u592a\\\\u5e73\\\\u6d0b\\\\u4e0d\\\\u591c\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.46328,\\n                        31.251464,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6d66\\\\u4e1c\\\\u4e2d\\\\u623f\\\\u91d1\\\\u8c0a\\\\u5e7f\\\\u573a\",\\n                    \"value\": [\\n                        121.519196,\\n                        31.147252,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5357\\\\u4eac\\\\u4e1c\\\\u8def\\\\u7b2c\\\\u4e00\\\\u767e\\\\u8d27\\\\u5e97\",\\n                    \"value\": [\\n                        121.481317,\\n                        31.240769,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6dee\\\\u6d77\\\\u8def\\\\u592a\\\\u5e73\\\\u6d0b\\\\u5e97\",\\n                    \"value\": [\\n                        121.467549,\\n                        31.223576,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u767e\\\\u8054\\\\u5357\\\\u6865\\\\u5e97\",\\n                    \"value\": [\\n                        121.490514,\\n                        30.92182,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5f00\\\\u5143\\\\u5730\\\\u4e2d\\\\u6d77\\\\u5e97\",\\n                    \"value\": [\\n                        121.225779,\\n                        31.043855,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u91d1\\\\u5c71\\\\u767e\\\\u8054\\\\u5e97\",\\n                    \"value\": [\\n                        121.354894,\\n                        30.737391,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6211\\\\u683c\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.429781,\\n                        31.244328000000003,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6c5f\\\\u6865\\\\u4e07\\\\u8fbe\\\\u5e97\",\\n                    \"value\": [\\n                        121.33073,\\n                        31.247020000000003,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5927\\\\u60a6\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.478725,\\n                        31.248893,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6210\\\\u5c71\\\\u8def\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.542118,\\n                        31.184962,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5b9d\\\\u5c71\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.417753,\\n                        31.276786,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u83b2\\\\u82b1\\\\u56fd\\\\u9645\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.409081,\\n                        31.139156,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e03\\\\u5b9d\\\\u51ef\\\\u5fb7\\\\u5e97\",\\n                    \"value\": [\\n                        121.348318,\\n                        31.17132,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u91d1\\\\u6865\\\\u5e97\",\\n                    \"value\": [\\n                        121.585552,\\n                        31.261473,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5858\\\\u6865\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.526688,\\n                        31.214227,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6c38\\\\u65b0\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.44902,\\n                        31.198699,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u767e\\\\u8054\\\\u4e2d\\\\u73af\\\\u5e97\",\\n                    \"value\": [\\n                        121.389893,\\n                        31.250845,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u9f99\\\\u4e4b\\\\u68a6\\\\u5e97\",\\n                    \"value\": [\\n                        121.484808,\\n                        31.276663,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u9655\\\\u897f\\\\u8def\\\\u5df4\\\\u9ece\\\\u6625\\\\u5929\\\\u5e97\",\\n                    \"value\": [\\n                        121.447881,\\n                        31.24915,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5927\\\\u5b81\\\\u56fd\\\\u9645\\\\u5e97\",\\n                    \"value\": [\\n                        121.459213,\\n                        31.281012,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u65b0\\\\u6885\\\\u8054\\\\u5408\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.52288,\\n                        31.235271,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e94\\\\u89d2\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.52031399999998,\\n                        31.306632,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6b63\\\\u5927\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.505676,\\n                        31.242789,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6e2f\\\\u6c47\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.44325,\\n                        31.201186,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u5357\\\\u4eac\\\\u4e1c\\\\u8def\\\\u4e2d\\\\u8054\\\\u5e97\",\\n                    \"value\": [\\n                        121.489976,\\n                        31.242919,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u8398\\\\u5e84\\\\u4ef2\\\\u76db\\\\u5e97\",\\n                    \"value\": [\\n                        121.39373700000002,\\n                        31.112857,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e94\\\\u89d2\\\\u573a\\\\u53c8\\\\u4e00\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.521831,\\n                        31.307534000000004,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e0a\\\\u6d77\\\\u957f\\\\u6cf0\\\\u5e7f\\\\u573a\\\\u5e97\",\\n                    \"value\": [\\n                        121.607512,\\n                        31.210366,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u6b63\\\\u5927\\\\u4e50\\\\u57ce\\\\u5e97\",\\n                    \"value\": [\\n                        121.464675,\\n                        31.192402,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u95f5\\\\u884c\\\\u6d66\\\\u6c5f\\\\u9547\\\\u5e97\",\\n                    \"value\": [\\n                        121.510835,\\n                        31.100538,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u5c71\\\\u4e1c\\\\u4e2d\\\\u8def145\\\\u53f7\",\\n                    \"value\": [\\n                        121.491114,\\n                        31.239373,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4e1c\\\\u65b9\\\\u8def1630\\\\u53f7\",\\n                    \"value\": [\\n                        121.531632,\\n                        31.213081,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u5236\\\\u9020\\\\u5c40\\\\u8def639\\\\u53f7\",\\n                    \"value\": [\\n                        121.492472,\\n                        31.208031,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u745e\\\\u91d1\\\\u4e8c\\\\u8def197\\\\u53f7\",\\n                    \"value\": [\\n                        121.472605,\\n                        31.217703000000004,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u63a7\\\\u6c5f\\\\u8def1665\\\\u53f7\",\\n                    \"value\": [\\n                        121.523675,\\n                        31.280067,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4e1c\\\\u65b9\\\\u8def1678\\\\u53f7\",\\n                    \"value\": [\\n                        121.530729,\\n                        31.211445,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u6dee\\\\u6d77\\\\u897f\\\\u8def241\\\\u53f7\",\\n                    \"value\": [\\n                        121.43348600000002,\\n                        31.204657,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u6b66\\\\u8fdb\\\\u8def85\\\\u53f7\",\\n                    \"value\": [\\n                        121.49498,\\n                        31.259005,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u5b9c\\\\u5c71\\\\u8def600\\\\u53f7\",\\n                    \"value\": [\\n                        121.430603,\\n                        31.184901,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u533b\\\\u5b66\\\\u9662\\\\u8def138\\\\u53f7\",\\n                    \"value\": [\\n                        121.458553,\\n                        31.204393,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4e4c\\\\u9c81\\\\u6728\\\\u9f50\\\\u4e2d\\\\u8def12\\\\u53f7\",\\n                    \"value\": [\\n                        121.45042,\\n                        31.222696000000003,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u67ab\\\\u6797\\\\u8def138\\\\u53f7\",\\n                    \"value\": [\\n                        121.461124,\\n                        31.20416,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u6c7e\\\\u9633\\\\u8def83\\\\u53f7\",\\n                    \"value\": [\\n                        121.459801,\\n                        31.216084,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u65b9\\\\u659c\\\\u8def419\\\\u53f7\",\\n                    \"value\": [\\n                        121.489711,\\n                        31.220091,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u96f6\\\\u9675\\\\u8def399\\\\u53f7\",\\n                    \"value\": [\\n                        121.459427,\\n                        31.196451,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u5ef6\\\\u957f\\\\u4e2d\\\\u8def301\\\\u53f7\",\\n                    \"value\": [\\n                        121.460247,\\n                        31.277936,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u65b0\\\\u6751\\\\u8def389\\\\u53f7\",\\n                    \"value\": [\\n                        121.200217,\\n                        30.797994,\\n                        2\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u653f\\\\u6c11\\\\u8def507\\\\u53f7\",\\n                    \"value\": [\\n                        121.50893899999998,\\n                        31.306621000000003,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u51e4\\\\u9633\\\\u8def415\\\\u53f7\",\\n                    \"value\": [\\n                        121.47413799999998,\\n                        31.23909,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u957f\\\\u6d77\\\\u8def174\\\\u53f7\",\\n                    \"value\": [\\n                        121.53438,\\n                        31.314777000000003,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u957f\\\\u6d77\\\\u8def225\\\\u53f7\",\\n                    \"value\": [\\n                        121.535568,\\n                        31.315785,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u7518\\\\u6cb3\\\\u8def110\\\\u53f7\",\\n                    \"value\": [\\n                        121.494791,\\n                        31.294773,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u9752\\\\u6d77\\\\u8def44\\\\u53f7\",\\n                    \"value\": [\\n                        121.47057,\\n                        31.236615000000004,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u5b9b\\\\u5e73\\\\u5357\\\\u8def725\\\\u53f7\",\\n                    \"value\": [\\n                        121.456107,\\n                        31.193764,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u666e\\\\u5b89\\\\u8def185\\\\u53f7\",\\n                    \"value\": [\\n                        121.484847,\\n                        31.228823,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u82b7\\\\u6c5f\\\\u4e2d\\\\u8def274\\\\u53f7\",\\n                    \"value\": [\\n                        121.477667,\\n                        31.265602,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u5ef6\\\\u5b89\\\\u897f\\\\u8def221\\\\u53f7\",\\n                    \"value\": [\\n                        121.448122,\\n                        31.225464,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u957f\\\\u4e50\\\\u8def536\\\\u53f7\",\\n                    \"value\": [\\n                        121.46268700000002,\\n                        31.226189,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u6c34\\\\u7535\\\\u8def56\\\\u53f7\",\\n                    \"value\": [\\n                        121.477515,\\n                        31.279301,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u5317\\\\u4eac\\\\u897f\\\\u8def1400\\\\u5f0424\\\\u53f7\",\\n                    \"value\": [\\n                        121.454787,\\n                        31.235549,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u6b66\\\\u5937\\\\u8def196\\\\u53f7\",\\n                    \"value\": [\\n                        121.43331,\\n                        31.219685,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u5eb7\\\\u5b9a\\\\u8def380\\\\u53f73\\\\u697c\",\\n                    \"value\": [\\n                        121.455435,\\n                        31.240979,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u8861\\\\u5c71\\\\u8def910\\\\u53f7\",\\n                    \"value\": [\\n                        121.445945,\\n                        31.201999,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u65e5\\\\u6708\\\\u5149\\\\u5e97\",\\n                    \"value\": [\\n                        121.475108,\\n                        31.21155,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u4e03\\\\u5b9d\\\\u51ef\\\\u5fb7\\\\u5e97\",\\n                    \"value\": [\\n                        121.348318,\\n                        31.17132,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u8398\\\\u5e84\\\\u4ef2\\\\u76db\\\\u5e97\",\\n                    \"value\": [\\n                        121.39373700000002,\\n                        31.112857,\\n                        1\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u4f18\\\\u8863\\\\u5e93\\\\u65e5\\\\u6708\\\\u5149\\\\u5e97\",\\n                    \"value\": [\\n                        121.475108,\\n                        31.21155,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u653f\\\\u6c11\\\\u8def507\\\\u53f7\",\\n                    \"value\": [\\n                        121.50893899999998,\\n                        31.306621000000003,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u5eb7\\\\u5b9a\\\\u8def380\\\\u53f73\\\\u697c\",\\n                    \"value\": [\\n                        121.455435,\\n                        31.240979,\\n                        0\\n                    ]\\n                },\\n                {\\n                    \"name\": \"\\\\u666e\\\\u5b89\\\\u8def185\\\\u53f7\",\\n                    \"value\": [\\n                        121.484847,\\n                        31.228823,\\n                        0\\n                    ]\\n                }\\n            ],\\n            \"label\": {\\n                \"show\": false,\\n                \"margin\": 8\\n            }\\n        }\\n    ],\\n    \"legend\": [\\n        {\\n            \"data\": [\\n                \"\"\\n            ],\\n            \"selected\": {\\n                \"\": true\\n            },\\n            \"show\": true,\\n            \"padding\": 5,\\n            \"itemGap\": 10,\\n            \"itemWidth\": 25,\\n            \"itemHeight\": 14,\\n            \"backgroundColor\": \"transparent\",\\n            \"borderColor\": \"#ccc\",\\n            \"borderWidth\": 1,\\n            \"borderRadius\": 0,\\n            \"pageButtonItemGap\": 5,\\n            \"pageButtonPosition\": \"end\",\\n            \"pageFormatter\": \"{current}/{total}\",\\n            \"pageIconColor\": \"#2f4554\",\\n            \"pageIconInactiveColor\": \"#aaa\",\\n            \"pageIconSize\": 15,\\n            \"animationDurationUpdate\": 800,\\n            \"selector\": false,\\n            \"selectorPosition\": \"auto\",\\n            \"selectorItemGap\": 7,\\n            \"selectorButtonGap\": 10\\n        }\\n    ],\\n    \"tooltip\": {\\n        \"show\": true,\\n        \"trigger\": \"item\",\\n        \"triggerOn\": \"mousemove|click\",\\n        \"axisPointer\": {\\n            \"type\": \"line\"\\n        },\\n        \"showContent\": true,\\n        \"alwaysShowContent\": false,\\n        \"showDelay\": 0,\\n        \"hideDelay\": 100,\\n        \"enterable\": false,\\n        \"confine\": false,\\n        \"appendToBody\": false,\\n        \"transitionDuration\": 0.4,\\n        \"formatter\": function (params) {        return params.name + \\' : \\' + params.value[2];    },\\n        \"textStyle\": {\\n            \"fontSize\": 14\\n        },\\n        \"borderWidth\": 0,\\n        \"padding\": 5,\\n        \"order\": \"seriesAsc\"\\n    },\\n    \"title\": [\\n        {\\n            \"show\": true,\\n            \"text\": \"\\\\u4e0a\\\\u6d77\\\\u5e02\\\\u70ed\\\\u95e8\\\\u5730\\\\u70b9\\\\u5206\\\\u7ec4\\\\u53ef\\\\u89c6\\\\u5316\",\\n            \"target\": \"blank\",\\n            \"subtarget\": \"blank\",\\n            \"padding\": 5,\\n            \"itemGap\": 10,\\n            \"textAlign\": \"auto\",\\n            \"textVerticalAlign\": \"auto\",\\n            \"triggerEvent\": false\\n        }\\n    ],\\n    \"visualMap\": {\\n        \"show\": true,\\n        \"type\": \"piecewise\",\\n        \"min\": 0,\\n        \"max\": 100,\\n        \"inRange\": {\\n            \"color\": [\\n                \"#50a3ba\",\\n                \"#eac763\",\\n                \"#d94e5d\"\\n            ]\\n        },\\n        \"calculable\": true,\\n        \"inverse\": false,\\n        \"splitNumber\": 5,\\n        \"hoverLink\": true,\\n        \"orient\": \"vertical\",\\n        \"padding\": 5,\\n        \"showLabel\": true,\\n        \"itemWidth\": 20,\\n        \"itemHeight\": 14,\\n        \"borderWidth\": 0,\\n        \"pieces\": [\\n            {\\n                \"min\": 0,\\n                \"max\": 0,\\n                \"label\": \"1\",\\n                \"color\": \"#50A3BA\"\\n            },\\n            {\\n                \"min\": 0,\\n                \"max\": 1,\\n                \"label\": \"2\",\\n                \"color\": \"#DD0200\"\\n            },\\n            {\\n                \"min\": 1,\\n                \"max\": 2,\\n                \"label\": \"3\",\\n                \"color\": \"#E2C568\"\\n            }\\n        ]\\n    },\\n    \"geo\": {\\n        \"map\": \"\\\\u4e0a\\\\u6d77\",\\n        \"roam\": true,\\n        \"aspectScale\": 0.75,\\n        \"nameProperty\": \"name\",\\n        \"selectedMode\": false,\\n        \"emphasis\": {}\\n    }\\n};\\n        chart_5449904cfd1e42fc9ebf829e27828592.setOption(option_5449904cfd1e42fc9ebf829e27828592);\\n    </script>\\n<br/>    </div>\\n    <script>\\n            $(\\'#********************************\\').css(\\'border-style\\', \\'dashed\\').css(\\'border-width\\', \\'0px\\');$(\"#********************************>div:nth-child(1)\").width(\"100%\").height(\"100%\");\\n            new ResizeSensor(jQuery(\\'#********************************\\'), function() { chart_********************************.resize()});\\n            $(\\'#8c01bd8603524aa5a0ad6df2339f25da\\').css(\\'border-style\\', \\'dashed\\').css(\\'border-width\\', \\'0px\\');$(\"#8c01bd8603524aa5a0ad6df2339f25da>div:nth-child(1)\").width(\"100%\").height(\"100%\");\\n            new ResizeSensor(jQuery(\\'#8c01bd8603524aa5a0ad6df2339f25da\\'), function() { chart_8c01bd8603524aa5a0ad6df2339f25da.resize()});\\n            $(\\'#5449904cfd1e42fc9ebf829e27828592\\').css(\\'border-style\\', \\'dashed\\').css(\\'border-width\\', \\'0px\\');$(\"#5449904cfd1e42fc9ebf829e27828592>div:nth-child(1)\").width(\"100%\").height(\"100%\");\\n            new ResizeSensor(jQuery(\\'#5449904cfd1e42fc9ebf829e27828592\\'), function() { chart_5449904cfd1e42fc9ebf829e27828592.resize()});\\n            var charts_id = [\\'********************************\\',\\'8c01bd8603524aa5a0ad6df2339f25da\\',\\'5449904cfd1e42fc9ebf829e27828592\\'];\\nfunction downloadCfg () {\\n    const fileName = \\'chart_config.json\\'\\n    let downLink = document.createElement(\\'a\\')\\n    downLink.download = fileName\\n\\n    let result = []\\n    for(let i=0; i<charts_id.length; i++) {\\n        chart = $(\\'#\\'+charts_id[i])\\n        result.push({\\n            cid: charts_id[i],\\n            width: chart.css(\"width\"),\\n            height: chart.css(\"height\"),\\n            top: chart.offset().top + \"px\",\\n            left: chart.offset().left + \"px\"\\n        })\\n    }\\n\\n    let blob = new Blob([JSON.stringify(result)])\\n    downLink.href = URL.createObjectURL(blob)\\n    document.body.appendChild(downLink)\\n    downLink.click()\\n    document.body.removeChild(downLink)\\n}\\n    </script>\\n</body>\\n</html>\\n'"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["# 然后运行下面这行代码。保存布局好的的仪表盘文件。\n", "page.save_resize_html('test.html', cfg_file='chart_config.json', dest='大屏展示1.html')"]}, {"cell_type": "markdown", "metadata": {"id": "C14C939F6E7E41538F144EC5719F6BC2", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["# 总结与展望"]}, {"cell_type": "markdown", "metadata": {"id": "E68269B2CB0844DB82869253C784C921", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 1 项目总结"]}, {"cell_type": "markdown", "metadata": {"id": "A3EF40539DD843E9864EC031FDCB86A4", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["本文展示了利用较少的数据量（主要是地点名称数据）也可以进行***可视化分析与数据分组*** 等操作，其中包含了地点名称数据向地点详细地址、经纬度等信息的转换，以及根据经纬度数据对地点的聚类分组。"]}, {"cell_type": "markdown", "metadata": {"id": "BF4F0C25DDC44753848FF820A99582E8", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["其中***难点*** 便在于***地点名称数据向地点详细地址、经纬度数据的转换***，这个过程中我们可能要调用适当的API来完成这一转换，本文中我们使用的是***geopy***工具包中的地理位置编码与反编码等方法来实现。\n", "\n", "在实际使用过程中，我们发现***geopy***包在对地理位置进行编码时对地点名称数据是有一定要求的，如“优衣库南京西路店”不能直接进行编码，于是我们统一使用“上海市（XX区）XX路”进行编码。"]}, {"cell_type": "markdown", "metadata": {"id": "BB8CDDCC6F2346CC8898014BA5CC545D", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["而由模糊地址向详细地址转换这一步暂时没有找到合适的API，这也是后期我们考虑优化的一个方向。"]}, {"cell_type": "markdown", "metadata": {"id": "EA8F3F0F1B0146378015674B1353197F", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["以上这种数据分组常见于寻找点群对应的城市中心，为门店选址提供依据等。"]}, {"cell_type": "markdown", "metadata": {"id": "C372F8DAD89F4F5A8EE6F79865643175", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2 后期展望"]}, {"cell_type": "markdown", "metadata": {"id": "612D61AAD5FD4AF9B1F17250CD0084A3", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["下面我们来看这样一个实际场景：\n", "\n", "* 知乎某运营部门拟定对居住在北京，且粉丝数10w的大V举行一次线下交流会，考虑到北京全市面积较大且交通不便，决定将此次聚会分散至几个不同的地点进行，假设每个聚会地点人均消费相同，且已有每个人居住地点的数据，那么我们应当如何选取聚会地点，才能使每个人距离聚会地点尽可能近且每个聚会地人数尽可能多呢？\n", "\n", "这就是一个典型的无监督数据分组问题，我们可以考虑使用上述的Kmeans算法来进行聚类。"]}, {"cell_type": "markdown", "metadata": {"id": "4C89590A459748EBB79C8661E034F654", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["1. 由于本文所使用的数据集较少，其中所包含的优衣库门店并非全量数据，所以项目整体更偏向于一种分析思路的阐明，如果我们拥有比较多的数据，预期将会得到一个比较好的分组结果；\n", "\n", "1. 另外Kmeans算法本身存在一些如对异常值较为敏感、数据量大时算法比较复杂等缺点，因此我们也可以考虑使用基于密度的聚类方法对数据来进行分组；\n", "\n", "1. K值的确定：以上即使我们使用了“肘方法”，但由于误差的下降较为平缓，因此选取的K值比较难界定是否最优，实际情形中我们还需要根据具体情况进行处理；\n", "\n", "1. 在计算空间两点距离时我们直接使用了最常见的欧式距离，但这种近似仅适用于经纬度相差不大的点群，当点群之间经纬度相差较大时，我们应该转而使用球面距离。"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "384px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 1}
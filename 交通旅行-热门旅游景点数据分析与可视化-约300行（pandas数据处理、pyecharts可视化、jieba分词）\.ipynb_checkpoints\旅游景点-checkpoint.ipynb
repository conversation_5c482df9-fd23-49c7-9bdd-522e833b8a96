{"cells": [{"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Table of Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#项目介绍\" data-toc-modified-id=\"项目介绍-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>项目介绍</a></span></li><li><span><a href=\"#1.-导入模块\" data-toc-modified-id=\"1.-导入模块-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>1. 导入模块</a></span></li><li><span><a href=\"#2.Pandas数据处理\" data-toc-modified-id=\"2.Pandas数据处理-3\"><span class=\"toc-item-num\">3&nbsp;&nbsp;</span>2.Pandas数据处理</a></span><ul class=\"toc-item\"><li><span><a href=\"#2.1-读取数据\" data-toc-modified-id=\"2.1-读取数据-3.1\"><span class=\"toc-item-num\">3.1&nbsp;&nbsp;</span>2.1 读取数据</a></span></li><li><span><a href=\"#2.2-查看索引、数据类型和内存信息\" data-toc-modified-id=\"2.2-查看索引、数据类型和内存信息-3.2\"><span class=\"toc-item-num\">3.2&nbsp;&nbsp;</span>2.2 查看索引、数据类型和内存信息</a></span></li><li><span><a href=\"#2.3-查看数值型列汇总统计\" data-toc-modified-id=\"2.3-查看数值型列汇总统计-3.3\"><span class=\"toc-item-num\">3.3&nbsp;&nbsp;</span>2.3 查看数值型列汇总统计</a></span></li><li><span><a href=\"#2.4-查看销量为0的行\" data-toc-modified-id=\"2.4-查看销量为0的行-3.4\"><span class=\"toc-item-num\">3.4&nbsp;&nbsp;</span>2.4 查看销量为0的行</a></span></li><li><span><a href=\"#2.5-去除销量为0的行数据\" data-toc-modified-id=\"2.5-去除销量为0的行数据-3.5\"><span class=\"toc-item-num\">3.5&nbsp;&nbsp;</span>2.5 去除销量为0的行数据</a></span></li><li><span><a href=\"#2.6-统计各列空值\" data-toc-modified-id=\"2.6-统计各列空值-3.6\"><span class=\"toc-item-num\">3.6&nbsp;&nbsp;</span>2.6 统计各列空值</a></span></li><li><span><a href=\"#2.7-将星级缺失值用‘未知’填充\" data-toc-modified-id=\"2.7-将星级缺失值用‘未知’填充-3.7\"><span class=\"toc-item-num\">3.7&nbsp;&nbsp;</span>2.7 将星级缺失值用‘未知’填充</a></span></li><li><span><a href=\"#2.8-也可以将所有缺失值都用‘未知’填充\" data-toc-modified-id=\"2.8-也可以将所有缺失值都用‘未知’填充-3.8\"><span class=\"toc-item-num\">3.8&nbsp;&nbsp;</span>2.8 也可以将所有缺失值都用‘未知’填充</a></span></li><li><span><a href=\"#2.9-按销量排序\" data-toc-modified-id=\"2.9-按销量排序-3.9\"><span class=\"toc-item-num\">3.9&nbsp;&nbsp;</span>2.9 按销量排序</a></span></li></ul></li><li><span><a href=\"#3.-Pyecharts数据可视化\" data-toc-modified-id=\"3.-Pyecharts数据可视化-4\"><span class=\"toc-item-num\">4&nbsp;&nbsp;</span>3. Pyecharts数据可视化</a></span><ul class=\"toc-item\"><li><span><a href=\"#3.1-销量前20热门景点数据\" data-toc-modified-id=\"3.1-销量前20热门景点数据-4.1\"><span class=\"toc-item-num\">4.1&nbsp;&nbsp;</span>3.1 销量前20热门景点数据</a></span></li><li><span><a href=\"#3.2-假期出行数据全国地图分布\" data-toc-modified-id=\"3.2-假期出行数据全国地图分布-4.2\"><span class=\"toc-item-num\">4.2&nbsp;&nbsp;</span>3.2 假期出行数据全国地图分布</a></span></li><li><span><a href=\"#3.3-各省市4A-5A景区数量柱状图\" data-toc-modified-id=\"3.3-各省市4A-5A景区数量柱状图-4.3\"><span class=\"toc-item-num\">4.3&nbsp;&nbsp;</span>3.3 各省市4A-5A景区数量柱状图</a></span></li><li><span><a href=\"#3.4-各省市4A-5A景区数量玫瑰图\" data-toc-modified-id=\"3.4-各省市4A-5A景区数量玫瑰图-4.4\"><span class=\"toc-item-num\">4.4&nbsp;&nbsp;</span>3.4 各省市4A-5A景区数量玫瑰图</a></span></li><li><span><a href=\"#3.5-各省市4A-5A景区数量阴影散点图\" data-toc-modified-id=\"3.5-各省市4A-5A景区数量阴影散点图-4.5\"><span class=\"toc-item-num\">4.5&nbsp;&nbsp;</span>3.5 各省市4A-5A景区数量阴影散点图</a></span></li><li><span><a href=\"#3.6-各省市4A-5A景区地图分布\" data-toc-modified-id=\"3.6-各省市4A-5A景区地图分布-4.6\"><span class=\"toc-item-num\">4.6&nbsp;&nbsp;</span>3.6 各省市4A-5A景区地图分布</a></span></li><li><span><a href=\"#3.7-门票价格区间占比玫瑰图\" data-toc-modified-id=\"3.7-门票价格区间占比玫瑰图-4.7\"><span class=\"toc-item-num\">4.7&nbsp;&nbsp;</span>3.7 门票价格区间占比玫瑰图</a></span></li><li><span><a href=\"#3.8-门票价格区间数量散点图\" data-toc-modified-id=\"3.8-门票价格区间数量散点图-4.8\"><span class=\"toc-item-num\">4.8&nbsp;&nbsp;</span>3.8 门票价格区间数量散点图</a></span></li><li><span><a href=\"#3.9-景点简介词云\" data-toc-modified-id=\"3.9-景点简介词云-4.9\"><span class=\"toc-item-num\">4.9&nbsp;&nbsp;</span>3.9 景点简介词云</a></span></li></ul></li><li><span><a href=\"#总结\" data-toc-modified-id=\"总结-5\"><span class=\"toc-item-num\">5&nbsp;&nbsp;</span>总结</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {"id": "E8FF169A027449A6AB12585BB252124E", "jupyter": {}, "mdEditEnable": true, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["# 项目介绍\n", "\n", "本期我们通过去全国各地区景点门票的售卖情况，简单分析一下全国比较热门的景点分布和国民出游情况，看看哪些景点比较受欢迎，希望对小伙伴们有所帮助，如有疑问或者需要改进的地方可以在评论区留言。  \n", "\n", "- 关键词\n", "\n", "pandas数据处理、pyecharts可视化、jieba分词"]}, {"cell_type": "markdown", "metadata": {"id": "E297C894A9784F55A63141CADE03A899", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["# 1. 导入模块  "]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "FA092A6B19024D0F94EE3CF11AB4DB18", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["import jieba\n", "import pandas as pd \n", "from collections import Counter\n", "from pyecharts.charts import Line,Pie,Scatter,Bar,Map,Grid\n", "from pyecharts.charts import WordCloud\n", "from pyecharts import options as opts\n", "from pyecharts.globals import ThemeType\n", "from pyecharts.globals import SymbolType\n", "from pyecharts.commons.utils import JsCode\n", "\n", "from pyecharts.globals import CurrentConfig, NotebookType,OnlineHostType\n", "CurrentConfig.NOTEBOOK_TYPE = NotebookType.JUPYTER_NOTEBOOK"]}, {"cell_type": "markdown", "metadata": {"id": "03382FF7E283464A86920CF4E62F41C5", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["# 2.<PERSON><PERSON>数据处理"]}, {"cell_type": "markdown", "metadata": {"id": "91041FC88B7D42C8856DF5E3C113108E", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2.1 读取数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "EFED20CBA70F46AE87311AC65E090CE3", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>城市</th>\n", "      <th>名称</th>\n", "      <th>星级</th>\n", "      <th>评分</th>\n", "      <th>价格</th>\n", "      <th>销量</th>\n", "      <th>省/市/区</th>\n", "      <th>坐标</th>\n", "      <th>简介</th>\n", "      <th>是否免费</th>\n", "      <th>具体地址</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>上海</td>\n", "      <td>上海迪士尼乐园</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>325.0</td>\n", "      <td>19459</td>\n", "      <td>上海·上海·浦东新区</td>\n", "      <td>121.667917,31.149712</td>\n", "      <td>每个女孩都有一场迪士尼梦</td>\n", "      <td>False</td>\n", "      <td>上海市浦东新区川沙镇黄赵路310号上海迪士尼乐园</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>上海</td>\n", "      <td>上海海昌海洋公园</td>\n", "      <td>4A</td>\n", "      <td>0.0</td>\n", "      <td>276.5</td>\n", "      <td>19406</td>\n", "      <td>上海·上海·浦东新区</td>\n", "      <td>121.915647,30.917713</td>\n", "      <td>看珍稀海洋生物 | 玩超刺激娱乐项目</td>\n", "      <td>False</td>\n", "      <td>上海市浦东新区南汇城银飞路166号</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>上海</td>\n", "      <td>上海野生动物园</td>\n", "      <td>5A</td>\n", "      <td>3.6</td>\n", "      <td>116.0</td>\n", "      <td>6764</td>\n", "      <td>上海·上海·浦东新区</td>\n", "      <td>121.728112,31.059636</td>\n", "      <td>全球动物聚集地 | 零距离和动物做朋友</td>\n", "      <td>False</td>\n", "      <td>上海市浦东新区南六公路178号</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>上海</td>\n", "      <td>东方绿舟</td>\n", "      <td>4A</td>\n", "      <td>3.5</td>\n", "      <td>40.0</td>\n", "      <td>5353</td>\n", "      <td>上海·上海·青浦区</td>\n", "      <td>121.015977,31.107866</td>\n", "      <td>全国首屈一指的青少年校外教育营地</td>\n", "      <td>False</td>\n", "      <td>上海市青浦区沪青平公路6888号</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>上海</td>\n", "      <td>东方明珠</td>\n", "      <td>5A</td>\n", "      <td>3.8</td>\n", "      <td>54.0</td>\n", "      <td>3966</td>\n", "      <td>上海·上海·浦东新区</td>\n", "      <td>121.50626,31.245369</td>\n", "      <td>感受云端漫步，品味老上海风情</td>\n", "      <td>False</td>\n", "      <td>上海市浦东新区世纪大道1号</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   城市        名称   星级   评分     价格     销量       省/市/区                    坐标  \\\n", "0  上海   上海迪士尼乐园  NaN  0.0  325.0  19459  上海·上海·浦东新区  121.667917,31.149712   \n", "1  上海  上海海昌海洋公园   4A  0.0  276.5  19406  上海·上海·浦东新区  121.915647,30.917713   \n", "2  上海   上海野生动物园   5A  3.6  116.0   6764  上海·上海·浦东新区  121.728112,31.059636   \n", "3  上海      东方绿舟   4A  3.5   40.0   5353   上海·上海·青浦区  121.015977,31.107866   \n", "4  上海      东方明珠   5A  3.8   54.0   3966  上海·上海·浦东新区   121.50626,31.245369   \n", "\n", "                    简介   是否免费                      具体地址  \n", "0         每个女孩都有一场迪士尼梦  False  上海市浦东新区川沙镇黄赵路310号上海迪士尼乐园  \n", "1   看珍稀海洋生物 | 玩超刺激娱乐项目  False         上海市浦东新区南汇城银飞路166号  \n", "2  全球动物聚集地 | 零距离和动物做朋友  False           上海市浦东新区南六公路178号  \n", "3     全国首屈一指的青少年校外教育营地  False          上海市青浦区沪青平公路6888号  \n", "4       感受云端漫步，品味老上海风情  False             上海市浦东新区世纪大道1号  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_excel('./旅游景点.xlsx')\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {"id": "F938ED28D0D64C09A8BC371FF95DF6BA", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2.2 查看索引、数据类型和内存信息"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "9A1C77F834EB4581BE4B1BABCF5AADED", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 2443 entries, 0 to 2442\n", "Data columns (total 11 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   城市      2443 non-null   object \n", " 1   名称      2443 non-null   object \n", " 2   星级      913 non-null    object \n", " 3   评分      2443 non-null   float64\n", " 4   价格      2443 non-null   float64\n", " 5   销量      2443 non-null   int64  \n", " 6   省/市/区   2443 non-null   object \n", " 7   坐标      2443 non-null   object \n", " 8   简介      2402 non-null   object \n", " 9   是否免费    2443 non-null   bool   \n", " 10  具体地址    2440 non-null   object \n", "dtypes: bool(1), float64(2), int64(1), object(7)\n", "memory usage: 193.4+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "markdown", "metadata": {"id": "5E3FA6570137497C95A7B35E70010E7F", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2.3 查看数值型列汇总统计"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "DE4983C061D0485FAD8A638192265C23", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>评分</th>\n", "      <th>价格</th>\n", "      <th>销量</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>2443.000000</td>\n", "      <td>2443.000000</td>\n", "      <td>2443.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.683135</td>\n", "      <td>151.780929</td>\n", "      <td>360.652886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>2.012527</td>\n", "      <td>649.170226</td>\n", "      <td>1043.798441</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.000000</td>\n", "      <td>32.000000</td>\n", "      <td>40.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.000000</td>\n", "      <td>62.000000</td>\n", "      <td>90.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3.700000</td>\n", "      <td>120.000000</td>\n", "      <td>270.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>5.000000</td>\n", "      <td>23888.000000</td>\n", "      <td>19459.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                评分            价格            销量\n", "count  2443.000000   2443.000000   2443.000000\n", "mean      1.683135    151.780929    360.652886\n", "std       2.012527    649.170226   1043.798441\n", "min       0.000000      0.000000      0.000000\n", "25%       0.000000     32.000000     40.000000\n", "50%       0.000000     62.000000     90.000000\n", "75%       3.700000    120.000000    270.000000\n", "max       5.000000  23888.000000  19459.000000"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "markdown", "metadata": {"id": "2A7273A39E6D44889A7943D23DEFF1B5", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2.4 查看销量为0的行"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "FC10C73FB8564E74ADCD386658C60D87", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>城市</th>\n", "      <th>名称</th>\n", "      <th>星级</th>\n", "      <th>评分</th>\n", "      <th>价格</th>\n", "      <th>销量</th>\n", "      <th>省/市/区</th>\n", "      <th>坐标</th>\n", "      <th>简介</th>\n", "      <th>是否免费</th>\n", "      <th>具体地址</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>301</th>\n", "      <td>台湾</td>\n", "      <td>复兴桥风景区</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>台湾·桃园</td>\n", "      <td>121.35037883956,24.815040245504</td>\n", "      <td>带动了北横碧水青山的另一种欢乐风情</td>\n", "      <td>False</td>\n", "      <td>桃园县复兴乡中正路15号</td>\n", "    </tr>\n", "    <tr>\n", "      <th>302</th>\n", "      <td>台湾</td>\n", "      <td>长春祠</td>\n", "      <td>NaN</td>\n", "      <td>3.5</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>台湾·花莲</td>\n", "      <td>121.60445032684,24.162302549393</td>\n", "      <td>一道飞瀑分流入溪，山水景色绝佳</td>\n", "      <td>True</td>\n", "      <td>台湾花莲县</td>\n", "    </tr>\n", "    <tr>\n", "      <th>303</th>\n", "      <td>台湾</td>\n", "      <td>淡水老街</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>台湾·淡水</td>\n", "      <td>121.44124069779,25.171297755773</td>\n", "      <td>美食汇聚的北台湾老街之一</td>\n", "      <td>True</td>\n", "      <td>台北县淡水镇中正路</td>\n", "    </tr>\n", "    <tr>\n", "      <th>304</th>\n", "      <td>台湾</td>\n", "      <td>士林官邸</td>\n", "      <td>NaN</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>台湾·台北</td>\n", "      <td>121.53013540214,25.093934468272</td>\n", "      <td>林木参天、绿树幽深，极富神秘色彩</td>\n", "      <td>True</td>\n", "      <td>台北市士林区中山北路五段兴福林路口</td>\n", "    </tr>\n", "    <tr>\n", "      <th>305</th>\n", "      <td>台湾</td>\n", "      <td>北安公园</td>\n", "      <td>NaN</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>台湾·台北</td>\n", "      <td>121.52694244942,25.076889096728</td>\n", "      <td>满园新绿，温雅如诗，连绵一片绿意盎然</td>\n", "      <td>False</td>\n", "      <td>台湾台北市中山北路四段北安路旁</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     城市      名称   星级   评分   价格  销量  省/市/区                               坐标  \\\n", "301  台湾  复兴桥风景区  NaN  0.0  0.0   0  台湾·桃园  121.35037883956,24.815040245504   \n", "302  台湾     长春祠  NaN  3.5  0.0   0  台湾·花莲  121.60445032684,24.162302549393   \n", "303  台湾    淡水老街  NaN  0.0  0.0   0  台湾·淡水  121.44124069779,25.171297755773   \n", "304  台湾    士林官邸  NaN  5.0  0.0   0  台湾·台北  121.53013540214,25.093934468272   \n", "305  台湾    北安公园  NaN  5.0  0.0   0  台湾·台北  121.52694244942,25.076889096728   \n", "\n", "                     简介   是否免费               具体地址  \n", "301   带动了北横碧水青山的另一种欢乐风情  False       桃园县复兴乡中正路15号  \n", "302     一道飞瀑分流入溪，山水景色绝佳   True              台湾花莲县  \n", "303        美食汇聚的北台湾老街之一   True          台北县淡水镇中正路  \n", "304    林木参天、绿树幽深，极富神秘色彩   True  台北市士林区中山北路五段兴福林路口  \n", "305  满园新绿，温雅如诗，连绵一片绿意盎然  False    台湾台北市中山北路四段北安路旁  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[df['销量']==0,:].head()"]}, {"cell_type": "markdown", "metadata": {"id": "5B2C5E4E0A6B4EDD81F4A5463FE557D5", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["一共有123行。"]}, {"cell_type": "markdown", "metadata": {"id": "CCB1E99F409548CFBC87E3E00D9A900D", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2.5 去除销量为0的行数据"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "34AA1739B5264055A6B35B30168940DD", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["(2320, 11)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df[df['销量']!=0] \n", "df.shape"]}, {"cell_type": "markdown", "metadata": {"id": "0E009A7038E74B488CBA5928B0452A0F", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["去除后还剩下2320行数据。"]}, {"cell_type": "markdown", "metadata": {"id": "38B1D5A86828490F8F138E3E37847BE7", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2.6 统计各列空值"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "3AAE68729FB84239803A046E3D475BF3", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["城市          0\n", "名称          0\n", "星级       1407\n", "评分          0\n", "价格          0\n", "销量          0\n", "省/市/区       0\n", "坐标          0\n", "简介         37\n", "是否免费        0\n", "具体地址        2\n", "dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {"id": "0A13A87BD07B4B688420D8374EFC3F50", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["星级存在1407个空值，简介37个空值，具体地址2个空值，其他列不存在空值，数据还算比较完整。"]}, {"cell_type": "markdown", "metadata": {"id": "893925C89CD64E9088B80E57165520C5", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2.7 将星级缺失值用‘未知’填充"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "B9557FC5FC764C2C8219C06A166082ED", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["城市        0\n", "名称        0\n", "星级        0\n", "评分        0\n", "价格        0\n", "销量        0\n", "省/市/区     0\n", "坐标        0\n", "简介       37\n", "是否免费      0\n", "具体地址      2\n", "dtype: int64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df['星级'].fillna('未知', inplace=True)\n", "df.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {"id": "79EAEE2CC12A46C7951E5D7AA86BDD46", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2.8 也可以将所有缺失值都用‘未知’填充"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "4AF9872713754FF9965D304D93B757F5", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["城市       0\n", "名称       0\n", "星级       0\n", "评分       0\n", "价格       0\n", "销量       0\n", "省/市/区    0\n", "坐标       0\n", "简介       0\n", "是否免费     0\n", "具体地址     0\n", "dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df.fillna('未知', inplace=True)\n", "df.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {"id": "2B95E79B168F44658FBBFADFC43D65D6", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 2.9 按销量排序"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "419C7BAA08DF42C08448C18BAC97D265", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>城市</th>\n", "      <th>名称</th>\n", "      <th>星级</th>\n", "      <th>评分</th>\n", "      <th>价格</th>\n", "      <th>销量</th>\n", "      <th>省/市/区</th>\n", "      <th>坐标</th>\n", "      <th>简介</th>\n", "      <th>是否免费</th>\n", "      <th>具体地址</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>上海</td>\n", "      <td>上海迪士尼乐园</td>\n", "      <td>未知</td>\n", "      <td>0.0</td>\n", "      <td>325.0</td>\n", "      <td>19459</td>\n", "      <td>上海·上海·浦东新区</td>\n", "      <td>121.667917,31.149712</td>\n", "      <td>每个女孩都有一场迪士尼梦</td>\n", "      <td>False</td>\n", "      <td>上海市浦东新区川沙镇黄赵路310号上海迪士尼乐园</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>上海</td>\n", "      <td>上海海昌海洋公园</td>\n", "      <td>4A</td>\n", "      <td>0.0</td>\n", "      <td>276.5</td>\n", "      <td>19406</td>\n", "      <td>上海·上海·浦东新区</td>\n", "      <td>121.915647,30.917713</td>\n", "      <td>看珍稀海洋生物 | 玩超刺激娱乐项目</td>\n", "      <td>False</td>\n", "      <td>上海市浦东新区南汇城银飞路166号</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>北京</td>\n", "      <td>故宫</td>\n", "      <td>5A</td>\n", "      <td>5.0</td>\n", "      <td>58.6</td>\n", "      <td>15277</td>\n", "      <td>北京·北京·东城区</td>\n", "      <td>116.403347,39.922148</td>\n", "      <td>世界五大宫之首，穿越与您近在咫尺</td>\n", "      <td>False</td>\n", "      <td>北京市东城区景山前街4号</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2187</th>\n", "      <td>陕西</td>\n", "      <td>秦始皇帝陵博物院（兵马俑）</td>\n", "      <td>5A</td>\n", "      <td>4.4</td>\n", "      <td>120.0</td>\n", "      <td>12714</td>\n", "      <td>陕西·西安·临潼区</td>\n", "      <td>109.266029,34.386024</td>\n", "      <td>世界第八大奇迹</td>\n", "      <td>False</td>\n", "      <td>陕西省西安市临潼县秦始皇陵东1.5公里处</td>\n", "    </tr>\n", "    <tr>\n", "      <th>474</th>\n", "      <td>四川</td>\n", "      <td>成都大熊猫繁育研究基地</td>\n", "      <td>4A</td>\n", "      <td>4.0</td>\n", "      <td>52.0</td>\n", "      <td>9731</td>\n", "      <td>四川·成都·成华区</td>\n", "      <td>104.152603,30.738951</td>\n", "      <td>无关黑与白， 不分胖与瘦， 可爱而又温暖</td>\n", "      <td>False</td>\n", "      <td>四川省成都市成华区外北熊猫大道1375号</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      城市             名称  星级   评分     价格     销量       省/市/区  \\\n", "0     上海        上海迪士尼乐园  未知  0.0  325.0  19459  上海·上海·浦东新区   \n", "1     上海       上海海昌海洋公园  4A  0.0  276.5  19406  上海·上海·浦东新区   \n", "211   北京             故宫  5A  5.0   58.6  15277   北京·北京·东城区   \n", "2187  陕西  秦始皇帝陵博物院（兵马俑）  5A  4.4  120.0  12714   陕西·西安·临潼区   \n", "474   四川    成都大熊猫繁育研究基地  4A  4.0   52.0   9731   四川·成都·成华区   \n", "\n", "                        坐标                    简介   是否免费  \\\n", "0     121.667917,31.149712          每个女孩都有一场迪士尼梦  False   \n", "1     121.915647,30.917713    看珍稀海洋生物 | 玩超刺激娱乐项目  False   \n", "211   116.403347,39.922148      世界五大宫之首，穿越与您近在咫尺  False   \n", "2187  109.266029,34.386024               世界第八大奇迹  False   \n", "474   104.152603,30.738951  无关黑与白， 不分胖与瘦， 可爱而又温暖  False   \n", "\n", "                          具体地址  \n", "0     上海市浦东新区川沙镇黄赵路310号上海迪士尼乐园  \n", "1            上海市浦东新区南汇城银飞路166号  \n", "211               北京市东城区景山前街4号  \n", "2187      陕西省西安市临潼县秦始皇陵东1.5公里处  \n", "474       四川省成都市成华区外北熊猫大道1375号  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df.sort_values('销量', ascending=False).head()"]}, {"cell_type": "markdown", "metadata": {"id": "CF493E19BF2F4022A7B9D30963657A1C", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["# 3. Pyecharts数据可视化"]}, {"cell_type": "markdown", "metadata": {"id": "A53EABB8204D47F38F63ECA8FAAF399C", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 3.1 销量前20热门景点数据"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"hide_input": true, "id": "2234662DFE604FD98FE71DB60F1E056E", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"50cb0d3a19854181b2748a5d934c5364\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts'], function(echarts) {\n", "                var chart_50cb0d3a19854181b2748a5d934c5364 = echarts.init(\n", "                    document.getElementById('50cb0d3a19854181b2748a5d934c5364'), 'white', {renderer: 'canvas'});\n", "                var option_50cb0d3a19854181b2748a5d934c5364 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"bar\",\n", "            \"name\": \"\\u70ed\\u95e8\\u666f\\u70b9\\u9500\\u91cf\",\n", "            \"xAxisIndex\": 0,\n", "            \"yAxisIndex\": 0,\n", "            \"legendHoverLink\": true,\n", "            \"data\": [\n", "                5195,\n", "                5260,\n", "                5300,\n", "                5353,\n", "                5498,\n", "                5702,\n", "                5808,\n", "                5920,\n", "                6005,\n", "                6468,\n", "                6545,\n", "                6764,\n", "                8891,\n", "                9618,\n", "                9633,\n", "                9731,\n", "                12714,\n", "                15277,\n", "                19406,\n", "                19459\n", "            ],\n", "            \"realtimeSort\": false,\n", "            \"showBackground\": false,\n", "            \"stackStrategy\": \"samesign\",\n", "            \"cursor\": \"pointer\",\n", "            \"barMinHeight\": 0,\n", "            \"barCategoryGap\": \"20%\",\n", "            \"barGap\": \"30%\",\n", "            \"large\": false,\n", "            \"largeThreshold\": 400,\n", "            \"seriesLayoutBy\": \"column\",\n", "            \"datasetIndex\": 0,\n", "            \"clip\": true,\n", "            \"zlevel\": 0,\n", "            \"z\": 2,\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"position\": \"right\",\n", "                \"margin\": 8\n", "            },\n", "            \"itemStyle\": {\n", "                \"color\": new echarts.graphic.LinearGradient(0, 0, 1, 0,    [{offset: 0, color: '#009ad6'}, {offset: 1, color: '#ed1941'}], false)\n", "            },\n", "            \"rippleEffect\": {\n", "                \"show\": true,\n", "                \"brushType\": \"stroke\",\n", "                \"scale\": 2.5,\n", "                \"period\": 4\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"\\u70ed\\u95e8\\u666f\\u70b9\\u9500\\u91cf\"\n", "            ],\n", "            \"selected\": {\n", "                \"\\u70ed\\u95e8\\u666f\\u70b9\\u9500\\u91cf\": true\n", "            },\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"xAxis\": [\n", "        {\n", "            \"name\": \"\\u9500\\u91cf\",\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            }\n", "        }\n", "    ],\n", "    \"yAxis\": [\n", "        {\n", "            \"name\": \"\\u666f\\u70b9\\u540d\\u79f0\",\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"data\": [\n", "                \"\\u957f\\u6068\\u6b4c\",\n", "                \"\\u606d\\u738b\\u5e9c\",\n", "                \"\\u5929\\u575b\\u516c\\u56ed\",\n", "                \"\\u4e1c\\u65b9\\u7eff\\u821f\",\n", "                \"\\u5317\\u4eac\\u91ce\\u751f\\u52a8\\u7269\\u56ed\",\n", "                \"\\u534e\\u6e05\\u5bab\",\n", "                \"\\u534e\\u5c71\\u666f\\u533a\",\n", "                \"\\u5357\\u4eac\\u603b\\u7edf\\u5e9c\",\n", "                \"\\u7626\\u897f\\u6e56\",\n", "                \"\\u4e03\\u5f69\\u4e91\\u5357\\u6b22\\u4e50\\u4e16\\u754c\",\n", "                \"\\u73e0\\u6d77\\u957f\\u9686\\u6d77\\u6d0b\\u738b\\u56fd\",\n", "                \"\\u4e0a\\u6d77\\u91ce\\u751f\\u52a8\\u7269\\u56ed\",\n", "                \"\\u957f\\u9686\\u91ce\\u751f\\u52a8\\u7269\\u4e16\\u754c\",\n", "                \"\\u516b\\u8fbe\\u5cad\\u957f\\u57ce\",\n", "                \"\\u9890\\u548c\\u56ed\",\n", "                \"\\u6210\\u90fd\\u5927\\u718a\\u732b\\u7e41\\u80b2\\u7814\\u7a76\\u57fa\\u5730\",\n", "                \"\\u79e6\\u59cb\\u7687\\u5e1d\\u9675\\u535a\\u7269\\u9662\\uff08\\u5175\\u9a6c\\u4fd1\\uff09\",\n", "                \"\\u6545\\u5bab\",\n", "                \"\\u4e0a\\u6d77\\u6d77\\u660c\\u6d77\\u6d0b\\u516c\\u56ed\",\n", "                \"\\u4e0a\\u6d77\\u8fea\\u58eb\\u5c3c\\u4e50\\u56ed\"\n", "            ]\n", "        }\n", "    ],\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u70ed\\u95e8\\u666f\\u70b9\\u9500\\u91cf\\u6570\\u636e\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ],\n", "    \"grid\": [\n", "        {\n", "            \"show\": false,\n", "            \"zlevel\": 0,\n", "            \"z\": 2,\n", "            \"left\": \"20%\",\n", "            \"right\": \"5%\",\n", "            \"containLabel\": false,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"shadowOffsetX\": 0,\n", "            \"shadowOffsetY\": 0\n", "        }\n", "    ]\n", "};\n", "                chart_50cb0d3a19854181b2748a5d934c5364.setOption(option_50cb0d3a19854181b2748a5d934c5364);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x2654665fd00>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 线性渐变\n", "color_js = \"\"\"new echarts.graphic.LinearGradient(0, 0, 1, 0,\n", "    [{offset: 0, color: '#009ad6'}, {offset: 1, color: '#ed1941'}], false)\"\"\"\n", "\n", "\n", "sort_info = df.sort_values(by='销量', ascending=True)\n", "b1 = (\n", "    Bar()\n", "    .add_xaxis(list(sort_info['名称'])[-20:])\n", "    .add_yaxis('热门景点销量', sort_info['销量'].values.tolist()[-20:],itemstyle_opts=opts.ItemStyleOpts(color=JsCode(color_js)))\n", "    .reversal_axis()\n", "    .set_global_opts(\n", "        title_opts=opts.TitleOpts(title='热门景点销量数据'),\n", "        yaxis_opts=opts.AxisOpts(name='景点名称'),\n", "        xaxis_opts=opts.AxisOpts(name='销量'),\n", "        )\n", "    .set_series_opts(label_opts=opts.LabelOpts(position=\"right\"))\n", "\n", ")\n", "# 将图形整体右移\n", "g1 = (\n", "    Grid()\n", "        .add(b1, grid_opts=opts.GridOpts(pos_left='20%', pos_right='5%'))  \n", ")\n", "g1.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "9E79713474E642B8B45B487AAA588B9F", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 3.2 假期出行数据全国地图分布"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"scrolled": true}, "outputs": [], "source": ["dictcode = {'北京': '北京市',\n", " '天津': '天津市',\n", " '河北': '河北省',\n", " '山西': '山西省',\n", " '内蒙古': '内蒙古自治区',\n", " '辽宁': '辽宁省',\n", " '吉林': '吉林省',\n", " '黑龙江': '黑龙江省',\n", " '上海': '上海市',\n", " '江苏': '江苏省',\n", " '浙江': '浙江省',\n", " '安徽': '安徽省',\n", " '福建': '福建省',\n", " '江西': '江西省',\n", " '山东': '山东省',\n", " '河南': '河南省',\n", " '湖北': '湖北省',\n", " '湖南': '湖南省',\n", " '广东': '广东省',\n", " '广西': '广西壮族自治区',\n", " '海南': '海南省',\n", " '重庆': '重庆市',\n", " '四川': '四川省',\n", " '贵州': '贵州省',\n", " '云南': '云南省',\n", " '西藏': '西藏自治区',\n", " '陕西': '陕西省',\n", " '甘肃': '甘肃省',\n", " '青海': '青海省',\n", " '宁夏': '宁夏回族自治区',\n", " '新疆': '新疆维吾尔自治区',\n", "'台湾':'台湾',\n", " '香港':'香港',\n", " '澳门':'澳门'}"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"hide_input": true, "id": "A45D19EE7D7747B585372EAF3F0DFAC4", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min', 'china':'https://assets.pyecharts.org/assets/v5/maps/china'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"842cf82b0b6b43a08cef69ac0eeb040c\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts', 'china'], function(echarts) {\n", "                var chart_842cf82b0b6b43a08cef69ac0eeb040c = echarts.init(\n", "                    document.getElementById('842cf82b0b6b43a08cef69ac0eeb040c'), 'white', {renderer: 'canvas'});\n", "                var option_842cf82b0b6b43a08cef69ac0eeb040c = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"map\",\n", "            \"name\": \"\\u5047\\u671f\\u51fa\\u884c\\u5206\\u5e03\",\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"margin\": 8\n", "            },\n", "            \"map\": \"china\",\n", "            \"data\": [\n", "                {\n", "                    \"name\": \"\\u4e0a\\u6d77\\u5e02\",\n", "                    \"value\": [\n", "                        84084\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e91\\u5357\\u7701\",\n", "                    \"value\": [\n", "                        28056\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5185\\u8499\\u53e4\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": [\n", "                        3959\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5317\\u4eac\\u5e02\",\n", "                    \"value\": [\n", "                        93987\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u53f0\\u6e7e\",\n", "                    \"value\": [\n", "                        1001\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5409\\u6797\\u7701\",\n", "                    \"value\": [\n", "                        3772\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u56db\\u5ddd\\u7701\",\n", "                    \"value\": [\n", "                        65052\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5929\\u6d25\\u5e02\",\n", "                    \"value\": [\n", "                        5254\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5b81\\u590f\\u56de\\u65cf\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": [\n", "                        5622\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5b89\\u5fbd\\u7701\",\n", "                    \"value\": [\n", "                        21027\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c71\\u4e1c\\u7701\",\n", "                    \"value\": [\n", "                        32147\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c71\\u897f\\u7701\",\n", "                    \"value\": [\n", "                        15904\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5e7f\\u4e1c\\u7701\",\n", "                    \"value\": [\n", "                        62757\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u5e7f\\u897f\\u58ee\\u65cf\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": [\n", "                        37946\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u65b0\\u7586\\u7ef4\\u543e\\u5c14\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": [\n", "                        3614\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c5f\\u82cf\\u7701\",\n", "                    \"value\": [\n", "                        80783\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c5f\\u897f\\u7701\",\n", "                    \"value\": [\n", "                        11046\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6cb3\\u5317\\u7701\",\n", "                    \"value\": [\n", "                        6826\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6cb3\\u5357\\u7701\",\n", "                    \"value\": [\n", "                        33776\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6d59\\u6c5f\\u7701\",\n", "                    \"value\": [\n", "                        45481\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6d77\\u5357\\u7701\",\n", "                    \"value\": [\n", "                        44123\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e56\\u5317\\u7701\",\n", "                    \"value\": [\n", "                        22563\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e56\\u5357\\u7701\",\n", "                    \"value\": [\n", "                        6980\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u6fb3\\u95e8\",\n", "                    \"value\": [\n", "                        3128\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u7518\\u8083\\u7701\",\n", "                    \"value\": [\n", "                        4338\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u798f\\u5efa\\u7701\",\n", "                    \"value\": [\n", "                        23256\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u897f\\u85cf\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": [\n", "                        7028\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u8d35\\u5dde\\u7701\",\n", "                    \"value\": [\n", "                        22499\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u8fbd\\u5b81\\u7701\",\n", "                    \"value\": [\n", "                        10423\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u91cd\\u5e86\\u5e02\",\n", "                    \"value\": [\n", "                        20054\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u9655\\u897f\\u7701\",\n", "                    \"value\": [\n", "                        64353\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u9752\\u6d77\\u7701\",\n", "                    \"value\": [\n", "                        4591\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u9999\\u6e2f\",\n", "                    \"value\": [\n", "                        1006\n", "                    ]\n", "                },\n", "                {\n", "                    \"name\": \"\\u9ed1\\u9f99\\u6c5f\\u7701\",\n", "                    \"value\": [\n", "                        4639\n", "                    ]\n", "                }\n", "            ],\n", "            \"roam\": true,\n", "            \"aspectScale\": 0.75,\n", "            \"nameProperty\": \"name\",\n", "            \"selectedMode\": false,\n", "            \"zoom\": 1,\n", "            \"mapValueCalculation\": \"sum\",\n", "            \"showLegendSymbol\": true,\n", "            \"emphasis\": {}\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"\\u5047\\u671f\\u51fa\\u884c\\u5206\\u5e03\"\n", "            ],\n", "            \"selected\": {\n", "                \"\\u5047\\u671f\\u51fa\\u884c\\u5206\\u5e03\": true\n", "            },\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u5047\\u671f\\u51fa\\u884c\\u6570\\u636e\\u5730\\u56fe\\u5206\\u5e03\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ],\n", "    \"visualMap\": {\n", "        \"show\": true,\n", "        \"type\": \"continuous\",\n", "        \"min\": 0,\n", "        \"max\": 100000,\n", "        \"inRange\": {\n", "            \"color\": [\n", "                \"white\",\n", "                \"#fa8072\",\n", "                \"#ed1941\"\n", "            ]\n", "        },\n", "        \"calculable\": true,\n", "        \"inverse\": false,\n", "        \"splitNumber\": 5,\n", "        \"hoverLink\": true,\n", "        \"orient\": \"vertical\",\n", "        \"padding\": 5,\n", "        \"showLabel\": true,\n", "        \"itemWidth\": 20,\n", "        \"itemHeight\": 140,\n", "        \"borderWidth\": 0\n", "    }\n", "};\n", "                chart_842cf82b0b6b43a08cef69ac0eeb040c.setOption(option_842cf82b0b6b43a08cef69ac0eeb040c);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x26546987f40>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df_tmp1 = df[['城市','销量']]\n", "df_counts = df_tmp1.groupby('城市').sum()\n", "m1 = (\n", "        Map()\n", "        .add('假期出行分布', [list(z) for z in zip([dictcode[x] for x in df_counts.index.values.tolist() ], df_counts.values.tolist())], 'china')\n", "        .set_global_opts(\n", "        title_opts=opts.TitleOpts(title='假期出行数据地图分布'),\n", "        visualmap_opts=opts.VisualMapOpts(max_=100000, is_piecewise=False,range_color=[\"white\", \"#fa8072\", \"#ed1941\"]),\n", "        )\n", "    )\n", "m1.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "6D2C936FC70948479A50BC99BB9E8FC3", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["**华东、华南、华中**等地区属于国民出游热点地区，尤其是**北京、上海、江苏、广东、四川、陕西**等地区出行比较密集。  \n", "\n", "## 3.3 各省市4A-5A景区数量柱状图"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"hide_input": true, "id": "006C1643C3F84F928FB35A8C581A02C0", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"691ebc19a37642ae97ecc20c96aedfa7\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts'], function(echarts) {\n", "                var chart_691ebc19a37642ae97ecc20c96aedfa7 = echarts.init(\n", "                    document.getElementById('691ebc19a37642ae97ecc20c96aedfa7'), 'white', {renderer: 'canvas'});\n", "                var option_691ebc19a37642ae97ecc20c96aedfa7 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"bar\",\n", "            \"name\": \"4A-5A\\u666f\\u533a\\u6570\\u91cf\",\n", "            \"legendHoverLink\": true,\n", "            \"data\": [\n", "                25,\n", "                31,\n", "                23,\n", "                38,\n", "                10,\n", "                32,\n", "                18,\n", "                18,\n", "                47,\n", "                30,\n", "                36,\n", "                33,\n", "                27,\n", "                15,\n", "                47,\n", "                34,\n", "                36,\n", "                39,\n", "                29,\n", "                24,\n", "                37,\n", "                14,\n", "                19,\n", "                19,\n", "                7,\n", "                18,\n", "                23,\n", "                33,\n", "                31,\n", "                18,\n", "                17\n", "            ],\n", "            \"realtimeSort\": false,\n", "            \"showBackground\": false,\n", "            \"stackStrategy\": \"samesign\",\n", "            \"cursor\": \"pointer\",\n", "            \"barMinHeight\": 0,\n", "            \"barCategoryGap\": \"20%\",\n", "            \"barGap\": \"30%\",\n", "            \"large\": false,\n", "            \"largeThreshold\": 400,\n", "            \"seriesLayoutBy\": \"column\",\n", "            \"datasetIndex\": 0,\n", "            \"clip\": true,\n", "            \"zlevel\": 0,\n", "            \"z\": 2,\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"margin\": 8\n", "            },\n", "            \"itemStyle\": {\n", "                \"color\": new echarts.graphic.LinearGradient(0, 1, 0, 0,    [{offset: 0, color: '#009ad6'}, {offset: 1, color: '#ed1941'}], false)\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"4A-5A\\u666f\\u533a\\u6570\\u91cf\"\n", "            ],\n", "            \"selected\": {\n", "                \"4A-5A\\u666f\\u533a\\u6570\\u91cf\": true\n", "            },\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"xAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"data\": [\n", "                \"\\u4e0a\\u6d77\",\n", "                \"\\u4e91\\u5357\",\n", "                \"\\u5185\\u8499\\u53e4\",\n", "                \"\\u5317\\u4eac\",\n", "                \"\\u5409\\u6797\",\n", "                \"\\u56db\\u5ddd\",\n", "                \"\\u5929\\u6d25\",\n", "                \"\\u5b81\\u590f\",\n", "                \"\\u5b89\\u5fbd\",\n", "                \"\\u5c71\\u4e1c\",\n", "                \"\\u5c71\\u897f\",\n", "                \"\\u5e7f\\u4e1c\",\n", "                \"\\u5e7f\\u897f\",\n", "                \"\\u65b0\\u7586\",\n", "                \"\\u6c5f\\u82cf\",\n", "                \"\\u6c5f\\u897f\",\n", "                \"\\u6cb3\\u5317\",\n", "                \"\\u6cb3\\u5357\",\n", "                \"\\u6d59\\u6c5f\",\n", "                \"\\u6d77\\u5357\",\n", "                \"\\u6e56\\u5317\",\n", "                \"\\u6e56\\u5357\",\n", "                \"\\u7518\\u8083\",\n", "                \"\\u798f\\u5efa\",\n", "                \"\\u897f\\u85cf\",\n", "                \"\\u8d35\\u5dde\",\n", "                \"\\u8fbd\\u5b81\",\n", "                \"\\u91cd\\u5e86\",\n", "                \"\\u9655\\u897f\",\n", "                \"\\u9752\\u6d77\",\n", "                \"\\u9ed1\\u9f99\\u6c5f\"\n", "            ]\n", "        }\n", "    ],\n", "    \"yAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            }\n", "        }\n", "    ],\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u5404\\u7701\\u5e024A-5A\\u666f\\u533a\\u6570\\u91cf\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ],\n", "    \"dataZoom\": [\n", "        {\n", "            \"show\": true,\n", "            \"type\": \"slider\",\n", "            \"showDetail\": true,\n", "            \"showDataShadow\": true,\n", "            \"realtime\": true,\n", "            \"start\": 20,\n", "            \"end\": 80,\n", "            \"orient\": \"horizontal\",\n", "            \"zoomLock\": false,\n", "            \"filterMode\": \"filter\"\n", "        },\n", "        {\n", "            \"show\": true,\n", "            \"type\": \"inside\",\n", "            \"showDetail\": true,\n", "            \"showDataShadow\": true,\n", "            \"realtime\": true,\n", "            \"start\": 20,\n", "            \"end\": 80,\n", "            \"orient\": \"horizontal\",\n", "            \"zoomLock\": false,\n", "            \"filterMode\": \"filter\"\n", "        }\n", "    ]\n", "};\n", "                chart_691ebc19a37642ae97ecc20c96aedfa7.setOption(option_691ebc19a37642ae97ecc20c96aedfa7);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x2654664c670>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# 线性渐变\n", "color_js = \"\"\"new echarts.graphic.LinearGradient(0, 1, 0, 0,\n", "    [{offset: 0, color: '#009ad6'}, {offset: 1, color: '#ed1941'}], false)\"\"\" \n", "\n", "df_tmp2 = df[df['星级'].isin(['4A', '5A'])]\n", "df_counts = df_tmp2.groupby('城市').count()['星级']\n", "b2 = (\n", "        Bar()\n", "            .add_xaxis(df_counts.index.values.tolist())\n", "            .add_yaxis('4A-5A景区数量', df_counts.values.tolist(),itemstyle_opts=opts.ItemStyleOpts(color=JsCode(color_js)))\n", "            .set_global_opts(\n", "            title_opts=opts.TitleOpts(title='各省市4A-5A景区数量'),\n", "            datazoom_opts=[opts.DataZoomOpts(), opts.DataZoomOpts(type_='inside')],\n", "        )\n", "    )\n", "b2.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "8A7747CCF5E944139712945EA05D50A8", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 3.4 各省市4A-5A景区数量玫瑰图"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"hide_input": true, "id": "312D5065C1CD4E2FB5DDEA9D53A39558", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"3513fb08a3534d9994c8e1d3456e295d\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts'], function(echarts) {\n", "                var chart_3513fb08a3534d9994c8e1d3456e295d = echarts.init(\n", "                    document.getElementById('3513fb08a3534d9994c8e1d3456e295d'), 'white', {renderer: 'canvas'});\n", "                var option_3513fb08a3534d9994c8e1d3456e295d = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"pie\",\n", "            \"colorBy\": \"data\",\n", "            \"legendHoverLink\": true,\n", "            \"selectedMode\": false,\n", "            \"selectedOffset\": 10,\n", "            \"clockwise\": true,\n", "            \"startAngle\": 90,\n", "            \"minAngle\": 0,\n", "            \"minShowLabelAngle\": 0,\n", "            \"avoidLabelOverlap\": true,\n", "            \"stillShowZeroSum\": true,\n", "            \"percentPrecision\": 2,\n", "            \"showEmptyCircle\": true,\n", "            \"emptyCircleStyle\": {\n", "                \"color\": \"lightgray\",\n", "                \"borderColor\": \"#000\",\n", "                \"borderWidth\": 0,\n", "                \"borderType\": \"solid\",\n", "                \"borderDashOffset\": 0,\n", "                \"borderCap\": \"butt\",\n", "                \"borderJoin\": \"bevel\",\n", "                \"borderMiterLimit\": 10,\n", "                \"opacity\": 1\n", "            },\n", "            \"data\": [\n", "                {\n", "                    \"name\": \"\\u5b89\\u5fbd\",\n", "                    \"value\": 47\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c5f\\u82cf\",\n", "                    \"value\": 47\n", "                },\n", "                {\n", "                    \"name\": \"\\u6cb3\\u5357\",\n", "                    \"value\": 39\n", "                },\n", "                {\n", "                    \"name\": \"\\u5317\\u4eac\",\n", "                    \"value\": 38\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e56\\u5317\",\n", "                    \"value\": 37\n", "                },\n", "                {\n", "                    \"name\": \"\\u6cb3\\u5317\",\n", "                    \"value\": 36\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c71\\u897f\",\n", "                    \"value\": 36\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c5f\\u897f\",\n", "                    \"value\": 34\n", "                },\n", "                {\n", "                    \"name\": \"\\u91cd\\u5e86\",\n", "                    \"value\": 33\n", "                },\n", "                {\n", "                    \"name\": \"\\u5e7f\\u4e1c\",\n", "                    \"value\": 33\n", "                },\n", "                {\n", "                    \"name\": \"\\u56db\\u5ddd\",\n", "                    \"value\": 32\n", "                },\n", "                {\n", "                    \"name\": \"\\u9655\\u897f\",\n", "                    \"value\": 31\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e91\\u5357\",\n", "                    \"value\": 31\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c71\\u4e1c\",\n", "                    \"value\": 30\n", "                },\n", "                {\n", "                    \"name\": \"\\u6d59\\u6c5f\",\n", "                    \"value\": 29\n", "                },\n", "                {\n", "                    \"name\": \"\\u5e7f\\u897f\",\n", "                    \"value\": 27\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e0a\\u6d77\",\n", "                    \"value\": 25\n", "                },\n", "                {\n", "                    \"name\": \"\\u6d77\\u5357\",\n", "                    \"value\": 24\n", "                },\n", "                {\n", "                    \"name\": \"\\u8fbd\\u5b81\",\n", "                    \"value\": 23\n", "                },\n", "                {\n", "                    \"name\": \"\\u5185\\u8499\\u53e4\",\n", "                    \"value\": 23\n", "                },\n", "                {\n", "                    \"name\": \"\\u7518\\u8083\",\n", "                    \"value\": 19\n", "                },\n", "                {\n", "                    \"name\": \"\\u798f\\u5efa\",\n", "                    \"value\": 19\n", "                },\n", "                {\n", "                    \"name\": \"\\u5b81\\u590f\",\n", "                    \"value\": 18\n", "                },\n", "                {\n", "                    \"name\": \"\\u5929\\u6d25\",\n", "                    \"value\": 18\n", "                },\n", "                {\n", "                    \"name\": \"\\u8d35\\u5dde\",\n", "                    \"value\": 18\n", "                },\n", "                {\n", "                    \"name\": \"\\u9752\\u6d77\",\n", "                    \"value\": 18\n", "                },\n", "                {\n", "                    \"name\": \"\\u9ed1\\u9f99\\u6c5f\",\n", "                    \"value\": 17\n", "                },\n", "                {\n", "                    \"name\": \"\\u65b0\\u7586\",\n", "                    \"value\": 15\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e56\\u5357\",\n", "                    \"value\": 14\n", "                },\n", "                {\n", "                    \"name\": \"\\u5409\\u6797\",\n", "                    \"value\": 10\n", "                },\n", "                {\n", "                    \"name\": \"\\u897f\\u85cf\",\n", "                    \"value\": 7\n", "                }\n", "            ],\n", "            \"radius\": [\n", "                \"30%\",\n", "                \"100%\"\n", "            ],\n", "            \"center\": [\n", "                \"50%\",\n", "                \"60%\"\n", "            ],\n", "            \"roseType\": \"area\",\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"position\": \"inside\",\n", "                \"margin\": 8,\n", "                \"fontSize\": 12,\n", "                \"fontStyle\": \"italic\",\n", "                \"fontWeight\": \"bold\",\n", "                \"fontFamily\": \"Microsoft YaHei\",\n", "                \"formatter\": \"{b}: {c}\"\n", "            },\n", "            \"labelLine\": {\n", "                \"show\": true,\n", "                \"showAbove\": false,\n", "                \"length\": 15,\n", "                \"length2\": 15,\n", "                \"smooth\": false,\n", "                \"minTurnAngle\": 90,\n", "                \"maxSurfaceAngle\": 90\n", "            },\n", "            \"rippleEffect\": {\n", "                \"show\": true,\n", "                \"brushType\": \"stroke\",\n", "                \"scale\": 2.5,\n", "                \"period\": 4\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"\\u5b89\\u5fbd\",\n", "                \"\\u6c5f\\u82cf\",\n", "                \"\\u6cb3\\u5357\",\n", "                \"\\u5317\\u4eac\",\n", "                \"\\u6e56\\u5317\",\n", "                \"\\u6cb3\\u5317\",\n", "                \"\\u5c71\\u897f\",\n", "                \"\\u6c5f\\u897f\",\n", "                \"\\u91cd\\u5e86\",\n", "                \"\\u5e7f\\u4e1c\",\n", "                \"\\u56db\\u5ddd\",\n", "                \"\\u9655\\u897f\",\n", "                \"\\u4e91\\u5357\",\n", "                \"\\u5c71\\u4e1c\",\n", "                \"\\u6d59\\u6c5f\",\n", "                \"\\u5e7f\\u897f\",\n", "                \"\\u4e0a\\u6d77\",\n", "                \"\\u6d77\\u5357\",\n", "                \"\\u8fbd\\u5b81\",\n", "                \"\\u5185\\u8499\\u53e4\",\n", "                \"\\u7518\\u8083\",\n", "                \"\\u798f\\u5efa\",\n", "                \"\\u5b81\\u590f\",\n", "                \"\\u5929\\u6d25\",\n", "                \"\\u8d35\\u5dde\",\n", "                \"\\u9752\\u6d77\",\n", "                \"\\u9ed1\\u9f99\\u6c5f\",\n", "                \"\\u65b0\\u7586\",\n", "                \"\\u6e56\\u5357\",\n", "                \"\\u5409\\u6797\",\n", "                \"\\u897f\\u85cf\"\n", "            ],\n", "            \"selected\": {},\n", "            \"show\": false,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u5730\\u533a\\u666f\\u70b9\\u6570\\u91cf\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ],\n", "    \"toolbox\": {\n", "        \"show\": true,\n", "        \"orient\": \"horizontal\",\n", "        \"itemSize\": 15,\n", "        \"itemGap\": 10,\n", "        \"left\": \"80%\",\n", "        \"feature\": {\n", "            \"saveAsImage\": {\n", "                \"type\": \"png\",\n", "                \"backgroundColor\": \"auto\",\n", "                \"connectedBackgroundColor\": \"#fff\",\n", "                \"show\": true,\n", "                \"title\": \"\\u4fdd\\u5b58\\u4e3a\\u56fe\\u7247\",\n", "                \"pixelRatio\": 1\n", "            },\n", "            \"restore\": {\n", "                \"show\": true,\n", "                \"title\": \"\\u8fd8\\u539f\"\n", "            },\n", "            \"dataView\": {\n", "                \"show\": true,\n", "                \"title\": \"\\u6570\\u636e\\u89c6\\u56fe\",\n", "                \"readOnly\": false,\n", "                \"lang\": [\n", "                    \"\\u6570\\u636e\\u89c6\\u56fe\",\n", "                    \"\\u5173\\u95ed\",\n", "                    \"\\u5237\\u65b0\"\n", "                ],\n", "                \"backgroundColor\": \"#fff\",\n", "                \"textareaColor\": \"#fff\",\n", "                \"textareaBorderColor\": \"#333\",\n", "                \"textColor\": \"#000\",\n", "                \"buttonColor\": \"#c23531\",\n", "                \"buttonTextColor\": \"#fff\"\n", "            },\n", "            \"dataZoom\": {\n", "                \"show\": true,\n", "                \"title\": {\n", "                    \"zoom\": \"\\u533a\\u57df\\u7f29\\u653e\",\n", "                    \"back\": \"\\u533a\\u57df\\u7f29\\u653e\\u8fd8\\u539f\"\n", "                },\n", "                \"icon\": {},\n", "                \"filterMode\": \"filter\"\n", "            },\n", "            \"magicType\": {\n", "                \"show\": true,\n", "                \"type\": [\n", "                    \"line\",\n", "                    \"bar\",\n", "                    \"stack\",\n", "                    \"tiled\"\n", "                ],\n", "                \"title\": {\n", "                    \"line\": \"\\u5207\\u6362\\u4e3a\\u6298\\u7ebf\\u56fe\",\n", "                    \"bar\": \"\\u5207\\u6362\\u4e3a\\u67f1\\u72b6\\u56fe\",\n", "                    \"stack\": \"\\u5207\\u6362\\u4e3a\\u5806\\u53e0\",\n", "                    \"tiled\": \"\\u5207\\u6362\\u4e3a\\u5e73\\u94fa\"\n", "                },\n", "                \"icon\": {}\n", "            }\n", "        }\n", "    }\n", "};\n", "                chart_3513fb08a3534d9994c8e1d3456e295d.setOption(option_3513fb08a3534d9994c8e1d3456e295d);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x26546940340>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df0 = df_counts.copy()\n", "df0.sort_values(ascending=False, inplace=True)\n", "c1 = (\n", "    Pie()\n", "    .add('', [list(z) for z in zip(df0.index.values.tolist(), df0.values.tolist())],\n", "         radius=['30%', '100%'],\n", "         center=['50%', '60%'],\n", "         rosetype='area',\n", "         )\n", "    .set_global_opts(title_opts=opts.TitleOpts(title='地区景点数量'),\n", "                     legend_opts=opts.LegendOpts(is_show=False),\n", "                     toolbox_opts=opts.ToolboxOpts())\n", "    .set_series_opts(label_opts=opts.LabelOpts(is_show=True, position='inside', font_size=12,\n", "                                               formatter='{b}: {c}', font_style='italic',\n", "                                               font_weight='bold', font_family='Microsoft YaHei'\n", "                                               ))\n", ")\n", "c1.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "DAE41EAF8FDE498E8D5A16248A075EE7", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 3.5 各省市4A-5A景区数量阴影散点图"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"hide_input": true, "id": "30FC8ACECB5E414DAAB0A4279D420E63", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"1581598aafbb48529eed4afc5d126505\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts'], function(echarts) {\n", "                var chart_1581598aafbb48529eed4afc5d126505 = echarts.init(\n", "                    document.getElementById('1581598aafbb48529eed4afc5d126505'), 'white', {renderer: 'canvas'});\n", "                var option_1581598aafbb48529eed4afc5d126505 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"scatter\",\n", "            \"name\": \"4A-5A\\u666f\\u533a\\u6570\\u91cf\",\n", "            \"symbolSize\": 50,\n", "            \"data\": [\n", "                [\n", "                    \"\\u4e0a\\u6d77\",\n", "                    25\n", "                ],\n", "                [\n", "                    \"\\u4e91\\u5357\",\n", "                    31\n", "                ],\n", "                [\n", "                    \"\\u5185\\u8499\\u53e4\",\n", "                    23\n", "                ],\n", "                [\n", "                    \"\\u5317\\u4eac\",\n", "                    38\n", "                ],\n", "                [\n", "                    \"\\u5409\\u6797\",\n", "                    10\n", "                ],\n", "                [\n", "                    \"\\u56db\\u5ddd\",\n", "                    32\n", "                ],\n", "                [\n", "                    \"\\u5929\\u6d25\",\n", "                    18\n", "                ],\n", "                [\n", "                    \"\\u5b81\\u590f\",\n", "                    18\n", "                ],\n", "                [\n", "                    \"\\u5b89\\u5fbd\",\n", "                    47\n", "                ],\n", "                [\n", "                    \"\\u5c71\\u4e1c\",\n", "                    30\n", "                ],\n", "                [\n", "                    \"\\u5c71\\u897f\",\n", "                    36\n", "                ],\n", "                [\n", "                    \"\\u5e7f\\u4e1c\",\n", "                    33\n", "                ],\n", "                [\n", "                    \"\\u5e7f\\u897f\",\n", "                    27\n", "                ],\n", "                [\n", "                    \"\\u65b0\\u7586\",\n", "                    15\n", "                ],\n", "                [\n", "                    \"\\u6c5f\\u82cf\",\n", "                    47\n", "                ],\n", "                [\n", "                    \"\\u6c5f\\u897f\",\n", "                    34\n", "                ],\n", "                [\n", "                    \"\\u6cb3\\u5317\",\n", "                    36\n", "                ],\n", "                [\n", "                    \"\\u6cb3\\u5357\",\n", "                    39\n", "                ],\n", "                [\n", "                    \"\\u6d59\\u6c5f\",\n", "                    29\n", "                ],\n", "                [\n", "                    \"\\u6d77\\u5357\",\n", "                    24\n", "                ],\n", "                [\n", "                    \"\\u6e56\\u5317\",\n", "                    37\n", "                ],\n", "                [\n", "                    \"\\u6e56\\u5357\",\n", "                    14\n", "                ],\n", "                [\n", "                    \"\\u7518\\u8083\",\n", "                    19\n", "                ],\n", "                [\n", "                    \"\\u798f\\u5efa\",\n", "                    19\n", "                ],\n", "                [\n", "                    \"\\u897f\\u85cf\",\n", "                    7\n", "                ],\n", "                [\n", "                    \"\\u8d35\\u5dde\",\n", "                    18\n", "                ],\n", "                [\n", "                    \"\\u8fbd\\u5b81\",\n", "                    23\n", "                ],\n", "                [\n", "                    \"\\u91cd\\u5e86\",\n", "                    33\n", "                ],\n", "                [\n", "                    \"\\u9655\\u897f\",\n", "                    31\n", "                ],\n", "                [\n", "                    \"\\u9752\\u6d77\",\n", "                    18\n", "                ],\n", "                [\n", "                    \"\\u9ed1\\u9f99\\u6c5f\",\n", "                    17\n", "                ]\n", "            ],\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"position\": \"right\",\n", "                \"margin\": 8\n", "            },\n", "            \"itemStyle\": {\n", "                \"normal\": {\n", "                    \"shadowColor\": \"#000000\",\n", "                    \"shadowBlur\": 20,\n", "                    \"shadowOffsetX\": 5,\n", "                    \"shadowOffsetY\": 15\n", "                }\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"4A-5A\\u666f\\u533a\\u6570\\u91cf\"\n", "            ],\n", "            \"selected\": {\n", "                \"4A-5A\\u666f\\u533a\\u6570\\u91cf\": true\n", "            },\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"xAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"data\": [\n", "                \"\\u4e0a\\u6d77\",\n", "                \"\\u4e91\\u5357\",\n", "                \"\\u5185\\u8499\\u53e4\",\n", "                \"\\u5317\\u4eac\",\n", "                \"\\u5409\\u6797\",\n", "                \"\\u56db\\u5ddd\",\n", "                \"\\u5929\\u6d25\",\n", "                \"\\u5b81\\u590f\",\n", "                \"\\u5b89\\u5fbd\",\n", "                \"\\u5c71\\u4e1c\",\n", "                \"\\u5c71\\u897f\",\n", "                \"\\u5e7f\\u4e1c\",\n", "                \"\\u5e7f\\u897f\",\n", "                \"\\u65b0\\u7586\",\n", "                \"\\u6c5f\\u82cf\",\n", "                \"\\u6c5f\\u897f\",\n", "                \"\\u6cb3\\u5317\",\n", "                \"\\u6cb3\\u5357\",\n", "                \"\\u6d59\\u6c5f\",\n", "                \"\\u6d77\\u5357\",\n", "                \"\\u6e56\\u5317\",\n", "                \"\\u6e56\\u5357\",\n", "                \"\\u7518\\u8083\",\n", "                \"\\u798f\\u5efa\",\n", "                \"\\u897f\\u85cf\",\n", "                \"\\u8d35\\u5dde\",\n", "                \"\\u8fbd\\u5b81\",\n", "                \"\\u91cd\\u5e86\",\n", "                \"\\u9655\\u897f\",\n", "                \"\\u9752\\u6d77\",\n", "                \"\\u9ed1\\u9f99\\u6c5f\"\n", "            ]\n", "        }\n", "    ],\n", "    \"yAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            }\n", "        }\n", "    ],\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ],\n", "    \"visualMap\": {\n", "        \"show\": false,\n", "        \"type\": \"continuous\",\n", "        \"min\": 0,\n", "        \"max\": 100,\n", "        \"inRange\": {\n", "            \"symbolSize\": [\n", "                5,\n", "                50\n", "            ]\n", "        },\n", "        \"calculable\": true,\n", "        \"inverse\": false,\n", "        \"splitNumber\": 5,\n", "        \"hoverLink\": true,\n", "        \"orient\": \"vertical\",\n", "        \"padding\": 5,\n", "        \"showLabel\": true,\n", "        \"itemWidth\": 20,\n", "        \"itemHeight\": 140,\n", "        \"borderWidth\": 0\n", "    }\n", "};\n", "                chart_1581598aafbb48529eed4afc5d126505.setOption(option_1581598aafbb48529eed4afc5d126505);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x2654665f4f0>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["item_style = {'normal': {'shadowColor': '#000000', \n", "                         'shadowBlur': 20,\n", "                         'shadowOffsetX':5, \n", "                         'shadowOffsetY':15\n", "                         }\n", "              }\n", "s1 = (\n", "        <PERSON><PERSON><PERSON>()\n", "        .add_xaxis(df_counts.index.values.tolist())\n", "        .add_yaxis('4A-5A景区数量', df_counts.values.tolist(),symbol_size=50,itemstyle_opts=item_style)\n", "        .set_global_opts(visualmap_opts=opts.VisualMapOpts(is_show=False, \n", "                                              type_='size',\n", "                                              range_size=[5,50]))\n", ")\n", "s1.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "049552DEB5504E14B0E01767CC42C762", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 3.6 各省市4A-5A景区地图分布"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"hide_input": true, "id": "0FCF25B83EDD41038514CB4F16372300", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min', 'china':'https://assets.pyecharts.org/assets/v5/maps/china'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"46635fb66ce24561b84c2de6f422eb10\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts', 'china'], function(echarts) {\n", "                var chart_46635fb66ce24561b84c2de6f422eb10 = echarts.init(\n", "                    document.getElementById('46635fb66ce24561b84c2de6f422eb10'), 'white', {renderer: 'canvas'});\n", "                var option_46635fb66ce24561b84c2de6f422eb10 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"map\",\n", "            \"name\": \"4A-5A\\u666f\\u533a\\u5206\\u5e03\",\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"margin\": 8\n", "            },\n", "            \"map\": \"china\",\n", "            \"data\": [\n", "                {\n", "                    \"name\": \"\\u4e0a\\u6d77\\u5e02\",\n", "                    \"value\": 25\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e91\\u5357\\u7701\",\n", "                    \"value\": 31\n", "                },\n", "                {\n", "                    \"name\": \"\\u5185\\u8499\\u53e4\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": 23\n", "                },\n", "                {\n", "                    \"name\": \"\\u5317\\u4eac\\u5e02\",\n", "                    \"value\": 38\n", "                },\n", "                {\n", "                    \"name\": \"\\u5409\\u6797\\u7701\",\n", "                    \"value\": 10\n", "                },\n", "                {\n", "                    \"name\": \"\\u56db\\u5ddd\\u7701\",\n", "                    \"value\": 32\n", "                },\n", "                {\n", "                    \"name\": \"\\u5929\\u6d25\\u5e02\",\n", "                    \"value\": 18\n", "                },\n", "                {\n", "                    \"name\": \"\\u5b81\\u590f\\u56de\\u65cf\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": 18\n", "                },\n", "                {\n", "                    \"name\": \"\\u5b89\\u5fbd\\u7701\",\n", "                    \"value\": 47\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c71\\u4e1c\\u7701\",\n", "                    \"value\": 30\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c71\\u897f\\u7701\",\n", "                    \"value\": 36\n", "                },\n", "                {\n", "                    \"name\": \"\\u5e7f\\u4e1c\\u7701\",\n", "                    \"value\": 33\n", "                },\n", "                {\n", "                    \"name\": \"\\u5e7f\\u897f\\u58ee\\u65cf\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": 27\n", "                },\n", "                {\n", "                    \"name\": \"\\u65b0\\u7586\\u7ef4\\u543e\\u5c14\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": 15\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c5f\\u82cf\\u7701\",\n", "                    \"value\": 47\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c5f\\u897f\\u7701\",\n", "                    \"value\": 34\n", "                },\n", "                {\n", "                    \"name\": \"\\u6cb3\\u5317\\u7701\",\n", "                    \"value\": 36\n", "                },\n", "                {\n", "                    \"name\": \"\\u6cb3\\u5357\\u7701\",\n", "                    \"value\": 39\n", "                },\n", "                {\n", "                    \"name\": \"\\u6d59\\u6c5f\\u7701\",\n", "                    \"value\": 29\n", "                },\n", "                {\n", "                    \"name\": \"\\u6d77\\u5357\\u7701\",\n", "                    \"value\": 24\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e56\\u5317\\u7701\",\n", "                    \"value\": 37\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e56\\u5357\\u7701\",\n", "                    \"value\": 14\n", "                },\n", "                {\n", "                    \"name\": \"\\u7518\\u8083\\u7701\",\n", "                    \"value\": 19\n", "                },\n", "                {\n", "                    \"name\": \"\\u798f\\u5efa\\u7701\",\n", "                    \"value\": 19\n", "                },\n", "                {\n", "                    \"name\": \"\\u897f\\u85cf\\u81ea\\u6cbb\\u533a\",\n", "                    \"value\": 7\n", "                },\n", "                {\n", "                    \"name\": \"\\u8d35\\u5dde\\u7701\",\n", "                    \"value\": 18\n", "                },\n", "                {\n", "                    \"name\": \"\\u8fbd\\u5b81\\u7701\",\n", "                    \"value\": 23\n", "                },\n", "                {\n", "                    \"name\": \"\\u91cd\\u5e86\\u5e02\",\n", "                    \"value\": 33\n", "                },\n", "                {\n", "                    \"name\": \"\\u9655\\u897f\\u7701\",\n", "                    \"value\": 31\n", "                },\n", "                {\n", "                    \"name\": \"\\u9752\\u6d77\\u7701\",\n", "                    \"value\": 18\n", "                },\n", "                {\n", "                    \"name\": \"\\u9ed1\\u9f99\\u6c5f\\u7701\",\n", "                    \"value\": 17\n", "                }\n", "            ],\n", "            \"roam\": true,\n", "            \"aspectScale\": 0.75,\n", "            \"nameProperty\": \"name\",\n", "            \"selectedMode\": false,\n", "            \"zoom\": 1,\n", "            \"mapValueCalculation\": \"sum\",\n", "            \"showLegendSymbol\": true,\n", "            \"emphasis\": {}\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"4A-5A\\u666f\\u533a\\u5206\\u5e03\"\n", "            ],\n", "            \"selected\": {\n", "                \"4A-5A\\u666f\\u533a\\u5206\\u5e03\": true\n", "            },\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u5730\\u56fe\\u6570\\u636e\\u5206\\u5e03\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ],\n", "    \"visualMap\": {\n", "        \"show\": true,\n", "        \"type\": \"piecewise\",\n", "        \"min\": 0,\n", "        \"max\": 50,\n", "        \"inRange\": {\n", "            \"color\": [\n", "                \"#50a3ba\",\n", "                \"#eac763\",\n", "                \"#d94e5d\"\n", "            ]\n", "        },\n", "        \"calculable\": true,\n", "        \"inverse\": false,\n", "        \"splitNumber\": 5,\n", "        \"hoverLink\": true,\n", "        \"orient\": \"vertical\",\n", "        \"padding\": 5,\n", "        \"showLabel\": true,\n", "        \"itemWidth\": 20,\n", "        \"itemHeight\": 14,\n", "        \"borderWidth\": 0\n", "    }\n", "};\n", "                chart_46635fb66ce24561b84c2de6f422eb10.setOption(option_46635fb66ce24561b84c2de6f422eb10);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x2654695f8e0>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df_tmp3 = df[df['星级'].isin(['4A', '5A'])]\n", "df_counts = df_tmp3.groupby('城市').count()['星级']\n", "m2 = (\n", "    Map()\n", "    .add('4A-5A景区分布', [list(z) for z in zip([dictcode[x] for x in df_counts.index.values.tolist() ], df_counts.values.tolist())], 'china')\n", "    .set_global_opts(\n", "    title_opts=opts.TitleOpts(title='地图数据分布'),\n", "    visualmap_opts=opts.VisualMapOpts(max_=50, is_piecewise=True),\n", "    )\n", ")\n", "m2.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "A349E7E012294947B6D8117841172692", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["**江苏、安徽、河南、北京、湖北**等地区4A、5A级景区数量比较多。  \n", "\n", "## 3.7 门票价格区间占比玫瑰图"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "F67A8AE64241445D980B5CBB4AC5B2D7", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["0-50       888\n", "50-100     725\n", "100-150    278\n", "150-200    184\n", "200-250     62\n", "250-300     59\n", "300-350     20\n", "350-400     19\n", "400-500     13\n", "Name: 价格, dtype: int64"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 门票价格占比\n", "price_level = [0, 50, 100, 150, 200, 250, 300, 350, 400, 500]    \n", "label_level = ['0-50', '50-100', '100-150', '150-200', '200-250', '250-300', '300-350', '350-400', '400-500']    \n", "jzmj_cut = pd.cut(df['价格'], price_level, labels=label_level)        \n", "df_price = jzmj_cut.value_counts()\n", "df_price"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"hide_input": true, "id": "A5BCB37CE0904684BC0D05EC27CFD0B4", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"52f47dd9c95c4bd685b7bf5693229a46\" style=\"width:800px; height:600px;\"></div>\n", "\n", "<script>\n", "        require(['echarts'], function(echarts) {\n", "                var chart_52f47dd9c95c4bd685b7bf5693229a46 = echarts.init(\n", "                    document.getElementById('52f47dd9c95c4bd685b7bf5693229a46'), 'white', {renderer: 'canvas'});\n", "                var option_52f47dd9c95c4bd685b7bf5693229a46 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"pie\",\n", "            \"colorBy\": \"data\",\n", "            \"legendHoverLink\": true,\n", "            \"selectedMode\": false,\n", "            \"selectedOffset\": 10,\n", "            \"clockwise\": true,\n", "            \"startAngle\": 90,\n", "            \"minAngle\": 0,\n", "            \"minShowLabelAngle\": 0,\n", "            \"avoidLabelOverlap\": true,\n", "            \"stillShowZeroSum\": true,\n", "            \"percentPrecision\": 2,\n", "            \"showEmptyCircle\": true,\n", "            \"emptyCircleStyle\": {\n", "                \"color\": \"lightgray\",\n", "                \"borderColor\": \"#000\",\n", "                \"borderWidth\": 0,\n", "                \"borderType\": \"solid\",\n", "                \"borderDashOffset\": 0,\n", "                \"borderCap\": \"butt\",\n", "                \"borderJoin\": \"bevel\",\n", "                \"borderMiterLimit\": 10,\n", "                \"opacity\": 1\n", "            },\n", "            \"data\": [\n", "                {\n", "                    \"name\": \"0-50\",\n", "                    \"value\": 888\n", "                },\n", "                {\n", "                    \"name\": \"50-100\",\n", "                    \"value\": 725\n", "                },\n", "                {\n", "                    \"name\": \"100-150\",\n", "                    \"value\": 278\n", "                },\n", "                {\n", "                    \"name\": \"150-200\",\n", "                    \"value\": 184\n", "                },\n", "                {\n", "                    \"name\": \"200-250\",\n", "                    \"value\": 62\n", "                },\n", "                {\n", "                    \"name\": \"250-300\",\n", "                    \"value\": 59\n", "                },\n", "                {\n", "                    \"name\": \"300-350\",\n", "                    \"value\": 20\n", "                },\n", "                {\n", "                    \"name\": \"350-400\",\n", "                    \"value\": 19\n", "                },\n", "                {\n", "                    \"name\": \"400-500\",\n", "                    \"value\": 13\n", "                }\n", "            ],\n", "            \"radius\": [\n", "                \"20%\",\n", "                \"60%\"\n", "            ],\n", "            \"center\": [\n", "                \"40%\",\n", "                \"50%\"\n", "            ],\n", "            \"roseType\": \"radius\",\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"margin\": 8,\n", "                \"formatter\": \"{b}: {c} ({d}%)\"\n", "            },\n", "            \"labelLine\": {\n", "                \"show\": true,\n", "                \"showAbove\": false,\n", "                \"length\": 15,\n", "                \"length2\": 15,\n", "                \"smooth\": false,\n", "                \"minTurnAngle\": 90,\n", "                \"maxSurfaceAngle\": 90\n", "            },\n", "            \"rippleEffect\": {\n", "                \"show\": true,\n", "                \"brushType\": \"stroke\",\n", "                \"scale\": 2.5,\n", "                \"period\": 4\n", "            },\n", "            \"position\": \"outside\"\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"0-50\",\n", "                \"50-100\",\n", "                \"100-150\",\n", "                \"150-200\",\n", "                \"200-250\",\n", "                \"250-300\",\n", "                \"300-350\",\n", "                \"350-400\",\n", "                \"400-500\"\n", "            ],\n", "            \"selected\": {},\n", "            \"type\": \"scroll\",\n", "            \"show\": true,\n", "            \"left\": \"80%\",\n", "            \"top\": \"25%\",\n", "            \"orient\": \"vertical\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u95e8\\u7968\\u4ef7\\u683c\\u5360\\u6bd4\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"left\": \"33%\",\n", "            \"top\": \"5%\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ]\n", "};\n", "                chart_52f47dd9c95c4bd685b7bf5693229a46.setOption(option_52f47dd9c95c4bd685b7bf5693229a46);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x265467ab0d0>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["p1 = (\n", "    Pie(init_opts=opts.InitOpts(\n", "            width='800px', height='600px',\n", "            )\n", "       )\n", "        .add(\n", "        '',\n", "        [list(z) for z in zip(df_price.index.tolist(), df_price.values.tolist())],\n", "        radius=['20%', '60%'],\n", "        center=['40%', '50%'],\n", "        rosetype='radius',\n", "        label_opts=opts.LabelOpts(is_show=True),\n", "        )    \n", "        .set_global_opts(title_opts=opts.TitleOpts(title='门票价格占比',pos_left='33%',pos_top=\"5%\"),\n", "                        legend_opts=opts.LegendOpts(type_='scroll', pos_left=\"80%\",pos_top=\"25%\",orient=\"vertical\")\n", "                        )\n", "        .set_series_opts(label_opts=opts.LabelOpts(formatter='{b}: {c} ({d}%)'),position='outside')\n", "    )\n", "p1.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "7BBAF122382A48D583FF9D4E598EC2EF", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 3.8 门票价格区间数量散点图"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"hide_input": true, "id": "0F746E7689CE47C482C9438E2162D618", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"c1634e36634f406fabc1fc621360b930\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts'], function(echarts) {\n", "                var chart_c1634e36634f406fabc1fc621360b930 = echarts.init(\n", "                    document.getElementById('c1634e36634f406fabc1fc621360b930'), 'white', {renderer: 'canvas'});\n", "                var option_c1634e36634f406fabc1fc621360b930 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"scatter\",\n", "            \"name\": \"\\u95e8\\u7968\\u4ef7\\u683c\\u533a\\u95f4\",\n", "            \"symbolSize\": 50,\n", "            \"data\": [\n", "                [\n", "                    \"0-50\",\n", "                    888\n", "                ],\n", "                [\n", "                    \"50-100\",\n", "                    725\n", "                ],\n", "                [\n", "                    \"100-150\",\n", "                    278\n", "                ],\n", "                [\n", "                    \"150-200\",\n", "                    184\n", "                ],\n", "                [\n", "                    \"200-250\",\n", "                    62\n", "                ],\n", "                [\n", "                    \"250-300\",\n", "                    59\n", "                ],\n", "                [\n", "                    \"300-350\",\n", "                    20\n", "                ],\n", "                [\n", "                    \"350-400\",\n", "                    19\n", "                ],\n", "                [\n", "                    \"400-500\",\n", "                    13\n", "                ]\n", "            ],\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"position\": \"right\",\n", "                \"margin\": 8\n", "            },\n", "            \"itemStyle\": {\n", "                \"color\": new echarts.graphic.RadialGradient(                    0.5, 0.5, 1,                    [{offset: 0,                      color: '#009ad6'},                     {offset: 1,                      color: '#ed1941'}                      ])\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"\\u95e8\\u7968\\u4ef7\\u683c\\u533a\\u95f4\"\n", "            ],\n", "            \"selected\": {\n", "                \"\\u95e8\\u7968\\u4ef7\\u683c\\u533a\\u95f4\": true\n", "            },\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"xAxis\": [\n", "        {\n", "            \"name\": \"\\u4ef7\\u683c\\u533a\\u95f4(\\u5143)\",\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"data\": [\n", "                \"0-50\",\n", "                \"50-100\",\n", "                \"100-150\",\n", "                \"150-200\",\n", "                \"200-250\",\n", "                \"250-300\",\n", "                \"300-350\",\n", "                \"350-400\",\n", "                \"400-500\"\n", "            ]\n", "        }\n", "    ],\n", "    \"yAxis\": [\n", "        {\n", "            \"name\": \"\\u6570\\u91cf\",\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            }\n", "        }\n", "    ],\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ],\n", "    \"visualMap\": {\n", "        \"show\": false,\n", "        \"type\": \"continuous\",\n", "        \"min\": 0,\n", "        \"max\": 100,\n", "        \"inRange\": {\n", "            \"symbolSize\": [\n", "                5,\n", "                50\n", "            ]\n", "        },\n", "        \"calculable\": true,\n", "        \"inverse\": false,\n", "        \"splitNumber\": 5,\n", "        \"hoverLink\": true,\n", "        \"orient\": \"vertical\",\n", "        \"padding\": 5,\n", "        \"showLabel\": true,\n", "        \"itemWidth\": 20,\n", "        \"itemHeight\": 140,\n", "        \"borderWidth\": 0\n", "    }\n", "};\n", "                chart_c1634e36634f406fabc1fc621360b930.setOption(option_c1634e36634f406fabc1fc621360b930);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x265467ab550>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["color_js = \"\"\"new echarts.graphic.RadialGradient(\n", "                    0.5, 0.5, 1,\n", "                    [{offset: 0,\n", "                      color: '#009ad6'},\n", "                     {offset: 1,\n", "                      color: '#ed1941'}\n", "                      ])\"\"\"\n", "\n", "s2 = (\n", "        <PERSON><PERSON><PERSON>()\n", "        .add_xaxis(df_price.index.tolist())\n", "        .add_yaxis('门票价格区间', df_price.values.tolist(),symbol_size=50,itemstyle_opts=opts.ItemStyleOpts(color=JsCode(color_js))) \n", "        .set_global_opts(\n", "            yaxis_opts=opts.AxisOpts(name='数量'),\n", "            xaxis_opts=opts.AxisOpts(name='价格区间(元)'))\n", "        .set_global_opts(visualmap_opts=opts.VisualMapOpts(is_show=False, \n", "                                              # 设置通过图形大小来表现数据\n", "                                              type_='size',\n", "                                              # 图形大小映射范围\n", "                                              range_size=[5,50]))\n", ")\n", "s2.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "3AAD62F9565742E69179EDB55C53FDA2", "jupyter": {}, "mdEditEnable": false, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 3.9 景点简介词云"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"hide_input": true, "id": "0BC6BC05107645F18AEC81A91AB0E3F1", "jupyter": {}, "notebookId": "61bc8d4159c2930017fd1f14", "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\jieba.cache\n", "Loading model cost 1.606 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min', 'echarts-wordcloud':'https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"c6467de607da40eda1eb9b1583d05b84\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts', 'echarts-wordcloud'], function(echarts) {\n", "                var chart_c6467de607da40eda1eb9b1583d05b84 = echarts.init(\n", "                    document.getElementById('c6467de607da40eda1eb9b1583d05b84'), 'white', {renderer: 'canvas'});\n", "                var option_c6467de607da40eda1eb9b1583d05b84 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"wordCloud\",\n", "            \"shape\": \"star\",\n", "            \"rotationRange\": [\n", "                0,\n", "                0\n", "            ],\n", "            \"rotationStep\": 45,\n", "            \"girdSize\": 20,\n", "            \"sizeRange\": [\n", "                5,\n", "                100\n", "            ],\n", "            \"data\": [\n", "                {\n", "                    \"name\": \"\\u6587\\u5316\",\n", "                    \"value\": 151,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(115,69,40)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f53\\u9a8c\",\n", "                    \"value\": 94,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(1,110,134)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e16\\u754c\",\n", "                    \"value\": 90,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(58,81,65)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e29\\u6cc9\",\n", "                    \"value\": 88,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(115,18,115)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u65c5\\u6e38\",\n", "                    \"value\": 85,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(29,73,159)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e2d\\u56fd\",\n", "                    \"value\": 81,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(30,116,11)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u611f\\u53d7\",\n", "                    \"value\": 72,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(62,156,54)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f11\\u95f2\",\n", "                    \"value\": 72,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(110,92,142)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\n\",\n", "                    \"value\": 66,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(6,42,140)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e00\\u4f53\",\n", "                    \"value\": 66,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(23,139,53)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u666f\\u533a\",\n", "                    \"value\": 60,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(130,146,134)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5386\\u53f2\",\n", "                    \"value\": 60,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(23,37,115)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u96c6\",\n", "                    \"value\": 55,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(65,24,63)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5927\\u578b\",\n", "                    \"value\": 53,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(74,6,54)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u81ea\\u7136\",\n", "                    \"value\": 50,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(78,88,125)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e50\\u56ed\",\n", "                    \"value\": 47,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(145,92,37)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e3b\\u9898\",\n", "                    \"value\": 47,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(122,85,157)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u52a8\\u7269\",\n", "                    \"value\": 45,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(24,42,115)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5efa\\u7b51\",\n", "                    \"value\": 44,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(126,6,46)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5ea6\\u5047\",\n", "                    \"value\": 41,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(157,89,110)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u72ec\\u7279\",\n", "                    \"value\": 40,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(78,73,57)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u98ce\\u60c5\",\n", "                    \"value\": 39,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(2,121,137)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u827a\\u672f\",\n", "                    \"value\": 39,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(91,29,114)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e00\\u5ea7\",\n", "                    \"value\": 39,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(62,12,126)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u672a\\u77e5\",\n", "                    \"value\": 37,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(32,15,102)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u535a\\u7269\\u9986\",\n", "                    \"value\": 36,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(51,106,65)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u516c\\u91cc\",\n", "                    \"value\": 36,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(65,1,86)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u9879\\u76ee\",\n", "                    \"value\": 35,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(73,109,17)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u98ce\\u5149\",\n", "                    \"value\": 35,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(125,14,102)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5a31\\u4e50\",\n", "                    \"value\": 34,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(105,69,22)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6d77\\u6d0b\",\n", "                    \"value\": 34,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(152,139,151)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u751f\\u6001\",\n", "                    \"value\": 34,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(102,156,49)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7279\\u8272\",\n", "                    \"value\": 33,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(121,23,120)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u666f\\u89c2\",\n", "                    \"value\": 33,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(149,111,152)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u517b\\u751f\",\n", "                    \"value\": 32,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(155,109,92)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u68ee\\u6797\",\n", "                    \"value\": 31,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(1,4,30)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u516c\\u56ed\",\n", "                    \"value\": 29,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(54,75,91)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u603b\",\n", "                    \"value\": 29,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(108,113,15)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5929\\u7136\",\n", "                    \"value\": 29,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(26,78,5)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u98ce\\u666f\",\n", "                    \"value\": 27,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(93,130,1)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4eab\\u53d7\",\n", "                    \"value\": 26,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(40,43,56)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6253\\u9020\",\n", "                    \"value\": 26,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(5,4,62)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5730\\u65b9\",\n", "                    \"value\": 26,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(73,40,144)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c71\",\n", "                    \"value\": 26,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(108,139,147)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u666f\\u8272\",\n", "                    \"value\": 26,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(130,132,67)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u89c2\\u5149\",\n", "                    \"value\": 26,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(160,154,108)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5343\\u5e74\",\n", "                    \"value\": 25,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(60,126,91)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c34\",\n", "                    \"value\": 25,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(58,119,93)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5723\\u5730\",\n", "                    \"value\": 24,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(78,83,135)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u56db\\u5b63\",\n", "                    \"value\": 24,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(33,3,153)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7f8e\\u666f\",\n", "                    \"value\": 24,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(137,108,110)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c71\\u6c34\",\n", "                    \"value\": 24,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(25,103,134)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u89c2\\u8d4f\",\n", "                    \"value\": 24,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(87,90,129)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u56fd\\u5bb6\",\n", "                    \"value\": 24,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(61,82,153)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e4b\\u5730\",\n", "                    \"value\": 23,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(132,17,140)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7efc\\u5408\\u6027\",\n", "                    \"value\": 23,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(137,94,132)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u9886\\u7565\",\n", "                    \"value\": 22,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(20,58,133)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u57ce\\u5e02\",\n", "                    \"value\": 22,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(72,8,29)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5927\\u81ea\\u7136\",\n", "                    \"value\": 22,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(145,18,35)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u597d\\u53bb\\u5904\",\n", "                    \"value\": 22,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(32,124,35)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u8457\\u540d\",\n", "                    \"value\": 22,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(39,50,61)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5ea6\\u5047\\u533a\",\n", "                    \"value\": 22,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(152,129,77)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u91cd\\u5e86\",\n", "                    \"value\": 22,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(106,62,101)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5ce1\\u8c37\",\n", "                    \"value\": 21,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(117,108,61)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u795e\\u5947\",\n", "                    \"value\": 21,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(104,141,27)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u56fd\\u9645\",\n", "                    \"value\": 21,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(50,143,151)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u57fa\\u5730\",\n", "                    \"value\": 20,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(56,47,24)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u53e4\\u4ee3\",\n", "                    \"value\": 20,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(33,153,31)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7ea6\",\n", "                    \"value\": 20,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(95,37,143)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u533a\",\n", "                    \"value\": 20,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(125,117,7)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u56ed\\u6797\",\n", "                    \"value\": 20,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(19,129,11)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6d6a\\u6f2b\",\n", "                    \"value\": 20,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(8,2,24)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c0f\\u9547\",\n", "                    \"value\": 19,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(88,83,144)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c34\\u4e0a\",\n", "                    \"value\": 19,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(37,48,87)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u68a6\\u5e7b\",\n", "                    \"value\": 19,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(111,98,54)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5904\",\n", "                    \"value\": 19,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(22,34,20)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c57\",\n", "                    \"value\": 19,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(117,57,96)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5168\\u7403\",\n", "                    \"value\": 18,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(125,89,12)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u52a8\\u7269\\u56ed\",\n", "                    \"value\": 18,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(144,86,66)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7cbe\\u534e\",\n", "                    \"value\": 18,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(86,61,145)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u9762\\u79ef\",\n", "                    \"value\": 18,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(63,43,130)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6b22\\u4e50\",\n", "                    \"value\": 18,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(8,31,88)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c5f\\u5357\",\n", "                    \"value\": 18,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(116,52,72)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u666f\\u70b9\",\n", "                    \"value\": 18,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(83,153,134)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5ba4\\u5185\",\n", "                    \"value\": 18,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(45,135,44)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e3b\\u9898\\u516c\\u56ed\",\n", "                    \"value\": 18,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(96,117,2)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e4b\\u65c5\",\n", "                    \"value\": 17,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(14,70,127)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6b23\\u8d4f\",\n", "                    \"value\": 17,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(84,42,91)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f5b\\u6559\",\n", "                    \"value\": 17,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(29,111,63)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u9986\",\n", "                    \"value\": 17,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(92,127,60)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7f8e\\u4e3d\",\n", "                    \"value\": 17,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(54,29,99)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u751f\\u6d3b\",\n", "                    \"value\": 17,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(48,95,123)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u62e5\\u6709\",\n", "                    \"value\": 17,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(72,72,33)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5168\\u56fd\",\n", "                    \"value\": 16,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(5,86,127)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e38\\u89c8\",\n", "                    \"value\": 16,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(40,148,88)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6211\\u56fd\",\n", "                    \"value\": 16,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(160,125,149)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5360\\u5730\\u9762\\u79ef\",\n", "                    \"value\": 16,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(89,81,136)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4ea9\",\n", "                    \"value\": 16,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(99,54,14)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6295\\u8d44\",\n", "                    \"value\": 16,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(29,135,56)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u9ec4\\u6cb3\",\n", "                    \"value\": 16,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(16,89,149)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5c55\\u793a\",\n", "                    \"value\": 16,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(114,65,114)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7c73\",\n", "                    \"value\": 16,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(43,43,160)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e38\\u5ba2\",\n", "                    \"value\": 16,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(140,116,90)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u523a\\u6fc0\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(145,127,154)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e0a\\u6d77\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(135,87,3)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u9910\\u996e\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(53,152,67)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u82b1\\u6d77\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(51,59,5)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u63a2\\u7d22\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(129,75,82)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u9b45\\u529b\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(46,89,109)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u56fd\\u5185\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(145,147,129)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6c11\\u65cf\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(108,46,81)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6f14\\u51fa\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(159,26,46)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u91ce\\u751f\\u52a8\\u7269\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(146,115,115)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4f20\\u7edf\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(148,47,122)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u63a5\\u89e6\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(89,56,121)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u58ee\\u89c2\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(72,109,122)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u8349\\u539f\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(94,125,84)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5750\\u843d\\u4e8e\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(123,44,147)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u84b8\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(22,14,133)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5929\\u4e0b\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(23,131,1)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e56\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(55,154,154)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u9152\\u5e97\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(122,43,149)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e3b\\u9898\\u4e50\\u56ed\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(95,83,38)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u4e2d\\u5fc3\",\n", "                    \"value\": 15,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(52,19,94)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5f0f\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(68,22,152)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u73af\\u5883\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(0,47,4)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6eb6\\u6d1e\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(137,86,160)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u91cc\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(104,91,31)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5883\\u5185\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(48,60,3)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6e38\\u4e50\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(55,149,144)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7011\\u5e03\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(145,67,58)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5730\\u4e0b\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(99,138,86)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5929\\u5802\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(6,113,147)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7406\\u60f3\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(142,115,23)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7f55\\u89c1\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(64,5,101)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u9057\\u5740\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(154,93,28)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6fb3\\u95e8\",\n", "                    \"value\": 14,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(141,45,68)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7f8e\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(27,137,150)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5145\\u6ee1\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(66,103,130)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u539f\\u59cb\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(49,6,30)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u53e4\\u57ce\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(14,91,113)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5efa\\u7b51\\u9762\\u79ef\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(89,36,143)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u8868\\u6f14\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(141,127,124)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5317\\u4eac\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(128,99,16)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u7edd\\u4f73\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(11,54,44)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u98ce\\u683c\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(44,18,21)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u56ed\\u533a\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(58,68,132)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u897f\\u5317\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(58,9,6)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u5178\\u578b\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(7,129,118)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6d77\\u5e95\",\n", "                    \"value\": 13,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(115,11,145)\"\n", "                    }\n", "                }\n", "            ],\n", "            \"drawOutOfBound\": false,\n", "            \"textStyle\": {\n", "                \"normal\": {\n", "                    \"fontFamily\": \"cursive\"\n", "                },\n", "                \"emphasis\": {}\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [],\n", "            \"selected\": {},\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderWidth\": 1,\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u666f\\u70b9\\u7b80\\u4ecb\\u8bcd\\u4e91\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ]\n", "};\n", "                chart_c6467de607da40eda1eb9b1583d05b84.setOption(option_c6467de607da40eda1eb9b1583d05b84);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x26546940460>"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["contents = \"\".join('%s' % i for i in df['简介'].values.tolist())\n", "contents_list = jieba.cut(contents)\n", "ac = Counter(contents_list)\n", "\n", "stopwords = []\n", "with open('./stopwords.txt', \"r\",encoding='utf-8') as f:  # 打开文件\n", "    data = f.read()  # 读取文件\n", "    stopwords = data.split('\\n')\n", "\n", "for i in stopwords:\n", "    del ac[i]\n", " \n", "w1 = (\n", "    WordCloud()\n", "    .add(\"\", \n", "         ac.most_common(150), \n", "         word_size_range=[5, 100], \n", "         textstyle_opts=opts.TextStyleOpts(font_family=\"cursive\"),\n", "         shape='star')\n", "    .set_global_opts(title_opts=opts.TitleOpts(title=\"景点简介词云\"))\n", ")\n", "w1.render_notebook()"]}, {"cell_type": "markdown", "metadata": {"id": "9958BE467A004FA18DD0EA008B333B6C", "jupyter": {}, "mdEditEnable": true, "notebookId": "61bc8d4159c2930017fd1f14", "runtime": {"execution_status": null, "status": "default"}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["# 总结  \n", "* 华东、华南、华中等地区属于国民出游热点地区，尤其是北京、上海、江苏、广东、四川、陕西等地区出行比较密集。  \n", "* 江苏、安徽、河南、北京、湖北等地区4A、5A级景区数量比较多。  \n", "* 门票价格100以内居多，大概占比70%，还是比较实惠的，而且一般景区还存在学生优惠等。  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {"height": "423px", "width": "322px"}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "165px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 4}
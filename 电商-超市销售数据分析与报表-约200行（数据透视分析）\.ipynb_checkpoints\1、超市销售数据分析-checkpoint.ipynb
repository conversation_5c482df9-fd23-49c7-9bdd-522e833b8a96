{"cells": [{"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Table of Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#项目介绍\" data-toc-modified-id=\"项目介绍-1\">项目介绍</a></span></li><li><span><a href=\"#哪些类别比较畅销?\" data-toc-modified-id=\"哪些类别比较畅销?-2\">哪些类别比较畅销?</a></span></li><li><span><a href=\"#哪些商品比较畅销?\" data-toc-modified-id=\"哪些商品比较畅销?-3\">哪些商品比较畅销?</a></span></li><li><span><a href=\"#不同门店的销售额占比\" data-toc-modified-id=\"不同门店的销售额占比-4\">不同门店的销售额占比</a></span></li><li><span><a href=\"#哪个时间段是超市的客流高封期?\" data-toc-modified-id=\"哪个时间段是超市的客流高封期?-5\">哪个时间段是超市的客流高封期?</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 项目介绍\n", "近些年来，国内大型连锁超市如雨后春笋般迸发，对于各个超市来说，竞争压力不可谓\n", "不大，为了拓展、保留客户，各种促销手段应运而生。\n", "\n", "以下为国内某连锁超市的成交统计数据，针对于该数据，挖掘其中价值，为该超市的促销手段提供技术支持。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3478, 7)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>商品ID</th>\n", "      <th>类别ID</th>\n", "      <th>门店编号</th>\n", "      <th>单价</th>\n", "      <th>销量</th>\n", "      <th>成交时间</th>\n", "      <th>订单ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>30006206</td>\n", "      <td>915000003</td>\n", "      <td>CDNL</td>\n", "      <td>25.23</td>\n", "      <td>0.328</td>\n", "      <td>2017-01-03 09:56:00</td>\n", "      <td>20170103CDLG000210052759</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>30163281</td>\n", "      <td>914010000</td>\n", "      <td>CDNL</td>\n", "      <td>2.00</td>\n", "      <td>2.000</td>\n", "      <td>2017-01-03 09:56:00</td>\n", "      <td>20170103CDLG000210052759</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>30200518</td>\n", "      <td>922000000</td>\n", "      <td>CDNL</td>\n", "      <td>19.62</td>\n", "      <td>0.230</td>\n", "      <td>2017-01-03 09:56:00</td>\n", "      <td>20170103CDLG000210052759</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>29989105</td>\n", "      <td>922000000</td>\n", "      <td>CDNL</td>\n", "      <td>2.80</td>\n", "      <td>2.044</td>\n", "      <td>2017-01-03 09:56:00</td>\n", "      <td>20170103CDLG000210052759</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>30179558</td>\n", "      <td>915000100</td>\n", "      <td>CDNL</td>\n", "      <td>47.41</td>\n", "      <td>0.226</td>\n", "      <td>2017-01-03 09:56:00</td>\n", "      <td>20170103CDLG000210052759</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       商品ID       类别ID  门店编号     单价     销量                成交时间  \\\n", "0  30006206  915000003  CDNL  25.23  0.328 2017-01-03 09:56:00   \n", "1  30163281  914010000  CDNL   2.00  2.000 2017-01-03 09:56:00   \n", "2  30200518  922000000  CDNL  19.62  0.230 2017-01-03 09:56:00   \n", "3  29989105  922000000  CDNL   2.80  2.044 2017-01-03 09:56:00   \n", "4  30179558  915000100  CDNL  47.41  0.226 2017-01-03 09:56:00   \n", "\n", "                       订单ID  \n", "0  20170103CDLG000210052759  \n", "1  20170103CDLG000210052759  \n", "2  20170103CDLG000210052759  \n", "3  20170103CDLG000210052759  \n", "4  20170103CDLG000210052759  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "from datetime import datetime\n", "\n", "# 导入数据源，parse_dates：将时间字符串转为日期时间格式\n", "data=pd.read_csv(\"order-14.3.csv\",parse_dates=[\"成交时间\"],encoding='gbk')\n", "print(data.shape)\n", "data.head()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 哪些类别比较畅销?"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>类别ID</th>\n", "      <th>销量</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>922000003</td>\n", "      <td>425.328</td>\n", "    </tr>\n", "    <tr>\n", "      <th>239</th>\n", "      <td>922000002</td>\n", "      <td>206.424</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>923000006</td>\n", "      <td>190.294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>216</th>\n", "      <td>915030104</td>\n", "      <td>175.059</td>\n", "    </tr>\n", "    <tr>\n", "      <th>238</th>\n", "      <td>922000001</td>\n", "      <td>121.355</td>\n", "    </tr>\n", "    <tr>\n", "      <th>367</th>\n", "      <td>960000000</td>\n", "      <td>121.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>234</th>\n", "      <td>920090000</td>\n", "      <td>111.565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>923000002</td>\n", "      <td>91.847</td>\n", "    </tr>\n", "    <tr>\n", "      <th>237</th>\n", "      <td>922000000</td>\n", "      <td>86.395</td>\n", "    </tr>\n", "    <tr>\n", "      <th>247</th>\n", "      <td>923000000</td>\n", "      <td>85.845</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          类别ID       销量\n", "240  922000003  425.328\n", "239  922000002  206.424\n", "251  923000006  190.294\n", "216  915030104  175.059\n", "238  922000001  121.355\n", "367  960000000  121.000\n", "234  920090000  111.565\n", "249  923000002   91.847\n", "237  922000000   86.395\n", "247  923000000   85.845"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# ascending=False 降序\n", "data.groupby(\"类别ID\")[\"销量\"].sum().reset_index().sort_values(by=\"销量\",ascending=False).head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 哪些商品比较畅销?"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>商品ID</th>\n", "      <th>销量</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>29989059</td>\n", "      <td>391.549</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>29989072</td>\n", "      <td>102.876</td>\n", "    </tr>\n", "    <tr>\n", "      <th>469</th>\n", "      <td>30022232</td>\n", "      <td>101.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>523</th>\n", "      <td>30031960</td>\n", "      <td>99.998</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>29989157</td>\n", "      <td>72.453</td>\n", "    </tr>\n", "    <tr>\n", "      <th>476</th>\n", "      <td>30023041</td>\n", "      <td>64.416</td>\n", "    </tr>\n", "    <tr>\n", "      <th>505</th>\n", "      <td>30026255</td>\n", "      <td>62.375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>29989058</td>\n", "      <td>56.052</td>\n", "    </tr>\n", "    <tr>\n", "      <th>510</th>\n", "      <td>30027007</td>\n", "      <td>48.757</td>\n", "    </tr>\n", "    <tr>\n", "      <th>903</th>\n", "      <td>30171264</td>\n", "      <td>45.000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         商品ID       销量\n", "8    29989059  391.549\n", "18   29989072  102.876\n", "469  30022232  101.000\n", "523  30031960   99.998\n", "57   29989157   72.453\n", "476  30023041   64.416\n", "505  30026255   62.375\n", "7    29989058   56.052\n", "510  30027007   48.757\n", "903  30171264   45.000"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.pivot_table(data,index=\"商品ID\",values=\"销量\",aggfunc=\"sum\").reset_index().sort_values(by=\"销量\",ascending=False).head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 不同门店的销售额占比"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["门店编号\n", "CDLG    10908.82612\n", "CDNL     8059.47867\n", "CDXL     9981.76166\n", "Name: 销售额, dtype: float64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>销售额占比</th>\n", "    </tr>\n", "    <tr>\n", "      <th>门店编号</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>CDLG</th>\n", "      <td>0.376815</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CDNL</th>\n", "      <td>0.278392</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CDXL</th>\n", "      <td>0.344792</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         销售额占比\n", "门店编号          \n", "CDLG  0.376815\n", "CDNL  0.278392\n", "CDXL  0.344792"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"销售额\"]=data[\"销量\"]*data[\"单价\"]\n", "# 不同门店销售\n", "print(data.groupby(\"门店编号\")[\"销售额\"].sum())\n", "# 不同门店销售额占比\n", "dfbb = data.groupby(\"门店编号\")[[\"销售额\"]].sum()/data[\"销售额\"].sum()\n", "dfbb.rename(columns={'销售额':'销售额占比'},inplace=True)\n", "dfbb"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot: ylabel='销售额'>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1600x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib as plt\n", "\n", "plt.rcParams['figure.figsize'] = (16.0, 8.0) # 设置figure_size尺寸\n", "plt.rcParams['font.sans-serif']=['SimHei']    # 用来设置字体样式以正常显示中文标签\n", "plt.rcParams['axes.unicode_minus']=False    # 默认是使用Unicode负号，设置正常显示字符，如正常显示负号\n", "plt.rcParams['font.size'] = 15\n", "\n", "(data.groupby(\"门店编号\")[\"销售额\"].sum()/data[\"销售额\"].sum()).plot.pie()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 哪个时间段是超市的客流高封期?"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot: xlabel='小时'>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABRUAAAKwCAYAAAAGDpigAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAAC5QElEQVR4nOzdd3jV9d3/8ddZ2clJyCKEsAkblBUEBLRVW9xWxd7allbrwlarba2tddRa25+t1vbWqhVXHajVIhYH3tUAIoSpMsNKICSQTfY85/v7I6MgQwI5+ZzxfFxXrltzvgkv7qtg8srn/XnbLMuyBAAAAAAAAAAnyG46AAAAAAAAAIDAQqkIAAAAAAAAoEsoFQEAAAAAAAB0CaUiAAAAAAAAgC6hVAQAAAAAAADQJZSKAAAAAAAAALqEUhEAAAAAAABAl1AqAgAAAAAAAOgSp+kA3cXr9aqoqEixsbGy2Wym4wAAAAAAAAABxbIs1dTUqE+fPrLbj38WMWhKxaKiImVkZJiOAQAAAAAAAAS0goIC9e3b97jPBE2pGBsbK6ntNx0XF2c4DQAAAAAAABBYqqurlZGR0dmzHU/QlIodI89xcXGUigAAAAAAAMBJOpGrBVnUAgAAAAAAAKBLKBUBAAAAAAAAdMkplYoff/yxnE6n8vPzj/vctm3bFBERoZkzZx719crKSt18881KT09XbGys5s6dq+rq6lOJBgAAAAAAAMBHTvpOxb1792rOnDnyeDzHfc6yLN1www3yeDx64oknjni9oaFB5513ntasWaPzzjtPmZmZeumll7R//3598MEHJxsPAAAAAAAAgI+c1EnFzZs368wzz1RpaelXPjt//nwtW7ZMt912m0aNGnXE648++qjWrFmj22+/Xe+//77+8pe/6P3339eSJUv0+uuvn0w8AAAAAAAAAD5ksyzL6soH7Nu3T6NHj1a/fv10xhln6Omnn1ZeXp4GDBhwxLMlJSUaPny4oqKitG3bNsXExBzxzKBBg1RaWqoDBw4oOjq68/1f+9rXFBsbq4ULF55QrurqarndblVVVbH9GQAAAAAAAOiirvRrXT6pWFZWptmzZ2v58uVKS0s77rO33XabKisr9eijjx61UDxw4IDy8vJ09tlnH1YoStLZZ5+t7OzsrsYDAAAAAAAA4GNdLhXHjBmjV155RW63+7jPffjhh3r11VfVp08fbd26VbfffrueeeYZ1dfXdz5TXFwsSRo7duwRHz948GBVVVWpoqKiqxEBAAAAAAAA+FCXF7U4HI6vfMbr9eqOO+6QJBUVFenee+/tfO2RRx7RO++8o8GDB3dueE5OTj7ic3SUlkVFRerVq9cRrzc1Nampqanz39kWDQAAAAAAAPSMk1rU8lUWLVqkjRs3ym63649//KPy8vK0ceNG3Xzzzdq6dauuvPJKSZLT2dZpRkVFHfE5wsLCJOmwk42Heuihh+R2uzvfMjIyfPFbAQAAAAAAAPAlXT6peCLefvttSdJPf/rTzhOLkvT444+rqKhICxcu1Pr16zvvUWxubj7ic7S0tBzzNUm66667dPvtt3f+e3V1NcUiAAAAAAAA0AN8clKxsLBQkvTd7373iNe+9rWvSZJ27NihPn36SJLy8/OPeK6kpESSFB8ff9RfIzw8XHFxcYe9AQAAAAAAAPA9n5SKsbGxktqWrXxZa2urpLbx5qSkJKWnp2v9+vVHPLd69WpJUmpqqi8iAgAAAAAAADhJPikVx4wZI0kqKCg44rWNGzdKkkaNGiVJmj17trKzs7Vz587OZ5qamvSvf/1Lw4cPP+oSFwAAAAAAAADm+KRUvPzyy2W323XffffJ4/F0vn/t2rV66aWXNG7cOGVmZkqS5s2bJ5vNpv/5n/9RYWGhGhsbdeONN6qoqEjXXHONL+IBAAAAAAAAOAU+WdQyevRo/fznP9fvf/97bd68WTNnztSBAwe0cOFCWZalv/71r53Pjhs3Tn/605/0k5/8RP369ZPL5VJTU5MmT56sn/3sZ76IBwAAAAAAAOAU+OSkoiQ99NBDeu211xQfH6+XXnpJ77//vrKysvSf//xHZ5555mHP/vjHP9ayZcv0rW99S5MnT9YDDzyg5cuXKywszFfxAAAAAAAAAJwkm2VZlukQ3aG6ulput1tVVVVsggYAAAAAAAC6qCv9ms9OKgIAAAAAAAAITpSKAAAAAAAAALqEUhEAAAAAAABAl1AqAgAAAAAAAOgSSkUAAAAAAAAAXUKpCAAnaGdJjQoq6k3HAAAAAADAOKfpAAAQCPaW1+v8v3wij9fSzbMG65azhyrMyc9lAAAAAAChie+IAeAELFizV02tXrV6Lf3lo5266H8/0cZ9VaZjAQAAAABgBKUiAHyFVo9Xb6zbJ0m6Zko/JUaHaduBGl3yxAo9/ME2NbV6DCcEAAAAAKBnUSoCwFf4OLdUpTVNSowO0z0XjNKSn8zQBWPT5PFaevzjXbrwr5/o84KDpmMCAAAAANBjKBUB4CssWL1XknT5hL4Kc9qVGBOu//2f8frb1eOVFBOm7cW1uvSJFfrD+9vU2MKpRQAAAABA8KNUBIDjOFDVqI9zSyRJV07KOOy1b45J05KfzNRF4/rIa0l/y96lC/76iTbsrTQRFQAAAACAHkOpCADH8c91BfJa0uQBvTQ4OeaI13tFh+kv3z5dT14zQUkx4dpZUqtv/e1TPfTeVk4tAgAAAACCFqUiAByD12vptbUFkqSrJmcc99lvjO6tD38yQ5eeni6vJT21dLfO/8tyrdvDqUUAAAAAQPChVASAY/h0V7kKKhoUG+HUN0enfeXzCdFhenTOafr7dycqOTZcu0rrdPmTn+rBxVs4tQgAAAAACCqUigBwDAvWtC1oueS0dEWGOU74484ZmaoPfzJDl41Pl2VJf1+ep9mPLdfa/ApfRQUAAAAAoEdRKgLAUVTUNWvJ5mJJXz36fDTxUWF65MrT9OzciUqNC9fusjpd8dRK/eadLWpo5tQiAAAAACCwUSoCwFG8tX6fmj1ejUl3a1Qf90l/nrOHp2rJT2bqigl9ZVnSsyvy9M3Hlml1HqcWAQAAAACBi1IRAL7Esiy9tqZtQcucSV0/pfhl7kiXHr5inJ77/iT1jotQfnm95jy9Uvct2qz65tZT/vwAAAAAAPQ0SkUA+JL1eyu1o6RWkS6HLj6tT7d93rOGpWjJ7TM0Z2KGLEt6/tN8fePPy7Vqd3m3/RoAAAAAAPQESkUA+JIFq9tOKZ4/Nk2xEa5u/dxxES794fKxeuEHk5XmjtDeinpd9fQq3fv2JtU1cWoRAAAAABAYKBUB4BA1jS369xf7JUlXdcPo87HMzEzWkp/M0Lcn95MkvbByj77x2DJ9uqvMZ78mAAAAAADdhVIRAA6x6PMiNbR4NCQlRhP6J/j014qNcOmhy8boH9dOVnp8pAoqGvQ/f8/R3Qs3qpZTiwAAAAAAP0apCACH6FjQctWkDNlsth75Nc8cmqwPfjJDV2e1nVp8adVenffoMq3YyalFAAAAAIB/olQEgHabi6r0xb4quRw2XXp6eo/+2jHhTj146Ri9cl2W+iZEqvBgg65+Jke//NdG1TS29GgWAAAAAAC+CqUiALTrOKV47sjeSowJN5Jh6pAkfXDbDH1nSn9J0is5e/WNPy/X8h2lRvIAAAAAAHA0lIoAIKmxxaN/bSiUJF012XcLWk5EdLhTD1wyWq/8MEsZvdpOLX5n/mrd9dYXqubUIgAAAADAD1AqAoCk9zbtV01jq9LjIzVtcJLpOJKkqYOT9P6tMzR36gBJ0qurC3Teo8uUnVtiNhgAAAAAIORRKgKApAWr20af50zKkN3eMwtaTkR0uFP3XTRKC66fov6JUdpf1ai5z63Rz//5uaoaOLUIAAAAADCDUhFAyNtdWqucvArZbdIVE/uajnNUUwYl6r1bz9T3pw2QzSa9vnafznt0mT7exqlFAAAAAEDPo1QEEPJeW9t2SnHWsBSluSMNpzm2qDCn7r1wlF6/4QwNTIrWgepGff/5NfrpG5+rqp5TiwAAAACAnkOpCCCktXi8enPdPklto8+BYNKAXnr3x2fquukDZbNJ/1y3T+f+ean+s7XYdDQAAAAAQIigVAQQ0v6ztVhltc1Kjg3X2cNTTMc5YZFhDt19wUj988YzNCgpWsXVTbr2hbW6/bXPdLC+2XQ8AAAAAECQo1QEENIWrGkbfb58Ql+5HIH3V+KE/r307q1n6voZg2S3SW9tKNQ5jy7Th1s4tQgAAAAA8J3A+w4aALpJ0cEGLd1eKkmaMzEwRp+PJsLl0C9nj9A/b5qqwcnRKq1p0g9fXKvbFmxQZR2nFgEAAAAA3Y9SEUDIen1tgSxLOmNQogYkRZuOc8rG90vQ4h+fqRtnDpbdJi38rEjnPLpMH2w+YDoaAAAAACDIUCoCCEker6U31rYtaLlqcuCeUvyyCJdDv/jmcL118zQNTYlRWW2TbvjHOv341Q2q4NQiAAAAAKCbUCoCCEmf7CxT4cEGuSNdOm9Ub9Nxut1pGfF650fTdfOswXLYbVr0eZHOfXSp3tu433Q0AAAAAEAQoFQEEJIWrN4rSbr09HRFuByG0/hGhMuhn39juP5181QNS41VWW2zbnp5vea9sl7ltU2m4wEAAAAAAhilIoCQU1bb1Lkdec6k4Bl9PpaxfeO16EfT9KOzh8hht2nxF/t1zqPLtPgLTi0CAAAAAE4OpSKAkPPW+n1q9VoalxGvEWlxpuP0iHCnQ3ecO0xvz5um4b1jVVHXrHmvrNdNL61TaQ2nFgEAAAAAXUOpCCCkWJalBWsKJElXhcApxS8bne7Wolum68dfGyqn3ab3Nh3QuY8u1aLPi2RZlul4AAAAAIAAQakIIKSsya/U7tI6RYU5dOG4PqbjGBHmtOv2czK1cN40jUiLU2V9i3786gbd+NI6ldQ0mo4HAAAAAAgAlIoAQsqCNW0LWi4c20cx4U7Dacwane7W2/Om6Sdfz5TTbtMHm4t17qPL9PZnhZxaBAAAAAAcF6UigJBR1dCidze2LSeZMzn0Rp+PJsxp161fH6pFt0zXqD5xOljfolsXfKbr/7FOJdWcWgQAAAAAHB2lIoCQseizQjW2eDUsNVanZ8SbjuNXRvaJ08J503THOZlyOWz6cEuxznl0md5av49TiwAAAACAI1AqAggZHQta5kzKkM1mM5zG/7gcdv3oa0P1zo+ma0y6W1UNLbr99c913QtrVcypRQAAAADAISgVAYSEjfuqtLmoWmEOuy49Pd10HL82vHec/nXzVP3svGEKc9j1n20lOueRpfrnOk4tAgAAAADaUCoCCAkdC1q+Mbq3EqLDDKfxf06HXfPOGqJ//3i6xvV1q7qxVT9943P94Pk1OlDFqUUAAAAACHWUigCCXn1zqxZ9ViRJumoSC1q6IjM1Vm/eNFV3fmO4whx2fZxbqnMeXarX1xZwahEAAAAAQhilIoCgt/iL/appalW/XlGaMijRdJyA43TYddOswVr84+k6LSNeNY2t+vk/v9D3nlujooMNpuMBAAAAAAygVAQQ9F47ZEGL3c6ClpM1tP3U4i9nD1eY065l20t17qPLtGD1Xk4tAgAAAECIoVQEENR2ltRo7Z5KOew2XT6hr+k4Ac9ht+n6GYP17o/P1Ph+8aptatUv3tqo7z67WoWcWgQAAACAkEGpCCCodZxSPGtYilLjIgynCR5DUmL0xo1Tdff5IxTutGv5jjKd9+gyvZLDqUUAAAAACAWUigCCVlOrR2+uL5QkfXsyC1q6m8Nu03VnDtJ7t56pif0TVNvUql/+a6O+M3+1CirqTccDAAAAAPgQpSKAoPV/W0pUUdes1LhwzcxMNh0naA1KjtFrN5yhX18wUhEuuz7ZWaZv/HmZ3lhbYDoaAAAAAMBHKBUBBK0Fa/ZKkq6YkCGng7/ufMlht+na6QP1/q0zNHlAL9U1e3Tnm19wzyIAAAAABCm+ywYQlAoq6vXJzjJJ0pUTGX3uKQOSorXg+ika29ctryWt3FVuOhIAAAAAwAcoFQEEpTfWFsiypOlDktQvMcp0nJBit9s0dXCSJGl1HqUiAAAAAAQjSkUAQcfjtfT62n2SpDmTOKVoQtagXpKknLwKw0kAAAAAAL5AqQgg6CzdXqID1Y1KiHLp3FGppuOEpIn9E2S3SXvK63WgqtF0HAAAAABANzulUvHjjz+W0+lUfn7+Vz6bm5uriIgIPf/880e8tnXrVl1wwQVKSEhQWlqafve7351KLAAhbsHqtq3Dl43vq3Cnw3Ca0BQb4dKoPm5JUg4j0AAAAAAQdJwn+4F79+7VnDlz5PF4Tuj5G264QU1NTUe8f/v27Zo+fbpqamp09dVXy7Is/frXv5Yk/fKXvzzZeABCVElNo/6zrUQSo8+mZQ3spY2FVcrJq9DFp6WbjgMAAAAA6EYnVSpu3rxZs2fPVmlp6Qk9/9xzz2np0qVHfe2WW25RRUWF3n77bV100UWSpFGjRumee+7RNddco379+p1MRAAh6p/r9snjtTS+X7wyU2NNxwlpkwf20jOf5ClnNycVAQAAACDYdHn8ed++fZo2bZrcbreuv/76r3y+rKxMP/vZz5SRceSJob179+rDDz/UWWed1VkoStLNN98sl8ult956q6vxAIQwy7L02pq20eerJvMDCdMmD+wlm03aVVqn0pojT6oDAAAAAAJXl0vFsrIyzZ49W8uXL1daWtpXPn/HHXeoqqpKf/nLX4547dNPP5UkXXjhhYe9Pzo6WllZWcrOzu5qPAAhbNXuCu0pr1dMuFPnj/nqv5/gW/FRYRrWflp0TT5boAEAAAAgmHS5VBwzZoxeeeUVud3ur3z2o48+0osvvqif/vSnOu200454vbi4WJI0duzYI14bPHiwdu/e3dV4AELYgjV7JUkXjuuj6PCTvjIW3WjKoERJYgQaAAAAAIJMl0tFh+PENqk2NTXpxhtv1NChQ3Xvvfce9Znq6mpJUnJy8hGvud1uFRUVHffzV1dXH/YGIHQdrG/We5sOSJK+PZkFLf5i8sBekqScPE4qAgAAAEAw6XKpeKIefPBB7dy5U3//+98VERFx1GeczraTRFFRUUe8FhYWpvr6+mN+/oceekhut7vz7Wh3NgIIHQs3FKq51asRaXEak/7VJ6nRMzpKxW0HanSwvtlwGgAAAABAd/FJqbht2zb94Q9/0HXXXaeZM2ce87no6GhJUnPzkd9otrS0HPX9He666y5VVVV1vhUUFJx6cAABybIsLehY0DIpQzabzXAidEiKCdeQlBhJ0mpOKwIAAABA0Oj2UtGyLN1www1KSkrSww8/fNxn+/TpI0nKz88/4rWSkhLFx8cf82PDw8MVFxd32BuA0PT5viptO1CjcKddl5yWbjoOvoQRaAAAAAAIPt2+yeDf//63li1bplmzZh1WKlZVVUmS3nrrLe3cuVPf+ta3NG7cOEnS+vXrNXv27MM+z+rVq5Wamtrd8QAEodfaF7TMHpMmd5TLcBp8WdbAXnolZ69y8ljWAgAAAADBottLxfLytm8as7OzlZ2dfcTr77zzjt555x0NGTJEc+fO1dChQ/XCCy/ozjvvlMvVVgZ88cUX2rJli2644YbujgcgyNQ1tWrRZ21LneZM4m5Vf5Q1sG0D9JaialU3tiguguIXAAAAAAJdt48/z507V5ZlHfGWl5cnSXruuedkWZbmzp0rSbr11lu1c+dO3XTTTWpoaFBhYaF+8IMfyLIsXX311d0dD0CQ+fcXRapr9mhgUrSy2sds4V96uyPUPzFKXktal19pOg4AAAAAoBv4bPvzibr55pt15ZVXav78+YqPj1f//v21bt063XbbbZo+fbrpeAD8XMeCljksaPFrHYXvKkagAQAAACAodPv4c1fZbDa99tprOv/88/Xyyy9Lajvt+O1vf9twMgD+LvdAjTbsPSin3abLxrOgxZ9lDUzU62v3sQEaAAAAAIKEzbIsy3SI7lBdXS23262qqio2QQMh4v53Nuu5Ffk6b1SqnvrORNNxcBz7Kus1/Q8fy2m36Yv7zlVUmPGfaQEAAAAAvqQr/Zrx8WcAOBmNLR79a0OhJOmqyf0Mp8FX6ZsQpfT4SLV6La3bw72KAAAAABDoKBUBBKQlW4p1sL5Fae4IzRiabDoOTkDHvYqMQAMAAABA4KNUBBCQFqzeK0m6YmKGHHYWtASCrEFtpWLObkpFAAAAAAh0lIoAAs6e8jp9uqtcNpt05cS+puPgBE0emChJ+qzgoBpbPIbTAAAAAABOBaUigIDz+toCSdKZQ5PVNyHKcBqcqAGJUUqJDVezx6sNew+ajgMAAAAAOAWUigACSqvHqzfW7pMkXTUpw3AadIXNZlPWoLbTityrCAAAAACBjVIRQED5OLdUJTVNSowO09dHpJqOgy6a3L6sJSev3HASAAAAAMCpoFQEEFBeW9O2oOVbE/oqzMlfYYFmSnupuH5vpZpbvYbTAAAAAABOFt+RAwgYB6oa9dG2EknSlRMZfQ5EQ1Ji1Cs6TI0tXm0sPGg6DgAAAADgJFEqAggY/1xXIK8lTR7QS0NSYkzHwUmw2WyaPKDttOKq3dyrCAAAAACBilIRQEDwei291r71eQ4LWgJa1qCOexUpFQEAAAAgUFEqAggIn+4qV0FFg2IjnJo9Js10HJyCrIFtG6DX5Veo1cO9igAAAAAQiCgVAQSEBe0LWi45LV2RYQ7DaXAqhveOVVyEU3XNHm0uqjYdBwAAAABwEigVAfi9irpmLdlcLInR52Bgt9s0eWDHCHS54TQAAAAAgJNBqQjA7721fp+aPV6NTo/T6HS36TjoBh0j0DksawEAAACAgESpCMCvWZal19Z0LGjpZzgNukvHspbV+RXyeC3DaQAAAAAAXUWpCMCvrd97UDtKahXhsuvi0/qYjoNuMjItTjHhTtU0tmrbAe5VBAAAAIBAQ6kIwK8tWN22oOX8MX0UF+EynAbdxemwa0L/BEmMQAMAAABAIKJUBOC3ahpb9O8v9kuSrprMgpZg0zkCnUepCAAAAACBhlIRgN965/P9amjxaHBytCa2n2pD8Mga+N97FS2LexUBAAAAIJBQKgLwWwvWtI0+XzWpn2w2m+E06G5j0uMV4bKroq5ZO0pqTccBAAAAAHQBpSIAv7S5qEpf7KuSy2HTZePTTceBD4Q5D7lXkRFoAAAAAAgolIoA/NLrawokSeeO7K3EmHDDaeArWQMTJUk5u8sNJwEAAAAAdAWlIgC/09ji0b82FEqS5kxiQUswm9x+r2JOHvcqAgAAAEAgoVQE4Hfe27Rf1Y2tSo+P1PQhSabjwIdOy4hXmNOu0pom5ZfXm44DAAAAADhBlIoA/M6C1W2jz3MmZchuZ0FLMItwOXRaRrwkRqABAAAAIJBQKgLwK7tLa5WTVyG7Tbp8Ql/TcdADsg4ZgQYAAAAABAZKRQB+5bW1bacUZ2Ymq098pOE06AmHLmvhXkUAAAAACAyUigD8RovHqzfX7ZMkXTW5n+E06Cnj+8fLabepqKpR+yobTMcBAAAAAJwASkUAfuM/W0tUVtuspJhwnT08xXQc9JCoMKfG9HVLYgQaAAAAAAIFpSIAv7FgzV5JbXcpuhz89RRKDh2BBgAAAAD4P75rB+AXig42aOn2UkltW58RWrIGtS1rWZ3PSUUAAAAACASUigD8whtr98mypCmDemlgUrTpOOhhE/snyG6T9pTX60BVo+k4AAAAAICvQKkIwDiP19Lr7Vufr5rEgpZQFBvh0qg+HfcqMgINAAAAAP6OUhGAcZ/sLFPhwQbFRTj1jdG9TceBIVkD20agWdYCAAAAAP6PUhGAca+1L2i5bHxfRbgchtPAlKxBLGsBAAAAgEBBqQjAqLLaJn24pVgSC1pC3aQBCbLZpF2ldSqtaTIdBwAAAABwHJSKAIx6a/0+tXgsjevr1oi0ONNxYFB8VJiGpcZKklYzAg0AAAAAfo1SEYAxlmVpwZr2BS2TWdACaUr7CPRqlrUAAAAAgF+jVARgzJr8Su0urVNUmEMXjutjOg78wGSWtQAAAABAQKBUBGDMgvYFLReMTVNMuNNwGviDjlJx24EaVdY1G04DAAAAADgWSkUARlQ1tOjdjfslMfqM/0qKCdeQlBhJ0pp8TisCAAAAgL+iVARgxKLPCtXY4lVmaoxOz4g3HQd+hBFoAAAAAPB/lIoAjOhY0DJnUj/ZbDbDaeBPsjpLRZa1AAAAAIC/olQE0OM2FVZpc1G1whx2XXZ6uuk48DMdG6C3FFWrurHFcBoAAAAAwNFQKgLoca+ublvQct7o3kqIDjOcBv4mNS5CAxKj5LWkdfmVpuMAAAAAAI6CUhFAj6pvbtWiz4okSVdNyjCcBv6q417FVYxAAwAAAIBfolQE0KPe3XhANU2t6tcrSme0j7kCX5Y1sO1/Gzm7WdYCAAAAAP6IUhFAj3ptTdvo85xJGbLbWdCCo8sa1HZScVNhleqaWg2nAQAAAAB8GaUigB6zs6RGa/IrZbdJl0/oazoO/FjfhCilx0eq1Wtp/V7uVQQAAAAAf0OpCKDHvLamQJJ09vAUpcZFGE4Df5fVfq8iI9AAAAAA4H8oFQH0iOZWr95cXyhJumpSP8NpEAg6RqBX51EqAgAAAIC/oVQE0CM+3FKsirpmpcSGa9awZNNxEAAmty9r+azgoBpbPIbTAAAAAAAORakIoEcsaF/QcsXEvnI6+KsHX21AYpRSYsPV7PFqw96DpuMAAAAAAA7Bd/YAfK6gol6f7CyTJM2ZyOgzTozNZlPWoLbTioxAAwAAAIB/oVQE4HNvrC2QZUnThiSqX2KU6TgIIJ3LWvLKDScBAAAAAByKUhGAT3m8ll5fu0+SNIcFLeiijlJx/d5KNbd6DacBAAAAAHSgVATgU8u2l+pAdaPio1w6b1Sq6TgIMENSYpQYHabGFq82Fh40HQcAAAAA0I5SEYBPvbq6bUHLZaf3VbjTYTgNAo3NZtPk9tOKq3ZzryIAAAAA+AtKRQA+U1LTqP9sK5EkXTU5w3AaBKrJnfcqUioCAAAAgL+gVATgM2+uK5THa2l8v3hlpsaajoMAlTWwbQP0uvwKtXq4VxEAAAAA/MEplYoff/yxnE6n8vPzj3ittrZWd9xxh/r16yeHwyG3260bb7xR9fX1Rzy7b98+/c///I+Sk5OVmJio22+/XS0tLacSDYBhlmXptTVto89XsaAFp2B471jFRThV1+zR5qJq03EAAAAAADqFUnHv3r2aM2eOPB7PEa95PB7Nnj1bjzzyiLxer775zW8qKipKTz31lK688srDni0tLdWMGTO0YMECnXXWWbriiiv05JNPat68eScbDYAfWLW7Qvnl9YoOc+j8sWmm4yCA2e22Q0agyw2nAQAAAABIJ1kqbt68WWeeeaZKS0uP+vqzzz6r5cuX68c//rHy8vL073//W9u3b9eoUaO0ePFibdiwofPZX/3qV8rLy9Njjz2m119/XU8++aReeOEFPfPMM8rJyTm53xUA4zpOKV50Wrqiw52G0yDQdYxA57CsBQAAAAD8QpdLxX379mnatGlyu926/vrrj/rMiy++qNNOO02PPfaYXC6XJCk2NlbXXXedJGn9+vWSpKamJr300ksaNGiQbrnlls6Pv/zyyzVo0CC9+uqrXf4NATDvYH2z3t10QJJ01SQWtODUZQ1qO6m4Or9CHq9lOA0AAAAAoMulYllZmWbPnq3ly5crLe3oI43Lly/XJ598csT7m5ubJUlOZ9uppQ0bNqihoUEXXHCBbDZb53M2m01nnXWWsrOzuxoPgB9YuKFQza1eDe8dq7F93abjIAiMTItTTLhTNY2t2naAexUBAAAAwLQul4pjxozRK6+8Irf7+EVBdHT0Ee979913JUlZWVmSpOLiYknS2LFjj3h28ODBysvL62o8AIZZlqUFawoktZ1SPPQHBsDJcjrsmtA/QRIj0AAAAADgD7pcKjocjpP6hVatWqWlS5dqxowZGj58uCSpurrttElycvIRz7vdblVXV6u2tvaon6+pqUnV1dWHvQEw7/N9Vdp2oEZhTrsuPb2v6TgIIp0j0HmUigAAAABg2klvf+4Kj8ejefPmyWaz6fe//33n+zvGoKOioo74mLCwMElSfX39UT/nQw89JLfb3fmWkcG9bYA/6FjQMnt0b7mjXIbTIJh0LGtZnV8hy+JeRQAAAAAwqUdKxQcffFDr16/XvHnzdMYZZ3S+v2NEuuOuxUO1tLQc8zVJuuuuu1RVVdX5VlBQ4IPkALqirqlViz4rkiTNmdTPcBoEmzHpbkW47Kqoa9aOkqOfYgcAAAAA9Ayfl4orVqzQAw88oNGjR+v//b//d9hrffr0kSTl5+cf8XElJSWSpPj4+KN+3vDwcMXFxR32BsCsf39RpLpmjwYkRmlK+6gq0F3CnIfeq1huOA0AAAAAhDaflorFxcWaM2eOIiIi9PrrrysyMvKw10eOHCmXy6X169cf8bGrV69WVFSUYmJifBkRQDfqWNAyZ1I/FrTAJzpGoHO4VxEAAAAAjPJZqdjQ0KBLL71UhYWFeuaZZzRixIgjnomKitKsWbP0z3/+UwcPHux8f3FxsT766CPNmDHDV/EAdLPcAzXasPegnHabvjUh3XQcBKnJA9tOwObkca8iAAAAAJjkk1LR4/Hoiiuu0MqVK/Wzn/1Mc+bMOeazt956q6qqqnT11VersrJSBw8e1Ny5c9XQ0KBrrrnGF/EA+MBr7acUvzYiRSmxEYbTIFidlhGvMKddpTVNyiurMx0HAAAAAEKW0xef9OWXX9bixYsVGRkph8Ohu++++7DXx48fr8suu0ySdP755+unP/2p/vjHPyolJUU2m00tLS265JJLdPXVV/siHoBu1tji0Vsb9kmSrmJBC3wowuXQaRnxWp1XodV5FRqUzBUZAAAAAGCCT0rFTz75RFLbCPTvf//7I17/3ve+11kqStLDDz+smTNn6umnn1Ztba2+9a1v6aabbvJFNAA+sGRLsQ7WtyjNHaEZmcmm4yDIZQ3spdV5FcrJq9BVkymxAQAAAMAEmxUkl1JVV1fL7XarqqqKTdBAD7v6mVVasbNcP/7aUN1+TqbpOAhyn+wo0zXzc9THHaEVvzibpUAAAAAA0E260q/5dPszgOC3p7xOK3aWy2aTrpjQ13QchIDx/ePltNtUVNWofZUNpuMAAAAAQEiiVARwSl5f27agZfqQJGX0ijKcBqEgKsypsX3dktq2QAMAAAAAeh6lIoCT1urx6o21bQtavs3dduhBkwcmSpJydpcbTgIAAAAAoYlSEcBJ+zi3VCU1TeoVHaavj0g1HQchJGtQL0mcVAQAAAAAUygVAZy019bslSR9a3y6wpz8dYKeM7F/guw2aW9FvfZXca8iAAAAAPQ0WgAAJ+VAVaM+2lYiSZozidFn9KzYCJdG9Wm7V3E1pxUBAAAAoMdRKgI4Kf9cVyCvJU0akKAhKTGm4yAEZQ1sG4FetZtSEQAAAAB6GqUigC7zei291r71mVOKMCVrUNuyltV5LGsBAAAAgJ5GqQigy1buLldBRYNiw52aPaa36TgIUZMGJMhmk3aV1qm0psl0HAAAAAAIKZSKALrs1dVtC1ouPr2PosKchtMgVMVHhWlYaqwk7lUEAAAAgJ5GqQigSyrqmrVkc7Ek6SpGn2HYFEagAQAAAMAISkUAXfKvDYVq9ng1qk+cRqe7TcdBiOtY1pLDSUUAAAAA6FGUigBOmGVZWtA++nzVZE4pwrxJ7aXitgM1qqxrNpwGAAAAAEIHpSKAE7Z+70HtKKlVhMuui8b1MR0HUFJMuIakxEiSVudzWhEAAAAAegqlIoAT9tqatlOKs8ekyR3pMpwGaNMxAs2yFgAAAADoOZSKAE5ITWOL3vl8vyTp24w+w49M7rxXkWUtAAAAANBTKBUBnJB3Pt+vhhaPBiVHa2L/BNNxgE4dG6C3FFWrurHFcBoAAAAACA2UigBOSMfo81WTMmSz2QynAf4rNS5CAxKj5LWkdfmVpuMAAAAAQEigVATwlbYUVevzfVVyOWy6bHxf03GAI3SMQK9iBBoAAAAAegSlIoCv1HFK8ZyRqUqKCTecBjhS1sC2Eeic3SxrAQAAAICeQKkI4LgaWzz614ZCSdKcSSxogX/KGtR2UnFTYZXqmloNpwEAAACA4EepCOC43t90QNWNrUqPj9SZQ5JMxwGOqm9ClNLjI9XqtbR+L/cqAgAAAICvUSoCOK5XV7eNPl85MUN2Owta4L+y2u9VZAQaAAAAAHyPUhHAMe0urVVOXoVsNumKiSxogX/rGIFenUepCAAAAAC+RqkI4JheX7tPkjQzM1l94iMNpwGOr2NZy2cFB9XY4jGcBgAAAACCG6UigKNq8Xj1z3VtpeJVLGhBAOifGKWU2HA1e7zasPeg6TgAAAAAENQoFQEc1X+2lqistklJMWH62ogU03GAr2Sz2ZQ1qO20Yk5eueE0AAAAABDcKBUBHNVra9oWtHxrQl+5HPxVgcDQsayFexUBAAAAwLdoCgAcoehgg5ZuL5XE6DMCS0epuH5vpZpbvYbTAAAAAEDwolQEcIQ31u6T12oraAYmRZuOA5ywISkxSowOU2OLV1/sO2g6DgAAAAAELUpFAIfxeC29vrZAknTV5AzDaYCusdlsmtx+WjGHEWgAAAAA8BlKRQCH+WRnmQoPNiguwqlvjk4zHQfoMkpFAAAAAPA9SkUAh+lY0HLp6emKcDkMpwG6Lmtg2wbodfkVavVwryIAAAAA+AKlIoBOZbVN+nBLsSRpDgtaEKCG946VO9KlumaPNhdVm44DAAAAAEGJUhFAp7fW71OLx9K4vm6N7BNnOg5wUux2myYN6BiBLjecBgAAAACCE6UiAEmSZVlasKZtQQunFBHosjruVdzNvYoAAAAA4AuUigAkSWv3VGp3aZ0iXQ5dOI4FLQhsWYPaSsXV+RXyeC3DaQAAAAAg+FAqApAkvbq6bUHLhePSFBvhMpwGODUj0+IUE+5UTWOrth3gXkUAAAAA6G6UigBU1dCidzful8ToM4KD02HXhP4JkhiBBgAAAABfoFQEoEWfF6mxxauhKTEa3y/edBygW3SMQLOsBQAAAAC6H6UiAC1oH32+anI/2Ww2w2mA7pE1MFGStDqvQpbFvYoAAAAA0J0oFYEQt6mwSpuLqhXmsOvS09NNxwG6zZh0tyJcdlXWt2hHSa3pOAAAAAAQVCgVgRC3YE3bKcVzR6WqV3SY4TRA9wlzHnqvIiPQAAAAANCdKBWBENbQ7NHbG4okSVexoAVBqGMEOiePZS0AAAAA0J0oFYEQ9nFuiWqaWtU3IVJTByeajgN0u6yBHctauFcRAAAAALoTpSIQwrJzSyRJ543qLbudBS0IPuMy4hXmtKu0pkl5ZXWm4wAAAABA0KBUBEKUZVlaur1UkjQzM9lwGsA3IlwOnZYRL4kRaAAAAADoTpSKQIjaur9GxdVNinQ5NLl9RBQIRlPa//e9mlIRAAAAALoNpSIQojpOKZ4xOFERLofhNIDvTO5Y1rK7nHsVAQAAAKCbUCoCIarjPkVGnxHsxvePl9NuU1FVo/ZVNpiOAwAAAABBgVIRCEE1jS1at6dSkjRrGKUigltUmFNj+7olca8iAAAAAHQXSkUgBK3YWa5Wr6WBSdHqnxhtOg7gc4eOQAMAAAAATh2lIhCClm5n9BmhJWtQ27IWTioCAAAAQPegVARCjGVZys5tW9Iyk9FnhIiJ/RNkt0l7K+q1v4p7FQEAAADgVFEqAiFmR0mt9lc1Ktxp1xmDEk3HAXpEbIRLo9Pb7lVczWlFAAAAADhllIpAiOnY+jxlUKIiXA7DaYCeM3lA2wj0qt2UigAAAABwqigVgRDTOfrMfYoIMVntJ3Nz8ljWAgAAAACnilIRCCG1Ta1ak992SmsW9ykixEwe0Es2m7S7tE6lNU2m4wAAAABAQKNUBELIyl3lavFY6tcrSgOTok3HAXqUO8qlYamxkrhXEQAAAABOFaUiEEI67lOcmZksm81mOA3Q86YwAg0AAAAA3eKUSsWPP/5YTqdT+fn5R339hRde0OjRoxUZGamJEydq5cqVR31u69atuuCCC5SQkKC0tDT97ne/O5VYAI7CsqzO+xQZfUaoyhrYtqyFk4oAAAAAcGpOulTcu3ev5syZI4/Hc9TX//jHP2ru3Lmqra3VTTfdJLvdrq9//evasWPHYc9t375d06dP15IlS3TJJZfovPPO069//WuKRaCb7SqtU+HBBoU57DpjcKLpOIARk9pLxW0HalRZ12w4DQAAAAAELufJfNDmzZs1e/ZslZaWHvX1/Px8/epXv9LgwYO1Zs0aJSQkqKWlRZMmTdKtt96qd999t/PZW265RRUVFXr77bd10UUXSZJGjRqle+65R9dcc4369et3MhEBfEnH6HPWoF6KCjupP/pAwEuKCdeQlBjtLKnV6vwKnTeqt+lIAAAAABCQunxScd++fZo2bZrcbreuv/76oz7z4osvqrm5Wffcc48SEhIkSS6XS3fccYc++OADlZe33WW1d+9effjhhzrrrLM6C0VJuvnmm+VyufTWW2+dzO8JwFEs3d72Q4CZmYw+I7QxAg0AAAAAp67LpWJZWZlmz56t5cuXKy0t7ajPfPrpp7LZbLrgggsOe//ZZ58tr9er5cuXdz4nSRdeeOFhz0VHRysrK0vZ2dldjQfgKOqbW5Wzu61A4T5FhLoslrUAAAAAwCnrcqk4ZswYvfLKK3K73cd8pri4WOnp6erVq9dh709PT1d4eLh2797d+ZwkjR079ojPMXjw4M7nAJyaVbvL1ezxKj0+UoOTY0zHAYzqOKm4paha1Y0thtMAAAAAQGDqcqnocDi+8pnq6molJx/9NJTb7VZRUVHnc5KO+uyhzx1NU1OTqqurD3sDcHQdW59nDkuWzWYznAYwKzUuQgMSo+S1pHX5labjAAAAAEBAOuntz8fjdDoVFRV11NfCwsJUX1/f+Zykoz576HNH89BDD8ntdne+ZWRkdENyIPhYltVZKs7iPkVAkpQ1sG0EehUj0AAAAABwUnxSKkZHR6u5ufmor7W0tHS+Fh0dLUlHffbQ547mrrvuUlVVVedbQUFBNyQHgk9+eb32VtTL5bBp6pAk03EAvzC5fQS6465RAAAAAEDX+KRU7NOnj/Lz8494v8fjUUVFheLj4zufk3TUZ0tKSjqfO5rw8HDFxcUd9gbgSNm5JZKkif17KSbcaTgN4B+yBrWVihsLq1TX1Go4DQAAAAAEHp+UiuPGjVNpaekRpwc3bNiglpYWpaamdj4nSevXrz/ic6xevbrzOQAnr3P0ma3PQKe+CVFKj4+Ux2tp/V7uVQQAAACArvJJqXj++edLkubPn3/Y+1999VVJ0owZMyRJQ4cO1dChQ/XCCy+opeW/Gzi/+OILbdmypfM5ACenscWjVbvb7oybNSzFcBrAv2QxAg0AAAAAJ80npeLUqVM1ceJE/eEPf9CiRYskSQsXLtQTTzyhzMxMTZ48ufPZW2+9VTt37tRNN92khoYGFRYW6gc/+IEsy9LVV1/ti3hAyFi1u1xNrV71jotQZmqM6TiAX+kYgc5hWQsAAAAAdJnPLlh7+eWXdfbZZ+viiy9WVFSU6uvrFRUVpeeee042m63zuZtvvlnLli3T/Pnz9Y9//EMej0cej0e33Xabpk+f7qt4QEg4dPT50D93AP67Afrzgio1tngU4XIYTgQAAAAAgcNnpWJmZqa++OIL/e53v1NOTo4GDBige+65R0OHDj3sOZvNptdee03nn3++Xn75ZUnS3Llz9e1vf9tX0YCQsWw79ykCx9I/MUopseEqqWnShr0HdcbgRNORAAAAACBg2CzLskyH6A7V1dVyu92qqqpiEzQgaW95vWY8/LGcdpvW33OO4iJcpiMBfudHr27QO58X6bavD9VtX880HQcAAAAAjOpKv+aTOxUBmJe9vUSSNL5/AoUicAwdy1pW57GsBQAAAAC6glIRCFJLcxl9Br7KlPZlLev3Vqq51Ws4DQAAAAAEDkpFIAg1tnj06a62jbazMlMMpwH81+DkGCVGh6mxxasv9h00HQcAAAAAAgalIhCE1uRXqKHFo5TYcI1IizUdB/BbNptNk9tHoHMYgQYAAACAE0apCASh7PbR55mZybLZbIbTAP4ti1IRAAAAALqMUhEIQku3d9ynyOgz8FUmD0yUJK3Lr1Crh3sVAQAAAOBEUCoCQWZfZb12ltTKbpOmD0kyHQfwe8N7x8od6VJds0ebiqpNxwEAAACAgECpCASZjtHn8f0S5I5yGU4D+D+73aZJA9pGoFfnlRtOAwAAAACBgVIRCDL/HX1ONpwECByd9yru5l5FAAAAADgRlIpAEGlu9erTnWWSuE8R6IqsQe0nFfMr5PFahtMAAAAAgP+jVASCyNr8CtU1e5QUE6aRaXGm4wABY2RanGLCnappbNW2A9yrCAAAAABfhVIRCCIdo88zMpNlt9sMpwECh9Nh18QBCZIYgQYAAACAE0GpCASRjiUtjD4DXTe5415FlrUAAAAAwFeiVASCRNHBBuUW18huk84ckmQ6DhBwsgYmSpJW51XIy72KAAAAAHBclIpAkFjWPvo8LiNeCdFhhtMAgWdMuluRLocq61u0s7TWdBwAAAAA8GuUikCQ6Bx9zmT0GTgZYU67xvePlyTl7GYEGgAAAACOh1IRCAItHq9W7CyTJM0clmw4DRC4OkagV+WxrAUAAAAAjodSEQgC6/ZUqqapVb2iwzQ23W06DhCwstqXtazOq5Blca8iAAAAABwLpSIQBJa236c4Y2iS7Hab4TRA4BqXEa8wp12lNU3KK6szHQcAAAAA/BalIhAEOu5TZPQZODURLodOy4iXJOUwAg0AAAAAx0SpCAS44upGbd1fLZtNmjGUUhE4VVMOGYEGAAAAABwdpSIQ4DpGn8emu5UYE244DRD4sga1LWvJ2V3OvYoAAAAAcAyUikCAW9o5+pxiOAkQHE7vFy+n3aaiqkbtq2wwHQcAAAAA/BKlIhDAWj1eLd/RXipmMvoMdIeoMKfG9m3bos69igAAAABwdJSKQAD7rOCgqhtbFR/l6lwuAeDUHToCDQAAAAA4EqUiEMA6tj6fOTRZDrvNcBogeExuX9bCSUUAAAAAODpKRSCAZW8vkcToM9DdJvZPkN0m7a2o1/4q7lUEAAAAgC+jVAQCVGlNkzYVVkuiVAS6W2yES6PT2+5VXM1pRQAAAAA4AqUiEKCWbW8bfR6dHqfk2HDDaYDgM3lA2wj0qt2UigAAAADwZZSKQIDK3s7WZ8CXOpe15LGsBQAAAAC+jFIRCEAer6XlO9pKxVnDUgynAYLT5AG9ZLNJu0vrVFrTZDoOAAAAAPgVSkUgAH2+76AO1rcoLsKp0zPiTccBgpI7yqVhqbGSuFcRAAAAAL6MUhEIQNm5bacUzxyaLKeDP8aAr0xhBBoAAAAAjoo2AghAS3NLJHGfIuBrWQPblrVwUhEAAAAADkepCASY8tomfVFYJUmaOYxSEfClye2l4rYDNaqsazacBgAAAAD8B6UiEGCW7yiTZUkj0uKUGhdhOg4Q1BJjwjUkJUaStDqf04oAAAAA0IFSEQgw2Yw+Az2qYwQ6ZzelIgAAAAB0oFQEAojXa2nZjjJJ0ixGn4EekdW+rGV1PstaAAAAAKADpSIQQDYWVqmirlkx4U5N6J9gOg4QEjpOKm4pqlZ1Y4vhNAAAAADgHygVgQCSnVsqSZo2JFEuB398gZ6QGhehAYlR8lrSWu5VBAAAAABJlIpAQFm6ve0+xVnDUgwnAUJL1sC2EeicPEpFAAAAAJAoFYGAcbC+WZ8VHJTEkhagp01mWQsAAAAAHIZSEQgQy3aUyWtJmakx6hMfaToOEFKyBrWVihsLq1TX1Go4DQAAAACYR6kIBIjsXEafAVP6JkQpPT5SHq+l9XsrTccBAAAAAOMoFYEA4PVaWra9TJI0i9FnwIiO04qMQAMAAAAApSIQELbsr1ZZbZOiwhyaOKCX6ThASMrquFcxr9xwEgAAAAAwj1IRCAAdo89TBycpzMkfW8CEjg3QnxdUqbHFYzgNAAAAAJhFOwEEgKXbSyVJs4Yx+gyY0j8xSqlx4Wr2eLVh70HTcQAAAADAKEpFwM9VNbRofXuBMZP7FAFjbDabJrefVmQEGgAAAECoo1QE/NwnO8rk8VoanBytjF5RpuMAIa3zXkWWtQAAAAAIcZSKgJ9bur3tPsVZw1IMJwEwpX0D9Pq9lWpu9RpOAwAAAADmUCoCfsyyLO5TBPzI4OQYJUaHqanVqy/2HTQdBwAAAACMoVQE/NjW/TUqrm5SpMuhSQN6mY4DhLy2exXbR6DzGIEGAAAAELooFQE/lt0++nzG4ERFuByG0wCQDrlXkVIRAAAAQAijVAT82NJcRp8Bf5M1qG0D9Lr8CrV6uFcRAAAAQGiiVAT8VE1ji9btqZQkzcpkSQvgL4alxsod6VJds0ebiqpNxwEAAAAAIygVAT+1YmeZWr2WBiZFq19ilOk4ANrZ7bbOO05zdpcbTgMAAAAAZlAqAn6qY+vzzExGnwF/M2VQW6m4mnsVAQAAAIQoSkXAD1mWpWzuUwT8VscG6NX5FfJ4LcNpAAAAAKDnUSoCfmh7ca32VzUq3GnXlPalEAD8x8i0OMWEO1XT2Kqt+7lXEQAAAEDooVQE/NDS7SWSpCmDEhXhchhOA+DLnA67Jg5IkMQINAAAAIDQ5NNS8YMPPtD06dOVkJCgXr16afr06Vq8ePERz7377ruaPHmyoqOjNXLkSL3zzju+jAX4PUafAf/XMQKdk8eyFgAAAAChx2elYnZ2ts4//3zV1tZq7ty5uvTSS7V161ZdeOGFeu211zqfe+ONN3TBBRcoPz9f1113ndLT03XppZdq+fLlvooG+LXaplatyW87+cSSFsB/ZQ1su5pgdV6FvNyrCAAAACDE2CzL8sl3QlOnTlVDQ4PWrFkjp9MpSTpw4IBGjRql3r17a/PmzaqurtbgwYPl9Xq1fv169e/fX5J0wQUXKC8vT5s3bz7hX6+6ulput1tVVVWKi4vzxW8J6BEfbinWD19cq369orT0Z7Nks9lMRwJwFM2tXo27f4kaWjxa8pMZykyNNR0JAAAAAE5JV/o1n51UXL9+vc4555zOQlGSevfurfHjx2vnzp2SpIULF6qsrEy33nprZ6EoSXfeeae2bNmizz77zFfxAL+Vndt2n+KsYckUioAfC3PaNb5/vCQpZzcj0AAAAABCi89KxaioKG3duvWw97W2tio3N1cDBgyQJH366aeSpAsvvPCw58444wxFREQoOzvbV/EAv2RZVud9iow+A/6vYwR6FctaAAAAAIQYn5WKl112mRYvXqw///nPqqys1L59+3TDDTeooKBA3//+9yVJxcXFstvtGjVq1GEf63Q61b9/f+3evdtX8QC/tKu0VoUHGxTmsOuMwYmm4wD4Clnty1pW51XIR7eJAAAAAIBfcn71IyfnscceU2FhoX7yk5/oJz/5iSTJZrPp17/+te68805J/53TDgsLO+Lj3W63ioqKjvn5m5qa1NTU1Pnv1dXV3fw7AHpexynFrEG9FBXmsz+eALrJuIx4hTntKq1pUl5ZnQYlx5iOBAAAAAA9wmcnFXNycrRixQpJUlJSkuLi4mRZlt58802tW7dOUtuJxKioqKN+fFhYmOrr64/5+R966CG53e7Ot4yMjO7/TQA9bOl2Rp+BQBLhcui0jHhJUg4j0AAAAABCiE9KxdbWVn33u99VeHi4Pv74Y5WWlqqyslLPP/+8du3apdmzZ6u+vl7R0dFqbm4+6udoaWk55muSdNddd6mqqqrzraCgwBe/FaDH1De3Kmd3WykxaxilIhAoprSPQLOsBQAAAEAo8UmpuGLFChUWFurXv/61Zs2a1fYL2e363ve+pzvvvFOlpaX68MMP1adPH5WWlh71RGJJSYni4+OP+WuEh4crLi7usDcgkK3aXa5mj1fp8ZEazAglEDCyBrXdf5rDvYoAAAAAQohPSsX9+/dLkoYNG3bEax1LWYqKijRu3DhJ0vr16w97pqysTHl5eUpNTfVFPMAvddynOGtYsmw2m+E0AE7U6f3i5bTbtL+qUfsqG0zHAQAAAIAe4ZNSMTm5bXRz165dR7z22WefSZISExP1zW9+U3a7Xc8888xhz7z66quSpBkzZvgiHuB3LMvqLBW5TxEILFFhTo3t65bUduIYAAAAAEKBT0rFESNGyG6365FHHtHOnTs73//vf/9bjz32mMLDwzVr1iz17dtXl112mV588UU9/fTTsixLK1as0P33399ZOgKhIL+8Xnsr6uVy2DR1SJLpOAC6qGMEejXLWgAAAACECJ+Uin369NFNN92kXbt2KTMzUykpKYqOjtaFF16o+vp6/eIXv1BKSook6YknntDw4cN1ww03KDo6WtOnT1dVVZWeeuop7klEyMjOLZEkTRrQSzHhTsNpAHTV5I5lLZSKAAAAAEKEz9qLv/71rxoxYoSeffZZbd26VV6vV6NHj9b111+vH/3oR53PJScna926dXr44Yf1n//8R8nJyfrFL36hiRMn+ioa4HcYfQYC28T+CbLbpL0V9dpf1aA0d6TpSAAAAADgUzYrSFZVVldXy+12q6qqihOOCCiNLR6Nu3+Jmlq9+uC2GRrWO9Z0JAAn4aL//URf7KvSY1edpotPSzcdBwAAAAC6rCv9mk/GnwGcuFW7y9XU6lWaO0KZqTGm4wA4SVntI9CrdjMCDQAAACD4USoChnWMPs8aliybzWY4DYCTNXlg27KWnDw2QAMAAAAIfpSKgGFLt3OfIhAMJg/oJZtN2l1ap5KaRtNxAAAAAMCnKBUBg/aU1ymvrE5Ou03ThiSZjgPgFLijXBreu+3OkTV5lYbTAAAAAIBvUSoCBnWcUpzQP0GxES7DaQCcqo57FRmBBgAAABDsKBUBgzruU5w5jNFnIBh0loosawEAAAAQ5CgVAUMaWzxauavtNNOszBTDaQB0h8ntpWJucY0q65oNpwEAAAAA36FUBAxZk1+hhhaPUmLDNSIt1nQcAN0gMSZcQ1JiJEmr8zmtCAAAACB4USoChnSOPmcmy2azGU4DoLswAg0AAAAgFFAqAoZ0LGmZNYzRZyCYZA1KlCStzmdZCwAAAIDgRakIGLCvsl47S2rlsNs0fWiS6TgAulHHScUtRdWqbmwxnAYAAAAAfINSETCgY/R5fL94uSNdhtMA6E6pcREakBglryWt5V5FAAAAAEGKUhEwoGP0eWZmsuEkAHwha2DbCDT3KgIAAAAIVpSKQA9rbvXq051lkrhPEQhWWYPal7XkUSoCAAAACE6UikAPW5tfobpmj5JiwjUyLc50HAA+MLn9XsWNhVWqa2o1nAYAAAAAuh+lItDDsttHn2dkJslutxlOA8AX+iZEKT0+Uh6vpXV7Kk3HAQAAAIBuR6kI9LCl7UtaGH0GglvHCPRqRqABAAAABCGn6QBAKCk62KDc4hrZbdKZQ5JMxwHgQ1kDe+mt9YXKySs3HQUAAL/V1OrRTS+t18pd5UqJC1dqbETb/42LUGr7/02J/e8/R4fzLSwA+Av+RgZ6UMfW53EZ8UqIDjOcBoAvdWyA/rygSo0tHkW4HIYTAQDgXyzL0l1vbdRH20okSXvK67WnvP64HxMb7jykdIzoLCJT4yLU2x2ulPZSMtzJf3cBwNcoFYEe1Dn6nMnoMxDs+idGKTUuXMXVTdqw96DOGJxoOhIAAH7l8Y936q31hXLYbfrznNPU2x2h4upGFVc3qaS6UQeqG1Vc3aiS6iYdqG5UfbNHNU2tqilt1a7SuuN+7oQoV3vpGKHU2PYS0n3IP8dFKCkmTE4HN4IBwMmiVAR6SIvHqxU7yyRJs4YlG04DwNdsNpuyBiZq0edFyskrp1QEAOAQ73xepD8u2S5Juu+iUbpwXJ+v/Jjaptb20vG/RWPHPxdXN6q4pq2QbG71qrK+RZX1Ldp2oOaYn89mk5JiwttGq2M7SsdDxq7b/2+vqDAWLALAUVAqAj1k3Z5K1TS1qld0mMaku03HAdADJg/s1VYq7mZZCwAAHdbtqdQdb3wuSbp2+kB9Z0r/E/q4mHCnYpJjNDg55pjPWJalqoYWFbcXjQeqG1XSfvqxrXhsOwVZUtMkj9dSaU2TSmuatEnVx/ycTrtNKbHhh5WOKe2nHVMPGcWOi3DKZqN8BBA6KBWBHtJxn+KMoUn8pBMIEVPaN0Cv31upplYP9zsBAEJeQUW9rn9xrZpbvfr6iBT9cvaIbv38NptN8VFhio8K07Descd8zuO1VFHX3HnysaN0LKk5pICsblJ5XZNavZaKqhpVVNV43F87wmVvKxiPsWymt7vt36PC+DYcQHDgbzOgh2R33Kc4jPsUgVAxODlGidFhKq9r1sZ9VZo4oJfpSAAAGFPd2KIfPL9G5XXNGpkWp8euOl0OQz9sd9htSo4NV3JsuEYfZ4qoxeNVac1/S8a20vHQ4rHtn6saWtTY4j3pZTO9Dzn5yLIZAIGCUhHoAcXVjdq6v1o2m3Tm0CTTcQD0EJvNpskDe+m9TQeUk1dBqQgACFktHq/mvbxeO0pqlRoXrvlzJyo63P+/HXU57OoTH6k+8ZHHfa6xxdN2t+MhpWN3L5vp7W77515RYWLw6cREhjl05tBkY+U1EOz8/29xIAh0jD6PTXcrMSbccBoAPSmrvVRctbtc884aYjoOAAA9zrIs3btos5bvKFOky6H535ukNPfxS7pAE+FyqF9ilPolRh33ue5eNoOvdstZQ/TT84aZjgEEJUpFoAcsbR99nsnoMxBysga1bX1et6dSrR6vnA674UQAAPSs+Z/k6ZWcvbLZpMeuOu2448bBrqvLZv5bOv535LqyvrkHEweuFo+lzwoO6qllu3TJ6ekaknLs/58DODmUioCPtXq8Wr6j4z7FZMNpAPS0Yamxcke6VNXQok1F1TotI950JAAAesyHW4r14LtbJUm/mj1C547qbTiR/zvRZTM4PsuydO0La/XRthL9euEmvfLDLLZzA92M4xKAj20oOKjqxlbFR7k0rm+86TgAepjdbtOk9rsUc3aXG04DAEDP2VRYpR+/ukGWJV2d1U/XTh9oOhJCiM1m0/0XjVK4066Vu8v19mdFpiMBQYdSEfCxjtFnLggGQteUQW2l4uq8CsNJAADoGfurGnTtC2vU0OLRmUOTdN9Fozglhh6X0StKPzq77U7r3y7eoqqGFsOJgOBCqQj4WPb2EknSrExGn4FQNXlge6mYXyGP1zKcBgAA36pratW1z69VcXWThqbE6PGrx8vFncIw5IczBmlQcrTKapv1pyW5puMAQYW/2QEfKqlp1KbCaknSDEpFIGSNTItTTLhTNY2t2rq/2nQcAAB8xuO1dOuCDdqyv1pJMWF6du4kxUW4TMdCCAt3OvTbi0dLkv6xao++2HfQbCAgiFAqAj60fHuZJGl0epySY8MNpwFgitNh18QBCZIYgQYABLffvbtV/7e1RGFOu57+7kRl9IoyHQnQ1CFJuuS0PrIs6Vf/2sTkCNBNKBUBH8re3r71OTPFcBIApmUNTJQk5eSxrAUAEJz+sWqP5n+SJ0n60xXjNL5fguFEwH/98vwRio1wamNhlV7O2WM6DhAUKBUBH/F4LS3f0V4qDmP0GQh1nfcq5lXIy0/HAQBBZun2Ut23aLMk6afnZurCcX0MJwIOlxIboZ+dN0yS9PAHuSqpaTScCAh8lIqAj3y+76AO1rcoLsKp0zLiTccBYNjYvm5FuhyqrG/RjpJa03EAAOg2uQdqdMvL6+XxWrpsfLrmnTXEdCTgqK7O6q+xfd2qaWzV7xZvNR0HCHiUioCPZOe2nVI8c2iynGy7A0Key2HXhP4d9yoyAg0ACA6lNU36wfNrVNPUqskDe+mhy8bIZrOZjgUclcNu028vGS2bTVr4WZE+3VlmOhIQ0Gg6AB9ZmlsiSZrJ6DOAdh0j0KtY1gIACAKNLR798MW1KjzYoAGJUXrqmgkKdzpMxwKOa2zfeF2T1V+SdPfbm9TU6jGcCAhclIqAD5TXNumLwipJ0sxMSkUAbbLaS8Wc3RWyLO5VBAAELq/X0h2vf67PCg7KHenSs3MnKSE6zHQs4IT89LxhSooJ1+7SOj2zPM90HCBgUSoCPrB8R5ksSxqRFqfUuAjTcQD4iXEZ8Qpz2lVW26S8sjrTcQAAOGmPfLhdizful8th01PfmaBByTGmIwEnzB3p0t3nj5Ak/eU/O1RQUW84ERCYKBUBH8huH31m6zOAQ0W4HJ2Lm3IYgQYABKg31hbofz/eKUl66LKxmjIo0XAioOsuPq2PzhiUqKZWr+5dtJkpEuAkUCoC3czrtbRsR9uFv4w+A/iyKZ0j0CxrAQAEnpW7yvXLf22UJN1y1hBdPqGv4UTAybHZbHrgktFyOWz6aFuJPthcbDoSEHAoFYFutrGwShV1zYoNd3ZuegWADlntpzly8rhXEQAQWHaX1urGl9apxWPp/LFpuv2cTNORgFMyJCVG188YJEn6zTubVdfUajgREFgoFYFulp1bKkmaNiRJLgd/xAAcbny/BDntNu2vatS+ygbTcQAAOCGVdc36wfNrVNXQotMy4vWnK8bJbreZjgWcslvOGqq+CZEqqmrUX/6zw3QcIKDQeADdLHt7232KM7lPEcBRRIY5NLavW5K0ihFoAEAAaGr16IZ/rFN+eb3S4yP19+9OVITLYToW0C0iwxy6/6JRkqT5n+Qp90CN4URA4KBUBLpRZV2zPi84KIklLQCO7dARaAAA/JllWbrrrY1anV+h2HCnnvv+JCXHhpuOBXSrr41I1bkjU9XqtXT3wo3yermiBjgRlIpAN1q+s0xeSxqWGqs0d6TpOAD8VFb7spbVlIoAAD/3+Mc79db6QjnsNv3v1eOVmRprOhLgE/deNEqRLofW5FfqzfX7TMcBAgKlItCNsnPbRp85pQjgeCb0T5DdJu2tqNf+Ku5VBAD4p3c+L9Ifl2yXJN130SjNzORrXASv9PhI3fb1oZKkh97bpsq6ZsOJAP9HqQh0E6/X0rLtZZLEF1wAjis2wqXR6W33Kubs5rQiAMD/rNtTqTve+FySdO30gfrOlP6GEwG+94PpA5WZGqOKumb9vw+2mY4D+D1KRaCbbNlfrbLaJkWHOTRxQC/TcQD4uY4RaO5VBAD4m4KKel3/4lo1t3r19REp+uXsEaYjAT3C5bDrt5eMkSS9urpA6/ZUGk4E+DdKRaCbdIw+Tx2SpDAnf7QAHN/kgR3LWtgADQDwH9WNLfrB82tUXteskWlxeuyq0+Ww20zHAnrM5IG9dPmEvpKkuxduUqvHazgR4L9oPoBukp1bKonRZwAnZvKAXrLZpN2ldSqpaTQdBwAAtXi8mvfyeu0oqVVqXLjmz52o6HCn6VhAj7vrm8PljnRp6/5qvbByj+k4gN+iVAS6QVV9i9bvbTsaz5IWACfCHeXS8N5xkqQ1eYzWAADMsixL9y7arOU7yhTpcmj+9yYpzR1pOhZgRGJMuH7xzeGSpEeW5OpAFT8ABo6GUhHoBp/sLJPXkoakxKhvQpTpOAACxH/vVWQEGgBg1vxP8vRKzl7ZbNJfvn1650IxIFTNmZih0/vFq67Zowf+vcV0HMAvUSoC3aDjPkVGnwF0RWepyAZoAIBBSzYf0IPvbpUk/Wr2CJ0zMtVwIsA8u92m314yWnabtHjjfi3dXmo6EuB3KBWBU2RZVud/YBh9BtAVk9tLxdziGpXWNBlOAwAIRZsKq3Trgs9kWdLVWf107fSBpiMBfmNUH7fmTm37M3Hv25vU2OIxnAjwL5SKwCnaur9GJTVNinQ5OgsCADgRiTHhGpnWdq/i1c+sUkFFveFEAIBQsr+qQde+sEYNLR6dOTRJ9100SjYbm56BQ/3knKFKjQtXfnm9/pa9y3QcwK9QKgKnKHt72+jz1MGJCnc6DKcBEGj+3+VjlRIbru3Ftbrk8RVat4dRaACA79U1tera59equLpJQ1Ni9PjV4+Vy8O0h8GWxES79+oKRkqS/Ze9SXlmd4USA/+C/GsApWprbNvo8k9FnACdhdLpbb98yTaP6xKm8rlnffjpHCzcUmo4FAAhiHq+lWxds0Jb91UqKCdOzcycpLsJlOhbgt84fk6Yzhyap2ePVPW9vkmVZpiMBfoFSETgFNY0tWrenUpI0KzPFcBoAgSrNHak3bjxD545MVbPHq9te+0x/WpIrr5cvWAEA3e93727V/20tUZjTrqe/O1EZvaJMRwL8ms1m0wMXj1aY067lO8q0eON+05EAv0CpCJyCFTvL1Oq1NCgpWv0S+WIMwMmLCnPqyWsm6KZZgyVJf/1op255db0amrkQHADQff6xao/mf5InSfrTFeM0vl+C4URAYBiQFK2bZrZ9nfabd7aoprHFcCLAvB4rFe+9917ZbDY9++yzR7y2atUqzZo1S7GxsRo4cKDmz5/fU7GAU9Kx9XlGJqPPAE6d3W7Tnd8YrocvHyuXw6Z3Nx7QnKdXqqS60XQ0AEAQWLq9VPct2ixJ+um5mbpwXB/DiYDActOswRqQGKWSmiY98uF203EA43qkVNy2bZt+//vfa+rUqfr+979/2GsrVqzQrFmztG7dOl1zzTUaP368rrvuOr3yyis9EQ04aZZlKbv9PsVZ3KcIoBtdMTFDL12bpYQol77YV6WLH1+hTYVVpmMBAAJY7oEa3fLyenm8li4bn655Zw0xHQkIOBEuh35z8WhJ0guf5mtzEV+fIbT5vFS0LEvXX3+9PB6PnnjiCdlsts7XPB6PfvjDH8rr9So7O1t/+9vf9Oabb+rmm2/Wbbfdpro6tirBf20vrtX+qkaFO+2aMijRdBwAQSZrUKIWzpumwcnR2l/VqCueXKkPNh8wHQsAEIBKa5r0g+fXqKapVZMH9tJDl4057PsyACduRmayzh+bJq8l3b1wE3dgI6T5vFR85plntHz5ct1yyy0aN27cYa+tWLFCW7du1TXXXKMJEyZ0vv/nP/+5ysrK9OGHH/o6HnDSsnNLJElTBiUqwuUwnAZAMOqfGK23bp6mM4cmqaHFoxtfWqcnl+5i4yAA4IQ1tnj0wxfXqvBggwYkRumpayYo3MnXrsCp+PX5IxUd5tCGvQe1YE2B6TiAMT4tFYuLi3XnnXeqd+/e+s1vfnPE659++qkk6cILLzzs/f3799fgwYOVnZ3ty3jAKem4T5HRZwC+5I506bm5k/SdKf1lWdLv39umn//zCzW3ek1HAwD4Oa/X0h2vf67PCg4qPsqlZ+dOUkJ0mOlYQMDr7Y7Q7ecOkyT94f1tKqttMpwIMMOnpeLtt9+uyspKZWVl6be//a3uuuuuw4rC4uJiSdLYsWOP+NjBgwdr9+7dvowHnLTaplatya+QJM0almI4DYBg53TY9cAlo3X/RaNkt0lvrNuna+bnqKKu2XQ0AIAf+9OHuVq8cb9cDpuevGaCBiXHmI4EBI3vndFfI9PiVNXQot+/t810HMAIn5WKa9as0auvvipJevvtt/Xwww/r97//vc466yzdeOONsixL1dXVkqTk5CNPerndbhUVFR3z8zc1Nam6uvqwN6CnfLqzTC0eS/16RWlAYpTpOABCxPemDtCzcycpJtyp1XkVuvSJFdpZUms6FgDAD72xtkCPf7xLkvTQZWO5AxzoZk6HXb+9tG1pyz/X7dPqvArDiYCe57NS8cEHH5RlWcrMzNT777+vvXv36r333tP48eP11FNP6a9//aucTqckKSrqyFImLCxM9fX1x/z8Dz30kNxud+dbRkaGr34rwBEOHX3mkmsAPWnWsBS9dfNU9U2I1J7yel36xAp9sqPMdCwAgB9Zuatcv/zXRknSLWcN0eUT+hpOBASn8f0S9O3JbV3E3Qs3qsXD9TQILT4pFZubm/Xee+/JZrPpX//6l8477zxlZGToG9/4ht577z2FhYXpmWeeUXR0dOfzX9bS0nLU93e46667VFVV1flWUMDlqOgZlmUpO5f7FAGYk5kaq7fnTdPE/gmqaWzV955brZdW7TEdCwDgB3aX1urGl9apxWPp/LFpuv2cTNORgKB25zeGq1d0mLYX12r+J3mm4wA9yielYllZmZqbmzVhwgSNHDnysNdSUlI0ZswY7dixQ3369JEk5efnH/E5SkpKFB8ff8xfIzw8XHFxcYe9AT1hV2mtCg82KMxpZ4wEgDGJMeF6+YdZuuz0dHm8lu5euEn3LdqsVn5CDgAhq7KuWT94fo2qGlp0Wka8/nTFONntTNUAvhQfFaa7vjlckvTY/+1Q4cEGw4mAnuOTUjE2NlZS27KVo2ltbVVYWJjGjRsnSVq/fv1hr3u9Xq1bt06pqam+iAecko5TilkDeykqzGk4DYBQFu506E9XjtPPzmvbPvj8p/m67sW1qmlsMZwMANDTmlo9uuEf65RfXq/0+Ej9/bsTFeFymI4FhITLJ/TV5AG91NDi0f2LNpuOA/QYn5WK/fv3P+pIckNDg3bt2qVRo0Zp+vTpiouL0zPPPHPYM4sXL1Z1dbVmzJjhi3jAKem4T3FmJqPPAMyz2Wyad9YQPXH1eEW47MrOLdW3/vapCiqOfS8xACC4WJalu97aqNX5FYoNd+q5709Scmy46VhAyLDZbHrgktFy2m1asqVY/9labDoS0CN8tqhlzpw5+vTTT/Xee+8d9v67775btbW1uvLKKxUZGanrr79eS5cu1b333qvW1lZt3bpVt956q8LDw3XFFVf4Kh5wUuqbW5Wzu22r16xhKYbTAMB/zR6TptdvOEMpseHaXlyrSx5foXV72EIIAKHg8Y936q31hXLYbfrfq8crMzXWdCQg5AzrHatrpw+UJN27aLMamj2GEwG+Z7Msy/LFJ66srNT06dOVm5uriy++WBkZGVq5cqVWr16t008/XStWrFBkZKQaGxt17rnnavny5YqIiFBTU5Msy9Kjjz6q22677YR/verqarndblVVVXG/Inzmo23F+sHza5UeH6lP7jyLzc8A/M7+qgZd98JabS6qVpjDrv93+Vhdcnq66VgAAB955/Mi/ejVDZKkBy4Zre9M6W84ERC66ppadc4jS1VU1ah5Zw3Wz84bbjoS0GVd6dd8dlIxISFBK1as0C9+8Qtt3rxZTz/9tAoLC3XddddpyZIlioyMlCRFRETo448/1p///GdNnTpV559/vj744IMuFYpATzl06zOFIgB/lOaO1Bs3nqFzR6aq2ePVba99pj8tyZXX65OfIQIADFq3p1J3vPG5JOna6QMpFAHDosOduveiUZKkp5ft1s6SGsOJAN/y2UnFnsZJRfiaZVma+XC29lbU6+/fnahzRrJICID/8notPbwkV3/L3iVJmj2mt/50xWmKDOPSfgAIBgUV9brk8RUqr2vW10ek6KnvTJSDTc+AcZZl6doX1uqjbSWaMqiXXv3hFA6kIKD4xUlFINjkldVpb0W9XA6bzhicaDoOAByX3W7Tnd8YrocvHyuXw6Z3Nx7QnKdXqqS60XQ0AMApqmpo0fefX6PyumaNTIvTY1edTqEI+Ambzab7LxqlCJddq3ZXaOFnhaYjAT5DqQicoI6tz5MG9FJMuNNwGgA4MVdMzNBL12YpIcqlL/ZV6eLHV2hTYZXpWACAk9Ti8eqWV9ZrZ0mtUuPCNX/uREXztSngVzJ6RelHZw+VJD24eKuqGloMJwJ8g1IROEGH3qcIAIEka1CiFs6bpsHJ0dpf1agrnlypDzYfMB0LANBFlmXp3kWbtXxHmSJdDs3/3iSluSNNxwJwFD88c5AGJ0errLZZf/wg13QcwCcoFYET0Nji0ard5ZKkWcNSDKcBgK7rnxitt26epjOHJqmhxaMbX1qnJ5fuUpBcrQwAIWH+J3l6JWevbDbpL98+XaPT3aYjATiGMKddD1w8WpL0Us4efV5w0GwgwAcoFYETsGp3uZpavUpzR2hoSozpOABwUtyRLj03d5K+M6W/LEv6/Xvb9PN/fqHmVq/paACAr7Bk8wE9+O5WSdKvZo9gaSAQAKYOSdIlp/WRZUl3L9wkj5cf5iK4UCoCJ+DQ0Wc2dwEIZE6HXQ9cMlr3XzRKdpv0xrp9umZ+jirqmk1HAwAcw6bCKt264DNZlnR1Vj9dO32g6UgATtCvzh+p2AinNhZW6eWcPabjAN2KUhE4AR1LWmZmMvoMIDh8b+oAPTt3kmLCnVqdV6FLn1ihnSW1pmMBAL5kf1WDrn1hjRpaPDpzaJLuu2gUP+QGAkhybLh+ft4wSdLD7+eqpKbRcCKg+1AqAl9hT3md8srq5LTbNG1Iouk4ANBtZg1L0Vs3T1XfhEjtKa/XpU+s0Cc7ykzHAgC0q2tq1bXPr1VxdZOGpsTo8avHy+XgWzgg0PxPVn+N7etWTVOrHly81XQcoNvwXyTgK3ScUpzQP0GxES7DaQCge2WmxurtedM0sX+Cahpb9b3nVuulVYzmAIBpHq+lWxds0Jb91UqKCdOzcycpjq9FgYDksNv020tGy2aT3v6sSCt28kNcBAdKReAr/Pc+RUafAQSnxJhwvfzDLF12ero8Xkt3L9yk+xZtVquHBS4AYMrv3t2q/9taojCnXU9/d6IyekWZjgTgFIztG6/vTOkvSfr125vU1OoxnAg4dZSKwHE0tni0cle5JGlmZrLhNADgO+FOh/505Tj9rP3On+c/zdd1L65VTWOL4WQAEHr+sWqP5n+SJ0n60xXjNL5fguFEALrDHecOU1JMuHaX1unvy3abjgOcMkpF4DjW5FeoocWj1LhwjUiLNR0HAHzKZrNp3llD9MTV4xXhsis7t1Tf+tunKqioNx0NAELG0u2lum/RZknST8/N1IXj+hhOBKC7uCNduvv8EZKkv360U3vL+RoLgY1SETiOjtHnmZnJbNkDEDJmj0nT6zecoZTYcG0vrtUlj6/Quj0VpmMBQNDLPVCjeS+vl8dr6bLx6Zp31hDTkQB0s4tP66OpgxPV1OrVvYs2ybIs05GAk0apCBxHdm6JJGlmJvcpAggtY/vG6+1bpmlkWpzK65r17adztHBDoelYABC0Smoa9YPn16i2qVWTB/bSQ5eN4YfaQBCy2Wz6zcWj5XLY9HFuqT7YXGw6EnDSKBWBYyioqNeu0jo57DZNH5pkOg4A9Lg0d6TeuPEMnTsyVc0er2577TM9siRXXi8/UQeA7tTY4tEPX1ynwoMNGpgUraeumaBwp8N0LAA+MiQlRjfMGCxJuv+dzaprajWcCDg5lIrAMSzd3jb6PL5fvNyRLsNpAMCM6HCnnrxmgm6c2faF718+2qkfvbpBDc1sLASA7uD1Wrrj9c/1ecFBxUe59OzcSUqIDjMdC4CPzTtriPomRGp/VaMe+88O03GAk0KpCBxDx32Ks4Yx+gwgtNntNv3im8P18OVj5XLYtHjjfl319EqVVDeajgYAAe9PH+Zq8cb9cjlsevKaCRqYFG06EoAeEBnm0G8uHiVJmv9JnrYdqDacCOg6SkXgKJpbvfp0V5mktiUtAADpiokZeunaLMVHufT5vipd/PgKbS6qMh0LAALWG2sL9PjHuyRJD102VlMGJRpOBKAnnT08VeeNSpXHa+nuf23iihkEHEpF4CjW5leovtmjpJhwjUyLMx0HAPxG1qBEvT1vmgYnR2t/VaOueHKllmw+YDoWAASclbvK9ct/bZQk3XLWEF0+oa/hRABMuPfCUYoKc2jtnkr9c/0+03GALqFUBI4iu/0+xZmZybLb2boHAIfqnxitt26epjOHJqm+2aMbXlqnp5bukmXx03UAOBG7S2t140vr1OKxdP7YNN1+TqbpSAAM6RMfqVu/NlSS9NC7W1VZ12w4EXDiKBWBo1jafp/izGGMPgPA0bgj25YJXDOlnyxLeui9bbrzzS/U3Oo1HQ0A/FplXbN+8PwaVTW06LSMeP3pinH8EBsIcT+YPlDDUmNVWd+iP7y/zXQc4IRRKgJfUnSwQbnFNbLbpBlDk0zHAQC/5XLY9cDFo3XfhSNlt0mvr92n78zP4SfsAHAMTa0e3fCPdcovr1d6fKT+/t2JinA5TMcCYJjLYddvLx0tSVqwpkDr9lQaTgScGEpF4EuWto8+n5YRr/ioMMNpAMC/2Ww2zZ02UPPnTlJMuFM5eRW65IkV2llSazoaAPgVy7J011sbtTq/QrHhTj33/UlKjg03HQuAn5g0oJeuaL9b9e6Fm9TqYfoD/o9SEfiSztHnzBTDSQAgcJw1LEVv3jRVfRMitae8Xpc+sUKf7CgzHQsA/Mb/frRTb60vlMNu0/9ePV6ZqbGmIwHwM7/45nC5I13aur9az3+abzoO8JUoFYFDtHi8WrGz7ZvgWdynCABdMqx3rBbOm6YJ/RNU09iq7z23Wi+t2mM6FgAYt+jzIv3pw+2SpPsuGqWZmXydCeBIiTHh+sU3h0uSHv1wu/ZXNRhOBBwfpSJwiHV7KlXT1KrE6DCNSXebjgMAAScpJlwvX5elS09Pl8dr6e6Fm3T/O5vl8bIZGkBoWrenUj9943NJ0rXTB+o7U/obTgTAn82ZmKHx/eJV1+zRb/+91XQc4LgoFYFDZLePPs/ITGYLHwCcpAiXQ49cOU4/PTdTkvTcinxd98Ia1TS2GE4GAD2roKJe17+4Vs2tXn19RKp+OXuE6UgA/JzdbtNvLxkju01avHF/553/gD+iVAQO0fEXNiMpAHBqbDabbjl7qJ64erwiXHZ9nFuqy/+2UgUV9aajAUCPqGpo0fefX6PyumaN6hOnx646TQ5+aA3gBIzsE6e5UwdKku55e5MaWzyGEwFHR6kItCuubtTW/dWy2dpOKgIATt3sMWl6/YYzlBIbrtziGl3y+Aqt21NhOhYA+JTXa+mWV9ZrZ0mtUuPCNf97kxQd7jQdC0AAuf3cTKXGhWtPeb2eyN5lOg5wVJSKQLuOrc9j+8arV3SY4TQAEDzG9o3X27dM08i0OJXXNevbT+do4YZC07EAwGfeXL9Py3eUKdLl0PzvTVJvd4TpSAACTEy4U/dcMEqS9GT2LuWV1RlOBByJUhFox+gzAPhOmjtSb9x4hs4dmapmj1e3vfaZHlmSKy8LXAAEmaqGFv3h/W2SpNu+PlSjWf4H4CTNHtNbMzKT1ezx6p63N8my+LoJ/oVSEZDU6vFq+Y62UnHWMEpFAPCF6HCnnrxmgm6cOViS9JePdupHr25QQzP3BAEIHn/+v+0qq23WoORofX/aQNNxAAQwm82m31w0SmFOu5bvKNO/v9hvOhJwGEpFQNKGgoOqbmxVfJRL4/rGm44DAEHLbrfpF98crocvHyuXw6bFG/frqqdXqqS60XQ0ADhl2w5U68WVeyRJ913YVgQAwKkYkBStm2e1/UD2gX9vUU1ji+FEwH/xXzlA/71P8cyhyWzlA4AecMXEDL10bZbio1z6fF+VLn58hTYXVZmOBQAnzbIs3fv2Znm8lr4xqjeL/wB0mxtnDtaAxCiV1DTpkQ+3m44DdKJUBCRlby+RJM3iiz8A6DFZgxL19rxpGpwcrf1VjbriyZVasvmA6VgAcFLe+WK/cvIqFO606+4LRpiOAyCIRLgc+s3FoyVJL3yar02F/CAW/oFSESGvpKZRmwqrJYmfKANAD+ufGK23bp6mM4cmqb7ZoxteWqenlu7iInIAAaWuqVUPLt4iSZp31hD1TYgynAhAsJmRmazzx6bJa0l3L9zEsjv4BUpFhLxl28skSaPT45QcG244DQCEHnekS8/OnaRrpvSTZUkPvbdNd775hZpbvaajAcAJ+etHO1Vc3aR+vaJ0/YxBpuMACFL3XDBSMeFOfVZwUK+u2Ws6DkCpCCzd3r71OTPFcBIACF0uh10PXDxa9104Unab9PraffrO/BxV1jWbjgYAx7WrtFbzP9ktqe0b/giXw3AiAMEqNS5Ct5+TKUn6f+/nqqy2yXAihDpKRYQ0j9fS8h3tpeIwRp8BwCSbzaa50wZq/txJigl3KievQpc8sUI7S2pNRwOAo7IsS/ct2qwWj6WzhiXrayP4ITUA3/ruGf01Mi1OVQ0teujdbabjIMRRKiKkfVZwUAfrWxQX4dRpGfGm4wAAJJ01LEVv3jRVfRMitae8Xpc+sUKf7CgzHQsAjrBkS7GW7yhTmMOuey4cJZvNZjoSgCDndNj120tHy2aT3ly/Tzm7y01HQgijVERI6xh9PnNospwO/jgAgL8Y1jtWC+dN04T+CappbNX3nlutl1btMR0LADo1tnj0m3falrP8cMZADUyKNpwIQKgY3y9BV03qJ6ltaQv3UMMUWhSEtKW5JZKkmYw+A4DfSYoJ18vXZenS09Pl8Vq6e+Em3f/OZnnYdgjAD/wte5cKDzYozR2heWcNMR0HQIi58xvDlBgdph0ltXp2RZ7pOAhRlIoIWeW1TfqisEqSNCuTUhEA/FGEy6FHrhynn57bdin5cyvydd0La1TT2GI4GYBQtre8Xn9bukuSdPf5IxUV5jScCECoiY8K012zR0iSHvu/HdpXWW84EUIRpSJC1vIdZbIsaURanFLiIkzHAQAcg81m0y1nD9UTV49XhMuuj3NLdfnfVqqggi+eAZjxwOItam71aurgRM0e09t0HAAh6lvj0zV5QC81tHh0f/t1DEBPolREyMpuH31m6zMABIbZY9L0+g1nKCU2XLnFNbrk8RVat6fCdCwAIebj3BJ9uKVYTrtN91/EchYA5thsNv320tFy2m36cEux/m9LselICDGUighJXq+lZe2bRBl9BoDAMbZvvN6+ZZpGpsWpvK5Z3346Rws3FJqOBSBENLX+dznL3KkDNDQ11nAiAKEuMzVW1545UJJ076LNamj2GE6EUEKpiJC0sbBKFXXNig13anz/BNNxAABdkOaO1Bs3nqFzR6aq2ePVba99pkeW5MrLAhcAPjb/kzzlldUpKSZct359qOk4ACBJ+vHZQ9XHHaHCgw3660c7TMdBCKFUREjKzi2VJE0bkiSXgz8GABBoosOdevKaCbpx5mBJ0l8+2qkfvbqBn84D8Jn9VQ366392SpJ+OXu4YiNchhMBQJvocKfuvWiUJOnvy3drZ0mN4UQIFbQpCEnZ27lPEQACnd1u0y++OVwPXz5WLodNizfu11VPr1RJdaPpaACC0IOLt6qhxaOJ/RN06enppuMAwGHOHZmqrw1PUYvH0t0LN8mymOCA71EqIuRU1jXrs4KDkqSZlIoAEPCumJihl67NUnyUS5/vq9LFj6/Q5qIq07EABJFPd5Xp31/sl90m3X8xy1kA+B+bzab7LhqlCJddq3ZXaOFn3DkN36NURMhZvrNMliUNS41VmjvSdBwAQDfIGpSot+dN0+DkaO2vatQVT67Uks0HTMcCEARaPF7dt2izJOnqrP4a1cdtOBEAHF1Gryj96Oy2+14fXLxVVfUthhMh2FEqIuRk5zL6DADBqH9itN66eZrOHJqk+maPbnhpnZ5auovxHwCn5MWVe7S9uFYJUS7dcW6m6TgAcFw/PHOQBidHq6y2WQ8v2WY6DoIcpSJCitdradn2tiUtjD4DQPBxR7r07NxJumZKP1mW9NB723Tnm1+oudVrOhqAAFRS06g/f7hdkvTzbwxXfFSY4UQAcHxhTrseuGS0JOnlnL2dV38BvkCpiJCyZX+1ymqbFR3m0MT+vUzHAQD4gMth1wMXj9Z9F46U3Sa9vnafvjM/R5V1zaajAQgwf3gvVzVNrRrb160rJ2aYjgMAJ2Tq4CRdenq6LEu6e+FGebxMbcA3KBURUjpGn6cOSVKYk//5A0CwstlsmjttoObPnaSYcKdy8ip0yRMrtLOk1nQ0AAFi3Z4Kvbl+nyTp/otGyWFnOQuAwPHL2SMUG+HUpsJqvbRqj+k4CFK0Kggp2blto8/cpwgAoeGsYSl686ap6psQqT3l9br0iRX6ZEeZ6VgA/JzHa+met9uWs1w5sa9O75dgOBEAdE1ybLh+ft4wSdIfP8hVSXWj4UQIRpSKCBlV9S1av7dSkjQzk1IRAELFsN6xWjhvmib0T1BNY6u+99xqfmIP4LheXb1Xm4uqFRvh1M+/Mdx0HAA4Kf+T1V9j+7pV09Sq3y7eajoOghClIkLGJzvL5LWkISkx6psQZToOAKAHJcWE6+XrsnTp6enyeC3dvXCT7n9nM3cMAThCZV2z/rgkV5J0xzmZSooJN5wIAE6Ow27Tg5eMkc0mLfq8SCt2Mq2B7kWpiJDRcZ/iLE4pAkBIinA59MiV4/TTczMlSc+tyNd1L6xRTWOL4WQA/MnDS3J1sL5Fw3vH6pop/U3HAYBTMqavW99p/7vs1ws3qanVYzgRggmlIkKCZVlaur3tPsWZ3KcIACHLZrPplrOH6omrxyvCZdfHuaW6/G8rVVBRbzoaAD+wcV+VXl29V1Lbchang2+XAAS+O84dpqSYcO0uq9PTS3ebjoMgwn8lERK27q9RSU2TIl0OTR7Yy3QcAIBhs8ek6fUbzlBKbLhyi2t0yeMrtG5PhelYAAzyei3ds2iTLEu6+LQ+yhqUaDoSAHQLd6RLv75ghCTpfz/eqb3l/DAV3aNHS8X77rtPNpvtiPe/8MILGj16tCIjIzVx4kStXLmyJ2MhBGRvbxt9njo4UeFOh+E0AAB/MLZvvN6+ZZpGpsWpvK5Z3346Rws3FJqOBcCQN9fv04a9BxUd5tAvZ48wHQcAutVF4/po6uBENbV6de+iTbIs7pXGqeuxUjE3N1cPPfTQEe//4x//qLlz56q2tlY33XST7Ha7vv71r2vHjh09FQ0hIDu3bfR5FqPPAIBDpLkj9caNZ+jckalq9nh122uf6ZElufKywAUIKVUNLfrD+9skST/+2lClxkUYTgQA3ctms+k3F4+Wy2HTx7ml+mDzAdOREAR6rFS84YYb1NzcfNj78vPz9atf/UqDBw/Whg0b9Mgjj2jFihUaOnSobr311p6KhiBX3dii9XsqJUkzM1MMpwEA+JvocKeevGaCbpw5WJL0l4926kevblBDMxeZA6Hiz/+3XWW1zRqUHK3vTxtoOg4A+MSQlBjdMKPt653739miuqZWw4kQ6HqkVHzuuee0dOlSZWRkHPb+F198Uc3NzbrnnnuUkJAgSXK5XLrjjjv0wQcfqLy8vCfiIch9urNMrV5Lg5Ki1S8xynQcAIAfsttt+sU3h+vhy8fK5bBp8cb9uurplSqpbjQdDYCPbTtQrRdX7pEk3XfhKIU5uXYeQPC65ewhyugVqf1Vjfrz/203HQcBzuf/xSwrK9PPfvYzZWVlae7cuYe99umnn8pms+mCCy447P1nn322vF6vli9f7ut4CAEdo89sfQYAfJUrJmbopWuzFB/l0uf7qnTx4yu0uajKdCwAPmJZlu59e7M8XkvfGNVbMzL5ehFAcItwOfSbi0ZLkp5dka9tB6oNJ0Ig83mpePvtt6u6ulrPPPOM7PbDf7ni4mKlp6erV6/Dt/Gmp6crPDxcu3ez6hynxrIsLd3eXiryRSIA4ARkDUrU2/OmaXBytPZXNeqKJ1dqCfcOAUHpnS/2KyevQuFOu+6+gOUsAELDWcNTdN6oVHm8lu7+1ybuksZJ82mp+NFHH+kf//iH7rzzTo0ePfqI16urq5WcfPSix+12q6io6Jifu6mpSdXV1Ye9AV+2vbhW+6saFe60a8qgRNNxAAABon9itN66eZrOHJqk+maPbnhpnZ5auotNiUAQqWtq1YOLt0iS5p01RH0TuCYHQOi498JRigpzaO2eSv1z3T7TcRCgfFYqNjU16cYbb9Tw4cN19913H/UZp9OpqKij/8c7LCxM9fX1x/z8Dz30kNxud+fbl+9rBCQpO7dEknTG4ERFuByG0wAAAok70qVn507SNVP6ybKkh97bpjvf/ELNrV7T0QB0g79+tFPF1U3q1ytK188YZDoOAPSoPvGRuu3rQyVJD723VZV1zV/xEcCRfFYqPvjgg9q5c6eeeeYZhYeHH/WZ6OjoIzZCd2hpaTnma5J01113qaqqqvOtoKCgW3IjuDD6DAA4FS6HXQ9cPFr3XThSdpv0+tp9+s78HL7wBgLcrtJazf+k7aqley4YyQ+fAYSk708bqGGpsaqsb9Ef3t9mOg4CkE9Kxa1bt+oPf/iDbrrpJk2bNu2Yz/Xp00f5+flHvN/j8aiiokLx8fHH/Njw8HDFxcUd9gYcqrapVWvyKyRJs4alGE4DAAhUNptNc6cN1Py5kxQT7lROXoUueWKFdpbUmo4G4CRYlqX7Fm1Wi8fSWcOS9bURfJ0IIDS5HHb99tK2q+oWrCnQ2vbvn4ET5fTFJ7377rvV3Nwsp9N52OjzsmXLOl+XpHHjxmnx4sUqKCg4bHx5w4YNamlpUWpqqi/iIUR8urNMLR5L/ROjNDAp2nQcAECAO2tYit68aaqufWGN9pTX69InVuhvV0/Q9KFJpqMB6IIlW4q1fEeZwhx23XPhKNlsNtORAMCYSQN66YoJffXGun265ZUNevuWaUqNizAdCwHCJ6VieXm5JOkvf/nLUV9/8MEHJUkrVqzQ7373O82fP1/33Xdf5+uvvvqqJGnGjBm+iIcQ0TH6PIvRZwBANxnWO1YL503TDf9Yp3V7KvW951br/otG6Zop/U1HA3ACGls8+s07bctZfjhjID94BgBJd18wUuv3VmpXaZ2ufWGNXr/hDEWF+aQuQpDxyfhzdna2LMs64u3ee++VpM5/nzp1qiZOnKg//OEPWrRokSRp4cKFeuKJJ5SZmanJkyf7Ih5CgGVZys5tv09xGKUiAKD7JMWE6+XrsnTp6enyeC3dvXCT7n9nszxeNkMD/u5v2btUeLBBae4IzTtriOk4AOAX3JEuPTd3snpFh2lTYbVuXfAZX9fghPhsUcuJevnll5WYmKiLL75Y0dHRuvTSS2W32/Xcc88xioCTtqu09v+3d9/RUVWL28efmUw6SSCk0EILNXQIHRERlKaIIogioICIKBZs1xcV60WxIT8V6aIUsdBFLyAoAtJDDZ3QCQmQ3mfO+weSay5FAklOZvL9rJV11z3nJPOgW3LmmX321smEdHnYrGpdncfSAAAFy8vdTR/1aaTn76glSZq+NkZDvtqk5Ixsk5MBuJpj59L0xW+HJEmju0cwCwcA/qZyWR9NHtBMHjarlu+JZeMWXBfTS8VatWppx44dGjVqlJo2bar+/fsrKipKbdq0MTsanNilWYotqwXK24Pd/AAABc9isejJjjX1+UNN5eVu1ap9cer9xXodP59mdjQAV/DW0j3KynGoTXhZdWtQzuw4AFDsNKsSqHG9G0qSJv1+WLM3HDM5EYo7i2EYLjGnNSkpSQEBAUpMTGQnaOjhqRu05kC8RnevqyG3VDc7DgDAxe04kaAhX23W2eRMlfX10KQBzdSsSqDZsQD8ZdW+s3pk+ibZrBYte/oW1Qz1MzsSABRb41cc0Mcr9svNatFXj7RgU7oSJj/9mukzFYGClpaVow2Hz0uSOtQOMTkNAKAkaFiptBY+2VYR5f11LjVL/SZt0IJtJ82OBUBSZs5/N2cZ1KYqhSIA/IORt9fIXTt6+KwtOhCbbHYkFFOUinA5v++PU5bdoUplvBUezI5+AICiUT7AW9893lp3RIQqy+7QM99G6aP/7JODhc4BU03944iOxKcqqJSnnu5U0+w4AFDsWSwWjb2vgZpXLaPkjBw9+tUmxadkmh0LxRClIlxKYlp27ifRXeqVY7MfAECR8vW0aWL/Znr81nBJ0qe/HtRTc7YpPctucjKgZDqdmK4JKw9Kkl7pVkd+Xu4mJwIA5+Bpc9OXD0eqcqCPjp9P12MzNysjm/sZ5EWpCJdhGIZe/nGHTiVmqGpZHz3TuZbZkQAAJZDVatHLXetoXO+GcnezaOnO03pg0nqdTcowOxpQ4ryzNFrp2XZFVimjXk0qmh0HAJxKoK+Hpg1qLn8vm7YeS9AL3++Qi2zLgQJCqQiXMWfjcS3bdUY2q0Wf9muiUp42syMBAEqw+yPD9M3glirt467tJxLV87O12n0q0exYuILMHGZeuKJ1h+K1ZMdpWS3SGz3r8QQLANyAGiGlNLF/M9msFi3efkofrzhgdiQUI5SKcAkHYpP15pLdkqQXu9RWw0qlzQ0EAICkltXLauGItgoP9tXpxAzdP3G9/rP7jNmxSjzDMHTwbIq+WH1I932xTnVe/VlPzdmmrByH2dFQQLLtDo1ZdPHe8KGWVVSvQoDJiQDAebWpEaR3etWXJH268oB+3HrC5EQoLpjKBaeXkW3XU3O2KSPboVtqBmlIu+pmRwIAIFeVsr768Ym2enL2Vq05EK9h32zRy13q6LH21Zk5VYTsDkNbjl7QiuhYLd8TqyPxqXnOL95+SulZOfrsoabytLmZlBIFZeb6o9ofm6IyPu4adQdL4gDAzerbvLKOxKdp4m+H9NIPO1SxtLdaVi9rdiyYzGK4yAPxSUlJCggIUGJiovz9/c2OgyL0+sJd+mr9UQWV8tBPT9+iED8vsyMBAHCZSzOnZm04JknqE1lJb9/TQB42HhwpLKmZOVpzIE7L95zVr3tjdSEtO/ecu5tFrcOD1DkiVAHe7nrhu+3KzHGofa1gTXq4mbzcKRad1dnkDN3+wW9KzszRv+9toH4tKpsdCQBcgsNhaMTsrVq264xK+7hrwRNtVTXI1+xYKGD56deYqQintnxPrL5af1SSNO7+RhSKAIBiy93Nqrfvqa8aIaX01pI9mrf5hI6eS9PE/s1UxtfD7HguIzYpQyuiY7ViT6zWHjqX55HmAG93dawTok51Q9W+VlCenYCDfD00+KvN+n1/nB6ZvklTB0XKx4NbZWf03rJ9Ss7MUcNKAeoTGWZ2HABwGVarRR/1aaxTCeu1/USiHp2xST8+0UalfbiPKamYqQindSYxQ13G/66EtGwNaVdNo3tEmB0JAIDrsmrfWT01e5tSMnNUpayPpg5srhohpcyO5ZQMw9C+2GSt2HPxsebtJ/JuhlM50EedI0LVqW6omlctI5vb1WeGboo5r0emb1JKZo6aVy2jaYOa5ykeUfxtOXpe932xXpI0/4k2alK5jMmJAMD1nE3OUK/P1ulkQrpaVQ/UzEdb8uSFC8lPv0apCKdkdxjqP2WD1h8+p/oV/fXD8DasfwQAcCr7ziTr0RmbdDIhXX5eNn3xUDO1qxlkdiynkG13aNOR81oeHasV0bE6fj49z/nGYaXVOSJUnSNCVTOkVL7Wrtx27IIGTNuo5IwcNQorrZmPtFCAD8WiM7A7DN39f39o96kk9YmspPd7NzI7EgC4rL1nktT7i/VKyczR/c0q6f3eDVkr2kVQKlIqurzPVh3UuF/2ycfDTUueaqfqwczuAAA4n/iUTA37eou2HL0gN6tFb9xdT/1bVTE7VrGUnJGt3/bHafmeWK3ae1ZJGTm55zxtVrWrEaROEaG6vU6IQvxvbjmUXScT1X/qBiWkZateBX99M7glj6g7gW/+PKrRC3bJz8umVc93UFApT7MjAYBLW7XvrAbP2CSHIb3Ypbae6FDD7EgoAJSKlIoubcvRC+rz5XrZHYbG9W6o+1krBwDgxDKy7Xr5hx1aEHVKkvRI26oa3T1CblY+7T+ZkK6Vf+3W/Ofhc8q2//e2NdDXQ7fXCVGniFDdUjOowNc/3HsmSQ9N3qBzqVmqU85PXw9uqWA/Sqri6kJqlm77cLUS0rI15q4IDWpbzexIAFAizFwfo9cW7pYkff5QU3VrUN7kRLhZlIqUii4rKSNb3cav0YkL6bq7UQWNf6AxU6wBAE7PMAz9368H9eHy/ZKk22oH69N+TUrcen6GYWj3qSQt33Pxsebdp5LynK8e7Hvxsea6oWpSuUyhF68HzybrwckbdDY5U+HBvpo9tJVCb3IWJArHK/N3avaGY6pTzk9Lnmp3zbUzAQAFa8yi3ZqxLkaeNqu+HdZajcNKmx0JN4FSkVLRJRmGoafmbNOSHacVFuitpSNvkX8Je7MFAHBtS3ec1nPzopSZ41DtUD9NGRipsEAfs2MVqswcu/48fF4r/ioSTydm5J6zWqRmVcqoU91QdYoIVbgJy53ExKfqwcl/6lRihqqU9dHsoa1UsbR3kefA1e08kai7P/tDhiF9+1grtaxe1uxIAFCi2B2Ghs7crF/3nlVQKU8tGNFGlcq49v2LK6NUpFR0SfM2H9eL3++QzWrRd4+3Zjc/AIBL2n48QUNmblZccqaCSnnoy4cj1ayKa/3OS0jL0up9F9dH/G1/nFIy/7s+ore7m9rXClKnuqHqWCdEZYvBunjHz6fpwSl/6vj5dFUq4605Q1u5fNnrLBwOQ/dNXKdtxxLUs3EFjX+gidmRAKBESsnMUe8v1mnvmWTVDvXT98Nbl7gnLlwFpSKloss5FJeiHp/+ofRsu164s7ZG3MYCsAAA13UqIV1DvtqsPaeT5GGzalzvhurZuKLZsW7KsXNpWh4dq+V7zmhTzAXZHf+9BQ3281SnuqHqHBGiNuFB8nJ3MzHplZ1KSNdDUzboSHyqygd4adaQlmwUVwx8t/m4Xvh+h3w93PTr8x14PB0ATHQqIV09P1uruORM3VorWFMHRrIchROiVKRUdCmZOXb1+myd9pxOUpvwsvp6cEsWrwcAuLzUzBw9822Ulu+JlSSN7FhDz3SqJauT/A50OAxtP5GgFdGxWrHnrPbFJuc5XzvUT50iQtQ5opwaVgxwij/X2aQMPThlgw6eTVGwn6dmD2mpmqF+ZscqsRLTs3X7h6sVn5Klf3Wto2G3hpsdCQBKvB0nEtTny/XKyHbo4VZV9GbPeuyD4GQoFSkVXcpbS/Zo6h9HVMbHXT8/055PoAEAJYbDYei9X/bqy98OS5K6NyyvD+9vVCxn8kkXd7Jedyj+r41WziouOTP3nJvVohZVA9Xpr41WKpd1zseH41My1X/KBu09k6xAXw99M7ilIipw72mGNxbv1vS1Maoe7Kufn24vDxuzYQCgOPh51xkNn7VFhiG91iNCj7arZnYk5AOlIqWiy1i196wembFJkjR1YKRurxtqciIAAIrevM3H9f/m71S23VCjsNKa/HAzhRSTD9nOpWTq171ntXxPrNYciFd6tj33XClPm26tFazOEaHqUDtYpX08TExacC6kZmnAtI3aeTJRAd7u+npwCzWsVNrsWCXK3jNJ6v7pH7I7DM18tIXa1wo2OxIA4G8m/X5I7/60VxaLNPnhSHWK4L28s6BUpFR0CWeTMtR1/BqdS83SoDZVNebuemZHAgDANH8ePqfHv9mihLRslQ/w0pSBkapXIcCULIfiUnJ3a95y9IL+tjyiygd4/bU+YqhaVg+Up614zqq8WYnp2Ro0faO2HUuQn6dNMx5t4XIb6hRXhmHogUl/asOR8+pSr5wmPtzM7EgAgP9hGIZemb9TczYel4+Hm757vLVp9y3IH0pFSkWn53AYGjBto/44GK865fy0YETbYvuoFwAARSUmPlWPfrVJh+NS5ePhpvEPNFHnIvjk3+4wtO3YBS3fE6vl0bE6HJea53y9Cv65RWK9Cv4lZu2klMwcPTp9kzbGnJevh5umDWqultXLmh3L5S3afkoj52yTp82qlaNuVaUyzvkoPQC4umy7Q4Omb9Tag+dUzt9LC59sy3JmToBSkVLR6U387ZDGLtsrL3erljzVTjVCWAQdAABJSkzL1ojZW/XHwXhZLNK/utbR0FuqF3iRl5aVozUHLq6P+OveszqfmpV7zt3NolbVy6pzRKhurxuqiqW9C/S1nUlaVo6GztystQfPycvdqikDmqtdzSCzY7ms1MwcdfxwtWKTMvVc51oaeXtNsyMBAK4hMT1b936+VofiUlW/or/mDWstHw+b2bFwDZSKlIpObfvxBN33xTrlOAyNvbeBHmhR2exIAAAUK9l2h8Ys2q1ZG45JkvpGhumte+rf9EYVZ5MytPKv9RH/OBivrBxH7jl/L5tuqxOizhGhal8rWP5e7jf1Wq4kI9uux7/ZotX74uRhs+rL/s10W50Qs2O5pLHL9mrib4dUOdBH/3m2PU+yAIATOHYuTfd8vlbnU7PUOSJUE/s3k5u1ZDzV4IwoFSkVnVZyRrZ6TPhDR8+lqXuD8vq/B5uUmEeoAADID8MwNGNdjN5askcOQ2pZLVAT+zdTGd/r3wzFMAztj03RiuhYLd8Tq6jjCXnOVyrjrc5/7dbcvFqg3N3YXfdqMnPsenL2Ni3fEyt3N4s+e7Cp7qhXzuxYLuVQXIq6fPK7su2Gpgxg0X8AcCZbjp5Xv8kblJXj0GPtq+uVbnXNjoSroFSkVHRaz34bpfnbTqpiaW/99PQtCvBmFgQAANeyat9ZPTV7m1Iyc1S1rI+mDmqu8OBSV70+x+7QppiL6yOuiI7VsfNpec43CiutznVD1CkiVLVD/fhwLx+y7Q49MzdKS3eels1q0ScPNFaPhhXMjuUSDOPiettrDsTrttrBmjaoOWMTAJzMwqiTenpulCTp3V4N9GBLnkosjvLTr/EgO4qNH7ee0PxtJ2W1SOMfaEyhCADAdbitdoh+GN5Gj87YpJhzaer12Vp90b+Z2tb477p+yRnZ+n1/vJbvOaNV++KUmJ6de87DZlXb8LLqHFFOt9cNYQH1m+DuZtX4BxrLw2bV/G0nNXLONmXbHerVpJLZ0Zzef/bEas2BeHm4WfXaXfUoFAHACfVsXFEx8Wn6eMV+vbpwlyoH+rAOsZNjpiKKhZj4VHX/dI1Ss+wsug0AwA2IT8nUsK+3aMvRC3KzWvRKt7rycLNoefRZrT8Ur2z7f2/5yvi4q2OdUHWOCNEtNYPl68nnzAXJ7jD0yo879e3m47JYpLH3NlDf5szGuFEZ2Xbd/uFvOpmQrhG3heuFO+uYHQkAcIMMw9Bz87Zr/raT8vOy6cfhbVQzlI1ZixMef6ZUdCpZOQ71nrhOO04kqkW1QM0Z2opFWwEAuAEZ2Xa9/MMOLYg6ddm5akG+6hwRqk51Q9W0cmnZWB+xUDkchl5ftFtf/3lUkvRWz3p6uHVVc0M5qY+X79f4lQdUPsBLK0fdyq6hAODkMnPs6j9lgzbFXFBYoLfmP9FWQaU8zY6Fv/D4M5zKh//Zpx0nEhXg7a5P+jamUAQA4AZ5ubvp476NVTPUT5+uPKD6FQPUqW6oOkeEKjzYl0dGi5DVatGbPevJw2bV1D+O6NWFu5WZ49CQW6qbHc2pHDuXpi9+OyRJGt09gkIRAFyAp81NXz4cqXs+W6tj59P02MzNmj20lbzc3cyOhnxipiJM9fv+OA2YtlGSNLF/M3Wpzy6JAADAdRiGoXG/7NPnqy8WYy/cWVsjbqthcirnMXTmZi3fE6s24WU1a0hLinEAcCEHz6bo3s/XKikjR3c1qqDxfRvLyiQj0+WnX+O5F5gmPiVTz83bLknq36oyhSIAAHA5FotFL9xZW892qiVJGvfLPn20fL9c5HP9QrVq31kt3xMrm9WiN+5mcxYAcDU1Qkpp4sPNZLNatHj7KX2yYr/ZkZBPlIowhcNhaNS87YpPyVSt0FIa3T3C7EgAAACFwmKx6OlONfVSl4sbjHy68oDe+3kfxeI1ZObY9ebiPZKkQW2qsog/ALioNuFBevfeBpKkT389qB+2nDA5EfKDUhGmmLb2iH7bHydPm1UT+jVl7QQAAODyhncI12s9Ln6QOvG3Q3prSTTF4lVM/eOIjsSnKqiUp57uVNPsOACAQtQnMkzDO4RLkl7+cYc2HD5nciJcL0pFFLldJxP13s97JUmje0Sodjk+eQYAACXDo+2q6e176ku6+CHrqwt3yeGgWPy704npmrDyoCTplW515OflbnIiAEBhe+GO2upav5yy7YaGfbNFMfGpZkfCdaBURJFKzczRU3O2Kdtu6I6IUPVvWdnsSAAAAEWqf6sqer93Q1ks0jd/HtPLP+6QnWIx1ztLo5WebVdklTLq1aSi2XEAAEXAarXooz6N1ahSgBLSsvXojE1KSMsyOxb+AaUiitSYRbt1JD5V5QO8/rqZZsFtAABQ8vSJDNMnfRvLzWrRvM0nNGpelHLsDrNjmW7doXgt2XFaVov0Rk82ZwGAksTbw02TB0aqYmlvHY5P1ePfbFFWDr8bizNKRRSZhVEn9d2WE7JapI/7NlZpHw+zIwEAAJimZ+OKmtCviWxWixZEndLIuduUXYKLxWy7Q2MW7ZYkPdSyiupVCDA5EQCgqIX4eWnqoEiV8rTpz8Pn9f/m72T94WKMUhFF4ti5NI2ev0uS9GTHmmpVvazJiQAAAMzXrUF5TezfTB5uVv2084yGf7NVmTl2s2OZYub6o9ofm6IyPu4adUcts+MAAExSp5y/JjzYRFaL9N2WE/rit0NmR8JVUCqi0GXbHRo5d5uSM3MUWaWMRnasYXYkAACAYqNTRKgmDWgmT5tVK6Jj9djMLcrILlnF4tnkDH2yfL8k6cUudXiiBQBKuNtqh2jM3fUkSe//vE8/7TxtciJcCaUiCt0nK/Yr6niC/Lxs+uSBxrK5MewAAAD+rkPtEE0f1Fze7m76bX+cHp2xSWlZOWbHKjLvLdun5MwcNawUoD6RYWbHAQAUAwNaV9WgNlUlSc9+G6Wo4wmm5sHlaHdQqNYdjNfnqy9OVR57b0NVKuNjciIAAIDiqU2NIM0c3EKlPG1ad+icBk7bqOSMbLNjFbotR8/rh60nJElv3F1PblY2ZwEAXPRqjwh1rBOizByHhny1WScupJkdCX9DqYhCcz41S898GyXDkPq1CFP3huXNjgQAAFCsNa8aqK8Ht5Cfl02bYi7o4akblZjuusWi3WHotYUXN2fpE1lJTSqXMTkRAKA4cbNa9Gm/Jqpb3l/xKZkaPGNzifjAzVlQKqJQGIahF7/frrPJmQoP9tWrPSLMjgQAAOAUmlQuozlDW6m0j7uijifooSl/6kJqltmxCsWcjce0+1SS/LxserFLHbPjAACKoVKeNk0dGKkQP0/ti03Wk7O3KcfuMDsWRKmIQjJz/VGtiD4rDzerJvRrKh8Pm9mRAAAAnEb9igGa+1grlfX10K6TSeo3+U/Fp2SaHatAXUjN0gf/2SdJGtW5loJKeZqcCABQXFUo7a2pA/+79vCYxbtlGIbZsUo8SkUUuD2nkvTOT9GSpFe61VFEBX+TEwEAADifOuX89e2wVgrx89TeM8nq++V6xSZlmB2rwIz7zz4lpGWrTjk/9W9Vxew4AIBirkGlAH3yQGNZLNI3fx7T9LUxZkcq8SgVUaDSsnL01Jytyspx6PY6IRr4105NAAAAyL8aIX76dlhrlQ/w0qG4VPX9cr1OJaSbHeum7TyRqDkbj0m6uDmLzY23JQCAf3ZnvXL6V9eLy2W8tXSPVuyJNTlRycZvbxSot5bs0aG4VIX4eWrc/Y1ksbB7HwAAwM2oFuSrecNaq1IZb8WcS1OfL9fr+Hnn3f3S4TD02qJdMgypZ+MKalm9rNmRAABOZOgt1dWvRZgMQxo5d5t2n0o0O1KJRamIAvPTztOas/G4LBbpk76NFejrYXYkAAAAlxAW6KN5w1qralkfnbiQrj5frteR+FSzY92QH7ae0LZjCfL1cNMr3eqaHQcA4GQsFove7FlfbWuUVVqWXYNnbHap5UGcCaUiCsSJC2l6+YcdkqTht4arTY0gkxMBAAC4lgqlvTVvWGvVCCml04kZ6vPleh2ITTY7Vr4kpmfrvZ/3SpJG3l5Tof5eJicCADgjdzerPn+omcKDfXUmKUODv9qktKwcs2OVOJSKuGk5doeemRulpIwcNQ4rrWc71zI7EgAAgEsK8ffS3MdaqU45P8UlZ+qBSX8q+nSS2bGu2ycr9is+JUvVg331SNtqZscBADixAG93TR/UQoG+Htp1MklPz42S3cGO0EWJUhE37dNfD2rz0Qvy87RpQr8mcmehbQAAgEITVMpTc4a2Uv2K/jqXmqV+k//UzhPFfz2pvWeSNHP9UUnSmLvqycPGPSMA4OZULuujyQOaycNm1fI9sbmz4VE0+E2Om7Lh8Dn9368HJElv96qvsEAfkxMBAAC4vjK+Hpo1pJUah5VWQlq2Hpzyp7Yeu2B2rKsyDEOvL9wtu8NQl3rl1L5WsNmRAAAuolmVQI3r3VCSNOn3w5q94ZjJiUoOSkXcsIS0LD3zbZQchtS7WSX1bFzR7EgAAAAlRoC3u74Z0lItqgYqOSNHD0/ZoI1Hzpsd64oW7zitDUfOy9Nm1egebM4CAChYPRtX1LOdLi7F9urCXfrjQLzJiUoGSkXcEMMw9NIPO3Q6MUPVgnz1xt31zI4EAABQ4pTytGnGo83VJrysUrPsGjhto9YeLF5vpFIzc/TO0j2SpBG31VClMjzZAgAoeCNvr6FeTSrK7jA0fNYWp9vMzBlRKuKGzNpwTL/sjpW7m0UT+jWRr6fN7EgAAAAlko+HTdMGNdettYKVnm3XIzM2adW+s2bHyjXh14OKTcpU5UAfPda+utlxAAAuymKxaOx9DdS8ahklZ+TokRmbFJ+SaXYsl0apiHzbdyZZby25+GnzS13qqH7FAJMTAQAAlGxe7m6aNKCZOtUNVVaOQ8NmbtF/dp8xO5YOxaVo6h+HJUmv9YiQl7ubyYkAAK7M0+amLx+OVJWyPjpxIV1DZ25WRrbd7Fgui1IR+ZKRbdfIOduUmePQrbWC9WjbamZHAgAAgC6+kfqif1N1b1BeWXaHnpi1VUt3nDYtj2EYGrNot7Lthm6rHazb64aYlgUAUHIE+npo2qDm8veyaduxBD3/3XY5HIbZsVwSpSLy5Z2l0doXm6ygUp764P5GslotZkcCAADAX9zdrBr/QGPd07iCchyGnpqzVfO3nTAly3/2xGrNgXh5uFn12l31ZLFw3wgAKBrhwaU08eFmslktWrLjtD5Zsd/sSC6JUhHX7ZfdZ/T1n0clSR/1aaRgP0+TEwEAAOB/2dys+rBPY/WJrCSHIT03b7vmbTpepBkysu16c/HF5XKGtq+makG+Rfr6AAC0CQ/Su/c2kCR9+utB/bDFnA/ZXFmhlorbt2/XHXfcIX9/f7m7uys8PFxz5sy57LqffvpJLVq0kK+vryIiIrR48eLCjIUbcDoxXS/9sEOSNKx9dbWvFWxyIgAAAFyNm9Wisfc2VP9WlWUY0os/7Mj9cLgofLH6kE4mpKt8gJdG3FajyF4XAIC/6xMZpuEdwiVJL/+4QxsOnzM5kWsptFIxKipKbdq00fLly1W3bl116NBBR48e1YMPPqjvv/8+97rvvvtOPXr0UExMjIYMGaKKFSuqV69eWrNmTWFFQz7ZHYaemRulhLRsNawUoFF31DY7EgAAAP6B1WrRWz3r566B/eqCXZqy5nChv+6xc2n64rdDkqTR3SPk42Er9NcEAOBqXrijtrrWL6dsu6Fh32xRTHyq2ZFchsUwjEJZrbJt27bavn27Fi5cqNtvv12S9Ouvv6pTp05q0KCBtm/frqSkJIWHh8vhcGjr1q2qUqWKJKlHjx46cuSIdu/efd2vl5SUpICAACUmJsrf378w/kgl1oSVB/Th8v3y9XDTkpG38PgKAACAEzEMQ+//sk9frL5Y9L3Ypbae6FB4sweHztys5Xti1Sa8rGYNaclaigAA06Vn2fXApPXafiJR1YN89eMTbVTax8PsWMVSfvq1QpmpuHfvXm3cuFFvv/12bqEoSR07dlTDhg21Y8cO2e12LViwQPHx8Xr66adzC0VJeumll7Rnzx5FRUUVRjzkw+aY8/pk5QFJ0lv31KdQBAAAcDIWi0Uv3llbz3SqKUl6/+d9+nj5fhXG3IJV+85q+Z5Y2awWvXE3m7MAAIoHbw83TR4YqYqlvXU4PlWPf7NFWTkOs2M5vUIpFevUqaOUlBSNGDHisnNZWVlyc3OTJK1bt06SdNddd+W5pnXr1vLy8tLq1asLIx6uU2J6tp6eGyW7w1CvJhV1b9NKZkcCAADADbBYLHqmUy292OXiMjbjVx7Q+7/sK9BiMTPnv5uzDGpTVTVD/QrsZwMAcLNC/Lw0dVCkSnna9Ofh8/p/83cWygdsJUmhrano6ekpd3f3PMdiYmIUHR2tyMhIubm5KTY2VlarVfXq1ctznc1mU5UqVXT4cOGv+YIrMwxDr/y4UycT0lWlrI/e7Fnvn78JAAAAxdoTHWro1R4Rki5upvLWkugCe0M19Y8jOhKfqqBSnnr6r1mRAAAUJ3XK+WvCg01ktUjfbTmRuwYwbkyh7v78v9577z1J0rBhwyT99zltD4/Ln2MPCAjQqVOnrvqzMjMzlZSUlOcLBWfe5uNauvO0bFaLPn2gify83P/5mwAAAFDsDW5XTW/dU1+SNG3tEb26cJccjpsrFk8npmvCyoOSpFe61eHeEQBQbN1WO0Rj7r44cer9n/fpp52nTU7kvIqsVNy8ebMmT56sevXqacCAAZIuzkj08fG54vUeHh5KS0u76s/797//rYCAgNyvsLCwQsldEh08m6wxiy4+uvL8nbXVKKy0uYEAAABQoB5uVUXv39dQFov0zZ/H9PKPO2S/iWLxnaXRSs+2K7JKGfVqUrEAkwIAUPAGtK6qR9pWlSQ9+22Uoo4nmJrHWRVJqZiRkaGBAwdKkqZOnZq7pqKvr6+ysrKu+D3Z2dlXPSdJ//rXv5SYmJj7dfz48YIPXgJlZNv11JwopWfb1a5GkB67pbrZkQAAAFAI+jQP08d9GstqkeZtPqFR86KUY8//ovXrDsVryY7TslqkN3qyOQsAwDmM7h6hjnVClJnj0JCvNuvEhatPbMOVFUmp+Oyzz2rPnj0aM2aMWrZsmXu8QoUKiouLu+KMxLNnz6p06dJX/Zmenp7y9/fP84WbN3bZXkWfTlJZXw991KeRrFZuCgEAAFzVPU0qakK/prJZLVoQdUoj525Tdj6KxWy7Q2MW7ZYkPdSyiupVCCisqAAAFCg3q0Wf9muiuuX9FZ+SqUdnbFJSRrbZsZxKoZeKs2bN0sSJE9W5c2e98sorec41atRIkrR169Y8x+Pj43XkyBGFhoYWdjz8zcroWM1YFyNJ+uD+Rgrx9zI3EAAAAApd94bl9UX/ZvJws+qnnWc0/JutysyxX9f3zlx/VPtjU1TGx12j7qhVyEkBAChYpTxtmjowUiF+ntofm6InZ2+7oVn7JVWhlorr16/XkCFDFBYWptmzZ8tqzftyXbt2ldVq1ZQpU/IcnzNnjiSpffv2hRkPfxOblKEXvt8hSXq0bTXdVifE5EQAAAAoKp0jQjVpQDN52qxaER2rx2ZuUUb2tYvFs8kZ+mT5fknSi13qqLTP5ZsvAgBQ3FUo7a2pA5vL291Nv++P05jFu2UYN7eBWUlRaKVidHS0unfvLkmaP3++goKCLrumUqVKuvfeezVz5kxNmjRJhmFo7dq1euONN1S2bFl17dq1sOLhb+wOQ89+G6XzqVmKKO+vl7rWNjsSAAAAiliH2iGaNujim6rf9sfp0RmblJaVc9Xr31u2T8mZOWpYKUB9Itk0EQDgvBpUCtAnDzTO3cBs+toYsyM5BYtRSPVrx44dtWrVKjVv3lx33HHHZecHDx6satWqKS4uTrfeequio6Pl7e2t9PR02Ww2zZ07V/fdd991v15SUpICAgKUmJjI+or59Pnqg3r/533ydnfTkpHtFB5cyuxIAAAAMMnGI+f1yPSNSs2yq3nVMpo2qLn8vNzzXLPl6Hnd98V6SdL8J9qoSeUyZkQFAKBATfr9kN79aa8sFmnyw5HqFFHyluXLT79WaKWih4eHsrOvvsDlqlWr1KFDB0lSenq6xo0bp5UrVyo4OFgvv/yyIiMj8/V6lIo3ZtuxC7p/4nrlOAy937shnzIDAABAW49d0MBpG5WckaPGYaX11aMtFOB9sVi0Owzd/X9/aPepJPWJrKT3ezcyOS0AAAXDMAy9Mn+n5mw8Lh8PN333eOsStwlZsSgVixqlYv4lZWSr+6drdPx8uno0LK8J/ZrIYmG3ZwAAAEg7TyTq4WkblJCWrfoV/fX1oy1VxtdD3/x5VKMX7JKfl02rnu+goFKeZkcFAKDAZNsdGjR9o9YePKdy/l5a+GRbhZagjWzz068V+u7PKJ4Mw9Do+bt0/Hy6KpXx1ju9GlAoAgAAIFeDSgGaM7SVyvp6aNfJJPWb/KcOnk3WB//ZJ0ka1bkWhSIAwOW4u1n1+UPNFB7sqzNJGRr81bXXGC7JKBVLqB+2ntSi7afkZrVo/ANNch9nAQAAAC6pW95f3w5rpRA/T+09k6yu49coIS1bdcr5qX+rKmbHAwCgUAR4u2v6oBYK/OuDtafnRsnucIkHfQsUpWIJdDguRa8t3CVJeq5zLTWrwsLaAAAAuLIaIX76dlhrlQ/wUrb94huqN+6uJ5sbbyUAAK6rclkfTR7QTB42q5bvidV7P+81O1Kxw51ACZOV49DIuduUlmVX6+pl9fit4WZHAgAAQDFXLchX84a11i01gzSqcy21rF7W7EgAABS6ZlUCNa53Q0nSpN8Pa/aGYyYnKl5sZgdA0Rr3y17tOpmkMj7u+rhvY7lZWUcRAAAA/yws0EdfD25pdgwAAIpUz8YVdfRcmj5avl+vLtylyoE+alczyOxYxQIzFUuQ1fvOavKaI5Kk93s3UrmAkrN7EQAAAAAAwI14qmMN9WpSUXaHoeGztuhAbLLZkYoFSsUSIi45U89/t12SNLB1FXWOCDU5EQAAAAAAQPFnsVg09r4Gal61jJIzcvTIDHaEligVSwSHw9Co77YrPiVLdcr56V/d6podCQAAAAAAwGl42tz05cORCg/21YjbasjHgxUF+SdQAkz944h+3x8nL3erJvRrIi93N7MjAQAAAAAAOJVAXw8te7q9PGzM0ZOYqejydpxI0Pu/XNz2/LUe9VQz1M/kRAAAAAAAAM6JQvG/+CfhwlIyczRyzjZl2w11rV9O/VqEmR0JAAAAAAAALoBS0YW9vnC3Ys6lqUKAl8be21AWi8XsSAAAAAAAAHABlIouasG2k/ph6wlZLdL4fk0U4ONudiQAAAAAAAC4CEpFF3T0XKpGL9glSRp5e001rxpociIAAAAAAAC4EkpFF5Ntd2jk3CilZOaoRdVAPXlbDbMjAQAAAAAAwMVQKrqYj5bv1/bjCQrwdtfHDzSWzY1/xQAAAAAAAChYNE4u5I8D8Zr42yFJ0nv3NVDF0t4mJwIAAAAAAIArolR0EedSMvXsvCgZhvRgy8rqUr+82ZEAAAAAAADgoigVXYBhGHr+u+2KS85UzZBSerV7hNmRAAAAAAAA4MIoFV3A9LUxWrUvTh42qyY82ETeHm5mRwIAAAAAAIALo1R0crtPJWrssr2SpFe711Wdcv4mJwIAAAAAAICro1R0YmlZOXpqzjZl2R3qHBGq/q2qmB0JAAAAAAAAJQClohN7Y9EeHY5LVTl/L71/X0NZLBazIwEAAAAAAKAEoFR0Ukt2nNK3m4/LYpE+7ttYZXw9zI4EAAAAAACAEoJS0QkdP5+mf/24U5L05G011Dq8rMmJAAAAAAAAUJJQKjqZHLtDT8/dpuSMHDWtXFpP317T7EgAAAAAAAAoYSgVncz4lQe09ViC/LxsGv9AE9nc+FcIAAAAAACAokUj5UTWHzqn/1t1UJL073sbKCzQx+REAAAAAAAAKIkoFZ3EhdQsPfttlAxD6hsZph4NK5gdCQAAAAAAACUUpaKTeOmHHTqTlKHqwb56/e4Is+MAAAAAAACgBKNUdBL9WlZWhQAvTejXRD4eNrPjAAAAAAAAoASzGIZhmB2iICQlJSkgIECJiYny9/c3O06hyMpxyMNGDwwAAAAAAICCl59+jYbKiVAoAgAAAAAAoDigpQIAAAAAAACQL5SKAAAAAAAAAPKFUhEAAAAAAABAvlAqAgAAAAAAAMgXSkUAAAAAAAAA+UKpCAAAAAAAACBfKBUBAAAAAAAA5AulIgAAAAAAAIB8oVQEAAAAAAAAkC+UigAAAAAAAADyhVIRAAAAAAAAQL5QKgIAAAAAAADIF0pFAAAAAAAAAPlCqQgAAAAAAAAgXygVAQAAAAAAAOQLpSIAAAAAAACAfKFUBAAAAAAAAJAvlIoAAAAAAAAA8oVSEQAAAAAAAEC+UCoCAAAAAAAAyBdKRQAAAAAAAAD5QqkIAAAAAAAAIF8oFQEAAAAAAADkC6UiAAAAAAAAgHyxmR2goBiGIUlKSkoyOQkAAAAAAADgfC71apd6tmtxmVIxOTlZkhQWFmZyEgAAAAAAAMB5JScnKyAg4JrXWIzrqR6dgMPh0KlTp+Tn5yeLxWJ2nAKXlJSksLAwHT9+XP7+/mbHgQthbKEwMK5QGBhXKCyMLRQGxhUKA+MKhYWxhUsMw1BycrIqVKggq/Xaqya6zExFq9WqSpUqmR2j0Pn7+/MfOAoFYwuFgXGFwsC4QmFhbKEwMK5QGBhXKCyMLUj6xxmKl7BRCwAAAAAAAIB8oVQEAAAAAAAAkC+Uik7C09NTr7/+ujw9Pc2OAhfD2EJhYFyhMDCuUFgYWygMjCsUBsYVCgtjCzfCZTZqAQAAAAAAAFA0mKkIAAAAAAAAIF8oFQEAAAAAAADkC6UiAAAAAAAAgHyhVHQyr7/+uiwWi6ZNm2Z2FDi5X375Re3atVOZMmUUGBiodu3aaenSpWbHgpNatWqVbDabYmJirnj+q6++Uv369eXt7a3IyEitX7++aAPCKV1rXKWkpGjUqFGqXLmy3NzcFBAQoMcff1xpaWlFHxRO5Z/+vvq7ffv2ycvLSzNmzCj0XHBu1zuu9u7dKy8vL916661FEwxO71pj6+zZs+rfv78qV64sHx8f1apVS6+99poyMjKKPiicwvbt23XHHXfI399f7u7uCg8P15w5cy677qefflKLFi3k6+uriIgILV682IS0cAaUik5k7969Gjt2rNq0aaNHHnnE7DhwYqtXr1b37t2VkpKiQYMGqVevXoqOjtZdd92lb7/91ux4cDLHjh1T3759Zbfbr3j+gw8+0KBBg5SSkqLhw4fLarWqU6dOOnDgQBEnhTO51riy2+3q1q2bPvroIzkcDnXt2lU+Pj768ssv1adPHxPSwln8099X/2vYsGHKzMws5FRwdtc7rgzD0LBhw2S32/X5558XUTo4s2uNrYyMDHXs2FGLFy/WnXfeqaFDh8rf319vvfWW7rvvPhPSoriLiopSmzZttHz5ctWtW1cdOnTQ0aNH9eCDD+r777/Pve67775Tjx49FBMToyFDhqhixYrq1auX1qxZY2J6FFsGnILD4TBuueUWw83NzYiKijI7Dpxc69atjcaNGxvZ2dm5x06fPm0EBgYaERERJiaDs9m1a5dRuXJlQ5IhyThy5Eie80eOHDE8PDyM8PBw4/z584ZhGEZWVpbRqFEjo2vXriYkhjP4p3E1adIkQ5IxcuRIIysryzAMw0hKSjLq1atnSDK2bt1qQmoUd/80rv7XtGnTcq+dPn16kWSE88nPuJo8ebIhyXj++eeLLiCc1j+NrS+//NKQZGzatCnP8QcffNCQZGzcuLEI08IZtGnTxvD19TVWrFiRe2zlypWGxWIxGjZsaBiGYSQmJhpBQUFGYGCgERMTk3td9+7deZ+IK2KmopOYMmWK1qxZoyeffFKNGjUyOw6c3NatW9W5c2fZbLbcY+XKlVPTpk118OBBE5PBmZw4cUJt27ZVQECAHnvssSteM3PmTGVlZem1115TmTJlJEnu7u4aNWqUfvnlF507d64oI8MJXO+4aty4scaPHy93d3dJkp+fn4YMGSLp4t9xwN9dz7j6u/j4eL3wwgsKCwsrgnRwVvkZV2fPntWLL76oihUr6vXXXy+ihHBW1zO2tm7dquDgYEVGRuY53q1bN0ninh557N27Vxs3btTbb7+t22+/Pfd4x44d1bBhQ+3YsUN2u10LFixQfHy8nn76aVWpUiX3updeekl79uxRVFSUCelRnFEqOoHY2Fi99NJLKleunN58802z48AF+Pj4KDo6Os+xnJwc7du3T1WrVjUnFJxOfHy8unXrpjVr1qh8+fJXvGbdunWyWCzq0aNHnuMdO3aUw+HgMQpc5nrG1Zo1a/THH39cdjwrK0uS8nxgAkjXN67+btSoUUpMTNSnn35aBOngrPIzrp555hlduHBBH3/8sUqVKlVECeGsrmds+fj46Pz58zp79mye47t27ZIk7umRR506dZSSkqIRI0Zcdi4rK0tubm6SLt67S9Jdd92V55rWrVvLy8tLq1evLvSscC6Uik7gueee04ULF9SyZUu9/fbb+te//sV/zLgp9957r5YuXapPPvlEFy5c0IkTJzRs2DAdP36c9Tpx3Ro0aKDZs2crICDgqtfExsaqYsWKCgwMzHO8YsWK8vT01OHDhws7JpzM9YwrSfL19b3s2E8//SRJatmyZaFkg/O63nElSb/++qtmzpyp559/Xo0bNy78cHBa1zuuli9frjlz5qhChQqKjo7Wc889pylTprCxFK7qesZWr1695HA41L9/fx08eFDJycmaO3euPv30U0VERKhFixZFmBjOwNPTM/cJj0tiYmIUHR2tyMhIubm5KTY2VlarVfXq1ctznc1mU5UqVbh3x2X4KL+Y27RpU+5uTAsXLsw9PnbsWA0bNkxffPGFLBaLWfHgpMaPH6+TJ0/q2Wef1bPPPitJslgsevXVV/XSSy+ZnA7O4tInmteSlJSk4ODgK54LCAjQqVOnCjoWnNz1jKsr+fPPP/Xbb7+pffv2qlOnTgGngrO73nGVmZmpxx9/XDVr1tTrr7+uM2fOFHIyOLPrGVcOh0OjRo2SJJ06dSrPo88fffSRFi9erPDw8ELLCOd0PWPrlltu0aRJkzR06FDVrFkz93jr1q01b968G/59ipLlvffek3RxYzLp4r17QECAPDw8LruWe3dcCTMVi7l33nlHhmGoVq1a+vnnn3Xs2DEtW7ZMTZs21ZdffqkJEyaYHRFOaMOGDVq7dq0kKSgoSP7+/jIMQz/88IO2bNlicjq4EpvNJh8fnyue8/DwYJYGCoTdbteIESNksVg0duxYs+PAib3zzjs6ePCgJk+eLC8vL7PjwAUsWrRIO3fulNVq1QcffKAjR45o586deuKJJxQdHc2O9bhhqampmjJliiTJy8tL5cuXl8Vi0c6dOzVr1iyT08EZbN68WZMnT1a9evU0YMAASdy7I/8oFYuxrKwsLVu2TBaLRfPnz9edd96psLAwdenSRcuWLZOHh0fuLxLgeuXk5GjAgAHy9PTUqlWrFBcXpwsXLmjGjBk6dOiQunXrxi8LFBhfX9/cde7+V3Z29lXPAfnxzjvvaOvWrRoxYoRat25tdhw4qb179+q9997TkCFDdOutt5odBy7i0pNGzz//vEaNGqWqVauqfv36+uyzz3TPPfdo69atbC6FG/Lmm29qw4YNeuWVV5SQkKBTp05p165dCg8P18svv6zvv//e7IgoxjIyMjRw4EBJ0tSpU3NntnLvjvyiVCzG4uPjlZWVpWbNmikiIiLPuZCQEDVo0EAHDhwwKR2c1dq1a3Xy5Em9+uqr6tChgyTJarVq4MCBeumllxQXF6fly5ebGxIuo0KFCoqJibnsuN1u1/nz51W6dOkizwTXsnbtWr311luqX7++3n//fbPjwEkZhqFhw4YpKChI48aNMzsOXMjJkyclKXcW0N9d2oGV+3nciHnz5qlp06Z655135OnpKUmKiIjQjz/+KIvFwmxFXNOzzz6rPXv2aMyYMXnWoq5QoYLi4uKuOMnk7Nmz3LvjMqypWIz5+flJ0lXXWcnJybniWgfAtZw+fVqSVLt27cvOXVqQl7UyUFAaNWqkpUuX6vjx4woLC8s9vm3bNmVnZys0NNTEdHB2sbGx6tu3r7y8vDRv3jx5e3ubHQlOasmSJfr999/VoUOHPKViYmKiJOnHH3/UwYMHdd9996lJkyZmxYQTutb9fE5OjiRxP48bcvr0abVq1eqy49WrV5eXlxf387iqWbNmaeLEiercubNeeeWVPOcaNWokSdq6davatWuXezw+Pl5HjhxR165dizQrij9KxWLMz89PVapU0fHjxy87l56erkOHDqlBgwYmJIMzu7RpxqFDhy47FxUVJUkqW7ZsUUaCC+vevbveffddTZ06VWPGjMk9fmkDqvbt25uUDM4uPT1dvXr10smTJzV37lzVrVvX7EhwYufOnZMkrV69WqtXr77s/OLFi7V48WLVqFGDUhH50qBBA/344486fvx4ns00JGnnzp2SdNkuq8D1CA4OvuL9/P79+5Wens79PK5o/fr1GjJkiMLCwjR79mxZrXkfXu3atausVqumTJmSp1Tk3h1Xw+PPxVzfvn21bt06LVu2LM/x0aNHKyUlhcWdkW9169aV1WrVRx99pIMHD+YeX7JkicaPHy9PT8/cx6KBm9WmTRtFRkbqvffe06JFiyRJCxYs0Oeff65atWqpRYsWJieEM7Lb7br//vu1fv16vfDCC+rbt6/ZkeDkBg0aJMMwLvs6cuSIJGn69OkyDEODBg0yNyicTu/evWW1WjVmzBjZ7fbc45s3b9Y333yjRo0aqVatWiYmhLOqX7++Nm/erK+++kqGYUiSYmJiNGTIEElSly5dzIyHYig6Olrdu3eXJM2fP19BQUGXXVOpUiXde++9mjlzpiZNmiTDMLR27Vq98cYbKlu2LDMVcRlmKhZzL7/8spYsWaK77rpLPXv2VFhYmNavX6+NGzeqSZMmuVu/A9erQoUKGj58uD777DPVqlVLQUFBSk1NzV034/XXX1dISIjJKeFKZs2apY4dO6pnz57y8fFRWlqafHx8NH36dFksFrPjwQnNmjVLS5culbe3t9zc3DR69Og855s2bap7773XpHQA8F/169fXiy++qLFjx2r37t269dZbdebMGS1YsECGYWjChAlmR4STGj16tH799VcNGjRITz31lLy8vBQXFydJqlOnjoYOHWpyQhQ3I0aM0IULF9S8eXPNnz9f8+fPz3N+8ODBqlatmj7//HPt3r1bw4YN0zPPPKP09HTZbDbNnTtX/v7+JqVHcUWpWMyVKVNGa9eu1QcffKDvv/9ey5YtU2BgoIYMGaJ///vfrB+FGzJhwgTVrVtX06ZNU3R0tBwOh+rXr6/HHntMTz31lNnx4GJq1aqlHTt26N1339WGDRtUtWpVvfbaa5c9BgZcrz/++EPSxUegx44de9n5gQMHUioCKDb+/e9/q0mTJvr888/1zTffKCcnRy1bttQ777yjW265xex4cFJt27bN3axs7dq1unDhgsqXL68uXbro3Xff5X0iLnPp/mnTpk3atGnTZec7deqkatWqKTg4WFu2bNG4ceO0cuVKBQcH6+WXX1ZkZGRRR4YTsBiX5koDAAAAAAAAwHVgTUUAAAAAAAAA+UKpCAAAAAAAACBfKBUBAAAAAAAA5AulIgAAAAAAAIB8oVQEAAAAAAAAkC+UigAAAHAK2dnZSkhIUGZmptlRAAAASjxKRQAAANyU77//XuXKldPXX39dqK+zdOlSlSlTRhMnTizU1wEAAMA/s5kdAAAAAM7NbrcrNjb2uq93OBxyOBxXPGezXf321MPDI8//AgAAwDzMVAQAAMBNuVTyXasQ/LunnnpK7u7uV/w6d+7cP76Ou7v7zYcGAADATWGmIgAAAG7KpZLPYrHk6/px48YpKChIkjRlyhStXbv2mrMQL/18q5XPxQEAAMxGqQgAAIAidalU7N27t6pWrSpJWr16tdauXav09HS9+eab8vT0lLu7u9zc3OTm5iaLxaLDhw9Lkn766ScdPXpUmZmZGjt2rFl/DAAAgBKNUhEAAADX7eDBg5o7d67uuece1a9f/6rXzZo1S4ZhqH///pedu9bjy1lZWfroo4/k4eEhm80mNzc3Wa1WWSwW5eTkSJKWLVumZcuWyW63UyoCAACYhGdHAAAAcN2WLVumV199VbNnz77qNUlJSXr66af1/PPPKyUl5bLz13pMukKFCrLb7UpPT1dycrISEhJ0/vx5nTt3TgsXLpQkffbZZ0pNTVVGRsbN/4EAAABwQygVAQAAcN0WLVokSVecgXiJv7+/Xn/9dcXGxurDDz+87PylNRGrVasmi8Uii8Wir776Ks85SUpNTS3I6AAAAChAlIoAAAC4LufOndPq1avVtGlTRUREXPPa4cOHq3r16vrwww91/vz5POcuzVQcO3asJk+erMmTJ6tNmza55+12u7p166Z27drJ4XAU/B8EAAAAN41SEQAAANdl1qxZysnJ0YABA/7xWpvNptGjRys5OVnjxo3Lc+5Sqdi3b18NGTJEQ4YMUc2aNXPPu7m5qUaNGoqKisqdwQgAAIDihVIRAAAA12Xy5Mmy2Wzq16/fdV3/8MMPq0qVKpowYYIuXLiQe9xut//j944ZM0a+vr566623lJ2dfcOZAQAAUDgoFQEAAPCPfvvtN+3atUvdu3dXSEjIdX2PzWbTyJEjlZqaqs8++yz3+LVKRcMwJEmBgYHq27evjh8/rjVr1txceAAAABQ4SkUAAAD8ozFjxkiSHnvssXx93+DBg+Xt7a1Jkyblro94qVRcu3atVqxYoRUrVuj06dN5zknSK6+8okOHDqljx44F8CcAAABAQbKZHQAAAADF2/79+7Vx40bVrl1bXbt2zdf3BgQE6PHHH1eFChWUlZUlLy8v5eTkSLryDtI5OTmy2S7eooaHh998eAAAABQKSkUAAABcU61atXTs2DEdPHgwd5OV/Pjoo4/y/P9LsxHXrFmjSpUqSZKef/55/fDDD7nFIwAAAIo3SkUAAAD8o7Jly6ps2bIF8rMulYqVKlVS1apVJUnt27fPncEIAACA4o81FQEAAHBTjh07Jkm5jy3/k9DQUNWrV08eHh65x0aOHKkFCxbI39//qt8XExMjSTc0WxIAAAAFy2Jc2mIPAAAAyIeePXtq1apVSk5OliTt3LlT9evXL/DXGT58uBYtWqS4uDhlZ2dr06ZNioyMLPDXAQAAwPVjpiIAAABuSO/evRUQEKA777xTX3/9daEUipL0xBNPqFy5cnrooYf0888/UygCAAAUA8xUBAAAAAAAAJAvzFQEAAAAAAAAkC+UigAAAAAAAADyhVIRAAAAAAAAQL5QKgIAAAAAAADIF0pFAAAAAAAAAPlCqQgAAAAAAAAgXygVAQAAAAAAAOQLpSIAAAAAAACAfKFUBAAAAAAAAJAvlIoAAAAAAAAA8uX/A7KNn5TuorzzAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 1600x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 利用自定义时间格式函数strftime提取小时数\n", "data[\"小时\"]=data[\"成交时间\"].map(lambda x:int(x.strftime(\"%H\")))\n", "# 对小时和订单去重\n", "traffic=data[[\"小时\",\"订单ID\"]].drop_duplicates()\n", "# 求每小时的客流量\n", "traffic.groupby(\"小时\")[\"订单ID\"].count().plot()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 2}
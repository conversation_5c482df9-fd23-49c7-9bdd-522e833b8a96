# 使用KMeans进行聚类,导入库
from sklearn.cluster import KMeans

from sklearn.metrics import silhouette_score

import matplotlib.pyplot as plt

import warnings
warnings.filterwarnings("ignore")
# 预处理
from sklearn import preprocessing
from sklearn.preprocessing import LabelEncoder
import pandas as pd
# 矩阵运算
import numpy as np

import pandas as pd

# 数据加载
data = pd.read_csv('./car_price.csv')
print(data.head(3))

# 删除，无价值的特征
train_x = data.drop(['car_ID','CarName'],axis = 1)
print(train_x.head(3))

# 将非数值字段转化为数值
le = LabelEncoder()
columns = ['fueltype','aspiration','doornumber','carbody','drivewheel','enginelocation',
           'enginetype','cylindernumber','fuelsystem']
for column in columns:
    # 训练并将标签转换为归一化的编码。
    train_x[column] = le.fit_transform(train_x[column])
print(train_x)

# 规范化到 [0,1] 空间
min_max_scaler=preprocessing.MinMaxScaler()
# MinMaxScaler()将每个要素缩放到给定范围，拟合数据，然后对其进行转换。
train_x = min_max_scaler.fit_transform(train_x)
print(train_x)


# 查找自己电脑上的中文字体，从打印输出中找到对应中文字体
# 本电脑上，我选择：STKaiti（注意，电脑不同，字体可能不同，需要各位自己修改）
from matplotlib.font_manager import FontManager
fm = FontManager()
[font.name for font in fm.ttflist]

plt.rcParams['font.family'] = 'STKaiti'
plt.rcParams['font.size'] = 20

# 选择聚类组数
# 每簇样本到其聚类中心的距离的平方和
# 各个簇的inertia相加的和越小，即簇内越相似
# 但是k越大inertia越小，追求k越大对应用无益处
sse = [] # 簇惯性
ss = [] # 轮廓系数
for k in range(2, 11):
    kmeans = KMeans(n_clusters=k)
    kmeans.fit(train_x)
    # 计算inertia簇内误差平方和
    sse.append(kmeans.inertia_)
    ss.append(silhouette_score(train_x,kmeans.predict(train_x)))


plt.figure(figsize=(16,6))
x = range(2, 11)
plt.subplot(1,2,1)
plt.plot(x, sse, 'o-')
plt.xlabel('K')
plt.ylabel('SSE簇惯性')

plt.subplot(1,2,2)
plt.plot(x,ss,'r*-')
plt.xlabel('K')
plt.ylabel('轮廓系数')
plt.savefig('./1-聚类簇数.png',dpi = 200)

# 使用KMeans聚类,分成8类
kmeans = KMeans(n_clusters=8)
kmeans.fit(train_x)  # 建模
# 预测
predict_y = kmeans.predict(train_x)
print(predict_y)

# 合并聚类结果，插入到原数据中,axis： 需要合并链接的轴，0是行，1是列
result = pd.concat((data,pd.DataFrame(predict_y)),axis=1)
# 将结果列重命名为'聚类结果'
result.rename({0:u'聚类结果'},axis=1,inplace=True)
print(result)

# 分组运算
g1 = result.groupby(by = ['聚类结果','carbody'])[['price']].mean()
print(g1)

g2 = g1.unstack() # 重塑，行变成了列
print(g2)

print(g2.sort_values(by = ('price','sedan')))

# 查看：类别是1的标准三厢车（具体根据分组运算结果确定）
cond = result.apply(lambda x : x['聚类结果'] == 0 and 'sedan' in x['carbody'],axis = 1)

columns = ['CarName',"wheelbase", "price",'horsepower','carbody','fueltype','聚类结果']
# 价格降序排名
print(result[cond][columns].sort_values('price',ascending = False))

# 根据条件（售价）筛选高端轿车（三厢车）
cond = result.apply(lambda x : x['聚类结果'] == 4  and 'sedan' in x['carbody'],axis =1)

columns = ['CarName',"wheelbase", "price",'horsepower','carbody','fueltype','聚类结果']
print(result[cond][columns].sort_values('price',ascending = False))

print(cond)


# 根据条件（售价）筛选中端SUV轿车
cond = result.apply(lambda x : x['聚类结果'] == 1 and 'wagon' in x['carbody'],axis =1)
columns = ['CarName',"wheelbase", "price",'horsepower','carbody','fueltype','聚类结果']
print(result[cond][columns].sort_values('price',ascending = False))

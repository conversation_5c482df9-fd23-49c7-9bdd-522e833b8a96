{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 数据处理"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 导入相应模型"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 339}, "colab_type": "code", "executionInfo": {"elapsed": 13585, "status": "ok", "timestamp": 1588476243809, "user": {"displayName": "<PERSON>", "photoUrl": "", "userId": "15935752008260826958"}, "user_tz": -480}, "id": "nHmJU1EDHAuu", "outputId": "1002dbe5-94f0-4447-9674-1be62f098005"}, "outputs": [], "source": ["import gc # 垃圾回收\n", "import pandas as pd \n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "#导入分析库\n", "from sklearn.model_selection import train_test_split # 数据拆分\n", "from sklearn.model_selection import StratifiedKFold # 同分布数据拆分，交叉验证\n", "import lightgbm as lgb # 微软\n", "import xgboost as xgb"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 数据加载"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 50}, "colab_type": "code", "id": "LcIL6KzTHx7S", "outputId": "d5450150-4599-450a-eb9f-ffd00c21e40c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 18.6 s\n"]}], "source": ["%%time\n", "# 加载数据\n", "# 用户行为日志\n", "user_log = pd.read_csv('./data_format1/user_log_format1.csv', dtype={'time_stamp':'str'})\n", "# 用户画像\n", "user_info = pd.read_csv('./data_format1/user_info_format1.csv')\n", "# 训练数据和测试数据\n", "train_data = pd.read_csv('./data_format1/train_format1.csv')\n", "test_data = pd.read_csv('./data_format1/test_format1.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 查看数据"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---data shape---\n", "(54925330, 7)\n", "(424170, 3)\n", "(260864, 3)\n", "(261477, 3)\n"]}], "source": ["print('---data shape---')     \n", "for data in [user_log, user_info, train_data, test_data]:\n", "    print(data.shape)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---data info ---\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 54925330 entries, 0 to 54925329\n", "Data columns (total 7 columns):\n", " #   Column       Dtype  \n", "---  ------       -----  \n", " 0   user_id      int64  \n", " 1   item_id      int64  \n", " 2   cat_id       int64  \n", " 3   seller_id    int64  \n", " 4   brand_id     float64\n", " 5   time_stamp   object \n", " 6   action_type  int64  \n", "dtypes: float64(1), int64(5), object(1)\n", "memory usage: 2.9+ GB\n", "None\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 424170 entries, 0 to 424169\n", "Data columns (total 3 columns):\n", " #   Column     Non-Null Count   Dtype  \n", "---  ------     --------------   -----  \n", " 0   user_id    424170 non-null  int64  \n", " 1   age_range  421953 non-null  float64\n", " 2   gender     417734 non-null  float64\n", "dtypes: float64(2), int64(1)\n", "memory usage: 9.7 MB\n", "None\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 260864 entries, 0 to 260863\n", "Data columns (total 3 columns):\n", " #   Column       Non-Null Count   Dtype\n", "---  ------       --------------   -----\n", " 0   user_id      260864 non-null  int64\n", " 1   merchant_id  260864 non-null  int64\n", " 2   label        260864 non-null  int64\n", "dtypes: int64(3)\n", "memory usage: 6.0 MB\n", "None\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 261477 entries, 0 to 261476\n", "Data columns (total 3 columns):\n", " #   Column       Non-Null Count   Dtype  \n", "---  ------       --------------   -----  \n", " 0   user_id      261477 non-null  int64  \n", " 1   merchant_id  261477 non-null  int64  \n", " 2   prob         0 non-null       float64\n", "dtypes: float64(1), int64(2)\n", "memory usage: 6.0 MB\n", "None\n"]}], "source": ["print('---data info ---')\n", "for data in [user_log, user_info, train_data, test_data]:\n", "    print(data.info())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>age_range</th>\n", "      <th>gender</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>376517</td>\n", "      <td>6.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>234512</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>344532</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>186135</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>30230</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  age_range  gender\n", "0   376517        6.0     1.0\n", "1   234512        5.0     0.0\n", "2   344532        5.0     0.0\n", "3   186135        5.0     0.0\n", "4    30230        5.0     0.0"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(user_info.head())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>34176</td>\n", "      <td>3906</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>34176</td>\n", "      <td>121</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>34176</td>\n", "      <td>4356</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>34176</td>\n", "      <td>2217</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>230784</td>\n", "      <td>4818</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  merchant_id  label\n", "0    34176         3906      0\n", "1    34176          121      0\n", "2    34176         4356      1\n", "3    34176         2217      0\n", "4   230784         4818      0"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>prob</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>163968</td>\n", "      <td>4605</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>360576</td>\n", "      <td>1581</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>98688</td>\n", "      <td>1964</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>98688</td>\n", "      <td>3645</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>295296</td>\n", "      <td>3361</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  merchant_id  prob\n", "0   163968         4605   NaN\n", "1   360576         1581   NaN\n", "2    98688         1964   NaN\n", "3    98688         3645   NaN\n", "4   295296         3361   NaN"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(train_data.head(),test_data.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 数据集成"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {}, "colab_type": "code", "id": "OT2QGDNkHAu4"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>label</th>\n", "      <th>origin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>34176</td>\n", "      <td>3906</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>34176</td>\n", "      <td>121</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>34176</td>\n", "      <td>4356</td>\n", "      <td>1.0</td>\n", "      <td>train</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>34176</td>\n", "      <td>2217</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>230784</td>\n", "      <td>4818</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  merchant_id  label origin\n", "0    34176         3906    0.0  train\n", "1    34176          121    0.0  train\n", "2    34176         4356    1.0  train\n", "3    34176         2217    0.0  train\n", "4   230784         4818    0.0  train"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["(522341, 4)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["train_data['origin'] = 'train'\n", "test_data['origin'] = 'test'\n", "# 集成\n", "all_data = pd.concat([train_data, test_data], ignore_index=True, sort=False)\n", "# prob测试数据中特有的一列\n", "all_data.drop(['prob'], axis=1, inplace=True) # 删除概率这一列\n", "display(all_data.head(),all_data.shape)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["(522341, 6)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>label</th>\n", "      <th>origin</th>\n", "      <th>age_range</th>\n", "      <th>gender</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>34176</td>\n", "      <td>3906</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>34176</td>\n", "      <td>121</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>34176</td>\n", "      <td>4356</td>\n", "      <td>1.0</td>\n", "      <td>train</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>34176</td>\n", "      <td>2217</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>230784</td>\n", "      <td>4818</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  merchant_id  label origin  age_range  gender\n", "0    34176         3906    0.0  train        6.0     0.0\n", "1    34176          121    0.0  train        6.0     0.0\n", "2    34176         4356    1.0  train        6.0     0.0\n", "3    34176         2217    0.0  train        6.0     0.0\n", "4   230784         4818    0.0  train        0.0     0.0"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 连接user_info表，通过user_id关联\n", "all_data = all_data.merge(user_info, on='user_id', how='left')\n", "display(all_data.shape,all_data.head())"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# 使用 merchant_id（原列名seller_id）\n", "user_log.rename(columns={'seller_id':'merchant_id'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["48"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["del train_data,test_data,user_info\n", "gc.collect()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 数据类型转换"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 54925330 entries, 0 to 54925329\n", "Data columns (total 7 columns):\n", " #   Column       Dtype  \n", "---  ------       -----  \n", " 0   user_id      int64  \n", " 1   item_id      int64  \n", " 2   cat_id       int64  \n", " 3   merchant_id  int64  \n", " 4   brand_id     float64\n", " 5   time_stamp   object \n", " 6   action_type  int64  \n", "dtypes: float64(1), int64(5), object(1)\n", "memory usage: 2.9+ GB\n"]}, {"data": {"text/plain": ["None"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Wall time: 8.94 ms\n"]}], "source": ["%%time\n", "display(user_log.info())"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>item_id</th>\n", "      <th>cat_id</th>\n", "      <th>merchant_id</th>\n", "      <th>brand_id</th>\n", "      <th>time_stamp</th>\n", "      <th>action_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>328862</td>\n", "      <td>323294</td>\n", "      <td>833</td>\n", "      <td>2882</td>\n", "      <td>2661.0</td>\n", "      <td>0829</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>328862</td>\n", "      <td>844400</td>\n", "      <td>1271</td>\n", "      <td>2882</td>\n", "      <td>2661.0</td>\n", "      <td>0829</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>328862</td>\n", "      <td>575153</td>\n", "      <td>1271</td>\n", "      <td>2882</td>\n", "      <td>2661.0</td>\n", "      <td>0829</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>328862</td>\n", "      <td>996875</td>\n", "      <td>1271</td>\n", "      <td>2882</td>\n", "      <td>2661.0</td>\n", "      <td>0829</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>328862</td>\n", "      <td>1086186</td>\n", "      <td>1271</td>\n", "      <td>1253</td>\n", "      <td>1049.0</td>\n", "      <td>0829</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  item_id  cat_id  merchant_id  brand_id time_stamp  action_type\n", "0   328862   323294     833         2882    2661.0       0829            0\n", "1   328862   844400    1271         2882    2661.0       0829            0\n", "2   328862   575153    1271         2882    2661.0       0829            0\n", "3   328862   996875    1271         2882    2661.0       0829            0\n", "4   328862  1086186    1271         1253    1049.0       0829            0"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Wall time: 7.99 ms\n"]}], "source": ["%%time\n", "display(user_log.head())"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 50}, "colab_type": "code", "id": "Emc8OvZvHAu8", "outputId": "5836054e-8a9d-4cdc-d7cc-db1865b4844e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 54925330 entries, 0 to 54925329\n", "Data columns (total 7 columns):\n", " #   Column       Dtype         \n", "---  ------       -----         \n", " 0   user_id      int32         \n", " 1   item_id      int32         \n", " 2   cat_id       int32         \n", " 3   merchant_id  int32         \n", " 4   brand_id     int32         \n", " 5   time_stamp   datetime64[ns]\n", " 6   action_type  int32         \n", "dtypes: datetime64[ns](1), int32(6)\n", "memory usage: 1.6 GB\n"]}, {"data": {"text/plain": ["None"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>item_id</th>\n", "      <th>cat_id</th>\n", "      <th>merchant_id</th>\n", "      <th>brand_id</th>\n", "      <th>time_stamp</th>\n", "      <th>action_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>328862</td>\n", "      <td>323294</td>\n", "      <td>833</td>\n", "      <td>2882</td>\n", "      <td>2661</td>\n", "      <td>1900-01-01 08:29:00</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>328862</td>\n", "      <td>844400</td>\n", "      <td>1271</td>\n", "      <td>2882</td>\n", "      <td>2661</td>\n", "      <td>1900-01-01 08:29:00</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>328862</td>\n", "      <td>575153</td>\n", "      <td>1271</td>\n", "      <td>2882</td>\n", "      <td>2661</td>\n", "      <td>1900-01-01 08:29:00</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>328862</td>\n", "      <td>996875</td>\n", "      <td>1271</td>\n", "      <td>2882</td>\n", "      <td>2661</td>\n", "      <td>1900-01-01 08:29:00</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>328862</td>\n", "      <td>1086186</td>\n", "      <td>1271</td>\n", "      <td>1253</td>\n", "      <td>1049</td>\n", "      <td>1900-01-01 08:29:00</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  item_id  cat_id  merchant_id  brand_id          time_stamp  \\\n", "0   328862   323294     833         2882      2661 1900-01-01 08:29:00   \n", "1   328862   844400    1271         2882      2661 1900-01-01 08:29:00   \n", "2   328862   575153    1271         2882      2661 1900-01-01 08:29:00   \n", "3   328862   996875    1271         2882      2661 1900-01-01 08:29:00   \n", "4   328862  1086186    1271         1253      1049 1900-01-01 08:29:00   \n", "\n", "   action_type  \n", "0            0  \n", "1            0  \n", "2            0  \n", "3            0  \n", "4            0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Wall time: 8.59 s\n"]}], "source": ["%%time\n", "# 用户行为数据类型转换\n", "user_log['user_id'] = user_log['user_id'].astype('int32')\n", "user_log['merchant_id'] = user_log['merchant_id'].astype('int32')\n", "user_log['item_id'] = user_log['item_id'].astype('int32')\n", "user_log['cat_id'] = user_log['cat_id'].astype('int32')\n", "user_log['brand_id'].fillna(0, inplace=True)\n", "user_log['brand_id'] = user_log['brand_id'].astype('int32')\n", "user_log['time_stamp'] = pd.to_datetime(user_log['time_stamp'], format='%H%M')\n", "user_log['action_type'] = user_log['action_type'].astype('int32')\n", "display(user_log.info(),user_log.head())"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["user_id             0\n", "merchant_id         0\n", "label          261477\n", "origin              0\n", "age_range        2578\n", "gender           7545\n", "dtype: int64"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(all_data.isnull().sum())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["user_id             0\n", "merchant_id         0\n", "label          261477\n", "origin              0\n", "age_range           0\n", "gender              0\n", "dtype: int64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 缺失值填充\n", "all_data['age_range'].fillna(0, inplace=True)\n", "all_data['gender'].fillna(2, inplace=True)\n", "all_data.isnull().sum()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 522341 entries, 0 to 522340\n", "Data columns (total 6 columns):\n", " #   Column       Non-Null Count   Dtype  \n", "---  ------       --------------   -----  \n", " 0   user_id      522341 non-null  int64  \n", " 1   merchant_id  522341 non-null  int64  \n", " 2   label        260864 non-null  float64\n", " 3   origin       522341 non-null  object \n", " 4   age_range    522341 non-null  float64\n", " 5   gender       522341 non-null  float64\n", "dtypes: float64(3), int64(2), object(1)\n", "memory usage: 27.9+ MB\n"]}], "source": ["all_data.info()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["256"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["2**8\n", "# -128 ~ 127"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["4294967296"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["2**32"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 522341 entries, 0 to 522340\n", "Data columns (total 6 columns):\n", " #   Column       Non-Null Count   Dtype \n", "---  ------       --------------   ----- \n", " 0   user_id      522341 non-null  int32 \n", " 1   merchant_id  522341 non-null  int32 \n", " 2   label        522341 non-null  object\n", " 3   origin       522341 non-null  object\n", " 4   age_range    522341 non-null  int8  \n", " 5   gender       522341 non-null  int8  \n", "dtypes: int32(2), int8(2), object(2)\n", "memory usage: 16.9+ MB\n"]}], "source": ["all_data['age_range'] = all_data['age_range'].astype('int8')\n", "all_data['gender'] = all_data['gender'].astype('int8')\n", "all_data['label'] = all_data['label'].astype('str')\n", "all_data['user_id'] = all_data['user_id'].astype('int32')\n", "all_data['merchant_id'] = all_data['merchant_id'].astype('int32')\n", "all_data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 用户特征工程(5min)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 50}, "colab_type": "code", "id": "2FbHkI6RHAu_", "outputId": "f53d20cc-9b15-4778-9f4f-9387fbed316e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 4min 18s\n"]}, {"data": {"text/plain": ["16279"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "##### 特征处理\n", "##### User特征处理\n", "groups = user_log.groupby(['user_id'])\n", "\n", "# 用户交互行为数量 u1\n", "temp = groups.size().reset_index().rename(columns={0:'u1'})\n", "all_data = all_data.merge(temp, on='user_id', how='left')\n", "\n", "# 细分\n", "# 使用 agg 基于列的聚合操作，统计唯一值个数 item_id, cat_id, merchant_id, brand_id\n", "# 用户，交互行为：点了多少商品呢？\n", "temp = groups['item_id'].agg([('u2', 'nunique')]).reset_index()\n", "all_data = all_data.merge(temp, on='user_id', how='left')\n", "\n", "# 用户，交互行为，具体统计：类目多少\n", "temp = groups['cat_id'].agg([('u3', 'nunique')]).reset_index()\n", "all_data = all_data.merge(temp, on='user_id', how='left')\n", "\n", "temp = groups['merchant_id'].agg([('u4', 'nunique')]).reset_index()\n", "all_data = all_data.merge(temp, on='user_id', how='left')\n", "\n", "temp = groups['brand_id'].agg([('u5', 'nunique')]).reset_index()\n", "all_data = all_data.merge(temp, on='user_id', how='left')\n", "\n", "\n", "# 购物时间间隔特征 u6 按照小时\n", "temp = groups['time_stamp'].agg([('F_time', 'min'), ('B_time', 'max')]).reset_index()\n", "temp['u6'] = (temp['B_time'] - temp['F_time']).dt.seconds/3600\n", "all_data = all_data.merge(temp[['user_id', 'u6']], on='user_id', how='left')\n", "\n", "\n", "# 统计操作类型为0，1，2，3的个数\n", "temp = groups['action_type'].value_counts().unstack().reset_index().rename(\n", "    columns={0:'u7', 1:'u8', 2:'u9', 3:'u10'})\n", "all_data = all_data.merge(temp, on='user_id', how='left')\n", "\n", "del temp,groups\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>label</th>\n", "      <th>origin</th>\n", "      <th>age_range</th>\n", "      <th>gender</th>\n", "      <th>u1</th>\n", "      <th>u2</th>\n", "      <th>u3</th>\n", "      <th>u4</th>\n", "      <th>u5</th>\n", "      <th>u6</th>\n", "      <th>u7</th>\n", "      <th>u8</th>\n", "      <th>u9</th>\n", "      <th>u10</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>34176</td>\n", "      <td>3906</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>108</td>\n", "      <td>5.833333</td>\n", "      <td>410.0</td>\n", "      <td>NaN</td>\n", "      <td>34.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>34176</td>\n", "      <td>121</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>108</td>\n", "      <td>5.833333</td>\n", "      <td>410.0</td>\n", "      <td>NaN</td>\n", "      <td>34.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>34176</td>\n", "      <td>4356</td>\n", "      <td>1.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>108</td>\n", "      <td>5.833333</td>\n", "      <td>410.0</td>\n", "      <td>NaN</td>\n", "      <td>34.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>34176</td>\n", "      <td>2217</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>108</td>\n", "      <td>5.833333</td>\n", "      <td>410.0</td>\n", "      <td>NaN</td>\n", "      <td>34.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>230784</td>\n", "      <td>4818</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>54</td>\n", "      <td>31</td>\n", "      <td>17</td>\n", "      <td>20</td>\n", "      <td>19</td>\n", "      <td>5.166667</td>\n", "      <td>47.0</td>\n", "      <td>NaN</td>\n", "      <td>7.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  merchant_id label origin  age_range  gender   u1   u2  u3   u4  \\\n", "0    34176         3906   0.0  train          6       0  451  256  45  109   \n", "1    34176          121   0.0  train          6       0  451  256  45  109   \n", "2    34176         4356   1.0  train          6       0  451  256  45  109   \n", "3    34176         2217   0.0  train          6       0  451  256  45  109   \n", "4   230784         4818   0.0  train          0       0   54   31  17   20   \n", "\n", "    u5        u6     u7  u8    u9  u10  \n", "0  108  5.833333  410.0 NaN  34.0  7.0  \n", "1  108  5.833333  410.0 NaN  34.0  7.0  \n", "2  108  5.833333  410.0 NaN  34.0  7.0  \n", "3  108  5.833333  410.0 NaN  34.0  7.0  \n", "4   19  5.166667   47.0 NaN   7.0  NaN  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["all_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 店铺特征工程(5min)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 104}, "colab_type": "code", "id": "YnvNCjc4HAvC", "outputId": "b8dfd4d9-f649-4e74-e695-4e0bf91ffe6e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 5min 21s\n"]}, {"data": {"text/plain": ["8798"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "##### 商家特征处理\n", "groups = user_log.groupby(['merchant_id'])\n", "\n", "# 商家被交互行为数量 m1\n", "temp = groups.size().reset_index().rename(columns={0:'m1'})\n", "all_data = all_data.merge(temp, on='merchant_id', how='left')\n", "\n", "# 统计商家被交互的 user_id, item_id, cat_id, brand_id 唯一值\n", "temp = groups['user_id', 'item_id', 'cat_id', 'brand_id'].nunique().reset_index().rename(\n", "    columns={\n", "    'user_id':'m2',\n", "    'item_id':'m3', \n", "    'cat_id':'m4', \n", "    'brand_id':'m5'})\n", "all_data = all_data.merge(temp, on='merchant_id', how='left')\n", "\n", "# 统计商家被交互的 action_type 唯一值\n", "temp = groups['action_type'].value_counts().unstack().reset_index().rename(  \n", "    columns={0:'m6', 1:'m7', 2:'m8', 3:'m9'})\n", "all_data = all_data.merge(temp, on='merchant_id', how='left')\n", "\n", "del temp\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>label</th>\n", "      <th>origin</th>\n", "      <th>age_range</th>\n", "      <th>gender</th>\n", "      <th>u1</th>\n", "      <th>u2</th>\n", "      <th>u3</th>\n", "      <th>u4</th>\n", "      <th>...</th>\n", "      <th>u10</th>\n", "      <th>m1</th>\n", "      <th>m2</th>\n", "      <th>m3</th>\n", "      <th>m4</th>\n", "      <th>m5</th>\n", "      <th>m6</th>\n", "      <th>m7</th>\n", "      <th>m8</th>\n", "      <th>m9</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>522336</th>\n", "      <td>228479</td>\n", "      <td>3111</td>\n", "      <td>nan</td>\n", "      <td>test</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>2004</td>\n", "      <td>1173</td>\n", "      <td>71</td>\n", "      <td>278</td>\n", "      <td>...</td>\n", "      <td>208.0</td>\n", "      <td>10105</td>\n", "      <td>4154</td>\n", "      <td>542</td>\n", "      <td>50</td>\n", "      <td>18</td>\n", "      <td>8997.0</td>\n", "      <td>9.0</td>\n", "      <td>687.0</td>\n", "      <td>412.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>522337</th>\n", "      <td>97919</td>\n", "      <td>2341</td>\n", "      <td>nan</td>\n", "      <td>test</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>55</td>\n", "      <td>29</td>\n", "      <td>14</td>\n", "      <td>17</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>5543</td>\n", "      <td>1592</td>\n", "      <td>352</td>\n", "      <td>93</td>\n", "      <td>19</td>\n", "      <td>4548.0</td>\n", "      <td>6.0</td>\n", "      <td>815.0</td>\n", "      <td>174.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>522338</th>\n", "      <td>97919</td>\n", "      <td>3971</td>\n", "      <td>nan</td>\n", "      <td>test</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>55</td>\n", "      <td>29</td>\n", "      <td>14</td>\n", "      <td>17</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>28892</td>\n", "      <td>7587</td>\n", "      <td>272</td>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>24602.0</td>\n", "      <td>94.0</td>\n", "      <td>2608.0</td>\n", "      <td>1588.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>522339</th>\n", "      <td>32639</td>\n", "      <td>3536</td>\n", "      <td>nan</td>\n", "      <td>test</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>72</td>\n", "      <td>46</td>\n", "      <td>24</td>\n", "      <td>33</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>14027</td>\n", "      <td>4956</td>\n", "      <td>322</td>\n", "      <td>19</td>\n", "      <td>3</td>\n", "      <td>12807.0</td>\n", "      <td>29.0</td>\n", "      <td>793.0</td>\n", "      <td>398.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>522340</th>\n", "      <td>32639</td>\n", "      <td>3319</td>\n", "      <td>nan</td>\n", "      <td>test</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>72</td>\n", "      <td>46</td>\n", "      <td>24</td>\n", "      <td>33</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>25959</td>\n", "      <td>7927</td>\n", "      <td>952</td>\n", "      <td>175</td>\n", "      <td>85</td>\n", "      <td>21737.0</td>\n", "      <td>34.0</td>\n", "      <td>2700.0</td>\n", "      <td>1488.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 25 columns</p>\n", "</div>"], "text/plain": ["        user_id  merchant_id label origin  age_range  gender    u1    u2  u3  \\\n", "522336   228479         3111   nan   test          6       0  2004  1173  71   \n", "522337    97919         2341   nan   test          8       1    55    29  14   \n", "522338    97919         3971   nan   test          8       1    55    29  14   \n", "522339    32639         3536   nan   test          0       0    72    46  24   \n", "522340    32639         3319   nan   test          0       0    72    46  24   \n", "\n", "         u4  ...    u10     m1    m2   m3   m4  m5       m6    m7      m8  \\\n", "522336  278  ...  208.0  10105  4154  542   50  18   8997.0   9.0   687.0   \n", "522337   17  ...    1.0   5543  1592  352   93  19   4548.0   6.0   815.0   \n", "522338   17  ...    1.0  28892  7587  272    7   2  24602.0  94.0  2608.0   \n", "522339   33  ...    1.0  14027  4956  322   19   3  12807.0  29.0   793.0   \n", "522340   33  ...    1.0  25959  7927  952  175  85  21737.0  34.0  2700.0   \n", "\n", "            m9  \n", "522336   412.0  \n", "522337   174.0  \n", "522338  1588.0  \n", "522339   398.0  \n", "522340  1488.0  \n", "\n", "[5 rows x 25 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(all_data.tail())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 用户和店铺联合特征工程(4min)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 104}, "colab_type": "code", "id": "n9FRm4FyHAvI", "outputId": "800ce225-0731-4d10-8c55-4b7cac22a4d8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 4min 22s\n"]}, {"data": {"text/plain": ["9096"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "##### 用户+商户特征\n", "groups = user_log.groupby(['user_id', 'merchant_id'])\n", "\n", "# 用户在不同商家交互统计\n", "temp = groups.size().reset_index().rename(columns={0:'um1'})\n", "all_data = all_data.merge(temp, on=['user_id', 'merchant_id'], how='left')\n", "\n", "# 统计用户在不同商家交互的 item_id, cat_id, brand_id 唯一值\n", "temp = groups['item_id', 'cat_id', 'brand_id'].nunique().reset_index().rename(\n", "    columns={\n", "    'item_id':'um2',\n", "    'cat_id':'um3',\n", "    'brand_id':'um4'})\n", "all_data = all_data.merge(temp, on=['user_id', 'merchant_id'], how='left')\n", "\n", "# 统计用户在不同商家交互的 action_type 唯一值\n", "temp = groups['action_type'].value_counts().unstack().reset_index().rename(\n", "    columns={\n", "    0:'um5',\n", "    1:'um6',\n", "    2:'um7',\n", "    3:'um8'})\n", "all_data = all_data.merge(temp, on=['user_id', 'merchant_id'], how='left')\n", "\n", "# 统计用户在不同商家购物时间间隔特征 um9 按照小时\n", "temp = groups['time_stamp'].agg([('F_time', 'min'), ('B_time', 'max')]).reset_index()\n", "temp['um9'] = (temp['B_time'] - temp['F_time']).dt.seconds/3600\n", "all_data = all_data.merge(temp[['user_id','merchant_id','um9']], on=['user_id', 'merchant_id'], how='left')\n", "\n", "del temp,groups\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>label</th>\n", "      <th>origin</th>\n", "      <th>age_range</th>\n", "      <th>gender</th>\n", "      <th>u1</th>\n", "      <th>u2</th>\n", "      <th>u3</th>\n", "      <th>u4</th>\n", "      <th>...</th>\n", "      <th>m9</th>\n", "      <th>um1</th>\n", "      <th>um2</th>\n", "      <th>um3</th>\n", "      <th>um4</th>\n", "      <th>um5</th>\n", "      <th>um6</th>\n", "      <th>um7</th>\n", "      <th>um8</th>\n", "      <th>um9</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>34176</td>\n", "      <td>3906</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>...</td>\n", "      <td>961.0</td>\n", "      <td>39</td>\n", "      <td>20</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>36.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.850000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>34176</td>\n", "      <td>121</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>...</td>\n", "      <td>2699.0</td>\n", "      <td>14</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>13.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0.050000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>34176</td>\n", "      <td>4356</td>\n", "      <td>1.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>...</td>\n", "      <td>196.0</td>\n", "      <td>18</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>12.0</td>\n", "      <td>NaN</td>\n", "      <td>6.0</td>\n", "      <td>NaN</td>\n", "      <td>0.016667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>34176</td>\n", "      <td>2217</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>...</td>\n", "      <td>4150.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>230784</td>\n", "      <td>4818</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>54</td>\n", "      <td>31</td>\n", "      <td>17</td>\n", "      <td>20</td>\n", "      <td>...</td>\n", "      <td>1959.0</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>7.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0.050000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 34 columns</p>\n", "</div>"], "text/plain": ["   user_id  merchant_id label origin  age_range  gender   u1   u2  u3   u4  \\\n", "0    34176         3906   0.0  train          6       0  451  256  45  109   \n", "1    34176          121   0.0  train          6       0  451  256  45  109   \n", "2    34176         4356   1.0  train          6       0  451  256  45  109   \n", "3    34176         2217   0.0  train          6       0  451  256  45  109   \n", "4   230784         4818   0.0  train          0       0   54   31  17   20   \n", "\n", "   ...      m9  um1  um2  um3  um4   um5  um6  um7  um8       um9  \n", "0  ...   961.0   39   20    6    1  36.0  NaN  1.0  2.0  0.850000  \n", "1  ...  2699.0   14    1    1    1  13.0  NaN  1.0  NaN  0.050000  \n", "2  ...   196.0   18    2    1    1  12.0  NaN  6.0  NaN  0.016667  \n", "3  ...  4150.0    2    1    1    1   1.0  NaN  1.0  NaN  0.000000  \n", "4  ...  1959.0    8    1    1    1   7.0  NaN  1.0  NaN  0.050000  \n", "\n", "[5 rows x 34 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(all_data.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 购买点击比"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"colab": {}, "colab_type": "code", "collapsed": true, "id": "bq0Ss1SvHAvK"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>label</th>\n", "      <th>origin</th>\n", "      <th>age_range</th>\n", "      <th>gender</th>\n", "      <th>u1</th>\n", "      <th>u2</th>\n", "      <th>u3</th>\n", "      <th>u4</th>\n", "      <th>...</th>\n", "      <th>um3</th>\n", "      <th>um4</th>\n", "      <th>um5</th>\n", "      <th>um6</th>\n", "      <th>um7</th>\n", "      <th>um8</th>\n", "      <th>um9</th>\n", "      <th>r1</th>\n", "      <th>r2</th>\n", "      <th>r3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>34176</td>\n", "      <td>3906</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>...</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>36.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>0.850000</td>\n", "      <td>0.082927</td>\n", "      <td>0.027572</td>\n", "      <td>0.027778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>34176</td>\n", "      <td>121</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>13.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0.050000</td>\n", "      <td>0.082927</td>\n", "      <td>0.066145</td>\n", "      <td>0.076923</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>34176</td>\n", "      <td>4356</td>\n", "      <td>1.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>12.0</td>\n", "      <td>NaN</td>\n", "      <td>6.0</td>\n", "      <td>NaN</td>\n", "      <td>0.016667</td>\n", "      <td>0.082927</td>\n", "      <td>0.158024</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>34176</td>\n", "      <td>2217</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.082927</td>\n", "      <td>0.071243</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>230784</td>\n", "      <td>4818</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>54</td>\n", "      <td>31</td>\n", "      <td>17</td>\n", "      <td>20</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>7.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>0.050000</td>\n", "      <td>0.148936</td>\n", "      <td>0.063164</td>\n", "      <td>0.142857</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 37 columns</p>\n", "</div>"], "text/plain": ["   user_id  merchant_id label origin  age_range  gender   u1   u2  u3   u4  \\\n", "0    34176         3906   0.0  train          6       0  451  256  45  109   \n", "1    34176          121   0.0  train          6       0  451  256  45  109   \n", "2    34176         4356   1.0  train          6       0  451  256  45  109   \n", "3    34176         2217   0.0  train          6       0  451  256  45  109   \n", "4   230784         4818   0.0  train          0       0   54   31  17   20   \n", "\n", "   ...  um3  um4   um5  um6  um7  um8       um9        r1        r2        r3  \n", "0  ...    6    1  36.0  NaN  1.0  2.0  0.850000  0.082927  0.027572  0.027778  \n", "1  ...    1    1  13.0  NaN  1.0  NaN  0.050000  0.082927  0.066145  0.076923  \n", "2  ...    1    1  12.0  NaN  6.0  NaN  0.016667  0.082927  0.158024  0.500000  \n", "3  ...    1    1   1.0  NaN  1.0  NaN  0.000000  0.082927  0.071243  1.000000  \n", "4  ...    1    1   7.0  NaN  1.0  NaN  0.050000  0.148936  0.063164  0.142857  \n", "\n", "[5 rows x 37 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["all_data['r1'] = all_data['u9']/all_data['u7'] # 用户购买点击比\n", "all_data['r2'] = all_data['m8']/all_data['m6'] # 商家购买点击比\n", "all_data['r3'] = all_data['um7']/all_data['um5'] #不同用户不同商家购买点击比\n", "display(all_data.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 空数据填充"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["user_id             0\n", "merchant_id         0\n", "label               0\n", "origin              0\n", "age_range           0\n", "gender              0\n", "u1                  0\n", "u2                  0\n", "u3                  0\n", "u4                  0\n", "u5                  0\n", "u6                  0\n", "u7                360\n", "u8             484162\n", "u9                  0\n", "u10            227482\n", "m1                  0\n", "m2                  0\n", "m3                  0\n", "m4                  0\n", "m5                  0\n", "m6                  0\n", "m7               4052\n", "m8                  0\n", "m9                  0\n", "um1                 0\n", "um2                 0\n", "um3                 0\n", "um4                 0\n", "um5             59408\n", "um6            512947\n", "um7                 0\n", "um8            425790\n", "um9                 0\n", "r1                360\n", "r2                  0\n", "r3              59408\n", "dtype: int64"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(all_data.isnull().sum())"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {}, "colab_type": "code", "id": "r1RSi-7_HAvN"}, "outputs": [], "source": ["all_data.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["user_id        0\n", "merchant_id    0\n", "label          0\n", "origin         0\n", "age_range      0\n", "gender         0\n", "u1             0\n", "u2             0\n", "u3             0\n", "u4             0\n", "u5             0\n", "u6             0\n", "u7             0\n", "u8             0\n", "u9             0\n", "u10            0\n", "m1             0\n", "m2             0\n", "m3             0\n", "m4             0\n", "m5             0\n", "m6             0\n", "m7             0\n", "m8             0\n", "m9             0\n", "um1            0\n", "um2            0\n", "um3            0\n", "um4            0\n", "um5            0\n", "um6            0\n", "um7            0\n", "um8            0\n", "um9            0\n", "r1             0\n", "r2             0\n", "r3             0\n", "dtype: int64"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["all_data.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 年龄性别类别型转换"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["0         6\n", "1         6\n", "2         6\n", "3         6\n", "4         0\n", "         ..\n", "522336    6\n", "522337    8\n", "522338    8\n", "522339    0\n", "522340    0\n", "Name: age_range, Length: 522341, dtype: int8"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["all_data['age_range']"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age_0</th>\n", "      <th>age_1</th>\n", "      <th>age_2</th>\n", "      <th>age_3</th>\n", "      <th>age_4</th>\n", "      <th>age_5</th>\n", "      <th>age_6</th>\n", "      <th>age_7</th>\n", "      <th>age_8</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age_0  age_1  age_2  age_3  age_4  age_5  age_6  age_7  age_8\n", "0      0      0      0      0      0      0      1      0      0\n", "1      0      0      0      0      0      0      1      0      0\n", "2      0      0      0      0      0      0      1      0      0\n", "3      0      0      0      0      0      0      1      0      0\n", "4      1      0      0      0      0      0      0      0      0\n", "5      0      0      0      0      1      0      0      0      0\n", "6      0      0      0      0      0      1      0      0      0\n", "7      0      0      0      0      0      1      0      0      0\n", "8      0      0      0      0      0      1      0      0      0\n", "9      0      0      0      0      1      0      0      0      0"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Wall time: 125 ms\n"]}], "source": ["%%time\n", "# 修改age_range字段名称为 age_0, age_1, age_2... age_8\n", "# 独立编码\n", "temp = pd.get_dummies(all_data['age_range'], prefix='age')\n", "display(temp.head(10))\n", "all_data = pd.concat([all_data, temp], axis=1)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 50}, "colab_type": "code", "id": "fFr7DhmAHAvP", "outputId": "3f308416-f212-481f-e9c3-41fbe8a364a8"}, "outputs": [{"data": {"text/plain": ["18438"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# 性别转换\n", "temp = pd.get_dummies(all_data['gender'], prefix='g')\n", "all_data = pd.concat([all_data, temp], axis=1) # 列进行合并\n", "\n", "# 删除原数据\n", "all_data.drop(['age_range', 'gender'], axis=1, inplace=True)\n", "\n", "del temp\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>label</th>\n", "      <th>origin</th>\n", "      <th>u1</th>\n", "      <th>u2</th>\n", "      <th>u3</th>\n", "      <th>u4</th>\n", "      <th>u5</th>\n", "      <th>u6</th>\n", "      <th>...</th>\n", "      <th>age_2</th>\n", "      <th>age_3</th>\n", "      <th>age_4</th>\n", "      <th>age_5</th>\n", "      <th>age_6</th>\n", "      <th>age_7</th>\n", "      <th>age_8</th>\n", "      <th>g_0</th>\n", "      <th>g_1</th>\n", "      <th>g_2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>34176</td>\n", "      <td>3906</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>108</td>\n", "      <td>5.833333</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>34176</td>\n", "      <td>121</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>108</td>\n", "      <td>5.833333</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>34176</td>\n", "      <td>4356</td>\n", "      <td>1.0</td>\n", "      <td>train</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>108</td>\n", "      <td>5.833333</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>34176</td>\n", "      <td>2217</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>451</td>\n", "      <td>256</td>\n", "      <td>45</td>\n", "      <td>109</td>\n", "      <td>108</td>\n", "      <td>5.833333</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>230784</td>\n", "      <td>4818</td>\n", "      <td>0.0</td>\n", "      <td>train</td>\n", "      <td>54</td>\n", "      <td>31</td>\n", "      <td>17</td>\n", "      <td>20</td>\n", "      <td>19</td>\n", "      <td>5.166667</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 47 columns</p>\n", "</div>"], "text/plain": ["   user_id  merchant_id label origin   u1   u2  u3   u4   u5        u6  ...  \\\n", "0    34176         3906   0.0  train  451  256  45  109  108  5.833333  ...   \n", "1    34176          121   0.0  train  451  256  45  109  108  5.833333  ...   \n", "2    34176         4356   1.0  train  451  256  45  109  108  5.833333  ...   \n", "3    34176         2217   0.0  train  451  256  45  109  108  5.833333  ...   \n", "4   230784         4818   0.0  train   54   31  17   20   19  5.166667  ...   \n", "\n", "   age_2  age_3  age_4  age_5  age_6  age_7  age_8  g_0  g_1  g_2  \n", "0      0      0      0      0      1      0      0    1    0    0  \n", "1      0      0      0      0      1      0      0    1    0    0  \n", "2      0      0      0      0      1      0      0    1    0    0  \n", "3      0      0      0      0      1      0      0    1    0    0  \n", "4      0      0      0      0      0      0      0    1    0    0  \n", "\n", "[5 rows x 47 columns]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["all_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 数据存储"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 50}, "colab_type": "code", "id": "-1cqxyEpHAvS", "outputId": "c523e9a4-acf4-426e-9387-84f816b03e2a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 9.14 s\n"]}], "source": ["%%time\n", "# train_data、test-data\n", "train_data = all_data[all_data['origin'] == 'train'].drop(['origin'], axis=1)\n", "test_data = all_data[all_data['origin'] == 'test'].drop(['label', 'origin'], axis=1)\n", "\n", "train_data.to_csv('train_data.csv')\n", "test_data.to_csv('test_data.csv')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "lWOUo9DVPiGa"}, "source": ["## 算法建模预测"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"colab": {}, "colab_type": "code", "id": "50VkvAJcHAvV"}, "outputs": [], "source": ["# 训练数据和目标值\n", "train_X, train_y = train_data.drop(['label'], axis=1), train_data['label']\n", "\n", "# 数据拆分保留20%作为测试数据\n", "X_train, X_valid, y_train, y_valid = train_test_split(train_X, train_y, test_size=.2)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "WmivfIenrLMR"}, "source": ["### LGB 模型"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"colab": {}, "colab_type": "code", "id": "kNz-dghnpJPR"}, "outputs": [], "source": ["def lgb_train(X_train, y_train, X_valid, y_valid, verbose=True):\n", "    model_lgb = lgb.LGBMClassifier(\n", "        max_depth=10, # 8 # 树最大的深度\n", "        n_estimators=5000, # 集成算法，树数量\n", "        min_child_weight=100, \n", "        colsample_bytree=0.7, # 特征筛选\n", "        subsample=0.9,  # 样本采样比例\n", "        learning_rate=0.1) # 学习率\n", "\n", "    model_lgb.fit(\n", "        X_train, \n", "        y_train,\n", "        eval_metric='auc',\n", "        eval_set=[(X_train, y_train), (X_valid, y_valid)],\n", "        verbose=verbose, # 是否打印输出训练过程\n", "        early_stopping_rounds=10) # 早停，等10轮决策，评价指标不在变化，停止\n", "\n", "    print(model_lgb.best_score_['valid_1']['auc'])\n", "    return model_lgb"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>u1</th>\n", "      <th>u2</th>\n", "      <th>u3</th>\n", "      <th>u4</th>\n", "      <th>u5</th>\n", "      <th>u6</th>\n", "      <th>u7</th>\n", "      <th>u8</th>\n", "      <th>...</th>\n", "      <th>age_2</th>\n", "      <th>age_3</th>\n", "      <th>age_4</th>\n", "      <th>age_5</th>\n", "      <th>age_6</th>\n", "      <th>age_7</th>\n", "      <th>age_8</th>\n", "      <th>g_0</th>\n", "      <th>g_1</th>\n", "      <th>g_2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>132344</th>\n", "      <td>210694</td>\n", "      <td>2928</td>\n", "      <td>11</td>\n", "      <td>9</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>3.150000</td>\n", "      <td>10.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19029</th>\n", "      <td>187320</td>\n", "      <td>3484</td>\n", "      <td>57</td>\n", "      <td>34</td>\n", "      <td>17</td>\n", "      <td>21</td>\n", "      <td>22</td>\n", "      <td>4.883333</td>\n", "      <td>46.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79580</th>\n", "      <td>345450</td>\n", "      <td>3350</td>\n", "      <td>67</td>\n", "      <td>46</td>\n", "      <td>9</td>\n", "      <td>17</td>\n", "      <td>17</td>\n", "      <td>5.916667</td>\n", "      <td>58.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99577</th>\n", "      <td>69541</td>\n", "      <td>3629</td>\n", "      <td>47</td>\n", "      <td>30</td>\n", "      <td>16</td>\n", "      <td>21</td>\n", "      <td>24</td>\n", "      <td>5.816667</td>\n", "      <td>39.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67041</th>\n", "      <td>214341</td>\n", "      <td>2856</td>\n", "      <td>12</td>\n", "      <td>7</td>\n", "      <td>4</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "      <td>5.100000</td>\n", "      <td>7.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>126273</th>\n", "      <td>78580</td>\n", "      <td>2895</td>\n", "      <td>424</td>\n", "      <td>224</td>\n", "      <td>47</td>\n", "      <td>82</td>\n", "      <td>90</td>\n", "      <td>4.100000</td>\n", "      <td>344.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4207</th>\n", "      <td>263820</td>\n", "      <td>1910</td>\n", "      <td>21</td>\n", "      <td>16</td>\n", "      <td>8</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>4.983333</td>\n", "      <td>19.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143047</th>\n", "      <td>230182</td>\n", "      <td>3123</td>\n", "      <td>29</td>\n", "      <td>18</td>\n", "      <td>15</td>\n", "      <td>13</td>\n", "      <td>13</td>\n", "      <td>5.983333</td>\n", "      <td>14.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>156183</th>\n", "      <td>86092</td>\n", "      <td>4044</td>\n", "      <td>165</td>\n", "      <td>106</td>\n", "      <td>38</td>\n", "      <td>65</td>\n", "      <td>68</td>\n", "      <td>5.150000</td>\n", "      <td>150.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>159771</th>\n", "      <td>106327</td>\n", "      <td>1499</td>\n", "      <td>230</td>\n", "      <td>128</td>\n", "      <td>44</td>\n", "      <td>63</td>\n", "      <td>65</td>\n", "      <td>5.983333</td>\n", "      <td>203.0</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>208691 rows × 45 columns</p>\n", "</div>"], "text/plain": ["        user_id  merchant_id   u1   u2  u3  u4  u5        u6     u7   u8  ...  \\\n", "132344   210694         2928   11    9   6   5   5  3.150000   10.0  0.0  ...   \n", "19029    187320         3484   57   34  17  21  22  4.883333   46.0  0.0  ...   \n", "79580    345450         3350   67   46   9  17  17  5.916667   58.0  0.0  ...   \n", "99577     69541         3629   47   30  16  21  24  5.816667   39.0  0.0  ...   \n", "67041    214341         2856   12    7   4   7   7  5.100000    7.0  0.0  ...   \n", "...         ...          ...  ...  ...  ..  ..  ..       ...    ...  ...  ...   \n", "126273    78580         2895  424  224  47  82  90  4.100000  344.0  0.0  ...   \n", "4207     263820         1910   21   16   8   6   6  4.983333   19.0  0.0  ...   \n", "143047   230182         3123   29   18  15  13  13  5.983333   14.0  0.0  ...   \n", "156183    86092         4044  165  106  38  65  68  5.150000  150.0  0.0  ...   \n", "159771   106327         1499  230  128  44  63  65  5.983333  203.0  2.0  ...   \n", "\n", "        age_2  age_3  age_4  age_5  age_6  age_7  age_8  g_0  g_1  g_2  \n", "132344      0      0      1      0      0      0      0    1    0    0  \n", "19029       0      0      0      0      0      0      0    0    1    0  \n", "79580       0      0      0      0      0      0      0    1    0    0  \n", "99577       0      0      0      0      0      0      0    0    1    0  \n", "67041       0      1      0      0      0      0      0    1    0    0  \n", "...       ...    ...    ...    ...    ...    ...    ...  ...  ...  ...  \n", "126273      0      1      0      0      0      0      0    1    0    0  \n", "4207        0      0      0      0      0      0      0    0    1    0  \n", "143047      0      1      0      0      0      0      0    1    0    0  \n", "156183      0      0      0      0      0      0      0    1    0    0  \n", "159771      0      1      0      0      0      0      0    1    0    0  \n", "\n", "[208691 rows x 45 columns]"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["array([[2.10694e+05, 2.92800e+03, 1.10000e+01, ..., 1.00000e+00,\n", "        0.00000e+00, 0.00000e+00],\n", "       [1.87320e+05, 3.48400e+03, 5.70000e+01, ..., 0.00000e+00,\n", "        1.00000e+00, 0.00000e+00],\n", "       [3.45450e+05, 3.35000e+03, 6.70000e+01, ..., 1.00000e+00,\n", "        0.00000e+00, 0.00000e+00],\n", "       ...,\n", "       [2.30182e+05, 3.12300e+03, 2.90000e+01, ..., 1.00000e+00,\n", "        0.00000e+00, 0.00000e+00],\n", "       [8.60920e+04, 4.04400e+03, 1.65000e+02, ..., 1.00000e+00,\n", "        0.00000e+00, 0.00000e+00],\n", "       [1.06327e+05, 1.49900e+03, 2.30000e+02, ..., 1.00000e+00,\n", "        0.00000e+00, 0.00000e+00]])"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["X_train.values"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 34}, "colab_type": "code", "id": "2UIZFG6wrCob", "outputId": "09e47aa8-08d5-4ab7-d108-b8445868a0fb", "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1]\ttraining's auc: 0.640009\ttraining's binary_logloss: 0.228212\tvalid_1's auc: 0.627955\tvalid_1's binary_logloss: 0.229246\n", "Training until validation scores don't improve for 10 rounds.\n", "[2]\ttraining's auc: 0.648943\ttraining's binary_logloss: 0.226955\tvalid_1's auc: 0.636741\tvalid_1's binary_logloss: 0.228055\n", "[3]\ttraining's auc: 0.654258\ttraining's binary_logloss: 0.225831\tvalid_1's auc: 0.639618\tvalid_1's binary_logloss: 0.227076\n", "[4]\ttraining's auc: 0.656223\ttraining's binary_logloss: 0.22493\tvalid_1's auc: 0.641308\tvalid_1's binary_logloss: 0.226275\n", "[5]\ttraining's auc: 0.657013\ttraining's binary_logloss: 0.224163\tvalid_1's auc: 0.642213\tvalid_1's binary_logloss: 0.225582\n", "[6]\ttraining's auc: 0.659946\ttraining's binary_logloss: 0.223479\tvalid_1's auc: 0.644374\tvalid_1's binary_logloss: 0.224994\n", "[7]\ttraining's auc: 0.661228\ttraining's binary_logloss: 0.222846\tvalid_1's auc: 0.644595\tvalid_1's binary_logloss: 0.224503\n", "[8]\ttraining's auc: 0.663428\ttraining's binary_logloss: 0.222264\tvalid_1's auc: 0.647436\tvalid_1's binary_logloss: 0.224012\n", "[9]\ttraining's auc: 0.665838\ttraining's binary_logloss: 0.221775\tvalid_1's auc: 0.649127\tvalid_1's binary_logloss: 0.223642\n", "[10]\ttraining's auc: 0.666995\ttraining's binary_logloss: 0.221347\tvalid_1's auc: 0.649872\tvalid_1's binary_logloss: 0.22332\n", "[11]\ttraining's auc: 0.668616\ttraining's binary_logloss: 0.220976\tvalid_1's auc: 0.650452\tvalid_1's binary_logloss: 0.223052\n", "[12]\ttraining's auc: 0.669547\ttraining's binary_logloss: 0.220616\tvalid_1's auc: 0.651267\tvalid_1's binary_logloss: 0.222777\n", "[13]\ttraining's auc: 0.670579\ttraining's binary_logloss: 0.220266\tvalid_1's auc: 0.650955\tvalid_1's binary_logloss: 0.222609\n", "[14]\ttraining's auc: 0.671493\ttraining's binary_logloss: 0.219968\tvalid_1's auc: 0.65102\tvalid_1's binary_logloss: 0.222445\n", "[15]\ttraining's auc: 0.672236\ttraining's binary_logloss: 0.219698\tvalid_1's auc: 0.651428\tvalid_1's binary_logloss: 0.222272\n", "[16]\ttraining's auc: 0.673087\ttraining's binary_logloss: 0.21945\tvalid_1's auc: 0.652159\tvalid_1's binary_logloss: 0.222096\n", "[17]\ttraining's auc: 0.674171\ttraining's binary_logloss: 0.219203\tvalid_1's auc: 0.65273\tvalid_1's binary_logloss: 0.221966\n", "[18]\ttraining's auc: 0.675745\ttraining's binary_logloss: 0.21888\tvalid_1's auc: 0.654083\tvalid_1's binary_logloss: 0.221723\n", "[19]\ttraining's auc: 0.676764\ttraining's binary_logloss: 0.218636\tvalid_1's auc: 0.655219\tvalid_1's binary_logloss: 0.221552\n", "[20]\ttraining's auc: 0.677629\ttraining's binary_logloss: 0.218454\tvalid_1's auc: 0.655209\tvalid_1's binary_logloss: 0.221491\n", "[21]\ttraining's auc: 0.679045\ttraining's binary_logloss: 0.218202\tvalid_1's auc: 0.656024\tvalid_1's binary_logloss: 0.221329\n", "[22]\ttraining's auc: 0.680286\ttraining's binary_logloss: 0.217981\tvalid_1's auc: 0.656549\tvalid_1's binary_logloss: 0.221209\n", "[23]\ttraining's auc: 0.681357\ttraining's binary_logloss: 0.217781\tvalid_1's auc: 0.657502\tvalid_1's binary_logloss: 0.221096\n", "[24]\ttraining's auc: 0.682264\ttraining's binary_logloss: 0.217597\tvalid_1's auc: 0.657392\tvalid_1's binary_logloss: 0.221047\n", "[25]\ttraining's auc: 0.684249\ttraining's binary_logloss: 0.217341\tvalid_1's auc: 0.658879\tvalid_1's binary_logloss: 0.220886\n", "[26]\ttraining's auc: 0.685028\ttraining's binary_logloss: 0.217194\tvalid_1's auc: 0.659082\tvalid_1's binary_logloss: 0.220833\n", "[27]\ttraining's auc: 0.686134\ttraining's binary_logloss: 0.216998\tvalid_1's auc: 0.659833\tvalid_1's binary_logloss: 0.220725\n", "[28]\ttraining's auc: 0.687514\ttraining's binary_logloss: 0.216796\tvalid_1's auc: 0.660909\tvalid_1's binary_logloss: 0.220592\n", "[29]\ttraining's auc: 0.688318\ttraining's binary_logloss: 0.216652\tvalid_1's auc: 0.66104\tvalid_1's binary_logloss: 0.220561\n", "[30]\ttraining's auc: 0.689628\ttraining's binary_logloss: 0.216438\tvalid_1's auc: 0.662094\tvalid_1's binary_logloss: 0.220424\n", "[31]\ttraining's auc: 0.690365\ttraining's binary_logloss: 0.216288\tvalid_1's auc: 0.662629\tvalid_1's binary_logloss: 0.220336\n", "[32]\ttraining's auc: 0.690825\ttraining's binary_logloss: 0.216191\tvalid_1's auc: 0.663061\tvalid_1's binary_logloss: 0.22029\n", "[33]\ttraining's auc: 0.691929\ttraining's binary_logloss: 0.215995\tvalid_1's auc: 0.663483\tvalid_1's binary_logloss: 0.220213\n", "[34]\ttraining's auc: 0.692572\ttraining's binary_logloss: 0.215865\tvalid_1's auc: 0.663522\tvalid_1's binary_logloss: 0.220184\n", "[35]\ttraining's auc: 0.693491\ttraining's binary_logloss: 0.215735\tvalid_1's auc: 0.663764\tvalid_1's binary_logloss: 0.220143\n", "[36]\ttraining's auc: 0.693826\ttraining's binary_logloss: 0.215662\tvalid_1's auc: 0.664167\tvalid_1's binary_logloss: 0.220083\n", "[37]\ttraining's auc: 0.69481\ttraining's binary_logloss: 0.215522\tvalid_1's auc: 0.664551\tvalid_1's binary_logloss: 0.220022\n", "[38]\ttraining's auc: 0.695568\ttraining's binary_logloss: 0.215406\tvalid_1's auc: 0.664845\tvalid_1's binary_logloss: 0.21997\n", "[39]\ttraining's auc: 0.69627\ttraining's binary_logloss: 0.215293\tvalid_1's auc: 0.664684\tvalid_1's binary_logloss: 0.219973\n", "[40]\ttraining's auc: 0.697596\ttraining's binary_logloss: 0.215094\tvalid_1's auc: 0.665466\tvalid_1's binary_logloss: 0.219856\n", "[41]\ttraining's auc: 0.698512\ttraining's binary_logloss: 0.214958\tvalid_1's auc: 0.665818\tvalid_1's binary_logloss: 0.219822\n", "[42]\ttraining's auc: 0.699389\ttraining's binary_logloss: 0.214806\tvalid_1's auc: 0.66594\tvalid_1's binary_logloss: 0.219798\n", "[43]\ttraining's auc: 0.700417\ttraining's binary_logloss: 0.21465\tvalid_1's auc: 0.666912\tvalid_1's binary_logloss: 0.219684\n", "[44]\ttraining's auc: 0.701304\ttraining's binary_logloss: 0.214515\tvalid_1's auc: 0.667371\tvalid_1's binary_logloss: 0.219616\n", "[45]\ttraining's auc: 0.701999\ttraining's binary_logloss: 0.214407\tvalid_1's auc: 0.667297\tvalid_1's binary_logloss: 0.219616\n", "[46]\ttraining's auc: 0.702397\ttraining's binary_logloss: 0.21433\tvalid_1's auc: 0.667578\tvalid_1's binary_logloss: 0.219579\n", "[47]\ttraining's auc: 0.703049\ttraining's binary_logloss: 0.214223\tvalid_1's auc: 0.667909\tvalid_1's binary_logloss: 0.219538\n", "[48]\ttraining's auc: 0.703763\ttraining's binary_logloss: 0.214098\tvalid_1's auc: 0.668234\tvalid_1's binary_logloss: 0.219481\n", "[49]\ttraining's auc: 0.70451\ttraining's binary_logloss: 0.213988\tvalid_1's auc: 0.668525\tvalid_1's binary_logloss: 0.219435\n", "[50]\ttraining's auc: 0.705208\ttraining's binary_logloss: 0.213872\tvalid_1's auc: 0.668805\tvalid_1's binary_logloss: 0.219399\n", "[51]\ttraining's auc: 0.705784\ttraining's binary_logloss: 0.213779\tvalid_1's auc: 0.669152\tvalid_1's binary_logloss: 0.219352\n", "[52]\ttraining's auc: 0.706611\ttraining's binary_logloss: 0.213656\tvalid_1's auc: 0.669293\tvalid_1's binary_logloss: 0.219345\n", "[53]\ttraining's auc: 0.707174\ttraining's binary_logloss: 0.213543\tvalid_1's auc: 0.669621\tvalid_1's binary_logloss: 0.219284\n", "[54]\ttraining's auc: 0.708152\ttraining's binary_logloss: 0.213403\tvalid_1's auc: 0.669801\tvalid_1's binary_logloss: 0.219254\n", "[55]\ttraining's auc: 0.70878\ttraining's binary_logloss: 0.213317\tvalid_1's auc: 0.670107\tvalid_1's binary_logloss: 0.219208\n", "[56]\ttraining's auc: 0.709259\ttraining's binary_logloss: 0.213236\tvalid_1's auc: 0.670336\tvalid_1's binary_logloss: 0.219184\n", "[57]\ttraining's auc: 0.709642\ttraining's binary_logloss: 0.213167\tvalid_1's auc: 0.670389\tvalid_1's binary_logloss: 0.219168\n", "[58]\ttraining's auc: 0.710617\ttraining's binary_logloss: 0.213007\tvalid_1's auc: 0.670692\tvalid_1's binary_logloss: 0.21911\n", "[59]\ttraining's auc: 0.711202\ttraining's binary_logloss: 0.212914\tvalid_1's auc: 0.6709\tvalid_1's binary_logloss: 0.21908\n", "[60]\ttraining's auc: 0.711672\ttraining's binary_logloss: 0.212832\tvalid_1's auc: 0.670922\tvalid_1's binary_logloss: 0.219069\n", "[61]\ttraining's auc: 0.712456\ttraining's binary_logloss: 0.212707\tvalid_1's auc: 0.67147\tvalid_1's binary_logloss: 0.218996\n", "[62]\ttraining's auc: 0.713186\ttraining's binary_logloss: 0.212587\tvalid_1's auc: 0.671937\tvalid_1's binary_logloss: 0.218944\n", "[63]\ttraining's auc: 0.713357\ttraining's binary_logloss: 0.212547\tvalid_1's auc: 0.672002\tvalid_1's binary_logloss: 0.218932\n", "[64]\ttraining's auc: 0.714047\ttraining's binary_logloss: 0.21245\tvalid_1's auc: 0.671905\tvalid_1's binary_logloss: 0.218946\n", "[65]\ttraining's auc: 0.714342\ttraining's binary_logloss: 0.212363\tvalid_1's auc: 0.672203\tvalid_1's binary_logloss: 0.218901\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[66]\ttraining's auc: 0.715136\ttraining's binary_logloss: 0.212228\tvalid_1's auc: 0.673082\tvalid_1's binary_logloss: 0.218789\n", "[67]\ttraining's auc: 0.715597\ttraining's binary_logloss: 0.212156\tvalid_1's auc: 0.673082\tvalid_1's binary_logloss: 0.218783\n", "[68]\ttraining's auc: 0.716101\ttraining's binary_logloss: 0.212053\tvalid_1's auc: 0.67344\tvalid_1's binary_logloss: 0.218737\n", "[69]\ttraining's auc: 0.716458\ttraining's binary_logloss: 0.211986\tvalid_1's auc: 0.673405\tvalid_1's binary_logloss: 0.218748\n", "[70]\ttraining's auc: 0.716961\ttraining's binary_logloss: 0.211888\tvalid_1's auc: 0.673533\tvalid_1's binary_logloss: 0.218733\n", "[71]\ttraining's auc: 0.717296\ttraining's binary_logloss: 0.211821\tvalid_1's auc: 0.673543\tvalid_1's binary_logloss: 0.218736\n", "[72]\ttraining's auc: 0.717596\ttraining's binary_logloss: 0.21176\tvalid_1's auc: 0.673506\tvalid_1's binary_logloss: 0.218735\n", "[73]\ttraining's auc: 0.718504\ttraining's binary_logloss: 0.211627\tvalid_1's auc: 0.673942\tvalid_1's binary_logloss: 0.218683\n", "[74]\ttraining's auc: 0.719108\ttraining's binary_logloss: 0.211535\tvalid_1's auc: 0.673671\tvalid_1's binary_logloss: 0.218715\n", "[75]\ttraining's auc: 0.719605\ttraining's binary_logloss: 0.21145\tvalid_1's auc: 0.67365\tvalid_1's binary_logloss: 0.218713\n", "[76]\ttraining's auc: 0.720196\ttraining's binary_logloss: 0.211357\tvalid_1's auc: 0.673731\tvalid_1's binary_logloss: 0.218711\n", "[77]\ttraining's auc: 0.720678\ttraining's binary_logloss: 0.211279\tvalid_1's auc: 0.673502\tvalid_1's binary_logloss: 0.218739\n", "[78]\ttraining's auc: 0.721217\ttraining's binary_logloss: 0.211189\tvalid_1's auc: 0.67352\tvalid_1's binary_logloss: 0.218737\n", "[79]\ttraining's auc: 0.72168\ttraining's binary_logloss: 0.211102\tvalid_1's auc: 0.673732\tvalid_1's binary_logloss: 0.218704\n", "[80]\ttraining's auc: 0.72211\ttraining's binary_logloss: 0.21102\tvalid_1's auc: 0.673899\tvalid_1's binary_logloss: 0.218685\n", "[81]\ttraining's auc: 0.722652\ttraining's binary_logloss: 0.210921\tvalid_1's auc: 0.674107\tvalid_1's binary_logloss: 0.218646\n", "[82]\ttraining's auc: 0.723073\ttraining's binary_logloss: 0.210841\tvalid_1's auc: 0.674044\tvalid_1's binary_logloss: 0.218652\n", "[83]\ttraining's auc: 0.723426\ttraining's binary_logloss: 0.21078\tvalid_1's auc: 0.674352\tvalid_1's binary_logloss: 0.218628\n", "[84]\ttraining's auc: 0.723881\ttraining's binary_logloss: 0.210701\tvalid_1's auc: 0.674625\tvalid_1's binary_logloss: 0.218606\n", "[85]\ttraining's auc: 0.724019\ttraining's binary_logloss: 0.21067\tvalid_1's auc: 0.674613\tvalid_1's binary_logloss: 0.218602\n", "[86]\ttraining's auc: 0.72435\ttraining's binary_logloss: 0.210604\tvalid_1's auc: 0.674498\tvalid_1's binary_logloss: 0.21861\n", "[87]\ttraining's auc: 0.724781\ttraining's binary_logloss: 0.210524\tvalid_1's auc: 0.674639\tvalid_1's binary_logloss: 0.218602\n", "[88]\ttraining's auc: 0.725353\ttraining's binary_logloss: 0.210443\tvalid_1's auc: 0.674719\tvalid_1's binary_logloss: 0.2186\n", "[89]\ttraining's auc: 0.725379\ttraining's binary_logloss: 0.210426\tvalid_1's auc: 0.674753\tvalid_1's binary_logloss: 0.218589\n", "[90]\ttraining's auc: 0.7259\ttraining's binary_logloss: 0.210349\tvalid_1's auc: 0.674833\tvalid_1's binary_logloss: 0.218581\n", "[91]\ttraining's auc: 0.726289\ttraining's binary_logloss: 0.210294\tvalid_1's auc: 0.67496\tvalid_1's binary_logloss: 0.218575\n", "[92]\ttraining's auc: 0.726779\ttraining's binary_logloss: 0.210219\tvalid_1's auc: 0.67511\tvalid_1's binary_logloss: 0.218557\n", "[93]\ttraining's auc: 0.727125\ttraining's binary_logloss: 0.210167\tvalid_1's auc: 0.675283\tvalid_1's binary_logloss: 0.218542\n", "[94]\ttraining's auc: 0.727719\ttraining's binary_logloss: 0.21006\tvalid_1's auc: 0.675772\tvalid_1's binary_logloss: 0.218488\n", "[95]\ttraining's auc: 0.728231\ttraining's binary_logloss: 0.209967\tvalid_1's auc: 0.676223\tvalid_1's binary_logloss: 0.218437\n", "[96]\ttraining's auc: 0.728485\ttraining's binary_logloss: 0.209917\tvalid_1's auc: 0.676342\tvalid_1's binary_logloss: 0.21842\n", "[97]\ttraining's auc: 0.728736\ttraining's binary_logloss: 0.209871\tvalid_1's auc: 0.676195\tvalid_1's binary_logloss: 0.218439\n", "[98]\ttraining's auc: 0.728967\ttraining's binary_logloss: 0.209832\tvalid_1's auc: 0.6763\tvalid_1's binary_logloss: 0.218419\n", "[99]\ttraining's auc: 0.729382\ttraining's binary_logloss: 0.209756\tvalid_1's auc: 0.676168\tvalid_1's binary_logloss: 0.218424\n", "[100]\ttraining's auc: 0.729608\ttraining's binary_logloss: 0.209714\tvalid_1's auc: 0.67635\tvalid_1's binary_logloss: 0.218401\n", "[101]\ttraining's auc: 0.730282\ttraining's binary_logloss: 0.209591\tvalid_1's auc: 0.676588\tvalid_1's binary_logloss: 0.218372\n", "[102]\ttraining's auc: 0.73078\ttraining's binary_logloss: 0.209502\tvalid_1's auc: 0.676768\tvalid_1's binary_logloss: 0.218357\n", "[103]\ttraining's auc: 0.731057\ttraining's binary_logloss: 0.209458\tvalid_1's auc: 0.67673\tvalid_1's binary_logloss: 0.218361\n", "[104]\ttraining's auc: 0.731472\ttraining's binary_logloss: 0.209388\tvalid_1's auc: 0.6767\tvalid_1's binary_logloss: 0.218354\n", "[105]\ttraining's auc: 0.731937\ttraining's binary_logloss: 0.209309\tvalid_1's auc: 0.676559\tvalid_1's binary_logloss: 0.218371\n", "[106]\ttraining's auc: 0.732157\ttraining's binary_logloss: 0.209267\tvalid_1's auc: 0.676517\tvalid_1's binary_logloss: 0.218377\n", "[107]\ttraining's auc: 0.732697\ttraining's binary_logloss: 0.209192\tvalid_1's auc: 0.676501\tvalid_1's binary_logloss: 0.218393\n", "[108]\ttraining's auc: 0.732975\ttraining's binary_logloss: 0.209138\tvalid_1's auc: 0.676487\tvalid_1's binary_logloss: 0.218389\n", "[109]\ttraining's auc: 0.733242\ttraining's binary_logloss: 0.209071\tvalid_1's auc: 0.676624\tvalid_1's binary_logloss: 0.218379\n", "[110]\ttraining's auc: 0.733616\ttraining's binary_logloss: 0.209012\tvalid_1's auc: 0.676653\tvalid_1's binary_logloss: 0.218379\n", "[111]\ttraining's auc: 0.733903\ttraining's binary_logloss: 0.208965\tvalid_1's auc: 0.676754\tvalid_1's binary_logloss: 0.218372\n", "[112]\ttraining's auc: 0.734388\ttraining's binary_logloss: 0.208888\tvalid_1's auc: 0.676774\tvalid_1's binary_logloss: 0.218363\n", "[113]\ttraining's auc: 0.734736\ttraining's binary_logloss: 0.208817\tvalid_1's auc: 0.676738\tvalid_1's binary_logloss: 0.218372\n", "[114]\ttraining's auc: 0.73495\ttraining's binary_logloss: 0.208776\tvalid_1's auc: 0.676778\tvalid_1's binary_logloss: 0.218366\n", "Early stopping, best iteration is:\n", "[104]\ttraining's auc: 0.731472\ttraining's binary_logloss: 0.209388\tvalid_1's auc: 0.6767\tvalid_1's binary_logloss: 0.218354\n", "0.6766996167512115\n"]}], "source": ["model_lgb = lgb_train(X_train.values, y_train, X_valid.values, y_valid, verbose=True)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 50}, "colab_type": "code", "collapsed": true, "id": "GX4r_eZ5O5IC", "outputId": "dd4b540b-fd8a-400b-ee27-ca4357c91432"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>prob</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>163968</td>\n", "      <td>4605</td>\n", "      <td>0.051875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>360576</td>\n", "      <td>1581</td>\n", "      <td>0.115620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>98688</td>\n", "      <td>1964</td>\n", "      <td>0.050633</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>98688</td>\n", "      <td>3645</td>\n", "      <td>0.032896</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>295296</td>\n", "      <td>3361</td>\n", "      <td>0.066129</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  merchant_id      prob\n", "0   163968         4605  0.051875\n", "1   360576         1581  0.115620\n", "2    98688         1964  0.050633\n", "3    98688         3645  0.032896\n", "4   295296         3361  0.066129"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Wall time: 1.53 s\n"]}, {"data": {"text/plain": ["22630"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "prob = model_lgb.predict_proba(test_data.values) # 预测\n", "\n", "submission = pd.read_csv('./data_format1/test_format1.csv')\n", "\n", "# 复购的概率\n", "submission['prob'] = pd.Series(prob[:,1]) # 预测数据赋值给提交数据\n", "\n", "display(submission.head())\n", "\n", "submission.to_csv('submission_lgb.csv', index=False)\n", "\n", "del submission\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["array([[0.94812479, 0.05187521],\n", "       [0.88438038, 0.11561962],\n", "       [0.94936734, 0.05063266],\n", "       ...,\n", "       [0.85627925, 0.14372075],\n", "       [0.95222606, 0.04777394],\n", "       [0.92295125, 0.07704875]])"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["prob"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "yqaIdRNDxRjj"}, "source": ["### XGB 模型"]}, {"cell_type": "code", "execution_count": 43, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 50}, "colab_type": "code", "id": "17s8JUMYHAvi", "outputId": "ea67e253-a58c-45bd-d9c7-ce7315cfc011"}, "outputs": [], "source": ["def xgb_train(X_train, y_train, X_valid, y_valid, verbose=True):\n", "    model_xgb = xgb.XGBClassifier(\n", "        max_depth=10, # raw8\n", "        n_estimators=5000,\n", "        min_child_weight=300, \n", "        colsample_bytree=0.7, \n", "        subsample=0.9, \n", "        learing_rate=0.1)\n", "    \n", "    model_xgb.fit(\n", "        X_train, \n", "        y_train,\n", "        eval_metric='auc',\n", "        eval_set=[(X_train, y_train), (X_valid, y_valid)],\n", "        verbose=verbose,\n", "        early_stopping_rounds=10)# 早停法，如果auc在10epoch没有进步就stop\n", "    print(model_xgb.best_score)\n", "    return model_xgb"]}, {"cell_type": "markdown", "metadata": {}, "source": ["模型训练"]}, {"cell_type": "code", "execution_count": 44, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 34}, "colab_type": "code", "id": "ephCxUW5bUOt", "outputId": "1e6da369-bc25-46fc-cb58-ea7f482b20d7", "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[19:20:55] WARNING: C:/Users/<USER>/workspace/xgboost-win64_release_1.5.1/src/learner.cc:576: \n", "Parameters: { \"learing_rate\" } might not be used.\n", "\n", "  This could be a false alarm, with some parameters getting used by language bindings but\n", "  then being mistakenly passed down to XGBoost core, or some parameter actually being used\n", "  but getting flagged wrongly here. Please open an issue if you find any such cases.\n", "\n", "\n", "0.673734\n"]}], "source": ["model_xgb = xgb_train(X_train, y_train, X_valid, y_valid, verbose=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["模型预测"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 50}, "colab_type": "code", "collapsed": true, "id": "pZvHurIDHAvj", "outputId": "6b5ebd6b-4f5c-4f59-9ce2-4f555f645961"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>merchant_id</th>\n", "      <th>prob</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>163968</td>\n", "      <td>4605</td>\n", "      <td>0.065637</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>360576</td>\n", "      <td>1581</td>\n", "      <td>0.091968</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>98688</td>\n", "      <td>1964</td>\n", "      <td>0.058792</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>98688</td>\n", "      <td>3645</td>\n", "      <td>0.053258</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>295296</td>\n", "      <td>3361</td>\n", "      <td>0.081707</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  merchant_id      prob\n", "0   163968         4605  0.065637\n", "1   360576         1581  0.091968\n", "2    98688         1964  0.058792\n", "3    98688         3645  0.053258\n", "4   295296         3361  0.081707"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Wall time: 980 ms\n"]}, {"data": {"text/plain": ["509"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "prob = model_xgb.predict_proba(test_data)\n", "submission = pd.read_csv('./data_format1/test_format1.csv')\n", "submission['prob'] = pd.Series(prob[:,1])\n", "submission.to_csv('submission_xgb.csv', index=False)\n", "display(submission.head())\n", "del submission\n", "gc.collect()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "AvMQL3-Mb4cA"}, "source": ["## 交叉验证多轮建模"]}, {"cell_type": "code", "execution_count": 62, "metadata": {"colab": {}, "colab_type": "code", "id": "pdGoRvWOlbqv"}, "outputs": [], "source": ["# 构造训练集和测试集\n", "def get_train_test_datas(train_df,label_df):\n", "    skv = StratifiedKFold(n_splits=10, shuffle=True)\n", "    trainX = []\n", "    trainY = []\n", "    testX = []\n", "    testY = []\n", "    # 索引：训练数据索引train_index,目标值的索引test_index\n", "    for train_index, test_index in skv.split(X=train_df, y=label_df):# 10轮for循环\n", "        \n", "        train_x, train_y, test_x, test_y = train_df.iloc[train_index, :], label_df.iloc[train_index], \\\n", "                                            train_df.iloc[test_index, :], label_df.iloc[test_index]\n", "\n", "        trainX.append(train_x)\n", "        trainY.append(train_y)\n", "        testX.append(test_x)\n", "        testY.append(test_y)\n", "    return trainX, testX, trainY, testY"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Uo2UhAdaWYwr"}, "source": ["### LGB 模型（1min）"]}, {"cell_type": "code", "execution_count": 63, "metadata": {"code_folding": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----训练数据，长度 10\n", "----验证数据，长度 10\n", "\n", "============================LGB training use Data 1/10============================\n", "\n", "0.6764325578514025\n", "\n", "============================LGB training use Data 2/10============================\n", "\n", "0.6891014761649508\n", "\n", "============================LGB training use Data 3/10============================\n", "\n", "0.6847981034207339\n", "\n", "============================LGB training use Data 4/10============================\n", "\n", "0.6740686492270855\n", "\n", "============================LGB training use Data 5/10============================\n", "\n", "0.6800922711164193\n", "\n", "============================LGB training use Data 6/10============================\n", "\n", "0.6866774295822827\n", "\n", "============================LGB training use Data 7/10============================\n", "\n", "0.6865939749602854\n", "\n", "============================LGB training use Data 8/10============================\n", "\n", "0.663655985712364\n", "\n", "============================LGB training use Data 9/10============================\n", "\n", "0.6850513956313553\n", "\n", "============================LGB training use Data 10/10============================\n", "\n", "0.6863430735031704\n", "Wall time: 1min 17s\n"]}], "source": ["%%time\n", "train_X, train_y = train_data.drop(['label'], axis=1), train_data['label']\n", "\n", "# 拆分为10份训练数据和验证数据\n", "X_train, X_valid, y_train, y_valid = get_train_test_datas(train_X, train_y)\n", "\n", "print('----训练数据，长度',len(X_train))\n", "print('----验证数据，长度',len(X_valid))\n", "\n", "pred_lgbms = [] # 列表，接受目标值，10轮，平均值\n", "\n", "for i in range(10):\n", "    print('\\n============================LGB training use Data {}/10============================\\n'.format(i+1))\n", "    model_lgb = lgb.LGBMClassifier(\n", "        max_depth=10, # 8\n", "        n_estimators=1000,\n", "        min_child_weight=100,\n", "        colsample_bytree=0.7,\n", "        subsample=0.9,\n", "        learning_rate=0.05)\n", "\n", "    model_lgb.fit(\n", "        X_train[i].values, \n", "        y_train[i],\n", "        eval_metric='auc',\n", "        eval_set=[(X_train[i].values, y_train[i]), (X_valid[i].values, y_valid[i])],\n", "        verbose=False,\n", "        early_stopping_rounds=10)\n", "\n", "    print(model_lgb.best_score_['valid_1']['auc'])\n", "\n", "    pred = model_lgb.predict_proba(test_data.values)\n", "    \n", "    pred = pd.DataFrame(pred[:,1]) # 将预测概率（复购）去处理，转换成DataFrame\n", "    \n", "    pred_lgbms.append(pred)\n", "\n", "# 求10轮平均值生成预测结果，保存\n", "# 每一轮的结果，作为一列，进行了添加\n", "pred_lgbms = pd.concat(pred_lgbms, axis=1) # 级联，列进行级联\n", "\n", "# 加载提交数据\n", "submission = pd.read_csv('./data_format1/test_format1.csv')\n", "\n", "submission['prob'] = pred_lgbms.mean(axis=1) # 10轮训练的平均值\n", "\n", "submission.to_csv('submission_KFold_lgb.csv', index=False)"]}, {"cell_type": "code", "execution_count": 55, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>0</th>\n", "      <th>0</th>\n", "      <th>0</th>\n", "      <th>0</th>\n", "      <th>0</th>\n", "      <th>0</th>\n", "      <th>0</th>\n", "      <th>0</th>\n", "      <th>0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.055886</td>\n", "      <td>0.046910</td>\n", "      <td>0.048247</td>\n", "      <td>0.044899</td>\n", "      <td>0.051961</td>\n", "      <td>0.055180</td>\n", "      <td>0.051898</td>\n", "      <td>0.052081</td>\n", "      <td>0.052636</td>\n", "      <td>0.051678</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.092411</td>\n", "      <td>0.103658</td>\n", "      <td>0.098715</td>\n", "      <td>0.092163</td>\n", "      <td>0.095193</td>\n", "      <td>0.097384</td>\n", "      <td>0.070938</td>\n", "      <td>0.086493</td>\n", "      <td>0.105066</td>\n", "      <td>0.089608</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.049865</td>\n", "      <td>0.046393</td>\n", "      <td>0.050722</td>\n", "      <td>0.054663</td>\n", "      <td>0.053192</td>\n", "      <td>0.046511</td>\n", "      <td>0.053459</td>\n", "      <td>0.046542</td>\n", "      <td>0.053645</td>\n", "      <td>0.042156</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.028297</td>\n", "      <td>0.030072</td>\n", "      <td>0.033617</td>\n", "      <td>0.027951</td>\n", "      <td>0.032181</td>\n", "      <td>0.029938</td>\n", "      <td>0.035848</td>\n", "      <td>0.038321</td>\n", "      <td>0.033284</td>\n", "      <td>0.029815</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.070862</td>\n", "      <td>0.064259</td>\n", "      <td>0.068224</td>\n", "      <td>0.070074</td>\n", "      <td>0.069326</td>\n", "      <td>0.059107</td>\n", "      <td>0.072940</td>\n", "      <td>0.077195</td>\n", "      <td>0.062571</td>\n", "      <td>0.069239</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261472</th>\n", "      <td>0.054240</td>\n", "      <td>0.057529</td>\n", "      <td>0.061744</td>\n", "      <td>0.051099</td>\n", "      <td>0.056373</td>\n", "      <td>0.044646</td>\n", "      <td>0.061189</td>\n", "      <td>0.045183</td>\n", "      <td>0.061670</td>\n", "      <td>0.062057</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261473</th>\n", "      <td>0.026819</td>\n", "      <td>0.021841</td>\n", "      <td>0.026320</td>\n", "      <td>0.028997</td>\n", "      <td>0.027158</td>\n", "      <td>0.029767</td>\n", "      <td>0.023243</td>\n", "      <td>0.023721</td>\n", "      <td>0.025249</td>\n", "      <td>0.034149</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261474</th>\n", "      <td>0.115838</td>\n", "      <td>0.134412</td>\n", "      <td>0.120677</td>\n", "      <td>0.117268</td>\n", "      <td>0.112812</td>\n", "      <td>0.113701</td>\n", "      <td>0.126516</td>\n", "      <td>0.126943</td>\n", "      <td>0.105635</td>\n", "      <td>0.118007</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261475</th>\n", "      <td>0.042335</td>\n", "      <td>0.034865</td>\n", "      <td>0.040677</td>\n", "      <td>0.037834</td>\n", "      <td>0.037391</td>\n", "      <td>0.036060</td>\n", "      <td>0.037305</td>\n", "      <td>0.044105</td>\n", "      <td>0.048137</td>\n", "      <td>0.041567</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261476</th>\n", "      <td>0.084441</td>\n", "      <td>0.078263</td>\n", "      <td>0.078885</td>\n", "      <td>0.082315</td>\n", "      <td>0.079591</td>\n", "      <td>0.075615</td>\n", "      <td>0.079137</td>\n", "      <td>0.081889</td>\n", "      <td>0.067901</td>\n", "      <td>0.091182</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>261477 rows × 10 columns</p>\n", "</div>"], "text/plain": ["               0         0         0         0         0         0         0  \\\n", "0       0.055886  0.046910  0.048247  0.044899  0.051961  0.055180  0.051898   \n", "1       0.092411  0.103658  0.098715  0.092163  0.095193  0.097384  0.070938   \n", "2       0.049865  0.046393  0.050722  0.054663  0.053192  0.046511  0.053459   \n", "3       0.028297  0.030072  0.033617  0.027951  0.032181  0.029938  0.035848   \n", "4       0.070862  0.064259  0.068224  0.070074  0.069326  0.059107  0.072940   \n", "...          ...       ...       ...       ...       ...       ...       ...   \n", "261472  0.054240  0.057529  0.061744  0.051099  0.056373  0.044646  0.061189   \n", "261473  0.026819  0.021841  0.026320  0.028997  0.027158  0.029767  0.023243   \n", "261474  0.115838  0.134412  0.120677  0.117268  0.112812  0.113701  0.126516   \n", "261475  0.042335  0.034865  0.040677  0.037834  0.037391  0.036060  0.037305   \n", "261476  0.084441  0.078263  0.078885  0.082315  0.079591  0.075615  0.079137   \n", "\n", "               0         0         0  \n", "0       0.052081  0.052636  0.051678  \n", "1       0.086493  0.105066  0.089608  \n", "2       0.046542  0.053645  0.042156  \n", "3       0.038321  0.033284  0.029815  \n", "4       0.077195  0.062571  0.069239  \n", "...          ...       ...       ...  \n", "261472  0.045183  0.061670  0.062057  \n", "261473  0.023721  0.025249  0.034149  \n", "261474  0.126943  0.105635  0.118007  \n", "261475  0.044105  0.048137  0.041567  \n", "261476  0.081889  0.067901  0.091182  \n", "\n", "[261477 rows x 10 columns]"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["pred_lgbms"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "49QqMtKjWtBX"}, "source": ["### XGB 模型（4min）"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["# 构造训练集和测试集\n", "def get_train_test_datas(train_df,label_df):\n", "    skv = StratifiedKFold(n_splits=20, shuffle=True)\n", "    trainX = []\n", "    trainY = []\n", "    testX = []\n", "    testY = []\n", "    # 索引：训练数据索引train_index,目标值的索引test_index\n", "    for train_index, test_index in skv.split(X=train_df, y=label_df):# 10轮for循环\n", "        \n", "        train_x, train_y, test_x, test_y = train_df.iloc[train_index, :], label_df.iloc[train_index], \\\n", "                                            train_df.iloc[test_index, :], label_df.iloc[test_index]\n", "\n", "        trainX.append(train_x)\n", "        trainY.append(train_y)\n", "        testX.append(test_x)\n", "        testY.append(test_y)\n", "    return trainX, testX, trainY, testY"]}, {"cell_type": "code", "execution_count": 57, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 353}, "colab_type": "code", "collapsed": true, "id": "bnCAP5aRt4y_", "outputId": "44d45321-4d6f-423b-8dbc-de3cf3f6370b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["------数据长度 20 20\n", "\n", "============================XGB training use Data 1/20============================\n", "\n", "0.710332\n", "\n", "============================XGB training use Data 2/20============================\n", "\n", "0.679258\n", "\n", "============================XGB training use Data 3/20============================\n", "\n", "0.70283\n", "\n", "============================XGB training use Data 4/20============================\n", "\n", "0.681381\n", "\n", "============================XGB training use Data 5/20============================\n", "\n", "0.688876\n", "\n", "============================XGB training use Data 6/20============================\n", "\n", "0.689305\n", "\n", "============================XGB training use Data 7/20============================\n", "\n", "0.674339\n", "\n", "============================XGB training use Data 8/20============================\n", "\n", "0.662421\n", "\n", "============================XGB training use Data 9/20============================\n", "\n", "0.684782\n", "\n", "============================XGB training use Data 10/20============================\n", "\n", "0.658125\n", "\n", "============================XGB training use Data 11/20============================\n", "\n", "0.667158\n", "\n", "============================XGB training use Data 12/20============================\n", "\n", "0.673516\n", "\n", "============================XGB training use Data 13/20============================\n", "\n", "0.64092\n", "\n", "============================XGB training use Data 14/20============================\n", "\n", "0.673833\n", "\n", "============================XGB training use Data 15/20============================\n", "\n", "0.677694\n", "\n", "============================XGB training use Data 16/20============================\n", "\n", "0.661524\n", "\n", "============================XGB training use Data 17/20============================\n", "\n", "0.694425\n", "\n", "============================XGB training use Data 18/20============================\n", "\n", "0.674241\n", "\n", "============================XGB training use Data 19/20============================\n", "\n", "0.674658\n", "\n", "============================XGB training use Data 20/20============================\n", "\n", "0.694476\n", "Wall time: 8min 39s\n"]}], "source": ["%%time\n", "train_X, train_y = train_data.drop(['label'], axis=1), train_data['label']\n", "\n", "# 拆分为20份训练数据和验证数据\n", "X_train, X_valid, y_train, y_valid = get_train_test_datas(train_X, train_y)\n", "\n", "print('------数据长度',len(X_train),len(y_train))\n", "\n", "pred_xgbs = []\n", "for i in range(20):\n", "    print('\\n============================XGB training use Data {}/20============================\\n'.format(i+1))\n", "    model_xgb = xgb.XGBClassifier(\n", "        max_depth=10, # raw8\n", "        n_estimators=5000,\n", "        min_child_weight=200, \n", "        colsample_bytree=0.7, \n", "        subsample=0.9,\n", "        learning_rate = 0.1)\n", "\n", "    model_xgb.fit(\n", "        X_train[i], \n", "        y_train[i],\n", "        eval_metric='auc',\n", "        eval_set=[(X_train[i], y_train[i]), (X_valid[i], y_valid[i])],\n", "        verbose=False,\n", "        early_stopping_rounds=10 # 早停法，如果auc在10epoch没有进步就stop\n", "    )    \n", "\n", "    print(model_xgb.best_score)\n", "\n", "    pred = model_xgb.predict_proba(test_data)\n", "    pred = pd.DataFrame(pred[:,1])\n", "    pred_xgbs.append(pred)\n", "\n", "# 求20轮平均值生成预测结果，保存\n", "pred_xgbs = pd.concat(pred_xgbs, axis=1)\n", "submission = pd.read_csv('./data_format1/test_format1.csv')\n", "submission['prob'] = pred_xgbs.mean(axis=1)\n", "submission.to_csv('submission_KFold_xgb.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"collapsed_sections": [], "name": "RepeatBuyersPrediction_0.6833.ipynb", "provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "256px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 1}
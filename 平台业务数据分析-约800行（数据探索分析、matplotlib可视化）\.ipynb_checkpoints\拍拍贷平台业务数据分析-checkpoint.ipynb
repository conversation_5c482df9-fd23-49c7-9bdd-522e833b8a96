{"cells": [{"cell_type": "markdown", "id": "emerging-surrey", "metadata": {"toc": true}, "source": ["<h1>Table of Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#项目介绍\" data-toc-modified-id=\"项目介绍-1\">项目介绍</a></span><ul class=\"toc-item\"><li><span><a href=\"#核心技术\" data-toc-modified-id=\"核心技术-1.1\">核心技术</a></span></li></ul></li><li><span><a href=\"#一、借款人分析\" data-toc-modified-id=\"一、借款人分析-2\">一、借款人分析</a></span><ul class=\"toc-item\"><li><span><a href=\"#1.分析用户画像（性别、学历、年龄、是否首标）\" data-toc-modified-id=\"1.分析用户画像（性别、学历、年龄、是否首标）-2.1\">1.分析用户画像（性别、学历、年龄、是否首标）</a></span></li><li><span><a href=\"#2.分析资金储备\" data-toc-modified-id=\"2.分析资金储备-2.2\">2.分析资金储备</a></span></li><li><span><a href=\"#3.分析逾期还款率（借款人的初始评级、借款类型、性别、年龄、借款金额等特征）\" data-toc-modified-id=\"3.分析逾期还款率（借款人的初始评级、借款类型、性别、年龄、借款金额等特征）-2.3\">3.分析逾期还款率（借款人的初始评级、借款类型、性别、年龄、借款金额等特征）</a></span></li><li><span><a href=\"#4.分析借款利率（借款人的初始评级、借款类型、性别、年龄、借款金额等特征）\" data-toc-modified-id=\"4.分析借款利率（借款人的初始评级、借款类型、性别、年龄、借款金额等特征）-2.4\">4.分析借款利率（借款人的初始评级、借款类型、性别、年龄、借款金额等特征）</a></span></li></ul></li><li><span><a href=\"#二、用户还款情况分析\" data-toc-modified-id=\"二、用户还款情况分析-3\">二、用户还款情况分析</a></span><ul class=\"toc-item\"><li><span><a href=\"#1.分析不同借款金额用户的还款情况\" data-toc-modified-id=\"1.分析不同借款金额用户的还款情况-3.1\">1.分析不同借款金额用户的还款情况</a></span></li><li><span><a href=\"#2.分析不同年龄段用户的还款情况\" data-toc-modified-id=\"2.分析不同年龄段用户的还款情况-3.2\">2.分析不同年龄段用户的还款情况</a></span></li><li><span><a href=\"#3.分析不同性别用户的还款情况\" data-toc-modified-id=\"3.分析不同性别用户的还款情况-3.3\">3.分析不同性别用户的还款情况</a></span></li><li><span><a href=\"#4.分析不同初始评级客户的还款情况\" data-toc-modified-id=\"4.分析不同初始评级客户的还款情况-3.4\">4.分析不同初始评级客户的还款情况</a></span></li><li><span><a href=\"#5.分析不同借款类型客户的还款情况\" data-toc-modified-id=\"5.分析不同借款类型客户的还款情况-3.5\">5.分析不同借款类型客户的还款情况</a></span></li></ul></li><li><span><a href=\"#三.计算金额催收回款率（催收回本金/所有逾期本金）\" data-toc-modified-id=\"三.计算金额催收回款率（催收回本金/所有逾期本金）-4\">三.计算金额催收回款率（催收回本金/所有逾期本金）</a></span><ul class=\"toc-item\"><li><span><a href=\"#1.不同等级（A-F）随逾期天数催收还款率的走势\" data-toc-modified-id=\"1.不同等级（A-F）随逾期天数催收还款率的走势-4.1\">1.不同等级（A-F）随逾期天数催收还款率的走势</a></span></li><li><span><a href=\"#2.不同借款期数随逾期天数催收还款率的走势\" data-toc-modified-id=\"2.不同借款期数随逾期天数催收还款率的走势-4.2\">2.不同借款期数随逾期天数催收还款率的走势</a></span></li><li><span><a href=\"#3.不同借款金额随逾期天数催收还款率的走势\" data-toc-modified-id=\"3.不同借款金额随逾期天数催收还款率的走势-4.3\">3.不同借款金额随逾期天数催收还款率的走势</a></span></li></ul></li><li><span><a href=\"#四.累积收益曲线\" data-toc-modified-id=\"四.累积收益曲线-5\">四.累积收益曲线</a></span></li></ul></div>"]}, {"cell_type": "markdown", "id": "intimate-amino", "metadata": {}, "source": ["## 项目介绍\n", "\n", "这个项目使用了拍拍贷真实业务数据。基于数据集研究了用户画像分析、资金储备、逾期还款率、借款利率、用户还款习惯、催收回款率、用户累积收益曲线的问题。\n", "\n", "### 核心技术\n", "\n", "- pandas数据分析处理、数据探索分析、matplotlib、数据可视化"]}, {"cell_type": "markdown", "id": "artistic-farming", "metadata": {}, "source": ["## 一、借款人分析\n", "首先我们来分析一下LC.csv数据集，LC (Loan Characteristics) 表为标的特征表，每支标一条记录。共有21个字段，包括一个主键（listingid）、7个标的特征和13个成交当时的借款人信息，全部为成交当时可以获得的信息。信息的维度比较广，大致可以分为基本信息，认证信息，信用信息，借款信息。\n", "\n", "基本信息：年龄、性别；\n", "\n", "认证信息：手机认证、户口认证、视频认证、征信认证、淘宝认证；\n", "\n", "信用信息：初始评级、历史正常还款期数、历史逾期还款期数；\n", "\n", "借款信息：历史成功借款金额、历史成功借款次数、借款金额、借款期限、借款成功日期\n", "\n", "对于LC数据集我们提出以下四个问题：\n", "\n", "1.用户画像，包含使用平台贷款业务的用户的性别比例，学历水平，是否为旧有用户，年龄分布等信息。\n", "\n", "2.资金储备，每日借款金额大概多少？波动有多大？从而公司每日需准备多少资金可以保证不会出现资金短缺？\n", "\n", "3.用户逾期率，借款人的初始评级、借款类型、性别、年龄等特征对于逾期还款的概率有无显著影响？哪些群体逾期还款率明显较高？\n", "\n", "4.借款利率，哪些群体更愿意接受较高的借款利率？"]}, {"cell_type": "code", "execution_count": 1, "id": "arctic-processor", "metadata": {"scrolled": true}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "LC = pd.read_csv('./data/LC.csv')\n", "LP = pd.read_csv('./data/LP.csv')"]}, {"cell_type": "markdown", "id": "scheduled-adams", "metadata": {}, "source": ["对数据进行清洗\n", "\n", "依次检查重复值、缺失值的处理，一致化以及异常值，数据集很干净。"]}, {"cell_type": "code", "execution_count": 2, "id": "limited-joint", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 328553 entries, 0 to 328552\n", "Data columns (total 21 columns):\n", " #   Column     Non-Null Count   Dtype  \n", "---  ------     --------------   -----  \n", " 0   ListingId  328553 non-null  int64  \n", " 1   借款金额       328553 non-null  int64  \n", " 2   借款期限       328553 non-null  int64  \n", " 3   借款利率       328553 non-null  float64\n", " 4   借款成功日期     328553 non-null  object \n", " 5   初始评级       328553 non-null  object \n", " 6   借款类型       328553 non-null  object \n", " 7   是否首标       328553 non-null  object \n", " 8   年龄         328553 non-null  int64  \n", " 9   性别         328553 non-null  object \n", " 10  手机认证       328553 non-null  object \n", " 11  户口认证       328553 non-null  object \n", " 12  视频认证       328553 non-null  object \n", " 13  学历认证       328553 non-null  object \n", " 14  征信认证       328553 non-null  object \n", " 15  淘宝认证       328553 non-null  object \n", " 16  历史成功借款次数   328553 non-null  int64  \n", " 17  历史成功借款金额   328553 non-null  float64\n", " 18  总待还本金      328553 non-null  float64\n", " 19  历史正常还款期数   328553 non-null  int64  \n", " 20  历史逾期还款期数   328553 non-null  int64  \n", "dtypes: float64(3), int64(7), object(11)\n", "memory usage: 52.6+ MB\n"]}], "source": ["LC.info()"]}, {"cell_type": "code", "execution_count": 3, "id": "israeli-piece", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ListingId</th>\n", "      <th>借款金额</th>\n", "      <th>借款期限</th>\n", "      <th>借款利率</th>\n", "      <th>年龄</th>\n", "      <th>历史成功借款次数</th>\n", "      <th>历史成功借款金额</th>\n", "      <th>总待还本金</th>\n", "      <th>历史正常还款期数</th>\n", "      <th>历史逾期还款期数</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>3.285530e+05</td>\n", "      <td>328553.000000</td>\n", "      <td>328553.000000</td>\n", "      <td>328553.000000</td>\n", "      <td>328553.000000</td>\n", "      <td>328553.000000</td>\n", "      <td>3.285530e+05</td>\n", "      <td>3.285530e+05</td>\n", "      <td>328553.000000</td>\n", "      <td>328553.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.907948e+07</td>\n", "      <td>4423.816906</td>\n", "      <td>10.213594</td>\n", "      <td>20.601439</td>\n", "      <td>29.143042</td>\n", "      <td>2.323159</td>\n", "      <td>8.785857e+03</td>\n", "      <td>3.721665e+03</td>\n", "      <td>9.947658</td>\n", "      <td>0.423250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>8.375769e+06</td>\n", "      <td>11219.664024</td>\n", "      <td>2.780444</td>\n", "      <td>1.772408</td>\n", "      <td>6.624286</td>\n", "      <td>2.922361</td>\n", "      <td>3.502736e+04</td>\n", "      <td>8.626061e+03</td>\n", "      <td>14.839899</td>\n", "      <td>1.595681</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.265410e+05</td>\n", "      <td>100.000000</td>\n", "      <td>1.000000</td>\n", "      <td>6.500000</td>\n", "      <td>17.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.190887e+07</td>\n", "      <td>2033.000000</td>\n", "      <td>6.000000</td>\n", "      <td>20.000000</td>\n", "      <td>24.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.952325e+07</td>\n", "      <td>3397.000000</td>\n", "      <td>12.000000</td>\n", "      <td>20.000000</td>\n", "      <td>28.000000</td>\n", "      <td>2.000000</td>\n", "      <td>5.000000e+03</td>\n", "      <td>2.542410e+03</td>\n", "      <td>5.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2.629862e+07</td>\n", "      <td>5230.000000</td>\n", "      <td>12.000000</td>\n", "      <td>22.000000</td>\n", "      <td>33.000000</td>\n", "      <td>3.000000</td>\n", "      <td>1.035500e+04</td>\n", "      <td>5.446810e+03</td>\n", "      <td>13.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3.281953e+07</td>\n", "      <td>500000.000000</td>\n", "      <td>24.000000</td>\n", "      <td>24.000000</td>\n", "      <td>56.000000</td>\n", "      <td>649.000000</td>\n", "      <td>7.405926e+06</td>\n", "      <td>1.172653e+06</td>\n", "      <td>2507.000000</td>\n", "      <td>60.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          ListingId           借款金额           借款期限           借款利率  \\\n", "count  3.285530e+05  328553.000000  328553.000000  328553.000000   \n", "mean   1.907948e+07    4423.816906      10.213594      20.601439   \n", "std    8.375769e+06   11219.664024       2.780444       1.772408   \n", "min    1.265410e+05     100.000000       1.000000       6.500000   \n", "25%    1.190887e+07    2033.000000       6.000000      20.000000   \n", "50%    1.952325e+07    3397.000000      12.000000      20.000000   \n", "75%    2.629862e+07    5230.000000      12.000000      22.000000   \n", "max    3.281953e+07  500000.000000      24.000000      24.000000   \n", "\n", "                  年龄       历史成功借款次数      历史成功借款金额         总待还本金  \\\n", "count  328553.000000  328553.000000  3.285530e+05  3.285530e+05   \n", "mean       29.143042       2.323159  8.785857e+03  3.721665e+03   \n", "std         6.624286       2.922361  3.502736e+04  8.626061e+03   \n", "min        17.000000       0.000000  0.000000e+00  0.000000e+00   \n", "25%        24.000000       0.000000  0.000000e+00  0.000000e+00   \n", "50%        28.000000       2.000000  5.000000e+03  2.542410e+03   \n", "75%        33.000000       3.000000  1.035500e+04  5.446810e+03   \n", "max        56.000000     649.000000  7.405926e+06  1.172653e+06   \n", "\n", "            历史正常还款期数       历史逾期还款期数  \n", "count  328553.000000  328553.000000  \n", "mean        9.947658       0.423250  \n", "std        14.839899       1.595681  \n", "min         0.000000       0.000000  \n", "25%         0.000000       0.000000  \n", "50%         5.000000       0.000000  \n", "75%        13.000000       0.000000  \n", "max      2507.000000      60.000000  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["LC.describe()"]}, {"cell_type": "markdown", "id": "dense-insulation", "metadata": {}, "source": ["观察一下年龄分布，最小17岁，最大56岁，平均年龄29岁，33岁以下的占比超过了75%。说明用户整体还是中青年。\n", "\n", "将年龄分为'15-20', '20-25', '25-30', '30-35', '35-40', '40+'比较合理\n", "\n", "观察一下借款金额分布，最小借款金额为100元，最大为50万元，平均值为4424元，低于5230的借款金额占到了75%。\n", "\n", "说明应该是小额借款比较多。将借款金额分为0-2000，2000-3000，3000-4000，4000-5000，5000-6000，6000以上比较合理"]}, {"cell_type": "code", "execution_count": 5, "id": "congressional-irrigation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3203276 entries, 0 to 3203275\n", "Data columns (total 10 columns):\n", " #   Column      Dtype  \n", "---  ------      -----  \n", " 0   ListingId   int64  \n", " 1   期数          int64  \n", " 2   还款状态        int64  \n", " 3   应还本金        float64\n", " 4   应还利息        float64\n", " 5   剩余本金        float64\n", " 6   剩余利息        float64\n", " 7   到期日期        object \n", " 8   还款日期        object \n", " 9   recorddate  object \n", "dtypes: float64(4), int64(3), object(3)\n", "memory usage: 244.4+ MB\n"]}], "source": ["LP.info()"]}, {"cell_type": "code", "execution_count": 6, "id": "excellent-karen", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ListingId</th>\n", "      <th>期数</th>\n", "      <th>还款状态</th>\n", "      <th>应还本金</th>\n", "      <th>应还利息</th>\n", "      <th>剩余本金</th>\n", "      <th>剩余利息</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>3.203276e+06</td>\n", "      <td>3.203276e+06</td>\n", "      <td>3.203276e+06</td>\n", "      <td>3.203276e+06</td>\n", "      <td>3.203276e+06</td>\n", "      <td>3.203276e+06</td>\n", "      <td>3.203276e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.947391e+07</td>\n", "      <td>5.904377e+00</td>\n", "      <td>6.037828e-01</td>\n", "      <td>4.604506e+02</td>\n", "      <td>4.232540e+01</td>\n", "      <td>1.846682e+02</td>\n", "      <td>1.472581e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>8.312219e+06</td>\n", "      <td>3.455267e+00</td>\n", "      <td>6.684636e-01</td>\n", "      <td>2.041906e+03</td>\n", "      <td>8.346626e+01</td>\n", "      <td>4.012435e+02</td>\n", "      <td>2.999337e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.265410e+05</td>\n", "      <td>1.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.222287e+07</td>\n", "      <td>3.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.881500e+02</td>\n", "      <td>1.301000e+01</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>2.025666e+07</td>\n", "      <td>6.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>3.309400e+02</td>\n", "      <td>2.978000e+01</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2.661693e+07</td>\n", "      <td>9.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>5.123400e+02</td>\n", "      <td>5.539000e+01</td>\n", "      <td>2.991400e+02</td>\n", "      <td>1.968000e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3.281953e+07</td>\n", "      <td>2.400000e+01</td>\n", "      <td>4.000000e+00</td>\n", "      <td>5.000000e+05</td>\n", "      <td>1.875000e+04</td>\n", "      <td>1.000000e+05</td>\n", "      <td>3.978370e+03</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          ListingId            期数          还款状态          应还本金          应还利息  \\\n", "count  3.203276e+06  3.203276e+06  3.203276e+06  3.203276e+06  3.203276e+06   \n", "mean   1.947391e+07  5.904377e+00  6.037828e-01  4.604506e+02  4.232540e+01   \n", "std    8.312219e+06  3.455267e+00  6.684636e-01  2.041906e+03  8.346626e+01   \n", "min    1.265410e+05  1.000000e+00  0.000000e+00  0.000000e+00  0.000000e+00   \n", "25%    1.222287e+07  3.000000e+00  0.000000e+00  1.881500e+02  1.301000e+01   \n", "50%    2.025666e+07  6.000000e+00  1.000000e+00  3.309400e+02  2.978000e+01   \n", "75%    2.661693e+07  9.000000e+00  1.000000e+00  5.123400e+02  5.539000e+01   \n", "max    3.281953e+07  2.400000e+01  4.000000e+00  5.000000e+05  1.875000e+04   \n", "\n", "               剩余本金          剩余利息  \n", "count  3.203276e+06  3.203276e+06  \n", "mean   1.846682e+02  1.472581e+01  \n", "std    4.012435e+02  2.999337e+01  \n", "min    0.000000e+00  0.000000e+00  \n", "25%    0.000000e+00  0.000000e+00  \n", "50%    0.000000e+00  0.000000e+00  \n", "75%    2.991400e+02  1.968000e+01  \n", "max    1.000000e+05  3.978370e+03  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["LP.describe()"]}, {"cell_type": "code", "execution_count": 8, "id": "greatest-polls", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 3203276 entries, 0 to 3203275\n", "Data columns (total 10 columns):\n", " #   Column      Dtype  \n", "---  ------      -----  \n", " 0   ListingId   int64  \n", " 1   期数          int64  \n", " 2   还款状态        int64  \n", " 3   应还本金        float64\n", " 4   应还利息        float64\n", " 5   剩余本金        float64\n", " 6   剩余利息        float64\n", " 7   到期日期        object \n", " 8   还款日期        object \n", " 9   recorddate  object \n", "dtypes: float64(4), int64(3), object(3)\n", "memory usage: 268.8+ MB\n", "None\n", "<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 328553 entries, 0 to 328552\n", "Data columns (total 21 columns):\n", " #   Column     Non-Null Count   Dtype  \n", "---  ------     --------------   -----  \n", " 0   ListingId  328553 non-null  int64  \n", " 1   借款金额       328553 non-null  int64  \n", " 2   借款期限       328553 non-null  int64  \n", " 3   借款利率       328553 non-null  float64\n", " 4   借款成功日期     328553 non-null  object \n", " 5   初始评级       328553 non-null  object \n", " 6   借款类型       328553 non-null  object \n", " 7   是否首标       328553 non-null  object \n", " 8   年龄         328553 non-null  int64  \n", " 9   性别         328553 non-null  object \n", " 10  手机认证       328553 non-null  object \n", " 11  户口认证       328553 non-null  object \n", " 12  视频认证       328553 non-null  object \n", " 13  学历认证       328553 non-null  object \n", " 14  征信认证       328553 non-null  object \n", " 15  淘宝认证       328553 non-null  object \n", " 16  历史成功借款次数   328553 non-null  int64  \n", " 17  历史成功借款金额   328553 non-null  float64\n", " 18  总待还本金      328553 non-null  float64\n", " 19  历史正常还款期数   328553 non-null  int64  \n", " 20  历史逾期还款期数   328553 non-null  int64  \n", "dtypes: float64(3), int64(7), object(11)\n", "memory usage: 55.1+ MB\n", "None\n"]}], "source": ["LP = LP.dropna(how='any')\n", "print(LP.info())\n", "LC = LC.dropna(how='any')\n", "print(LC.info())"]}, {"cell_type": "markdown", "id": "drawn-receiver", "metadata": {}, "source": ["### 1.分析用户画像（性别、学历、年龄、是否首标）\n", "\n", "按‘性别’、‘年龄’、‘是否首标’、‘学历认证’字段对‘借款金额’进行加总，用饼图或柱状图将结果可视化"]}, {"cell_type": "code", "execution_count": 11, "id": "arbitrary-tucson", "metadata": {}, "outputs": [], "source": ["# 让图表直接在jupyter中展示出来\n", "%matplotlib inline\n", "# 解决中文乱码问题\n", "plt.rcParams[\"font.sans-serif\"] = 'SimHei'\n", "# 解决负号无法正常显示问题\n", "plt.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "code", "execution_count": 12, "id": "harmful-hamburg", "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x432 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA24AAAHQCAYAAADZDjlLAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAoxUlEQVR4nO3df5CdVYHv6+8yQAoIAdQIEo9GGe5FEVCJEmISGm5AAQeUUUAc9BTMjSJQljo6OuRq4YEjUtbgqAwONegZKRhJCf64ID/ChCbhBoGOEpiREZCDMiAhDoEQRhlIr/tHb0JoOukd00kvup+nqot3771Wst56d730J+/ut0utNQAAALTrZaO9AAAAADZOuAEAADROuAEAADROuAEAADROuAEAADROuAEAADRum9FewHNe+cpX1mnTpo32MgAAAEbFsmXLfldrnTLUa82E27Rp09LX1zfaywAAABgVpZRfb+g1H5UEAABonHADAABonHADAABonHADAABonHADAABonHADAABonHADAABonHADAABonHADAABonHADGGMee+yxLFy4ML/73e+26N+zYsWKPPPMM1v07wAABgg3gDHkt7/9bY466qjcdtttOeSQQ7Jy5cokA5H11re+dYPzLrzwwvT09KSnpydvectb8tGPfjRJcsopp2TmzJk5++yzkyTf/OY3M2vWrDz11FO5/vrrs+222275nQIARi7cSikvL6UcVkp55Uj9mQBsmn/913/N+eefnzPPPDPvete78rOf/SxJ8pd/+Zf5/e9/v8F5p556anp7e9Pb25vZs2dn3rx5ufLKK7N27dosXbo0Dz/8cO69997ccccdOemkk3L77bdnhx122Fq7BQDjXlfhVkq5uJSytJQyfwOvvzrJ1UnekeTGUsqUUso2pZTflFJ6O1/7juC6ARjC3LlzM2PGjCxevDi33XZbDjrooCxatCg77rhjdt9992HnP/TQQ1mxYkUOOOCA9Pb25rjjjkuSHHroobn55ptTa80zzzyT66+/PkccccSW3h0AoGPYcCulHJtkQq11ZpI9Sil7DTFsnySfrLWek+S6JG9Lsl+Sf6q19nS+7hrJhQMwtFprLr/88my77bapteZLX/pSzj333K7mXnDBBTn11FOTJE899VSmTp2aJJk8eXJWrFiRww8/PFdddVVe85rX5Oijj86NN964xfYDAHheN1fcepIs6GwvSjJr8IBa6w211p+WUuZk4KrbLUlmJHlfKeXmUsqlpZRtRmjNAGxEKSUXXHBBZs6cma997Ws57bTTsssuuww7r7+/PzfeeGMOOeSQJMmkSZPWfbxyzZo16e/vz/HHH5+zzjoru+yyS4466qhcccUVW3JXAICObsJtxyQPdbZXJ9ltqEGllJLk+CTPJFmb5PYkB9daZyV5PMmRQ8yZV0rpK6X0PfcD9AD88b7yla/ku9/9bpLk8ccfz7XXXpsLLrggPT09ueOOO/IXf/EXG5y7ZMmSHHjggeseH3DAAbn55puTJMuXL8+0adOSJPfcc0/23HPPTJw4Mf39/VtuZwCAdboJtzVJtu9sT9rQnDrgtCRLk7wnyZ211t92Xv63JC/6iGWt9aJa6/Ra6/QpU6Zs8uIBeKF58+blkksuyZw5c9bdWOS5m4685S1vyT/8wz/kF7/4RebPf/GPLF933XWZM2fOusfvfe97c8kll+RTn/pUFixYkKOOOiqrV6/O7rvvnje96U256KKLMnfu3K25ewAwbpVa68YHlPLhJK+qtX61lHJWkl/WWi8bNOavkvy21vrdUso3klyV5JQk5yT5lyQLk/zPWusNG/p7pk+fXvv6+jZvbwAYUatWrcrChQszZ86crm5uAgD88Uopy2qt04d8rYtwm5xkSZJ/TnJEkhOSfKDWOn+9Mbtm4OfgJmYg1E7LwA1LLktSkvy41nrmxv4e4QYAAIxnGwu3YW8YUmtdXUrpSXJYkvNqrY8kWT5ozKrO6+v7lwzcWRJgzJn2uatHewkM44FzjxrtJQDAiOnqTo+dMFsw7EAAAABGXFe/gBsAAIDRI9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaJ9wAAAAaN2LhVkp5eSnlsFLKK0fqzwQAAKDLcCulXFxKWVpKmb+B11+d5Ook70hyYyllSjfzAAAAGN6w4VZKOTbJhFrrzCR7lFL2GmLYPkk+WWs9J8l1Sd7W5TwAAACG0c0Vt54kCzrbi5LMGjyg1npDrfWnpZQ5Gbjqdks38wAAABheN+G2Y5KHOturk+w21KBSSklyfJJnkqztZl4pZV4ppa+U0rdy5cpNXDoAAMD40E24rUmyfWd70obm1AGnJVma5D3dzKu1XlRrnV5rnT5lypRNXTsAAMC40E24LcvzH3PcP8kDgweUUv6qlPLhzsNdkjzezTwAAACGt00XY36YZEkpZY8kRyQ5oZRydq11/TtFXpRkQSnlL5L8S5Lrk+w0aN6MEV05AADAODFsuNVaV5dSepIcluS8WusjSZYPGrOq8/r6Bs97YgTWCwAAMO50c8XtuTBbMOzAEZoHAADA87r6BdwAAACMHuEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQOOEGAADQuK7CrZRycSllaSll/gZe37mUck0pZWEp5QellO1KKduUUn5TSuntfO07sksHAAAYH4YNt1LKsUkm1FpnJtmjlLLXEMM+lORvaq2HJXkkybuT7Jfkn2qtPZ2vu0Zy4QAAAONFN1fcepIs6GwvSjJr8IBa69/VWhd2Hk5J8miSGUneV0q5uZRyaSllm8HzSinzSil9pZS+lStX/lE7AAAAMNZ1E247Jnmos706yW4bGlhKOSjJrrXWnya5PcnBtdZZSR5PcuTg8bXWi2qt02ut06dMmbKpawcAABgXXnQVbAhrkmzf2Z6UDcReKeXlSb6R5M86T91Za326s/1vSYb6iCUAAADD6OaK27I8//HI/ZM8MHhAKWW7DHyc8vO11l93nr6klLJ/KWVCkvclWb75ywUAABh/ugm3HyY5qZTyN0mOS/KvpZSzB405JckBSc7s3EHy+CRfSnJJkjuS3FJrvWHEVg0AbLLHHnssCxcuzO9+97sR/XNXrFiRZ555ZkT/TABeaNiPStZaV5dSepIcluS8WusjGXT1rNZ6YZILh5i+3wisEQDYRE888UROOOGEPPvss5k0aVK+9rWv5YQTTsh73vOefOpTn8qiRYsy1M+Xr1q1Kh/60Ify5JNPZp999sm3vvWtXHjhhbn88suTJI8//ngOPPDA7Lvvvvne976X6667Ltdff31OOumkrb2LAONKV7/Hrda6qta6oBNtAEDjLr300nzqU5/KwoULs/vuu2f58uU5//zzc+aZZ+Zd73pXfvaznw0575JLLsmf//mfZ8mSJXnyySfT19eXU089Nb29vent7c3s2bMzb9683HHHHTnppJNy++23Z4cddtjKewcw/nQVbgDAS8vHP/7xHHbYYUmSlStX5lWvelVmzJiRxYsX57bbbstBBx005LxXvOIV+eUvf5nHH388Dz74YF772teue+2hhx7KihUrcsABB6TWmmeeeSbXX399jjjiiK2yTwDjmXADgDHslltuyapVqzJjxozUWnP55Zdn2223zYQJE4YcP2vWrNx77735+te/nr333ju77rrrutcuuOCCnHrqqUmSww8/PFdddVVe85rX5Oijj86NN964VfYHYLwSbgAwRj322GM544wz8u1vfztJUkrJBRdckJkzZ+aqq64acs5f//Vf51vf+la+8IUvZO+99853vvOdJEl/f39uvPHGHHLIIUmS448/PmeddVZ22WWXHHXUUbniiiu2zk4BjFPCDQDGoP/6r//Kcccdly9/+ct53etel6985Sv57ne/m2TgBiO77LLLkPP+8z//M3fddVfWrl2bW2+9NaWUJMmSJUty4IEHvmDsPffckz333DMTJ05Mf3//Ft0fgPFOuAHAGHTxxRdn2bJlOeecc9LT05Np06blkksuyZw5c7J27docfvjh+cUvfpH58+e/YN7nP//5zJs3LzvvvHMee+yxfPCDH0ySXHfddZkzZ866catXr87uu++eN73pTbnooosyd+7crbp/AONNqbWO9hqSJNOnT699fX2jvQyArkz73NWjvQSG8cC5R432EgBgk5RSltVapw/1mituAAAAjRv2F3ADABvm6mv7XH0FxgJX3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABon3AAAABrXVbiVUi4upSwtpczfwOs7l1KuKaUsLKX8oJSyXTfzAAAAGN6w4VZKOTbJhFrrzCR7lFL2GmLYh5L8Ta31sCSPJHl3l/MAAAAYRjdX3HqSLOhsL0oya/CAWuvf1VoXdh5OSfJoN/MAAAAYXjfhtmOShzrbq5PstqGBpZSDkuxaa/1pN/NKKfNKKX2llL6VK1du0sIBAADGi27CbU2S7TvbkzY0p5Ty8iTfSHJyt/NqrRfVWqfXWqdPmTJlU9YNAAAwbnQTbsvy/Mcc90/ywOABnZuRLEjy+Vrrr7udBwAAwPC26WLMD5MsKaXskeSIJCeUUs6uta5/p8hTkhyQ5MxSyplJLhxi3oyRXDgAAMB4MWy41VpXl1J6khyW5Lxa6yNJlg8ac2EGYu0FBs17YvOXCwAAMP50c8UttdZVef4OkV37Y+cBAADwvK5+ATcAAACjR7gBAAA0TrgBAAA0TrgBAAA0TrgBAAA0TrgBAAA0TrgBAAA0TrgBAAA0TrgBAAA0TrgBAAA0TrgBAAA0TrgBm+TBBx8c7SUAAIw7wg3GiBUrVmT27NlJki9+8Yvp6elJT09P9t5773z5y18ecs7Gxh199NH5+c9/niQ588wzc9RRR6XWmhtvvHHL7wwAAC+wzWgvANh8q1atykc+8pE89dRTSZKzzjpr3Wsf+MAH8uEPf3jIeRsad+mll+YNb3hD3vrWtyYZiML99tsvP//5z/Pa1752S+0GAAAb4IobjAETJkzI5ZdfnsmTJ7/g+dtvvz1Tp07N1KlTNzp//XGPPfZYPv3pT2fXXXddd3Wt1ppnn302S5YsycEHH7zF9gMAgKEJNxgDJk+enJ133vlFz//t3/5tzjjjjGHnrz/u/PPPzwc+8IF89KMfzXe/+938+Mc/zpvf/Ob8+te/Tiklc+bMyd133z3i+wAAwIYJNxijHn/88Tz66KPZc889N2ncz3/+85x22mnZfffdc9xxx6W3tzef/OQnc9JJJ2WHHXbIsccem6uvvnpr7AIAAB3CDcaoH/3oRznyyCM3edyf/Mmf5P7770+S9PX15XWve12SgcDbaaedMnHixPT392+ZRQMAMCThBmPUddddlzlz5qx7vGjRonzzm98cdtxnP/vZfPOb38w73/nOLF68OCeffHLuueee7L///nnHO96Rb3zjG37ODQBgKyu11tFeQ5Jk+vTpta+vb7SXAdCVaZ/zcdHWPXDuUVvl7/FeaN/Wei8AbK5SyrJa6/ShXnPFDQAAoHF+jxtsAv+y3j7/sg4AjEWuuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADROuAEAADSuq3ArpVxcSllaSpm/kTG7lVKWrPd4ainl30spvZ2vKSOxYAAAgPFm2HArpRybZEKtdWaSPUopew0xZtck/5hkx/WePjDJObXWns7XypFaNAAAwHjSzRW3niQLOtuLkswaYszaJMcnWb3eczOSfLyUcksp5fzNWSQAAMB41k247Zjkoc726iS7DR5Qa11da31i0NPXJJlZaz0oyf9RStlv8LxSyrxSSl8ppW/lShfkAAAAhtJNuK1Jsn1ne1KXc5Jkaa31yc72vyV50Ucsa60X1Vqn11qnT5niR+AAAACG0k2ELcvzH4/cP8kDXf7Z15VSXl1K2SHJu5L8y6YvDwAAgG26GPPDJEtKKXskOSLJCaWUs2utG7zDZMdZSW5M8l9JvlVr/eVmrRQAAGCcGjbcaq2rSyk9SQ5Lcl6t9ZEkyzcwtme97RuT7D0iqwQAABjHurnillrrqjx/Z0kAAAC2om5vNAIAAMAoEW4AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAACNE24AAGyS+++/P4sWLcrTTz/9otcefPDBUVgRjH3CDQBgHFixYkVmz56dJPnNb36Tnp6eHHrooZk3b15qrRucd/fdd+eYY45Z9/j666/PqaeemoULF+bII49Mkpxyyik5+eSTkyS9vb1bbidgHNtmtBcAAMCWtWrVqnzkIx/JU089lST5+7//+1x44YV54xvfmCOOOCJ33XVX9ttvvxfN+9WvfpXPfOYzWbNmzbrn7rnnnixYsCA777xzpk+fnqeffjqllCTJrbfemhkzZmydnYJxxhU3AIAxbsKECbn88sszefLkJMk555yTN77xjUmS//iP/8grX/nKIefttNNOueKKK17w3Omnn57tttsul112WV7/+tdn4sSJqbWmv78/99xzT/baa68tuzMwTgk3AIAxbvLkydl5551f9Pzll1+effbZJ3vssceQ8171qldl4sSJL3p+xYoV+f73v5+pU6cmSV7xilek1ppHH300s2fPzqOPPjqyOwAINwCA8ej+++/PV7/61Xzta1/b5LnTpk3LlVdemWXLluWRRx7Jeeedl8MPPzzPPvtsjj322CxevHjkFwzjnHADABhnVq1alQ9+8IP59re/PeSVuI055phjct9996W/vz9/+MMfsuOOO2bt2rXrfs5t4sSJ6e/v3xLLhnHNzUkAAMaZc889N7/5zW9yxhlnJEnOOuusrF27Nr/4xS9y+umnb3Tu/Pnzc+KJJ2bbbbfNxz72sey000656aabMnfu3Nx33305+eSTc+21126N3YBxpWzs9q9b0/Tp02tfX99oLwM2atrnrh7tJTCMB849aqv8Pd4L7fNe4Dlb670AsLlKKctqrdOHes1HJQEAABrno5IAACPA1df2ufrKS5krbgAAAI0TbgAAAI0TbgAAAI0TbgAAwB/l/vvvz6JFi/L000+/6LUHH3xwFFY0dgk3AAAYR1asWJHZs2eve3z33XfnmGOO2eicxx9/PHPmzMk73/nOXHPNNUmS66+/PqeeemoWLlyYI488Mklyyimn5OSTT06S9Pb2bpkdGKfcVRIAAMaJVatW5SMf+UieeuqpJMmvfvWrfOYzn8maNWs2Ou8LX/hCTj755Jx00kl5z3vek3e/+9255557smDBguy8886ZPn16nn766ZRSkiS33nprZsyYscX3ZzxxxQ0AAMaJCRMm5PLLL8/kyZOTJDvttFOuuOKKYectXrw473//+zNhwoTsueeeeeCBB3L66adnu+22y2WXXZbXv/71mThxYmqt6e/vzz333JO99tprS+/OuCLcAABgnJg8eXJ23nnndY9f9apXZeLEicPO22abbTJp0qR1f8aKFSuSDHzs8vvf/36mTp2aJHnFK16RWmseffTRzJ49O48++ugW2IvxSbgBAAAbNWHChHXba9asSX9/f5Jk2rRpufLKK7Ns2bI88sgjOe+883L44Yfn2WefzbHHHpvFixeP1pLHHOEGAABs1D777JO+vr4kyZ133pnXve51OeaYY3Lfffelv78/f/jDH7Ljjjtm7dq1637ObeLEiesCj83n5iQAAMA6l112Wbbbbru8//3vX/fcqaeemlNOOSUHHnhgJk2alKlTp2b+/Pk58cQTs+222+ZjH/tYdtppp9x0002ZO3du7rvvvpx88sm59tprR3FPxhbhBgAA48zgW/Wv//jEE0980fi3v/3tueKKK3LHHXfkT//0T9c9d9ttt71g3MEHH5wkmTJlSu6+++6RXfQ4J9wAAGAETfvc1aO9hC1o+/xl3w2jvYjN9sC5R432EjaZn3EDAABonHADAABonHADAABonHADAABonHADAABonHADAABonHADAABonHADAABonHADAABoXFfhVkq5uJSytJQyfyNjdiulLFnv8ballKs6804eicUCAACMR8OGWynl2CQTaq0zk+xRStlriDG7JvnHJDuu9/QZSfo6895TStlphNYMAAAwrnRzxa0nyYLO9qIks4YYszbJ8UlWb2De0iTTB08qpcwrpfSVUvpWrlzZ5ZIBAADGl27CbcckD3W2VyfZbfCAWuvqWusTf8S8i2qt02ut06dMmdL9qgEAAMaRbsJtTZLtO9uTupyzOfMAAABYTzcxtSzPfzxy/yQPdPln/7HzAAAAWM82XYz5YZIlpZQ9khyR5IRSytm11g3eYbLjH5P8pJQyO8mbkty6WSsFAAAYp4a94lZrXZ2BG438NMkhtdblG4q2WmvPetu/TnJYkv8vydxa69qRWDAAAMB4080Vt9RaV+X5O0R2rdb68B8zDwAAgOe5YQgAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjhBsAAEDjugq3UsrFpZSlpZT53Y4ppWxTSvlNKaW387XvSC0aAABgPBk23EopxyaZUGudmWSPUspeXY7ZL8k/1Vp7Ol93jfTiAQAAxoNurrj1JFnQ2V6UZFaXY2YkeV8p5eZSyqWllG02b6kAAADjUzfhtmOShzrbq5Ps1uWY25McXGudleTxJEcOnlRKmVdK6Sul9K1cuXITlw4AADA+dBNua5Js39metIE5Q425s9b6285z/5bkRR+xrLVeVGudXmudPmXKlE1aOAAAwHjRTbgty/Mfj9w/yQNdjrmklLJ/KWVCkvclWb5ZKwUAABinuvm5sx8mWVJK2SPJEUlOKKWcXWudv5ExM5LcmeSyJCXJj2utN4zkwgEAAMaLYcOt1rq6lNKT5LAk59VaH8mgq2dDjHkiyRMZuLMkAAAAm6GrOz3WWlfl+btG/tFjAAAA2HRd/QJuAAAARo9wAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwY4OefPLJPPHEE6O9DAAAGPeE2xhxyimnZObMmTn77LOHfP3ZZ5/Na1/72vT09KSnpyd33XVXkuSLX/xi3v72t+f0009PkvzoRz/K2972tjz44IP5yU9+ku23336r7QMAADA04TYGXHnllVm7dm2WLl2ahx9+OPfee++Lxtx555354Ac/mN7e3vT29mbfffdNX19fbr755tx22215zWtekxtuuCHXXXddvvSlL+WWW27JM888k+22224U9ggAAFifcBsDent7c9xxxyVJDj300Nx8880vGvPTn/40P/jBDzJr1qx86EMfyrPPPpvFixfnz/7sz1JKydy5c7NkyZK87GUvy+9///ssWbIkBx988NbeFQAAYAjCbQx46qmnMnXq1CTJ5MmTs2LFiheNefvb356bbropN998c3bZZZf85Cc/GXLecccdl69//et5wxvekE9/+tO57LLLtuq+AAAALybcxoBJkybl97//fZJkzZo16e/vf9GY/fbbL69+9auTJHvvvXfuvffeIefNmTMnl112Wf7bf/tvecMb3pB//ud/3no7AgAADEm4jQEHHHDAuo9HLl++PNOmTXvRmJNOOinLly/P2rVr84Mf/CD777//BufddNNNmTVrVrbZZpuUUrbWbgAAABuwzWgvgM333ve+N7Nnz87DDz+ca665Jt/73vcyf/78F9xh8gtf+EJOPPHE1Fpz9NFHZ+7cuenv78/nP//5fOITn8i1116ba6+9Nv39/dlhhx2y2267ZenSpTnmmGNGcc8AAIBEuI0JkydPTm9vbxYuXJjPfvaz2X333bP//vu/YMyb3/zm3HnnnS947mUve1luuOGGXH311fnEJz6R17/+9UmSY489NkmyaNGirbMDAADARgm3MWLXXXddd2fJTbH99tvn/e9//xZYEQAAMFKE2zCmfe7q0V4Cw3jg3KNGewkAALBFuTkJAABA44QbAABA47oKt1LKxaWUpaWU+Zsyppt5AAAAbNyw4VZKOTbJhFrrzCR7lFL26mZMN/MAAAAYXjdX3HqSLOhsL0oyq8sx3cwDAABgGN3cVXLHJA91tlcn+ZMuxww7r5QyL8m8zsM1pZRfdrdsNtMrk/xutBcxUspXRnsFL2lj6r2QeD9sBu8FnuO9wPrG1PvBe2GzeC9sHa/b0AvdhNuaJNt3tidl6Kt0Q40Zdl6t9aIkF3WxBkZQKaWv1jp9tNfB6PNe4DneCzzHe4H1eT/wHO+F0dfNRyWX5fmPOe6f5IEux3QzDwAAgGF0c8Xth0mWlFL2SHJEkhNKKWfXWudvZMyMJHWI5wAAANhEw15xq7WuzsCNRn6a5JBa6/JB0TbUmCeGem5kl85m8PFUnuO9wHO8F3iO9wLr837gOd4Lo6zUWkd7DQAAAGxEV7+AGwAAgNEj3AB4gVLKjqWU/6uU8prRXgsjo5Ty8lLKYaWUV26NebTLMYWXLuE2RpRSdiulLOlsTy2l/HsppbfzNWUDc3YupVxTSllYSvlBKWW7zvMXl1KWllLmDzWPdg11TLs5nhuYt00p5TfrvY/23Zr7wubZwDEd9niWUnZIcm2Sg5L8v6WUfTrPn1VKub2U8s2tuBuMgFLKq5NcneQdSW4spUzp8rww1DznhZewDRzTro9nKWX7Usqv1nvsvDAGdb6n/Hlne8hzRSnlf43K4sY54TYGlFJ2TfKPGfil50lyYJJzaq09na+VG5j6oSR/U2s9LMkjSd5dSjk2yYRa68wke5RS9trS62dEDT6mJ6S74/mi90KS/ZL803rvo7u2wvoZOYOP6efS3fHcK8lXaq1nJ7k4yaxSyvQM/HqXdyT591LK3K2wfkbOPkk+WWs9J8l1SQ5Nd+eFwfPeFueFl7rBx/TkbNrxnJ9kjyRxXhjTvppke98Ttke4jQ1rkxyfZHXn8YwkHy+l3FJKOX9Dk2qtf1drXdh5OCXJoxm4E+iCznOL8vzv4uMlYIhj+ufp4nhu4L0wI8n7Sik3l1IuLaV08+tDaMQQx/TZdHE8O3cOvqqU8tYk70tyfZI5Sa6oA3ezuiHJ7K2wC4yQWusNtdafllLmZOCb7Helu/PC4Hm3xHnhJW2IY/r7dHk8Syl7J9k3ya2dp5wXxqBSyqFJnsrAP/j1ZNC5opTyd6WU3gz8Y39vKeWsUVnoOOWEOwZ0fvVCSinPPXVNkv9Ra32ylHJ1KWW/JKcl+T/Xm7ao1vqlzryDkuzaOZn/30ke6oxZneRPtsY+MLKeO6YZ+MX3LziepZS/T3fvhbVJDq61/raUckGSI5P8eGvtAyNjvffCwiTfWf94llIOSHLwesN/UWv9eGf7TzNwFf/3nf8+9/Go1Ul22yqLZ8SUgf9BHJ/kmSQlXZ4XBs1bm+T2OC+8pA06pssz6Hhu5Lzw1SRnJPlO53nnhTGmDPzIzBeSvDcDv6N5xww6Vzz3/4hSyv+qtf73rb/K8U24jU1La61Pd7b/LcletdaPDjWwlPLyJN9I8medp9Yk2b6zPSmuyr7kDDqmn8qg47kJ74U7B7+Pttii2SIGHdNHhjgvfHFDczvftD+U5CNxXnjJ61wVOa2U8j+SvD9dnhcGzXtPkh86L7y0DTqmu9dab+q8tMHzQinlw0luqrX+7/X+kdh5Yez5XJILaq2Pd46zY9wYB2Bsuq6U8urOTQbeleRfhhrU+ZeVBUk+X2v9defpZXn+YzP7Z+CKDS8RQxzTro7nBt4Ll5RS9i+lTMjAR+aWb8m1M7KGOKZdHc9SyvGllP+n83CXJI/HeeElrZTyV51vvJOBY3puujsvDJ73eJwXXtKGOKbf6vJ4vjvJ0Z2PyL2llHJVnBfGorkZiPreJG/JwCcvhjzGrraNDr+AewwppfTWWntKKYckuTDJfyW5qNY65N2eSimnJvmfef5EfWEGPma5JMk/JzkiyYxa6xNbfPGMiCGO6XcycNVto8dzA++Ff01yWQY+VvXjWuuZW3b1jKQhjumNGbjyttHj2Qm+f8rAz8U9nOS/Z+BcsiRJXwa+gXt3rfV/b8n1M3I6N7BakGRiBv4h7/NJFmf488Lgeadl4OYWzgsvUUMc0wuTXJpNOJ7rfa/xsjgvjFmdeDs6vidsinDjRTon9sOSLK61PjLa62HzOJ6MhFLK9kmOSvKzWuv9o70eNo/zAiPBeWHsc65oi3ADAABonJ9xAwAAaJxwAwAAaJxwAwAAaJxwAwAAaJxwAwAAaNz/DwZoRxhCCo58AAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 1080x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#性别分析\n", "male = LC[LC['性别'] == '男']\n", "female = LC[LC['性别'] == '女']\n", "sex = (male,female)\n", "sex_data = (male['借款金额'].sum(), female['借款金额'].sum())\n", "sex_idx = ('男', '女')\n", "plt.figure(figsize=(15, 6))\n", "plt.subplot(1,3,1)\n", "plt.pie(sex_data, labels=sex_idx, autopct='%.1f%%')\n", "\n", "#新老客户分析\n", "new = LC[LC['是否首标'] == '是']\n", "old = LC[LC['是否首标'] == '否']\n", "newold_data = (new['借款金额'].sum(), old['借款金额'].sum())\n", "newold_idx = ('新客户', '老客户')\n", "plt.subplot(1,3,2)\n", "plt.pie(newold_data, labels=newold_idx, autopct='%.1f%%')\n", "\n", "#学历分析\n", "ungraduate = LC[LC['学历认证'] == '未成功认证']\n", "graduate = LC[LC['学历认证'] == '成功认证']\n", "education_data = (ungraduate['借款金额'].sum(), graduate['借款金额'].sum())\n", "education_idx = ('大专以下', '大专及以上')\n", "plt.subplot(1,3,3)\n", "plt.pie(education_data, labels=education_idx, autopct='%.1f%%')\n", "plt.show()\n", "\n", "#年龄分析\n", "ageA = LC.loc[(LC['年龄'] >= 15) & (LC['年龄'] < 20)]\n", "ageB = LC.loc[(LC['年龄'] >= 20) & (LC['年龄'] < 25)]\n", "ageC = LC.loc[(LC['年龄'] >= 25) & (LC['年龄'] < 30)]\n", "ageD = LC.loc[(LC['年龄'] >= 30) & (LC['年龄'] < 35)]\n", "ageE = LC.loc[(LC['年龄'] >= 35) & (LC['年龄'] < 40)]\n", "ageF = LC.loc[LC['年龄'] >= 40]\n", "age = (ageA, ageB, ageC, ageD, ageE, ageF)\n", "age_total = 0\n", "age_percent =[]\n", "for i in age:\n", "    tmp = i['借款金额'].sum()\n", "    age_percent.append(tmp)\n", "    age_total  += tmp\n", "age_percent /= age_total\n", "age_idx = ['15-20', '20-25', '25-30', '30-35', '35-40', '40+']\n", "plt.figure(figsize=(15, 8))\n", "plt.bar(age_idx, age_percent)\n", "for (a, b) in zip(age_idx, age_percent):\n", "    plt.text(a, b+0.001, '%.2f%%' % (b * 100), ha='center', va='bottom', fontsize=10)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "falling-picking", "metadata": {}, "source": ["结论：\n", "\n", "1.男性客户的贡献的贷款金额占到了69%，可能的原因是男性更倾向于提前消费且贷款金额较大。\n", "\n", "2.非首标的金额占比达到66%，说明用户倾向于多次使用，产品粘性较高。\n", "\n", "3.大专以下学历的贷款金额更多，但是由于可能有很多用户并未认证学历，所以数据存在出入。\n", "\n", "4.年龄段在25-30岁之间的借款金额最多，而20-35岁的人群占比超过75%，是该产品的主力消费人群。"]}, {"cell_type": "markdown", "id": "revised-heading", "metadata": {}, "source": ["### 2.分析资金储备\n", "\n", "每日的借款金额大概多少？波动有多大？公司每日需要准备多少资金可以保证不会出现资金短缺？"]}, {"cell_type": "code", "execution_count": 13, "id": "provincial-resident", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x432 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["5204663.8 2203394.1435809094\n"]}], "source": ["from datetime import datetime\n", "\n", "#分析每日贷款金额的走势\n", "loan = LC[['借款成功日期', '借款金额']].copy()\n", "loan['借款日期'] = pd.to_datetime(loan['借款成功日期'])\n", "loan1 = loan.pivot_table(index='借款日期', aggfunc='sum').copy()\n", "plt.figure(figsize=(15, 6))\n", "plt.subplot(1,2,1)\n", "plt.plot(loan1)\n", "plt.xlabel('日期')\n", "plt.ylabel('借款金额')\n", "plt.title('每天贷款金额波动图')\n", "\n", "#分析每月贷款金额的走势\n", "loan['借款成功月份'] = [datetime.strftime(x, '%Y-%m') for x in loan['借款日期']]\n", "loan2 = loan.pivot_table(index='借款成功月份', aggfunc='sum').copy()\n", "plt.subplot(1,2,2)\n", "plt.plot(loan2)\n", "plt.xlabel('月份')\n", "plt.xticks(['2015-01','2015-07','2016-01','2016-07','2017-01'])\n", "plt.ylabel('借款金额')\n", "plt.title('每月贷款金额波动图')\n", "plt.show()\n", "\n", "# 对2017年1月的数据继续进行分析，并求出平均值和标准差\n", "loan3 = loan1.loc['2017-01']\n", "avg = loan3['借款金额'].mean()\n", "std = loan3['借款金额'].std()\n", "print(avg, std)"]}, {"cell_type": "markdown", "id": "thousand-enterprise", "metadata": {}, "source": ["结论：\n", "\n", "1.每日贷款金额呈现的是一个往上的趋势,但是每天的波动较大。\n", "\n", "2.每月贷款分析结论：从2015年1月到2017年1月，月度贷款金额呈现上升趋势，上升速度随着时间增快。\n", "\n", "3.2017年1月每日的借款金额达到5204664元，标准差为2203394，根据3σ原则，想使每日借款金额充足的概率达到99.9%，则每日公式账上需准备5204664+2203394×3=11814846元。"]}, {"cell_type": "markdown", "id": "tamil-heating", "metadata": {}, "source": ["### 3.分析逾期还款率（借款人的初始评级、借款类型、性别、年龄、借款金额等特征）\n", "\n", "逾期还款率 = 历史逾期还款期数/（历史逾期还款期数+历史正常还款期数）"]}, {"cell_type": "code", "execution_count": 42, "id": "general-yacht", "metadata": {}, "outputs": [], "source": ["#初始评级的数据划分\n", "level_idx = ('A','B','C','D','E','F')\n", "lev = []\n", "for i in level_idx:\n", "    temp = LC[LC['初始评级'] == i]\n", "    lev.append(temp)\n", "    \n", "#借款类型的数据划分\n", "kind_idx = ('电商', 'APP闪电','普通', '其他')\n", "kind = []\n", "for i in kind_idx:\n", "    temp = LC[LC['借款类型'] == i]\n", "    kind.append(temp)\n", "    \n", "#不同借款金额的数据划分  \n", "amount_idx = ('0-2000', '2000-3000', '3000-4000', '4000-5000', '5000-6000', '6000+')\n", "amountA = LC.loc[(LC['借款金额'] > 0) & (LC['借款金额'] < 2000)]\n", "amountB = LC.loc[(LC['借款金额'] >= 2000) & (LC['借款金额'] < 3000)]\n", "amountC = LC.loc[(LC['借款金额'] >= 3000) & (LC['借款金额'] < 4000)]\n", "amountD = LC.loc[(LC['借款金额'] >= 4000) & (LC['借款金额'] < 5000)]\n", "amountE = LC.loc[(LC['借款金额'] >= 5000) & (LC['借款金额'] < 6000)]\n", "amountF = LC.loc[(LC['借款金额'] >= 6000)]\n", "amount = (amountA, amountB, amountC, amountD,amountE,amountF)\n", "\n", "#逾期还款率的分析图\n", "def depayplot(i,idx,data,xlabel,title,index):\n", "    depay = []\n", "    for a in data:\n", "        a['逾期还款率'] = a['历史逾期还款期数']/(a['历史逾期还款期数']+a['历史正常还款期数'])*100\n", "        tmp = a[index].mean()\n", "        depay.append(tmp)\n", "    plt.subplot(2,3,i)\n", "    plt.bar(idx, depay)\n", "    for (a, b) in zip(idx, depay):\n", "        plt.text(a, b+0.001, '%.2f%%'% b, ha='center', va='bottom', fontsize=10)\n", "    plt.xlabel(xlabel)\n", "    plt.ylabel(index)\n", "    plt.title(title)"]}, {"cell_type": "code", "execution_count": 47, "id": "assured-pollution", "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\users\\<USER>\\envs\\jupytervir\\lib\\site-packages\\ipykernel_launcher.py:31: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x720 with 5 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(15, 10))\n", "index = '逾期还款率'\n", "# 根据初始评级对逾期还款率进行分析\n", "depayplot(1,level_idx,lev,'初始评级','不同初始评级客户逾期还款率',index)\n", "\n", "# 根据年龄对逾期还款率进行分析\n", "depayplot(2,age_idx,age,'年龄','不同年龄客户逾期还款率',index)\n", "\n", "# 根据借款类型对逾期还款率进行分析\n", "depayplot(3,kind_idx,kind,'借款类型','不同借款类型客户逾期还款率',index)\n", "\n", "# 根据性别对逾期还款率进行分析\n", "depayplot(4,sex_idx,sex,'性别','不同性别客户逾期还款率',index)\n", "\n", "# 根据借款金额对逾期还款率进行分析\n", "depayplot(5,amount_idx,amount,'借款金额','不同借款金额客户逾期还款率',index)\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "id": "literary-priority", "metadata": {}, "source": ["结论：\n", "\n", "1.初始评级对于贷款者的还款能力有比较好的预测作用，EF两级反转可能是因为样本数量较少，ABCD四个等级的平均逾期还款率都比较小，而EF两级明显增大，故公司对于这两类贷款者要谨慎对待。\n", "\n", "2.年龄对于逾期率的分布较为平均，25-30岁的年轻人可以重点关注。\n", "\n", "3.APP闪电的逾期还款率明显低于其他三种，故公司可以多考虑与“APP闪电”借款类型的合作。\n", "\n", "4.女性的逾期率高于男性，可能是由于生活中男性收入较女性高造成的。\n", "\n", "5.借款金额在2000以下的逾期还款率最低，2000-3000之间的最高。可以多考虑小额贷款降低逾期风险。"]}, {"cell_type": "markdown", "id": "sized-membership", "metadata": {}, "source": ["### 4.分析借款利率（借款人的初始评级、借款类型、性别、年龄、借款金额等特征）\n", "\n", "哪些客户群体更愿意接受较高的借款利率？"]}, {"cell_type": "code", "execution_count": 49, "id": "indie-airport", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\users\\<USER>\\envs\\jupytervir\\lib\\site-packages\\ipykernel_launcher.py:31: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x720 with 5 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#借款利率的分析图\n", "plt.figure(figsize=(15, 10))\n", "index1 = '借款利率'\n", "\n", "# 根据初始评级对借款利率进行分析\n", "depayplot(1,level_idx,lev,'初始评级','不同初始评级客户借款利率',index1)\n", "\n", "# 根据年龄对借款利率进行分析\n", "depayplot(2,age_idx,age,'年龄','不同年龄客户借款利率',index1)\n", "\n", "# 根据借款类型对借款利率进行分析\n", "depayplot(3,kind_idx,kind,'借款类型','不同借款类型客户借款利率',index1)\n", "\n", "# 根据性别对借款利率进行分析\n", "depayplot(4,sex_idx,sex,'性别','不同性别客户借款利率',index1)\n", "\n", "# 根据借款金额对借款利率进行分析\n", "depayplot(5,amount_idx,amount,'借款金额','不同借款金额客户借款利率',index1)\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "id": "developed-spirit", "metadata": {}, "source": ["结论：\n", "\n", "1.年龄对于借款利率的分布较为平均，差异性很小。\n", "\n", "2.初始评级的平均借款利率由小到大排列为ABCDFDE。\n", "\n", "3.电商的借款利率明显低于其他三种。\n", "\n", "4.女性所能接受的借款利率低于男性。\n", "\n", "5.借款金额对于借款利率的分布较为平均，差异性很小。"]}, {"cell_type": "markdown", "id": "violent-passing", "metadata": {}, "source": ["\n", "对于以上四个问题综合分析LC数据集：\n", "\n", "\n", "1、“男性”、“回头客”、“中青年”是拍拍贷用户群体的主要特征。\n", "\n", "2、每日公司账上需准备7,283,728元，方可保证出现当日出借金额不足的可能性小于0.1%。\n", "\n", "3、“初始评级”为D的群体，借款利率与E，F大致相当，但其逾期还款率却只有E，F群体的三分之一，相同的收益水平下风险大大降低，应多发展评级为D的客户或提高其贷款额度。\n", "\n", "4、通过“app闪电”贷款的逾期还款率远低于其他项，约为其他借款类型的三分之一至四分之一，而平均借款利率却和其他项相差不大，证明“app闪电”是该公司优质的合作方，其所引流来得客户质量很高，“拍拍贷”应与“app闪电”继续加深合作。\n", "\n", "5、“电商”中的贷款客户，收益率水平明显较低，逾期率却不低，在该群体中的贷款收益小，风险大。\n", "\n", "6、从性别上看，男性群体贷款利率较高，逾期风险较小，相较女性一定程度上是更为优质的客户，但并不明显。"]}, {"cell_type": "markdown", "id": "animal-saturn", "metadata": {}, "source": ["## 二、用户还款情况分析"]}, {"cell_type": "markdown", "id": "romance-grocery", "metadata": {}, "source": ["基于LCLP.csv 数据，分析用户的还款习惯（提前一次性全部还款 、部分提前还款以及逾期还款）的金额占比。\n", "\n", "将数据集按借款金额分组，并按还款状态和还款日期分成四种还款情况并进行统计：\n", "\n", "（1）一次性全部还款：其还款状态标记为‘已提前还清该标全部欠款’；\n", "\n", "（2）部分提前还款：其还款状态标记为’已正常还款’，并且当期的还款日期早于到期日期；\n", "\n", "（3）正常还款：其还款状态标记为’已正常还款’，并且当期的还款日期即为到期日期；\n", "\n", "（4）逾期还款：还款状态标记为‘未还款’，‘已逾期还款’或者‘已部分还款’。\n", "\n", "用百分堆积柱状图展示在不同年龄段（15 -20 ，20 -25 ，25 -30 ， 30-35 ，35 -40 ，40+ ）,不同性别（ 男、女），不同初始评级（A-F），不同借款类型、不同借款金额（1-1000，1000 -2000，2000-3000，3000+）、不同期数（1-24）的走势。"]}, {"cell_type": "code", "execution_count": 50, "id": "dietary-liberia", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Int64Index: 1023959 entries, 0 to 1603587\n", "Data columns (total 31 columns):\n", " #   Column      Non-Null Count    Dtype  \n", "---  ------      --------------    -----  \n", " 0   ListingId   1023959 non-null  int64  \n", " 1   借款金额        1023959 non-null  int64  \n", " 2   借款期限        1023959 non-null  int64  \n", " 3   借款利率        1023959 non-null  float64\n", " 4   借款成功日期      1023959 non-null  object \n", " 5   初始评级        1023959 non-null  object \n", " 6   借款类型        1023959 non-null  object \n", " 7   是否首标        1023959 non-null  object \n", " 8   年龄          1023959 non-null  int64  \n", " 9   性别          1023959 non-null  object \n", " 10  手机认证        1023959 non-null  object \n", " 11  户口认证        1023959 non-null  object \n", " 12  视频认证        1023959 non-null  object \n", " 13  学历认证        1023959 non-null  object \n", " 14  征信认证        1023959 non-null  object \n", " 15  淘宝认证        1023959 non-null  object \n", " 16  历史成功借款次数    1023959 non-null  int64  \n", " 17  历史成功借款金额    1023959 non-null  float64\n", " 18  总待还本金       1023959 non-null  float64\n", " 19  历史正常还款期数    1023959 non-null  int64  \n", " 20  历史逾期还款期数    1023959 non-null  int64  \n", " 21  逾期还款率       1023959 non-null  float64\n", " 22  期数          1023959 non-null  float64\n", " 23  还款状态        1023959 non-null  float64\n", " 24  应还本金        1023959 non-null  float64\n", " 25  应还利息        1023959 non-null  float64\n", " 26  剩余本金        1023959 non-null  float64\n", " 27  剩余利息        1023959 non-null  float64\n", " 28  到期日期        1023959 non-null  object \n", " 29  还款日期        1023959 non-null  object \n", " 30  recorddate  1023959 non-null  object \n", "dtypes: float64(10), int64(7), object(14)\n", "memory usage: 250.0+ MB\n"]}], "source": ["# 删除尚未到期的记录\n", "LP = LP[LP['到期日期'] <= LP['recorddate']]\n", "#LP.info()\n", "#LP.describe()\n", "# 将LC和LP两个表融合起来\n", "LCLP = pd.merge(LC, LP, how='left', on=['ListingId'])\n", "# 删除数据不全的记录\n", "LCLP = LCLP.dropna(how='any')\n", "LCLP.info()\n", "#LCLP.describe()"]}, {"cell_type": "code", "execution_count": 51, "id": "medical-suspension", "metadata": {}, "outputs": [], "source": ["#定义用户还款习惯分析可视化函数\n", "def repayhabit(group,num,idx,xlabel,color):\n", "    # 一次性全部还款\n", "    onetime = []\n", "    for a in group:\n", "        ot = a.loc[a['还款状态'] == 3]['应还本金'].sum(\n", "            ) + a.loc[a['还款状态'] == 3]['应还利息'].sum()\n", "        onetime.append(ot)\n", "    # 部分提前还款\n", "    partial = []\n", "    for a in group:\n", "        pa = a.loc[(a['还款状态'] == 1) & (a['还款日期'] < a['到期日期'])]['应还本金'].sum(\n", "            ) + a.loc[(a['还款状态'] == 1) & (a['还款日期'] < a['到期日期'])]['应还利息'].sum()\n", "        partial.append(pa)\n", "    # 逾期还款\n", "    pastdue = []\n", "    for a in group:\n", "        pas = a.loc[(a['还款状态'] == 2) | (a['还款状态'] == 4)|(a['还款状态'] == 0)]['应还本金'].sum() + \\\n", "            a.loc[(a['还款状态'] == 2) | (a['还款状态'] == 4)|(a['还款状态'] == 0)]['应还利息'].sum()\n", "        pastdue.append(pas)\n", "    # 正常还款\n", "    normal = []\n", "    for a in group:\n", "        nm = a.loc[(a['还款状态'] == 1) & (a['还款日期'] == a['到期日期'])]['应还本金'].sum(\n", "        ) + a.loc[(a['还款状态'] == 1) & (a['还款日期'] == a['到期日期'])]['应还利息'].sum()\n", "        normal.append(nm)\n", "    \n", "    tot = []\n", "    for i in range(num):\n", "        t = onetime[i]+partial[i]+pastdue[i]+normal[i]\n", "        tot.append(t)\n", "\n", "    print(tot)\n", "\n", "    temp = []\n", "    for i in range(num):\n", "        tp = (100 * onetime[i] / tot[i], 100 * partial[i] / tot[i],\n", "                100 * normal[i] / tot[i], 100 * pastdue[i] / tot[i])\n", "        temp.append(tp)\n", "        \n", "    df = pd.DataFrame(temp)\n", "    df.index = idx\n", "    df.columns = ('提前一次性', '部分提前', '正常', '逾期')\n", "    print(df)\n", "\n", "    df.plot(kind='bar', colormap=color, stacked=True)\n", "    plt.ylabel('还款金额')\n", "    plt.xlabel(xlabel)\n", "    plt.legend(loc='best')\n", "    plt.show()\n", "    "]}, {"cell_type": "markdown", "id": "limiting-switzerland", "metadata": {}, "source": ["### 1.分析不同借款金额用户的还款情况"]}, {"cell_type": "code", "execution_count": 52, "id": "electrical-insert", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[28456834.85, 69903191.44000001, 99595369.9, 72161874.03, 51083566.29000001, 269236628.2506]\n", "               提前一次性       部分提前         正常         逾期\n", "0-2000     10.204426  60.954742  16.233811  12.607020\n", "2000-3000  10.208217  54.959603  20.400835  14.431346\n", "3000-4000  14.874141  50.961604  21.902815  12.261440\n", "4000-5000  14.678874  50.698304  22.775784  11.847038\n", "5000-6000  15.703463  50.299053  23.239861  10.757622\n", "6000+      11.688029  39.376116  39.790049   9.145806\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["\n", "amountA = LCLP.loc[(LCLP['借款金额'] > 0) & (LCLP['借款金额'] < 2000)]\n", "amountB = LCLP.loc[(LCLP['借款金额'] >= 2000) & (LCLP['借款金额'] < 3000)]\n", "amountC = LCLP.loc[(LCLP['借款金额'] >= 3000) & (LCLP['借款金额'] < 4000)]\n", "amountD = LCLP.loc[(LCLP['借款金额'] >= 4000) & (LCLP['借款金额'] < 5000)]\n", "amountE = LCLP.loc[(LCLP['借款金额'] >= 5000) & (LCLP['借款金额'] < 6000)]\n", "amountF = LCLP.loc[(LCLP['借款金额'] >= 6000)]\n", "amountgroup = [amountA, amountB, amountC, amountD,amountE,amountF]\n", "\n", "repayhabit(amountgroup,6,amount_idx,'借款金额','Greys_r')"]}, {"cell_type": "markdown", "id": "flush-territory", "metadata": {}, "source": ["- 在根据借款金额分组中，得到结果如下：\n", "\n", "A组（0-2000）：总金额2.85千万。（1）一次性全部还款：占比 10.20%；（2）部分提前还款：占比60.95%；（3）正常还款：占比 16.23%； （4）逾期还款：占比 12.61%。\n", "\n", "B组（2000-3000）：总金额 7千万。（1）一次性全部还款：占比 10.21%；（2）部分提前还款：占比54.96%；（3）正常还款：占比 20.40%； （4）逾期还款：占比 14.43%。\n", "\n", "C组（3000-4000）：总金额 10千万。（1）一次性全部还款：占比 14.87%；（2）部分提前还款：占比50.96%；（3）正常还款：占比 21.90%； （4）逾期还款：占比 12.26%。\n", "\n", "D组（4000-5000）：总金额 7.22千万。（1）一次性全部还：占比 14.68%；（2）部分提前还款：占比50.70%；（3）正常还款：占比 22.78%； （4）逾期还款：占比 11.85%。\n", "\n", "E组（5000-6000）：总金额 5.11千万。（1）一次性全部还款：占比 15.70%；（2）部分提前还款：占比50.30%；（3）正常还款：占比 23.24%； （4）逾期还款：占比 10.76%。\n", "\n", "F组（6000+）：总金额 26.92千万。（1）一次性全部还款：占比 11.69%；（2）部分提前还款：占比39.38%；（3）正常还款：占比 39.79%； （4）逾期还款：占比 9.15%。\n", "\n", "- 从对借款金额分组的统计结果以及上图结果中可以看出：\n", "\n", "（1）借款总额6000元以上最多，3000-4000其次，说明3000-4000元的借款金额是最多的。\n", "\n", "（2）逾期风险在各金额组表现比较平均，其中2000-3000最大，6000+最小。\n", "\n", "（3）随着标的金额增加，部分提前还款的总金额比例在减少，正常还款的总金额比例在增加。"]}, {"cell_type": "markdown", "id": "individual-broad", "metadata": {}, "source": ["### 2.分析不同年龄段用户的还款情况"]}, {"cell_type": "code", "execution_count": 53, "id": "falling-boutique", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1325708.5400000003, 85978811.91999999, 203407279.9106, 149443150.8962, 79947743.0043, 70334770.4895]\n", "           提前一次性       部分提前         正常         逾期\n", "15-20  10.441107  62.896452  13.114767  13.547674\n", "20-25  13.428313  53.199581  20.048697  13.323409\n", "25-30  14.002901  47.665900  26.687108  11.644091\n", "30-35  12.363756  43.932650  33.824134   9.879460\n", "35-40  10.805522  44.388718  34.672769  10.132990\n", "40+    10.882495  42.854777  37.205296   9.057432\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["ageA = LCLP.loc[(LCLP['年龄'] >= 15) & (LCLP['年龄'] < 20)]\n", "ageB = LCLP.loc[(LCLP['年龄'] >= 20) & (LCLP['年龄'] < 25)]\n", "ageC = LCLP.loc[(LCLP['年龄'] >= 25) & (LCLP['年龄'] < 30)]\n", "ageD = LCLP.loc[(LCLP['年龄'] >= 30) & (LCLP['年龄'] < 35)]\n", "ageE = LCLP.loc[(LCLP['年龄'] >= 35) & (LCLP['年龄'] < 40)]\n", "ageF = LCLP.loc[LCLP['年龄'] >= 40]\n", "agegroup = [ageA, ageB, ageC, ageD, ageE, ageF]\n", "\n", "repayhabit(agegroup,6,age_idx,'年龄','Reds_r')"]}, {"cell_type": "markdown", "id": "engaged-winner", "metadata": {}, "source": ["- 在年龄分组中，得到结果如下：\n", "\n", "A组（15-20岁）：总金额0.13千万。（1）一次性全部还款：占比 10.44%；（2）部分提前还款：占比62.90%；（3）正常还款：占比 13.11%； （4）逾期还款：占比 13.55%。\n", "\n", "B组（20-25岁）：总金额 8.60千万。（1）一次性全部还款：占比 13.43%；（2）部分提前还款：占比53.2%；（3）正常还款：占比 20.05%； （4）逾期还款：占比 13.32%。\n", "\n", "C组（25-30岁）：总金额 20.34千万。（1）一次性全部还款：占比 14.00%；（2）部分提前还款：占比47.67%；（3）正常还款：占比 26.69%； （4）逾期还款：占比 11.64%。\n", "\n", "D组（30-35岁）：总金额 14.94千万。（1）一次性全部还款：占比 12.36%；（2）部分提前还款：占比43.92%；（3）正常还款：占比 33.82%； （4）逾期还款：占比 9.88%。\n", "\n", "E组（35-40岁）：总金额 8.00千万。（1）一次性全部还款：占比 10.81%；（2）部分提前还款：占比44.39%；（3）正常还款：占比 34.67%； （4）逾期还款：占比 10.13%。\n", "\n", "F组（40岁+）：总金额 7.03千万。（1）一次性全部还款：占比 10.88%；（2）部分提前还款：占比42.85%；（3）正常还款：占比 37.21%； （4）逾期还款：占比 9.06%。\n", "\n", "- 从对年龄分组的统计结果以及上图结果中可以看出：\n", "\n", "（1）拍拍贷的客户群体中25-30岁年龄组的贷款金额最高，15-20岁最低；\n", "\n", "（2）各年龄组的还款习惯大体一致，从金额上来说，部分提前还款和正常还款是最常用的方式；\n", "\n", "（3）逾期还款风险最高的年龄组为15-20岁组；\n", "\n", "（4）25-30岁年龄组一次性提前还款的金额占比最高。"]}, {"cell_type": "markdown", "id": "civic-newfoundland", "metadata": {}, "source": ["### 3.分析不同性别用户的还款情况"]}, {"cell_type": "code", "execution_count": 54, "id": "accessory-tamil", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[431899402.1953, 158538062.5653]\n", "       提前一次性       部分提前         正常         逾期\n", "男  13.159444  45.775236  30.093425  10.971895\n", "女  11.417458  48.638828  29.114713  10.829001\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["\n", "male = LCLP.loc[LCLP['性别'] == \"男\"]\n", "female = LCLP.loc[LCLP['性别'] == \"女\"]\n", "sexgroup = (male,female)\n", "\n", "repayhabit(sexgroup,2,sex_idx,'性别','Greens_r')"]}, {"cell_type": "markdown", "id": "comparable-catalog", "metadata": {}, "source": ["- 在男女性别组中，得到结果如下：\n", "\n", "男性：总还款金额 43.19千万。（1）一次性全部还款占比 13.16%；（2）部分提前还款占比45.78%；（3）正常还款占比 30.09%； （4）逾期还款占比10.97%。\n", "\n", "女性：总还款金额 15.85千万。（1）一次性全部还款占比 11.42%；（2）部分提前还款占比48.64%；（3）正常还款占比29.11%； （4）逾期还款占比10.83%。\n", "\n", "- 从对男女性别组的统计结果以及上图结果中可以看出：\n", "\n", "（1）拍拍贷男性客户的贷款金额约为女性客户的2.7倍；\n", "\n", "（2）男性及女性的还款习惯大体上比较一致，从金额上来说，部分提前还款>正常还款>一次性提前还款>逾期还款；\n", "\n", "（3）男性客户一次性提前还款的金额占比较女性为高；\n", "\n", "（4）女性逾期还款的风险略低于男性；\n", "\n", "（5）女性部分提前还款的金额占比略大于男性。"]}, {"cell_type": "markdown", "id": "broke-situation", "metadata": {}, "source": ["### 4.分析不同初始评级客户的还款情况"]}, {"cell_type": "code", "execution_count": 55, "id": "peaceful-chair", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[24260113.047399998, 129789781.91, 292672443.2151, 131419854.39039999, 10771732.247699998, 1523539.95]\n", "       提前一次性       部分提前         正常         逾期\n", "A  10.951641  42.540019  39.727788   6.780552\n", "B   7.686131  37.447042  47.651688   7.215139\n", "C  14.192953  49.919494  24.995052  10.892502\n", "D  14.592841  49.269359  21.846049  14.291750\n", "E  13.213394  40.965391  22.906776  22.914440\n", "F  10.752586  41.241621  20.679682  27.326111\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAEBCAYAAABxK3LCAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAg60lEQVR4nO3deXwV5dn/8c8VsrFEQUCUuoAVeVoVRKkIYqVWFgFBKSACFlnEDdfaQhVEpAW0qGwiov6qiKI+/LBFQNlBcakGq7gU6wKUpQgBJDEaQpLr+WMOMSAhCck5hzDf9+uVF3Nm5py5JsB8z8zc9z3m7oiISPgkxLsAERGJDwWAiEhIKQBEREJKASAiElIKABGRkFIAiIiEVGK8CyiLOnXqeIMGDeJdhohIpbJ69eoMd6974PxKFQANGjQgPT093mWIiFQqZrbhYPN1CUhEJKQUACIiIaUAEBEJqUp1D0BEjh579+5l06ZN5OTkxLuUo0ZqaionnXQSSUlJpVpfASAicbFp0ybS0tJo0KABZhbvcio9d2fHjh1s2rSJhg0bluo9ugQkInGRk5ND7dq1dfCvIGZG7dq1y3RGFdUAMLN6ZvZGZDrJzOaZ2VtmNqC4eSISHjr4V6yy/j6jFgBmVgt4BqgemXULkO7urYDOZpZWzDwRkSNKbm4ua9asKXy9efNmvvzyy3J/7tatW3nzzTcPuiw/P5+srKxyb+NQonkPIB+4Cvh75HUbYFhk+i2geTHzlhf9EDMbDAwGOOWUUw6rkJy8w3rbYUuN4Z2Vo3nfQPtX0Y6k/XOHgiLPo6p+7pAK3fb3/5xS4jrLli1jyZIljBkzhnbt2pGbm1u4LDk5mUWLFgFBAPTv35+lS5dSs2ZNxo4dS/v27Wl42k/LVFNWVhYPP/QQI++7D4BnnplBamoqLVtd+KN1v/xqHffeO4Lnn5/Fli1bqFGjBj26/4bZs2dz7LHHlmm7xYnaPwd3z4T9TkmqA5sj05lAvWLmHfg504HpAM2bN9fjy0SkQuTl5ZGYmEhycjL5+fkkJSVx7rnnFi7/+OOPGTt2LIsXLyYhIYFq1arRvXt3AN59913Wrl3LxIkTadPmV9x9zz2l2mZaWhobN27kySefZNCgQcya9Typqam8/PIcAGrWqsWcOS8DQYsegC1btjB50iQ6dupESkoKKSkpFfY7iOX3gW+BqsBuoEbk9cHmiYhE3Zw5c3j44YfZsWMHCQkJFBQU0Ldv38LlQ4cO5Y477qBTp05kZmYCkJiYyAUXXABAZmYm776XTsuWLcu03YmTJnHDDddzyimncOGFrRl1//1Uq1aNL774ggceGAfA22+/zStz55KVlcXQoX9g586drH5/NWs+/JAuXbqQnZ3N8uXLSU5OLtfvIJatgFYDrSPTTYH1xcwTEYm6nj17Mn78ePr06cPw4cPp0KEDU6ZMKfxp164dqamp3H333Xz11Vds3bqVG2+8kW3bttG/f3927NjBjBnPULVqVfLz88nPzy92W/n5+RQUFABQvXp1nn12JsuWLmXkfffxxPTpvPDCC6xbt47evfsAsHbtWurVq0edOnU4/xfnk5+fz6JFizn//POZO3cub775ZrkP/hDbM4BngAVmdhHwc+AfBJd/DpwnIhIT69atY+bMmWRmZvLBBx9QrVo1cnJySE1NZf369aSlpZGYmMiKFStITk5mz549JCQkkJWVhZkVXuIeO2YM48aNLfagnJubyyMTJnLdddcBcN2gQUyeMoXU1FS2bdtGzVq1uPzyywvXT05O5tzzzuODDz/g8i5daHTGGbj7fp9XKQLA3dtE/txgZm0JvvHf6+75wMHmiYhE3ZQpU1i0aBG9evXiT3/6E7NmzaJr16506dKFefPmFd4jmDt3LiNHjuTYY4+lW7duB/2s4SNGMHzEiFJtd+nSpUBwjd/defvtt/jPxv9w/fXXF67Tp08fNm3aBASjIK9YsYIOHdrz4Qcf0KVLFxITE1mwYEE5fwMx7gns7luAl0qaJyISbUOGDOGcc85hyZIlAEybNo1evXqxbds2OnXqxKmnnsrUqVMBmDBhAikpKWzfvp28vDzy8g6v+VZ2djYjht/D3+e+AsAD48Zx9dW9KSgo4L6RI7lv1KiDvu/aa6/l2muvpcvlnZk9e3bhDeLy0lAQInJEyH6/5GabJUk4zH5ln3zyCenp6UyaNInatWszf/78wmUFBQWMHDmSmjVr8t5773HCCSfwt7/9jXXr1pV5OwsWLKBXr6vJy8vj2mv7cUzaMUyaPBmAW4YMoVu3Kxk//iFOO+003L3wp6CggCpVquxXE0BCQvlu42ooCBEJpa1bt3LTTTdRq1YtBg0axMqVK9m1axcbNmygWbNmnHXWWTz11FN8+23QOHHUqFGceOKJAHz//ff06dOHVgdpv38oPXr04NbbbmP16tV06HBZ4cEfYPKUKfTo0bOw89eePXvYs2cPa9as4bIO7bnssg7k5eVxxRVX0KFDBxYvXlzu34EVvbFwpGvevLkfzhPBjubONkfzvoH2r6IdSfv31b//xf/87GcVur2yngGsX7+eBg0asHnzZn7yk5+UeXsFMT58lmb//vWvf/GzA36vZrba3Zv/6PMqrDIRkUpm3zPGD+fgfzRQAIiIhJQCQEQkpBQAIiIhFYpmoPM++W9Mt9e96Ykx3Z6IHL7du3eTkpJSYW3rSyM3N5e1a9fSpEkTIBheOicnh5/+tGyji5ZXKALgaKZwk6NFqwdWVujnvTPs4lKtt3jxYlatWsWECRMAePLJJwufqtW1a1dOPvlkAJ5++mm6d+/OqlWrMDPat2+/3+csW7aMpUuW8OcxY2jfvh17iwwtnZSczMKFiwpf5+bmMnBAfxYvCYaXHjd2LO3at1cAiIjEwo4dO7jyyitJSUkhJyeHJk2acNdddzF16lQefPBBhgwZQp8+weBsGzZsYPLkyfTr149mzZrRuXNnLr74YpJTgrOGgw4t3azI0NKffAzAuLFjWbLkh+Gle/b4YXjpzz5by+RJZRteurwUACISSrVr16Z///7s3r2bgQMH8stf/pJrrrmGGTNm0Lx5cxo2bEitWrWAYNiIMWPGYGbUq1ePvn370qdPH56f9QJJSUnMmTOHCY/sP7R0nyJDSw8bNhSA2++4g46HGF46Pb3sw0uXhwJARELps88+Y/fu3Tz77LN8/vnnNGrUiFdeCcbomTdvHl26dAHgkUceoVatWrRr167wvbfddhubN2/motYXMv2JJ+nZsyf169dn6ZIl3DN8OFMmT+bRKT8MbdGubfDe1NRU7rnnbnr06Em1atUYO+bPvPraQoYNG8qIEfcyY8YzXHLJJTH7HSgARCSU0tLSaNKkCQ899FDhvBo1agDwi1/8ggEDBtCxY0eWLVvGGWecwVlnnUVGRgYNGzYsHPv/ppuHkJ2dDQRDSz/3XDC09IcffkDVatXYk5NDSmoq6zesp0ZaGgMGDCAxMZGVJQwvHSsKABEJpfr16/Poo4/y5ptvkpiYSEZGBv/4R/BIksaNG9O7d29efPHFwrOCCy+8kI8//ph7772XDRs2cOedd9KvXz8AHp0yhUWLF3HVVb0YXWRo6Su6duGVV34YWnqfeyPDS3f/zcGHl44V9QMQkdDatm0b06ZNY8mSJdSpU4ekpCQA3n//fT766CN2795duO7MmTMLW/5s2bKlsHUQwM1DhvD73/+h8CA//fFpVK1alW3bttG5cyduu/XW/bY7ccIEHhg3rtzDS5eXzgBE5Ijw1tDSNds8lLIOBpeQkMCAAQOoVq0a//znPwuHX96yZQtnnnkmgwYNAmDSpEkkJibSokULAP7973/vFwBF7RtaenJkaOl58+bvt7ygoIB7I8NLp6cHw0u//PLhDS9dXgoAOaKpn4NEU2pqKjNmzOD000/nN7/5DXv37iU3N5fOnTsD4O5ceeWV1K5dm5kzZwLBsNCLFy9mxowZhZ+zdetWhtx8EwMGDGTwdYNYvmIl8+fNY8OGDZx3bjP27t3LbbffwcCBA8mODC99/6hRnFBkeOlr+vah37X9Y7r/oRgOevaHR+9B5GjeNzj690/DQcd3OOjSyM7Opnr16gddVnQ46PIOLV0aGg5aRCSGijv4H6gyDi2tABARCSkFgIhISCkARERCSq2AROSIMGdNxd7wL80N/QM7aBW37Pe//z2jR49mzpw5ZGRkMGTIEAYPHsxj0x6nSpUqFVp3LCkAROJIzVzj6+abb2bt2rWYGd988w07d+7ktNNOA+DUU09l6tSpLF++nF27djFx4kR27dpFVlYWo0ePJisri1dffZUWLVpQt27dOO/J4VEAiEhoPf7444XTK1as4LXXXmPcuHGF87766isefvhhbj2gJy9A06ZNeeGFWVStWpVf//rXMam3oikARCTUbr31ViZNmvSj+fvG9b/44ot57LHH2Lx5837LzYy7fv8HjjnmmFiVWuEUACISaitWrCicfv7553nnnXdwdy699FJGjBjByJEj6dixI/fdd99+75syZQrXXHNNbIutYAoAEQm1hIQfGkP27t17v0tA+9x///1kZGSwatUqMjMz6dixI6NHj45lmVGhABAROYQRI0Ywf/58jjvuOLZt28bevXv59NNPycvLo2atWsyZ83K8SzxsCgAROSJ0a1L+FkplGQsoPz9/v2//B8rNzSUpKYkqVarw4IMPcumllzJ79mwyMjK44YYb2L59OzfdfHO5a44nBYCIhNKcOXOYNGkSNWvWpE2bNoXz903v2bOH5557jkaNGnH88ccDQd+AvXv38sYbbzBy5Ehuv+POOFRecRQAIhJKPXr0oEePHiWut69fAECvXr0Kp5ctW7bfaKCVUcyGgjCzWma2wMzeMLNpkXlPmdlbZjY8VnWIiEgglmMBXQPMdPeLgDQz+wNQxd1bAfXNrFEMaxERCb1YBsAOoLGZ1QROBhoAL0WWLQNax7AWEZHQi2UArAIaAbcCa4EUYF/Xukyg3sHeZGaDzSzdzNK3b98ek0JFJJwKCgriXUJMxTIAxgA3uPv9BAHQG6gaWVajuFrcfbq7N3f35pV1wCURKVlufvl/cvJ++CmNXbt2FU5nZ2fTpEkTFi1atN86I0aMYOHChezdu5dmzZoBcOyxx9KmTRtOa9iAuXPnVtjvINZiGQDVgLPNrArQAhjHD5d9mgLrY1iLiIRcVlYWXbt2ZePGjXz77bf069ePiRMn8sYbbzBs2DDWrVvH0qVLefrppxk6dCht27bliy++4IknnqBx48asWLGCfv2uJTk5Od67cthiGQBjgenAbuA44BHgGjN7GOgJzI9hLSIScmlpaTz++OOkp6fTrVs3Lr74YmbPns3XX3/NRRddxJgxY9i8eTPXX389EyZMYMWKFZx55plcd911h+xAVpnEbC/c/V13P9Pda7h7W3fPBNoA7wC/cvfdsapFRGThwoXceeedVK9encTERE4++WS2b99OixYt+O6778jPz+e3v/0tALfffvt+ncW+/PJL2rRpwzPPPB2f4itIXGPM3Xe5+0vuvjWedYhI+LRv356uXbuSlJREZmYma9euZd26dZgZZsa2bdsK123bti19+/YtfF23bl2mTZtGp06d41F6hVFPYBGJmkM98awh+XzzfW7h62pxupZuZuTn55ORkcGePXvIzc0lPz8fgMmTJzNr1izq1KnD559/zrp163j00UdJS0sjIyOD7777Li41VxQFgIiE3jHHHMP48eP56KOPuOqqq8jOzmb06NHccsstJCUl0bJlS7Kysti5cyf5+flccskltG7dmsWLl8S79HJRAIjIEeG73NySVyrBcdVKfxbh7ixfvpyFCxfStm1b1q9fT/v27alVqxYrV65k3LhxLFu2jNdee43BgweTl5fHoEGD+Oijj3jhhRfIzs5m48aNtLrwwnLXHS8KABEJpXnz5vHzn/+cq6++mvHjx9OvXz8AZs+ezffff09mZib9+vXj6aefJiEhgeTkZEaNGsXChQtp3Lgx33zzDVWrVuWCCy6I854cPgWAiITS5ZdfTufOnTEzpk+fXqr3NGzYkBtuuAGAmjVrMnnKlGiWGHVHR2NWEal0nOAyTDyZleEJMpVAWX+fCgARiYtcqpD1za64h8DRwt3ZsWMHqamppX6PLgGJSFxs9TTI2EVyRgYV9T386+QqFfRJpRPr7CrphCU1NZWTTjqp1J+nABCRuCiwKmyhZoV+Zveflf+5wmVR2kHnKkpqBR+xFQAiIofpUB3doqF704oNON0DEBEJKQWAiEhIKQBEREJKASAiElIKABGRkFIAiIiElAJARCSkFAAiIiGlABARCSkFgIhISCkARERCSgEgIhJSCgARkZBSAIiIhJQCQEQkpBQAIiIhpQfCyBFt/Kv/jun2KvqBGyJHMgWASBwp4CSedAlIRCSkFAAiIiGlABARCSkFgIhISMU8AMxsqpldHpl+yszeMrPhsa5DRCTsig0AM0sws7aHWJ5iZo3LsjEzuwg4wd1fMbNuQBV3bwXUN7NGZfksEREpn0M1A3Xgd8BiM7sauAXYFFn2KXAusAYo1bd3M0sCngAWmFlXoA3wUmTxMqA18HkZ6xcRkcNU7BmAuztwrplNBToCjwNfExz8vwKed/eyXLr5beS9DwLnAzcDmyPLMoF6B3uTmQ02s3QzS9++fXsZNiciIodS0j2ANcBfgHVAXWA+wbf0s4FrzKwsvUqaAdPdfSswE3gdqBpZVqO4Wtx9urs3d/fmdevWLcPmRETkUA51DyAJeA9YDywATgHaE3x730xwSehFMyttb+IvgNMi082BBgSXfQCaRrYjIiIxUuzB2933An80s6+B3sAqYDbwAvAOwTf6j9w9r5Tbegr4f2bWC0giuAcw18zqA5cBFxzuToiISNkVGwCRyzu/Aj5x96Vmth64Afg70JkgAMaVdkPungX0OGAbbYC2wIPuvruMtYuISDkc6vLN6cCZAGb2V4L7ANnAScAnBJeE5pVn4+6+ix9aAomISAwdqhXQG+5+D3Ai8HZk9ktACsG1+w8owxmAiIgcWUpzA3ckwU3fjgTX6VcBye7+WTQLExGR6CoxANz9JQAzu97dHy+6zMzOdvePolWciIhET6nGAjKzVsAVkenLiix6NAo1iYhIDJS2Df99wM7I9DAzuwp4GciNRlEiIhJ9JZ4BmNldwBKCnsAQHPTvJBgL6OzolSYiItF0yDMAM/szkOPuo82sg5n9FjiBoB/AV8B/Y1CjiFRSeubxke1QHcHSgCYE4/8Uzo78VCr6Rygi8mOH6geQ5e6XAyeZ2dDIvGcIvvW/C/Q51PtFROTIVuIBPDLkc11+uAmcDFxO8KyAbdErTUREoqm0rYCGASsj4wPd5O6fAJiZBugXEamkShUA7p5nZjcC2939v0XmXx21ykREjnCV/f5iaZqBNjSz64AdRYd+NrPUyNDOIiJSCZXmJm594EKg674ZZnY88DdKfwlJRESOMCX1A2gaWWcR8LmZPQCcDBwP3Onua6JfooiIRENJZwAXA2MJWv3kEgTBqwStf4ab2XHRLU9ERKKlpAB4Avgj8BxwpbsvBXa6e2+CIJgV5fpERCRKSrqGn0jw7N4LgUlm1hO43cyaEPQN6Bzd8kREJFpKOgMYRnDwP5HgElA2UJPgoe4ZkWUiIlIJlXQGsBI4juAxkPt8C6wmCIQHgBbRKU1ERKKppAB4G2gJnEEQAqlAPnAJUA24MqrViYhI1JR0Cagm8ALQ1t1fAdKBce7+O2ABMDC65YmISLSUFAAz3f0zdy8ws9bAS8CnAJFA+F20CxQRkego6RJQLoCZ1QZGAh3dvegAcNvNzNzdo1WgiIhER0kBkGxmkwgeCTkFaGlm+943F/hWB38RkcqpNGcAKwha+3wHPEPwRLBEgstHOdEsTkpW2UcjFJH4KSkAzN3nmNmrwKNAirv/BcDMjiHoDyAiIpVQSTeBlwO4+/fuPgA428wuiSy7nqCfgIiIVEIlnQG8aWYrgF8D/QkGgbvUzNoCdxA8NF5ERCqhYs8AzCwVGAJc7+75BA+BnwssJLgp/B4wysx0GUhEpBIq9gzA3XOA7kVmTXf31/e9MLPlQBd33xvF+kREJEpK/UQvd591wOsCgqeCiYhIJVSaR0KKiMhRKOYBYGb1zOyfkemnzOwtMxse6zpERMIuHmcA44GqZtYNqOLurYD6ZtYoDrWIiIRWTAMg0ocgG9hK8KSxlyKLlgGti3nPYDNLN7P07du3H2wVERE5DDELADNLBu4leMoYQHVgc2Q6E6h3sPe5+3R3b+7uzevWrRv9QkVEQiKWZwDDgEfd/ZvI62+BqpHpGjGuRUQk9ErdDLQCXApcYmY3A+cApwAbgXeApsBnMaxFRCT0YhYA7v7LfdOR4SW6AG+YWX3gMuCCWNUiIiJxuuzi7m3cPZPgRvA7wK/cfXc8ahERCatYXgL6EXffxQ8tgUREJIZ041VEJKQUACIiIaUAEBEJKQWAiEhIKQBEREJKASAiElIKABGRkFIAiIiElAJARCSkFAAiIiGlABARCSkFgIhISCkARERCSgEgIhJSCgARkZBSAIiIhJQCQEQkpBQAIiIhpQAQEQkpBYCISEgpAEREQkoBICISUgoAEZGQUgCIiISUAkBEJKQUACIiIaUAEBEJKQWAiEhIKQBEREJKASAiElIKABGRkFIAiIiEVMwCwMyONbNXzWyxmb1sZslm9pSZvWVmw2NVh4iIBGJ5BtAHeNjd2wJbgV5AFXdvBdQ3s0YxrEVEJPQSY7Uhd59a5GVdoC8wIfJ6GdAa+DxW9YiIhF3M7wGYWUugFrAR2ByZnQnUK2b9wWaWbmbp27dvj1GVIiJHv5gGgJkdB0wGBgDfAlUji2oUV4u7T3f35u7evG7durEpVEQkBGJ5EzgZeAn4o7tvAFYTXPYBaAqsj1UtIiIS2zOAgcB5wD1mtgIw4BozexjoCcyPYS0iIqEXy5vAjwGPFZ1nZnOBtsCD7r47VrWIiEgMA+Bg3H0XwWUhERGJMfUEFhEJKQWAiEhIKQBEREJKASAiElIKABGRkIprKyCRsPvwxf+N7QaHXRzb7ckRTQEgRzQdIEWiR5eARERCSmcAIhI1OoM7sukMQEQkpBQAIiIhpQAQEQkpBYCISEiF4iawbkSJSDRU9mOLzgBEREJKASAiElIKABGRkFIAiIiElAJARCSkQtEK6GhW2VshiEj86AxARCSkFAAiIiGlABARCSkFgIhISCkARERCSgEgIhJSCgARkZBSAIiIhJQCQEQkpBQAIiIhpQAQEQkpBYCISEgpAEREQiruAWBmT5nZW2Y2PN61iIiESVwDwMy6AVXcvRVQ38waxbMeEZEwMXeP38bNJgGvufsCM+sOpLn7Xw9YZzAwOPKyMfBZDEusA2TEcHuxdDTvG2j/KjvtX8U61d3rHjgz3g+EqQ5sjkxnAqcfuIK7Twemx7Kofcws3d2bx2Pb0XY07xto/yo77V9sxPsewLdA1ch0DeJfj4hIaMT7gLsaaB2Zbgqsj18pIiLhEu9LQH8D3jCz+sBlwAXxLedH4nLpKUaO5n0D7V9lp/2LgbjeBAYws1pAW+B1d98a12JEREIk7gEgIiLxEe97ACIiEicKABGRkFIAFMPMfmJmA83spXjXUhEs0N7MWptZFTO7x8zGmdnJ8a6tIphZNTO73cw6RV7fZWY3mVnVkt5bGZjZWZH9udvMbjCzM+JdU0UxsyZFpq3IdI/4VFSxzOymeNdQHAVAhJklm9mlZjbezD4APgZOAabGt7IK8zxwBXAbsJKgJ+J3kflHg2eBHODTyOuVQDVgVtwqqiBmNgh4FWhC0HmyKbDwaDlAAhOKTC8tMn1jjOuIlu77JsxsSjwLOVC8m4EeSXYAycCjwK+B2e4+Mr4lVah67n41gJmtcfc7ItPt4ltWhTnB3afte+Hu7wHvmdmVcaypogwGmrr7zn0zzKwmsAD433gVFSVW8iqV2s/jXUBRCoAfnAK0A9oDq4A6ZnY7sMzd18SzsApS08xaEvwHy4tMJ/BDT+zKbqmZLSM4KO4k6FneFkiPa1UVIwloXPTySERKPIqJghPMrDfBv816RafjW1aFKbp/+6YBcPe4noGrGWgxzOxsgjBo5+6V/luymf21uGXu3j+WtURLJNTaExw4MoE33X1ufKsqv6P9787Mij3TdvdRsawlGg6xf+7u98e0mAMoAEREQko3gUVEQkoBIKFmZof8P2BmKWZWo8hrM7Mf3Tszs6aRZQlmlnTAsuSi2zlamt5K5acAkLB7zcxqmdlJZtbIzE4v8pNM0PTygSLrXwY8fpDP6QHcF1n/NTNbZGbfmNkigiac/wNgZnWAZyJ9MV43s2Vm9pqZLTGzi6K5oyIHUgBIKEW+rScBfyJoTXMZ8AdgPjAEeBn4GXAO8LmZ7WuR0g8YbWZdIgMZ7nNv5M8PgasjDQfWRv68Hfg8svwW4G6gJ8EN623u3gHYhoZDlxhTAEhYnQK8ATwCvOzuTwCjgVXufjvBgfwc4FagFfC6mbWKLF9P0OlsKoCZJbl7QaTfSH1+3Pns/xM0bzwPaAHUAgZGlu2N/Fnf3TdGYT9FiqV+ABJK7r4BuMDM/gSkm1kjgr4D30RWqUFwFnAZwbf+lwnC4vRI1/6dQA0z6wa0NLMOwHx3H2ZmOWa275LPhcD77r7JzE4kCJYHgKsJeionmdkJwNlmNg/o4u4FsfgdiCgAJLQivaCHEjyYaAXB86m/iiyuQfDI0hpATWAj0BfY5ZG202aWAhS4+xwz+zvQ1syqEFxK2hT5nM3AKDOr4u7vmVltYLe7fxI5I+hEcFnoeILe5zr4S8woACSUIuPoXEfwWNIU4D8EjyedF1klATgRqEtwvX4J0BkYYWZfR9b5qbufWORjHbiJYMwlJ7iH8GRk2etm9iIwG1hiZvcCGwguF40iCKF3Kno/RQ5FHcEklMzsNOC/wCsEg3WdRHAwbubueWa2zN0vMbNzgdcJbuR+B1Rz9ycjn/EPd28RmW4NXAXs3DeGlJm94+4XFNlmMsG9h+MIwuVGoDfwIHAu0N7dt0d950UidAYgoeTuXwGYGe7+jZndR3BTNz/SVDM5supAoA/QgOD/yx1m1jeyrOhYNWcRjCV1qMHnzgPGEIx4eSrBZaccghE+CwiaiioAJGbUCkjCLs3MLiAYHnsawUH8LeBJM+sI5Lr73919IsEZwF/cvY27tyG4EbzPPKClu39aZF6NItO4+9vu/iuCEWe/Bv4BzAEeAy4Fhh5FQzxLJaBLQCKltK8HsLvnVeDnFRS98Ru5WZxfEZ8vUhIFgIhISOkSkIhISCkARERCSgEgIhJSCgARkZBSAIiIhNT/AdTxXywplm+iAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["levelgroup = []\n", "for i in level_idx:\n", "    l = LCLP[(LCLP['初始评级'] == i)]\n", "    levelgroup.append(l)\n", "    \n", "repayhabit(levelgroup,6,level_idx,'初始评级','Blues_r')"]}, {"cell_type": "markdown", "id": "organizational-tennis", "metadata": {}, "source": ["- 在初始评级分组中，得到结果如下：\n", "\n", "A级：总金额2.43千万。（1）一次性全部还款：占比 10.95%；（2）部分提前还款：占比42.54%；（3）正常还款：占比 39.73%； （4）逾期还款：占比 6.78%。\n", "\n", "B级：总金额 12.98千万。（1）一次性全部还款：占比 7.68%；（2）部分提前还款：占比37.45%；（3）正常还款：占比 47.65%； （4）逾期还款：占比 7.22%。\n", "\n", "C级：总金额 29.27千万。（1）一次性全部还款：占比 14.19%；（2）部分提前还款：占比49.92%；（3）正常还款：占比 25.00%； （4）逾期还款：占比 10.89%。\n", "\n", "D级：总金额 13.14千万。（1）一次性全部还款：占比 14.59%；（2）部分提前还款：占比49.27%；（3）正常还款：占比 21.85%； （4）逾期还款：占比 14.29%。\n", "\n", "E级：总金额 1.08千万。（1）一次性全部还款：占比 13.21%；（2）部分提前还款：占比40.97%；（3）正常还款：占比 22.91%； （4）逾期还款：占比 22.91%。\n", "\n", "F级：总金额 0.15千万。（1）一次性全部还款：占比 10.75%；（2）部分提前还款：占比41.24%；（3）正常还款：占比 20.68%； （4）逾期还款：占比 27.33%。\n", "\n", "- 从对初始评级分组的统计结果可以看出：\n", "\n", "（1）B级客户借款总额最多，占到了大约50%的金额。B、C、D级客户是借款的主力军。\n", "\n", "（2）提前一次性还款的占比相对比较平均，其中D级最大为14.59%。\n", "\n", "（3）逾期风险随着级别而呈总体增加趋势，F级客户的逾期占比达到了27.33%。\n", "\n", "（4）部分提前和正常还款还是占到了大多数。\n", "\n", "（5）总的来说，初始评级具有重要的参考意义。"]}, {"cell_type": "markdown", "id": "corresponding-strength", "metadata": {}, "source": ["### 5.分析不同借款类型客户的还款情况"]}, {"cell_type": "code", "execution_count": 57, "id": "impaired-electronics", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[85700890.47, 74452365.96, 234675993.36, 195608214.9706]\n", "           提前一次性       部分提前         正常         逾期\n", "电商      4.218635  26.927505  62.071671   6.782188\n", "APP闪电   8.959958  61.125398  18.677700  11.236944\n", "普通     17.162002  45.092948  26.095824  11.649226\n", "其他     12.461221  51.329790  24.430785  11.778204\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["kindgroup = []\n", "for i in kind_idx:\n", "    l = LCLP[(LCLP['借款类型'] == i)]\n", "    kindgroup.append(l)\n", "    \n", "repayhabit(kindgroup,4,kind_idx,'借款类型','Reds_r')"]}, {"cell_type": "markdown", "id": "vietnamese-fighter", "metadata": {}, "source": ["- 在借款类型分组中，得到结果如下：\n", "\n", "电商：总金额8.57千万。（1）一次性全部还款：占比 4.22%；（2）部分提前还款：占比26.93%；（3）正常还款：占比 62.07%； （4）逾期还款：占比 6.78%。\n", "\n", "APP闪电：总金额 7.45千万。（1）一次性全部还款：占比 8.96%；（2）部分提前还款：占比61.13%；（3）正常还款：占比 18.68%； （4）逾期还款：占比11.24%。\n", "\n", "普通：总金额 23.47千万。（1）一次性全部还款：占比 17.16%；（2）部分提前还款：占比45.09%；（3）正常还款：占比 26.10%； （4）逾期还款：占比 11.65%。\n", "\n", "其他：总金额 19.56千万。（1）一次性全部还款：占比 12.46%；（2）部分提前还款：占比51.33%；（3）正常还款：占比 24.43%； （4）逾期还款：占比 11.78%。\n", "\n", "- 从对借款类型分组的统计结果可以看出：\n", "\n", "（1）普通借款类型的借款金额总数最大，其次是其他，电商和APP闪电差不多。\n", "\n", "（2）逾期风险电商最低，为6.78%。其他三种类型差不多。\n", "\n", "（3）部分提前和正常还款还是占到了大多数。值得注意的是除了电商，其他三种类型的部分提前还款都占比很大。"]}, {"cell_type": "code", "execution_count": 59, "id": "bottom-newark", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[135981578.1922, 108408463.47999999, 93715601.03, 75294688.9507, 60648276.0339, 47917912.09030001, 19092666.1202, 15699880.5325, 12331986.870000001, 9339947.0008, 6687098.62, 4416541.7700000005, 301461.70999999996, 169327.15999999997, 143667.96000000002, 83483.42, 69138.15, 50308.28, 36507.18, 19895.030000000002, 17235.37, 7340.29, 4459.52, 0.0]\n", "        提前一次性       部分提前         正常         逾期\n", "1   11.445900  47.586534  33.902811   7.064756\n", "2   12.167250  50.653062  28.728319   8.451370\n", "3   15.843928  46.505031  28.396910   9.254131\n", "4   14.029461  44.266042  30.340452  11.364045\n", "5   12.805724  44.175306  30.414081  12.604889\n", "6    9.611442  44.158815  31.276022  14.953720\n", "7   15.717427  44.974685  22.785384  16.522504\n", "8   14.343482  44.269646  23.244959  18.141913\n", "9   11.168077  44.718088  23.378983  20.734852\n", "10  10.209341  44.175032  23.368664  22.246963\n", "11   7.335732  43.147448  24.866896  24.649924\n", "12   3.498742  46.299654  23.112021  27.089583\n", "13  40.902773  19.437918  24.870807  14.788502\n", "14  11.418682  31.512434  29.150120  27.918764\n", "15  16.023385  28.828836  24.818436  30.329344\n", "16   0.000000  29.785926  27.199592  43.014481\n", "17   0.000000  17.118002  31.699792  51.182205\n", "18   0.000000  20.567111  30.283941  49.148947\n", "19   0.000000  21.394969  23.491982  55.113049\n", "20   0.000000  23.093557  35.159937  41.746507\n", "21  22.619068  16.714292  34.597227  26.069414\n", "22   0.000000  72.794535  27.205465   0.000000\n", "23   0.000000   0.000000  83.207610  16.792390\n", "24        NaN        NaN        NaN        NaN\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\users\\<USER>\\envs\\jupytervir\\lib\\site-packages\\ipykernel_launcher.py:37: RuntimeWarning: invalid value encountered in double_scalars\n", "c:\\users\\<USER>\\envs\\jupytervir\\lib\\site-packages\\ipykernel_launcher.py:38: RuntimeWarning: invalid value encountered in double_scalars\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["term_idx = ('1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24')\n", "termgroup = []\n", "for i in range(1,25):\n", "    term = LCLP.loc[(LCLP['期数'] == i)]\n", "    termgroup.append(term)\n", "\n", "repayhabit(termgroup,24,term_idx,'期数','Reds_r')"]}, {"cell_type": "markdown", "id": "conventional-justice", "metadata": {}, "source": ["- 从对期数分组的统计结果可以看出：\n", "\n", "（1）借款金额是随着期数增加呈现出下降的趋势。\n", "\n", "（2）不同的还款行为在不同的借款期限下的表现差异比较大，部分提前还款和正常还款是最常用的方式；\n", "\n", "（3）逾期风险随着借款期限变长而呈总体增加趋势，期限为20个月的逾期金额占比为最高，达到了57.30%；\n", "\n", "（4）期限为13个月的提前一次性还款占比最高，达到了16.77%。\n", "\n", "（5）借款期限太长的样本数量太少，不能排除偶然性。"]}, {"cell_type": "markdown", "id": "organizational-insight", "metadata": {}, "source": ["## 三.计算金额催收回款率（催收回本金/所有逾期本金）"]}, {"cell_type": "markdown", "id": "cosmetic-visit", "metadata": {}, "source": ["在不同等级（A-F）、不同借款期数（1-24）和不同借款金额（0-2000，2000-3000，3000-4000，4000-5000，5000-6000，6000+）等，随逾期天数增加而呈现的走势。\n", "\n", "1）x轴为逾期天数，y轴为金额催收回款率，不同参数对应不同曲线；\n", "\n", "2）催收回款的定义为逾期90天之内的逾期还款。"]}, {"cell_type": "markdown", "id": "victorian-<PERSON><PERSON><PERSON>", "metadata": {}, "source": ["### 1.不同等级（A-F）随逾期天数催收还款率的走势"]}, {"cell_type": "code", "execution_count": 62, "id": "senior-elevation", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x576 with 6 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from datetime import datetime,timedelta\n", "\n", "#LCLP.info()\n", "LCLP['recorddate'] = pd.to_datetime(LCLP['recorddate'])\n", "LCLP['到期日期'] = pd.to_datetime(LCLP['到期日期'])\n", "LCLP['还款日期'] = pd.to_datetime(LCLP['还款日期'], errors='coerce')\n", "LCLP['lateday'] = LCLP['还款日期']-LCLP['到期日期']\n", "\n", "depay = LCLP[LCLP['lateday']><PERSON>elta(days=0)]\n", "\n", "#不同等级（A-F）随逾期天数催收还款率的走势\n", "df = depay.groupby(['初始评级','lateday'])['应还本金'].sum()\n", "df1 = df.to_frame().pivot_table(index='lateday',columns = '初始评级', values ='应还本金')\n", "tmp = df1.fillna(0)\n", "df2 = depay.groupby(['初始评级'])['应还本金'].sum()\n", "tmp_1 = tmp[tmp.index <= timedelta(days=90)]\n", "tmp_1 = tmp_1/df2\n", "\n", "plt.figure(figsize=(15, 8))\n", "for i in range(6):\n", "    plt.subplot(2,3,i+1)\n", "    plt.plot(range(90),tmp_1[level_idx[i]])\n", "    plt.title(level_idx[i])\n", "plt.show()"]}, {"cell_type": "markdown", "id": "conditional-lafayette", "metadata": {}, "source": ["不同等级（A-F）随逾期天数催收还款率的走势大致相同，也就是大部分人都在逾期十天之内还款，说明他们有可能忘记还款；特别是在4、5天的还款的人数和金额最多。"]}, {"cell_type": "markdown", "id": "unlimited-framework", "metadata": {}, "source": ["### 2.不同借款期数随逾期天数催收还款率的走势"]}, {"cell_type": "code", "execution_count": 75, "id": "choice-familiar", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 7.,  9., 10., 11., 12.,  4.,  8.,  2.,  3.,  6.,  1.,  5., 13.,\n", "       14., 15., 16., 17., 18., 19., 20., 21., 23.])"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["depay['期数'].unique()"]}, {"cell_type": "code", "execution_count": 73, "id": "beautiful-terrorist", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1080x864 with 21 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#不同借款期数随逾期天数催收还款率的走势\n", "df = depay.groupby(['期数','lateday'])['应还本金'].sum()\n", "df1 = df.to_frame().pivot_table(index='lateday',columns = '期数', values ='应还本金')\n", "tmp = df1.fillna(0)\n", "df2 = depay.groupby(['期数'])['应还本金'].sum()\n", "tmp_1 = tmp[tmp.index <= timedelta(days=90)]\n", "tmp_1 = tmp_1/df2\n", "\n", "plt.figure(figsize=(15, 12))\n", "for i in range(1,22):\n", "    plt.subplot(4,6,i)\n", "    plt.plot(range(90),tmp_1[i])\n", "    plt.xticks([0,30,60,90])\n", "    plt.title(str(i))\n", "plt.show()"]}, {"cell_type": "markdown", "id": "fifty-shopping", "metadata": {}, "source": ["不同借款期数的金额收回款率随逾期天数的趋势没有明显的规律。\n", "\n", "在12期及之前大部分人都在逾期十天之内还款，特别是在4、5天的还款的人数和金额最多。 但是13之后呈现出10天之后回款率的依然很大。也有可能是因为数据量导致异常值凸显，但是也说明了借款期数长的回款率不够稳定。"]}, {"cell_type": "markdown", "id": "minor-mon<PERSON>e", "metadata": {}, "source": ["### 3.不同借款金额随逾期天数催收还款率的走势"]}, {"cell_type": "code", "execution_count": null, "id": "known-singer", "metadata": {}, "outputs": [], "source": ["#不同借款金额随逾期天数催收还款率的走势\n", "def function(a):\n", "    if a>0 and a<2000:\n", "        return '0-2000'\n", "    elif a>=2000 and a<3000:\n", "        return '2000-3000'\n", "    elif a>=3000 and a<4000:\n", "        return '3000-4000'\n", "    elif a>=4000 and a<5000:\n", "        return '4000-5000'\n", "    elif a>=5000 and a<6000:\n", "        return '5000-6000'\n", "    else:\n", "        return '6000+'\n", "\n", "depay['金额类型'] = depay.apply(lambda x:function(x['借款金额']),axis=1)\n", "\n", "df = depay.groupby(['金额类型','lateday'])['应还本金'].sum().copy()\n", "df1 = df.to_frame().pivot_table(index='lateday',columns = '金额类型', values ='应还本金')\n", "tmp = df1.fillna(0)\n", "df2 = depay.groupby(['金额类型'])['应还本金'].sum()\n", "tmp_1 = tmp[tmp.index <= timedelta(days=90)]\n", "tmp_1 = tmp_1/df2\n", "\n", "plt.figure(figsize=(15, 8))\n", "for i in range(6):\n", "    plt.subplot(2,3,i+1)\n", "    plt.plot(range(90),tmp_1[amount_idx[i]])\n", "    plt.xticks([0,30,60,90])\n", "    plt.title(amount_idx[i])\n", "plt.show()"]}, {"cell_type": "markdown", "id": "entire-infection", "metadata": {}, "source": ["对不同借款金额对于进入催收回款率影响较大，借款金额越多，逾期的可能性就越大。"]}, {"cell_type": "markdown", "id": "moved-petroleum", "metadata": {}, "source": ["## 四.累积收益曲线"]}, {"cell_type": "markdown", "id": "inner-participation", "metadata": {}, "source": ["LCIS数据提供了该客户投资的从2015年1月1日起成交的所有标。包括投标记录和还款状况。请计算并画出该投资人从2016年9月开始到2017年2月，每月月底的累计收益曲线。\n", "\n", "调用draw()函数，可以对任一用户的数据画出累积收益曲线"]}, {"cell_type": "code", "execution_count": 76, "id": "rational-plumbing", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from datetime import datetime,timedelta\n", "LCIS = pd.read_csv(\"./data/LCIS.csv\",encoding = 'utf-8')\n", "\n", "# 计算从2016年9月至2017年2月所有的利息\n", "def getinterest(df):\n", "    df_1 = df[['ListingId','标当前状态','上次还款日期','上次还款利息']]\n", "    df_1 = df_1[(df_1['标当前状态'] =='正常还款中') | (df_1['标当前状态'] =='已还清')]\n", "    df_1['上次还款日期'] = df_1['上次还款日期'].where(df_1['上次还款日期'].notnull(),'2016/08/31')\n", "    df_1['上次还款日期'] = pd.to_datetime(df_1['上次还款日期'], errors='coerce')\n", "    df_1 = df_1[df_1['上次还款日期']>='2016-09-01'].drop_duplicates()\n", "    df_1_1 = df_1.groupby(['上次还款日期'])['上次还款利息'].sum().to_frame().reset_index()\n", "    return df_1_1\n", "\n", "# 计算从2016年9月至2017年2月所有的亏损\n", "def getloss(df):    \n", "    df_2 = df[['ListingId', '待还本金', '标当前状态', '上次还款日期', '下次计划还款日期', 'recorddate']]\n", "    df_2 = df_2[(df_2['标当前状态']=='逾期中')]\n", "    df_2['下次计划还款日期'] = pd.to_datetime(df_2['下次计划还款日期'], errors='coerce')\n", "    df_2['recorddate'] = pd.to_datetime(df_2['recorddate'], errors='coerce')\n", "    \n", "    # 往回看90天到2016-06-03\n", "    df_2 = df_2[df_2['下次计划还款日期']>='2016-06-03']\n", "    df_2['delay'] = df_2.apply(lambda x: (x['recorddate'] - x['下次计划还款日期']).days, axis = 1)\n", "    df_2_1 = df_2[df_2['delay']>=90].sort_values(['ListingId','delay'])\n", "    df_2_1['date'] = df_2['下次计划还款日期'] + <PERSON><PERSON><PERSON>(days=90)\n", "    df_2_2 = df_2_1.loc[df_2_1.sort_values('recorddate').iloc[:,0].drop_duplicates().index]\n", "    df_2_2 = df_2_2[['date','待还本金']].groupby(['date'])['待还本金'].sum().to_frame().reset_index()\n", "    return df_2_2\n", "\n", "# merge gain and loss\n", "def profit(df):\n", "    df_1_1 = getinterest(df)\n", "    df_2_2 = getloss(df)\n", "    df_now = pd.merge(df_1_1,df_2_2, how = 'left', left_on = '上次还款日期', right_on = 'date')    \n", "    df_now['待还本金'] = df_now['待还本金'].where(df_now['待还本金'].notnull(),0)\n", "    df_now['差别'] = df_now['上次还款利息'] - df_now['待还本金']\n", "    return df_now\n", "\n", "def draw(df):\n", "    df_now = profit(df)\n", "    plt.plot(df_now['上次还款日期'], np.cumsum(df_now['差别']), label=\"利息\")\n", "    plt.title('累积收益曲线')\n", "    plt.xlabel('时间')\n", "    plt.ylabel('收益金额')\n", "    plt.show()\n", "\n", "draw(LCIS)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "349.091px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 5}
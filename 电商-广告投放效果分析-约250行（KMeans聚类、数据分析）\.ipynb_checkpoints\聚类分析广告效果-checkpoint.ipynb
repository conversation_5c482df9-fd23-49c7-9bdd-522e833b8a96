{"cells": [{"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Table of Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#项目介绍\" data-toc-modified-id=\"项目介绍-1\">项目介绍</a></span><ul class=\"toc-item\"><li><ul class=\"toc-item\"><li><span><a href=\"#数据维度概况\" data-toc-modified-id=\"数据维度概况-1.0.1\">数据维度概况</a></span></li><li><span><a href=\"#数据13个维度介绍\" data-toc-modified-id=\"数据13个维度介绍-1.0.2\">数据13个维度介绍</a></span></li></ul></li></ul></li><li><span><a href=\"#导入库，加载数据\" data-toc-modified-id=\"导入库，加载数据-2\">导入库，加载数据</a></span><ul class=\"toc-item\"><li><span><a href=\"#数据审查\" data-toc-modified-id=\"数据审查-2.1\">数据审查</a></span></li></ul></li><li><span><a href=\"#数据处理\" data-toc-modified-id=\"数据处理-3\">数据处理</a></span></li><li><span><a href=\"#建立模型\" data-toc-modified-id=\"建立模型-4\">建立模型</a></span></li><li><span><a href=\"#聚类结果特征分析与展示\" data-toc-modified-id=\"聚类结果特征分析与展示-5\">聚类结果特征分析与展示</a></span></li><li><span><a href=\"#数据结论\" data-toc-modified-id=\"数据结论-6\">数据结论</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 项目介绍"]}, {"cell_type": "markdown", "metadata": {"id": "AF51B1D65F66434797BCEC6B648ADC08", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["假如公司投放广告的渠道很多，每个渠道的客户性质也可能不同，比如在优酷视频投广告和今日头条投放广告，效果可能会有差异。现在需要对广告效果分析实现有针对性的广告效果测量和优化工作。\n", "\n", "本案例，通过各类广告渠道90天内额日均UV，平均注册率、平均搜索率、访问深度、平均停留时长、订单转化率、投放时间、素材类型、广告类型、合作方式、广告尺寸和广告卖点等特征，将渠道分类，找出每类渠道的重点特征，为加下来的业务讨论和数据分析提供支持。"]}, {"cell_type": "markdown", "metadata": {"id": "D83DBCB7868F48899539977366B60926", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["####  数据维度概况\n", "\n", "除了渠道唯一标识，共12个维度，889行，有缺失值，有异常值。\n", "\n", "#### 数据13个维度介绍\n", "\n", "1、渠道代号：渠道唯一标识  \n", "2、日均UV：每天的独立访问量  \n", "3、平均注册率=日均注册用户数/平均每日访问量  \n", "4、平均搜索量：每个访问的搜索量  \n", "5、访问深度：总页面浏览量/平均每天的访问量  \n", "6、平均停留时长=总停留时长/平均每天的访问量  \n", "7、订单转化率=总订单数量/平均每天的访客量  \n", "8、投放时间：每个广告在外投放的天数  \n", "9、素材类型：'jpg' 'swf' 'gif' 'sp'  \n", "10、广告类型：banner、tips、不确定、横幅、暂停  \n", "11、合作方式：'roi' 'cpc' 'cpm' 'cpd'  \n", "12、广告尺寸：'140*40' '308*388' '450*300' '600*90' '480*360' '960*126' '900*120'\n", "'390*270'  \n", "13、广告卖点：打折、满减、满赠、秒杀、直降、满返  "]}, {"cell_type": "markdown", "metadata": {"id": "325CA93116DA485C8434104F08E1DCC8", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 导入库，加载数据"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "5E3B0E0CE494419D96EEE8A15B836AF3", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["import pandas as pd \n", "import numpy as np \n", "import matplotlib as mpl \n", "import matplotlib.pyplot as plt \n", "from sklearn.preprocessing import MinMaxScaler,OneHotEncoder \n", "from sklearn.metrics import silhouette_score # 导入轮廓系数指标\n", "from sklearn.cluster import KMeans # KMeans模块\n", "%matplotlib inline\n", "## 设置属性防止中文乱码\n", "mpl.rcParams['font.sans-serif'] = [u'SimHei']\n", "mpl.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "markdown", "metadata": {"id": "F329962C04684C1789F7BFFD76A69738", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["以上是加载库的国际惯例，OneHotEncoder是独热编码，如果一个类别特征有n个类别，将该变量按照类别分裂成N维新变量，包含则标记为1，否则为0，用N维特征表示原来的特征。"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "73421CF2FD504E208E2F9F5A72646B28", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>渠道代号</th>\n", "      <th>日均UV</th>\n", "      <th>平均注册率</th>\n", "      <th>平均搜索量</th>\n", "      <th>访问深度</th>\n", "      <th>平均停留时间</th>\n", "      <th>订单转化率</th>\n", "      <th>投放总时间</th>\n", "      <th>素材类型</th>\n", "      <th>广告类型</th>\n", "      <th>合作方式</th>\n", "      <th>广告尺寸</th>\n", "      <th>广告卖点</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A203</td>\n", "      <td>3.69</td>\n", "      <td>0.0071</td>\n", "      <td>0.0214</td>\n", "      <td>2.3071</td>\n", "      <td>419.77</td>\n", "      <td>0.0258</td>\n", "      <td>20</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>roi</td>\n", "      <td>140*40</td>\n", "      <td>打折</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A387</td>\n", "      <td>178.70</td>\n", "      <td>0.0040</td>\n", "      <td>0.0324</td>\n", "      <td>2.0489</td>\n", "      <td>157.94</td>\n", "      <td>0.0030</td>\n", "      <td>19</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>cpc</td>\n", "      <td>140*40</td>\n", "      <td>满减</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A388</td>\n", "      <td>91.77</td>\n", "      <td>0.0022</td>\n", "      <td>0.0530</td>\n", "      <td>1.8771</td>\n", "      <td>357.93</td>\n", "      <td>0.0026</td>\n", "      <td>4</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>cpc</td>\n", "      <td>140*40</td>\n", "      <td>满减</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A389</td>\n", "      <td>1.09</td>\n", "      <td>0.0074</td>\n", "      <td>0.3382</td>\n", "      <td>4.2426</td>\n", "      <td>364.07</td>\n", "      <td>0.0153</td>\n", "      <td>10</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>cpc</td>\n", "      <td>140*40</td>\n", "      <td>满减</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A390</td>\n", "      <td>3.37</td>\n", "      <td>0.0028</td>\n", "      <td>0.1740</td>\n", "      <td>2.1934</td>\n", "      <td>313.34</td>\n", "      <td>0.0007</td>\n", "      <td>30</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>cpc</td>\n", "      <td>140*40</td>\n", "      <td>满减</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   渠道代号    日均UV   平均注册率   平均搜索量    访问深度  平均停留时间   订单转化率  投放总时间 素材类型    广告类型  \\\n", "0  A203    3.69  0.0071  0.0214  2.3071  419.77  0.0258     20  jpg  banner   \n", "1  A387  178.70  0.0040  0.0324  2.0489  157.94  0.0030     19  jpg  banner   \n", "2  A388   91.77  0.0022  0.0530  1.8771  357.93  0.0026      4  jpg  banner   \n", "3  A389    1.09  0.0074  0.3382  4.2426  364.07  0.0153     10  jpg  banner   \n", "4  A390    3.37  0.0028  0.1740  2.1934  313.34  0.0007     30  jpg  banner   \n", "\n", "  合作方式    广告尺寸 广告卖点  \n", "0  roi  140*40   打折  \n", "1  cpc  140*40   满减  \n", "2  cpc  140*40   满减  \n", "3  cpc  140*40   满减  \n", "4  cpc  140*40   满减  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["raw_data = pd.read_csv(r'./ad_performance.csv')\n", "raw_data.head()"]}, {"cell_type": "markdown", "metadata": {"id": "9D08684BC49B493EA843A0D2D35251AC", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["渠道代号是唯一标识，日均UV到投放总时间是数值型（float和int）变量，后面是字符型变量。"]}, {"cell_type": "markdown", "metadata": {"id": "831E8AE7A1554FAB804EFE04DDEC5E93", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["### 数据审查"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "38D632FCAAA544CDA288EA2F830103E3", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>渠道代号</th>\n", "      <th>日均UV</th>\n", "      <th>平均注册率</th>\n", "      <th>平均搜索量</th>\n", "      <th>访问深度</th>\n", "      <th>平均停留时间</th>\n", "      <th>订单转化率</th>\n", "      <th>投放总时间</th>\n", "      <th>素材类型</th>\n", "      <th>广告类型</th>\n", "      <th>合作方式</th>\n", "      <th>广告尺寸</th>\n", "      <th>广告卖点</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A203</td>\n", "      <td>3.69</td>\n", "      <td>0.0071</td>\n", "      <td>0.0214</td>\n", "      <td>2.3071</td>\n", "      <td>419.77</td>\n", "      <td>0.0258</td>\n", "      <td>20</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>roi</td>\n", "      <td>140*40</td>\n", "      <td>打折</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A387</td>\n", "      <td>178.70</td>\n", "      <td>0.0040</td>\n", "      <td>0.0324</td>\n", "      <td>2.0489</td>\n", "      <td>157.94</td>\n", "      <td>0.0030</td>\n", "      <td>19</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>cpc</td>\n", "      <td>140*40</td>\n", "      <td>满减</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   渠道代号    日均UV   平均注册率   平均搜索量    访问深度  平均停留时间   订单转化率  投放总时间 素材类型    广告类型  \\\n", "0  A203    3.69  0.0071  0.0214  2.3071  419.77  0.0258     20  jpg  banner   \n", "1  A387  178.70  0.0040  0.0324  2.0489  157.94  0.0030     19  jpg  banner   \n", "\n", "  合作方式    广告尺寸 广告卖点  \n", "0  roi  140*40   打折  \n", "1  cpc  140*40   满减  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看基本状态\n", "raw_data.head(2)  # 打印输出前2条数据"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "685F2E4ACAF54F5D84A132AC046431A4", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 889 entries, 0 to 888\n", "Data columns (total 13 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   渠道代号    889 non-null    object \n", " 1   日均UV    889 non-null    float64\n", " 2   平均注册率   889 non-null    float64\n", " 3   平均搜索量   889 non-null    float64\n", " 4   访问深度    889 non-null    float64\n", " 5   平均停留时间  887 non-null    float64\n", " 6   订单转化率   889 non-null    float64\n", " 7   投放总时间   889 non-null    int64  \n", " 8   素材类型    889 non-null    object \n", " 9   广告类型    889 non-null    object \n", " 10  合作方式    889 non-null    object \n", " 11  广告尺寸    889 non-null    object \n", " 12  广告卖点    889 non-null    object \n", "dtypes: float64(6), int64(1), object(6)\n", "memory usage: 90.4+ KB\n"]}], "source": ["raw_data.info()# 打印数据类型分布"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "0CF6FAF7AD6441988B59201C548B5165", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>日均UV</th>\n", "      <td>889.0</td>\n", "      <td>540.85</td>\n", "      <td>1634.41</td>\n", "      <td>0.06</td>\n", "      <td>6.18</td>\n", "      <td>114.18</td>\n", "      <td>466.87</td>\n", "      <td>25294.77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>平均注册率</th>\n", "      <td>889.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>平均搜索量</th>\n", "      <td>889.0</td>\n", "      <td>0.03</td>\n", "      <td>0.11</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.01</td>\n", "      <td>1.04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>访问深度</th>\n", "      <td>889.0</td>\n", "      <td>2.17</td>\n", "      <td>3.80</td>\n", "      <td>1.00</td>\n", "      <td>1.39</td>\n", "      <td>1.79</td>\n", "      <td>2.22</td>\n", "      <td>98.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>平均停留时间</th>\n", "      <td>887.0</td>\n", "      <td>262.67</td>\n", "      <td>224.36</td>\n", "      <td>1.64</td>\n", "      <td>126.02</td>\n", "      <td>236.55</td>\n", "      <td>357.98</td>\n", "      <td>4450.83</td>\n", "    </tr>\n", "    <tr>\n", "      <th>订单转化率</th>\n", "      <td>889.0</td>\n", "      <td>0.00</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>投放总时间</th>\n", "      <td>889.0</td>\n", "      <td>16.05</td>\n", "      <td>8.51</td>\n", "      <td>1.00</td>\n", "      <td>9.00</td>\n", "      <td>16.00</td>\n", "      <td>24.00</td>\n", "      <td>30.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        count    mean      std   min     25%     50%     75%       max\n", "日均UV    889.0  540.85  1634.41  0.06    6.18  114.18  466.87  25294.77\n", "平均注册率   889.0    0.00     0.00  0.00    0.00    0.00    0.00      0.04\n", "平均搜索量   889.0    0.03     0.11  0.00    0.00    0.00    0.01      1.04\n", "访问深度    889.0    2.17     3.80  1.00    1.39    1.79    2.22     98.98\n", "平均停留时间  887.0  262.67   224.36  1.64  126.02  236.55  357.98   4450.83\n", "订单转化率   889.0    0.00     0.01  0.00    0.00    0.00    0.00      0.22\n", "投放总时间   889.0   16.05     8.51  1.00    9.00   16.00   24.00     30.00"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["raw_data.describe().round(2).T # 打印原始数据基本描述性信息"]}, {"cell_type": "markdown", "metadata": {"id": "83348266138A4B718B6A4CDEA13D133E", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["上面代码，分别展示前两条数据、所有特征的数据类型、以及数值型特征的五值分布"]}, {"cell_type": "markdown", "metadata": {"id": "D709F29D3FED41BC8EB431AB3A9C847B", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["查看缺失值情况："]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "2E6A457DDFEA43968E6074D50F5546BF", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["渠道代号      False\n", "日均UV      False\n", "平均注册率     False\n", "平均搜索量     False\n", "访问深度      False\n", "平均停留时间     True\n", "订单转化率     False\n", "投放总时间     False\n", "素材类型      False\n", "广告类型      False\n", "合作方式      False\n", "广告尺寸      False\n", "广告卖点      False\n", "dtype: bool"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 缺失值审查\n", "na_cols = raw_data.isnull().any(axis=0)  # 查看每一列是否具有缺失值\n", "na_cols"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "1A382308B24240CA8B7FFA44FD2ACC8E", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/plain": ["平均停留时间    2\n", "渠道代号      0\n", "日均UV      0\n", "平均注册率     0\n", "平均搜索量     0\n", "访问深度      0\n", "订单转化率     0\n", "投放总时间     0\n", "素材类型      0\n", "广告类型      0\n", "合作方式      0\n", "广告尺寸      0\n", "广告卖点      0\n", "dtype: int64"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["raw_data.isnull().sum().sort_values(ascending=False)# 查看具有缺失值的行总记录数"]}, {"cell_type": "markdown", "metadata": {"id": "754E5EB794A345FB9701597223A7DC0C", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["变量之间的相关性分析："]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "805C832B78F64FE98C513A57B622876B", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>日均UV</th>\n", "      <th>平均注册率</th>\n", "      <th>平均搜索量</th>\n", "      <th>访问深度</th>\n", "      <th>平均停留时间</th>\n", "      <th>订单转化率</th>\n", "      <th>投放总时间</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>日均UV</th>\n", "      <td>1.00</td>\n", "      <td>-0.05</td>\n", "      <td>-0.07</td>\n", "      <td>-0.02</td>\n", "      <td>0.04</td>\n", "      <td>-0.05</td>\n", "      <td>-0.04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>平均注册率</th>\n", "      <td>-0.05</td>\n", "      <td>1.00</td>\n", "      <td>0.24</td>\n", "      <td>0.11</td>\n", "      <td>0.22</td>\n", "      <td>0.32</td>\n", "      <td>-0.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>平均搜索量</th>\n", "      <td>-0.07</td>\n", "      <td>0.24</td>\n", "      <td>1.00</td>\n", "      <td>0.06</td>\n", "      <td>0.17</td>\n", "      <td>0.13</td>\n", "      <td>-0.03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>访问深度</th>\n", "      <td>-0.02</td>\n", "      <td>0.11</td>\n", "      <td>0.06</td>\n", "      <td>1.00</td>\n", "      <td>0.72</td>\n", "      <td>0.16</td>\n", "      <td>0.06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>平均停留时间</th>\n", "      <td>0.04</td>\n", "      <td>0.22</td>\n", "      <td>0.17</td>\n", "      <td>0.72</td>\n", "      <td>1.00</td>\n", "      <td>0.25</td>\n", "      <td>0.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>订单转化率</th>\n", "      <td>-0.05</td>\n", "      <td>0.32</td>\n", "      <td>0.13</td>\n", "      <td>0.16</td>\n", "      <td>0.25</td>\n", "      <td>1.00</td>\n", "      <td>-0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>投放总时间</th>\n", "      <td>-0.04</td>\n", "      <td>-0.01</td>\n", "      <td>-0.03</td>\n", "      <td>0.06</td>\n", "      <td>0.05</td>\n", "      <td>-0.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        日均UV  平均注册率  平均搜索量  访问深度  平均停留时间  订单转化率  投放总时间\n", "日均UV    1.00  -0.05  -0.07 -0.02    0.04  -0.05  -0.04\n", "平均注册率  -0.05   1.00   0.24  0.11    0.22   0.32  -0.01\n", "平均搜索量  -0.07   0.24   1.00  0.06    0.17   0.13  -0.03\n", "访问深度   -0.02   0.11   0.06  1.00    0.72   0.16   0.06\n", "平均停留时间  0.04   0.22   0.17  0.72    1.00   0.25   0.05\n", "订单转化率  -0.05   0.32   0.13  0.16    0.25   1.00  -0.00\n", "投放总时间  -0.04  -0.01  -0.03  0.06    0.05  -0.00   1.00"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 相关性分析\n", "raw_data.corr(numeric_only=True).round(2).T # 打印原始数据相关性信息"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "4B6DCC2D6C8E466EBB68CBE49D347014", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_14424\\3904037074.py:3: FutureWarning: The default value of numeric_only in DataFrame.corr is deprecated. In a future version, it will default to False. Select only valid columns or specify the value of numeric_only to silence this warning.\n", "  corr = raw_data.corr().round(2)\n"]}, {"data": {"text/plain": ["<AxesSubplot: >"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 相关性可视化展示\n", "import seaborn as sns \n", "corr = raw_data.corr().round(2)\n", "sns.heatmap(corr,cmap='Reds',annot = True)"]}, {"cell_type": "markdown", "metadata": {"id": "4B1D632750F24201B643A11F562FF5E6", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["可以看到，“访问深度”和“平均停留时间”相关性比较高，相关性高说明两个变量在建立模型的时候，作用是一样或者效果是一样的，可以考虑组合或者删除其一。"]}, {"cell_type": "markdown", "metadata": {"id": "470D73D973B74C0990F36B68ED2B28A5", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 数据处理"]}, {"cell_type": "markdown", "metadata": {"id": "EADCB4250A064322910C3F85DBF4EA52", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["数据了解的差不多了，我们开始时处理数据，把常规数据通过清洗、转换、规约、聚合、抽样等方式变成机器学习可以识别或者提升准确度的数据。"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"id": "125A2FECFC1446BF8C848A27C7D7EAAF", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["# 1 删除平均平均停留时间列\n", "raw_data2 = raw_data.drop(['平均停留时间'],axis=1)"]}, {"cell_type": "markdown", "metadata": {"id": "F3195663E9D14A0FA066E6BD4DE31794", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["类别变量的独热编码："]}, {"cell_type": "code", "execution_count": 20, "metadata": {"id": "6093D9016CC9491D81C4DE77BE962804", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["变量【素材类型】的取值有：\n", "['jpg' 'swf' 'gif' 'sp']\n", "-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·\n", "变量【广告类型】的取值有：\n", "['banner' 'tips' '不确定' '横幅' '暂停']\n", "-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·\n", "变量【合作方式】的取值有：\n", "['roi' 'cpc' 'cpm' 'cpd']\n", "-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·\n", "变量【广告尺寸】的取值有：\n", "['140*40' '308*388' '450*300' '600*90' '480*360' '960*126' '900*120'\n", " '390*270']\n", "-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·\n", "变量【广告卖点】的取值有：\n", "['打折' '满减' '满赠' '秒杀' '直降' '满返']\n", "-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·-·\n"]}], "source": ["# 类别变量取值\n", "cols=[\"素材类型\",\"广告类型\",\"合作方式\",\"广告尺寸\",\"广告卖点\"]\n", "for x in cols:\n", "    data=raw_data2[x].unique()\n", "    print(\"变量【{0}】的取值有：\\n{1}\".format(x,data))\n", "    print(\"-·\"*20)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"id": "97A45A97712C412A83159A16B33DF11C", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[0. 1. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 1. 1. 0. 0. 0. 0. 0. 0. 0. 1. 0. 0.\n", "  0. 0. 0.]\n", " [0. 1. 0. 0. 1. 0. 0. 0. 0. 1. 0. 0. 0. 1. 0. 0. 0. 0. 0. 0. 0. 0. 1. 0.\n", "  0. 0. 0.]]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\preprocessing\\_encoders.py:828: FutureWarning: `sparse` was renamed to `sparse_output` in version 1.2 and will be removed in 1.4. `sparse_output` is ignored unless you leave `sparse` to its default value.\n", "  warnings.warn(\n"]}], "source": ["# 字符串分类独热编码处理\n", "cols = ['素材类型','广告类型','合作方式','广告尺寸','广告卖点'] \n", "model_ohe = OneHotEncoder(sparse=False)  # 建立OneHotEncode对象\n", "ohe_matrix = model_ohe.fit_transform(raw_data2[cols])  # 直接转换\n", "print(ohe_matrix[:2])"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"id": "F8B50698985F494988F1433D97B6B6D7", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>素材类型_gif</th>\n", "      <th>素材类型_jpg</th>\n", "      <th>素材类型_sp</th>\n", "      <th>素材类型_swf</th>\n", "      <th>广告类型_banner</th>\n", "      <th>广告类型_tips</th>\n", "      <th>广告类型_不确定</th>\n", "      <th>广告类型_暂停</th>\n", "      <th>广告类型_横幅</th>\n", "      <th>合作方式_cpc</th>\n", "      <th>...</th>\n", "      <th>广告尺寸_480*360</th>\n", "      <th>广告尺寸_600*90</th>\n", "      <th>广告尺寸_900*120</th>\n", "      <th>广告尺寸_960*126</th>\n", "      <th>广告卖点_打折</th>\n", "      <th>广告卖点_满减</th>\n", "      <th>广告卖点_满赠</th>\n", "      <th>广告卖点_满返</th>\n", "      <th>广告卖点_直降</th>\n", "      <th>广告卖点_秒杀</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 27 columns</p>\n", "</div>"], "text/plain": ["   素材类型_gif  素材类型_jpg  素材类型_sp  素材类型_swf  广告类型_banner  广告类型_tips  广告类型_不确定  \\\n", "0         0         1        0         0            1          0         0   \n", "1         0         1        0         0            1          0         0   \n", "2         0         1        0         0            1          0         0   \n", "3         0         1        0         0            1          0         0   \n", "4         0         1        0         0            1          0         0   \n", "\n", "   广告类型_暂停  广告类型_横幅  合作方式_cpc  ...  广告尺寸_480*360  广告尺寸_600*90  广告尺寸_900*120  \\\n", "0        0        0         0  ...             0            0             0   \n", "1        0        0         1  ...             0            0             0   \n", "2        0        0         1  ...             0            0             0   \n", "3        0        0         1  ...             0            0             0   \n", "4        0        0         1  ...             0            0             0   \n", "\n", "   广告尺寸_960*126  广告卖点_打折  广告卖点_满减  广告卖点_满赠  广告卖点_满返  广告卖点_直降  广告卖点_秒杀  \n", "0             0        1        0        0        0        0        0  \n", "1             0        0        1        0        0        0        0  \n", "2             0        0        1        0        0        0        0  \n", "3             0        0        1        0        0        0        0  \n", "4             0        0        1        0        0        0        0  \n", "\n", "[5 rows x 27 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# 用pandas的方法\n", "ohe_matrix1=pd.get_dummies(raw_data2[cols])\n", "ohe_matrix1.head(5)"]}, {"cell_type": "markdown", "metadata": {"id": "ABFAE91E9ABF4DC09FC60F0052C90C74", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["数据标准化："]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "D228E4D6CDA34F188AF7337D8C3C1B47", "jupyter": {}, "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[0.   0.18 0.02 0.01 0.12 0.66]\n", " [0.01 0.1  0.03 0.01 0.01 0.62]\n", " [0.   0.06 0.05 0.01 0.01 0.1 ]\n", " ...\n", " [0.01 0.01 0.   0.   0.   0.72]\n", " [0.05 0.   0.   0.   0.   0.31]\n", " [0.   0.   0.   0.53 0.   0.62]]\n"]}], "source": ["# 数据标准化\n", "sacle_matrix = raw_data2.iloc[:, 1:7]  # 获得要转换的矩阵\n", "model_scaler = MinMaxScaler()  # 建立MinMaxScaler模型对象\n", "data_scaled = model_scaler.fit_transform(sacle_matrix)  # MinMaxScaler标准化处理\n", "print(data_scaled.round(2))"]}, {"cell_type": "markdown", "metadata": {"id": "5910F4BB712445B7A20DA6E1FA64510C", "jupyter": {}, "mdEditEnable": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["数据处理完，我们将独热编码的数据和标准化转化后的数据合并："]}, {"cell_type": "code", "execution_count": 24, "metadata": {"id": "9BE1C0F67E7B4BAAB835B081228B4E8A", "jupyter": {}, "scrolled": false, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [], "source": ["# # 合并所有维度\n", "X = np.hstack((data_scaled, ohe_matrix))"]}, {"cell_type": "markdown", "metadata": {"id": "4DC3FD3FB5344E4F8AC993EB83268FBC", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["数据处理完，就可以带入模型进行训练了。"]}, {"cell_type": "markdown", "metadata": {"id": "1510714D2D4B41D28531565F99B415AF", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 建立模型\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"id": "73B875528C4B4E568F3038E91B4C8218", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["*************************K值对应的轮廓系数:*************************\n", "[[2.         0.38655493]\n", " [3.         0.45757883]\n", " [4.         0.50209812]\n", " [5.         0.4800359 ]\n", " [6.         0.47761127]\n", " [7.         0.48317001]]\n", "最优的K值是:4 \n", "对应的轮廓系数是:0.5020981194788053\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\Envs\\jv\\lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n"]}], "source": ["# 通过平均轮廓系数检验得到最佳KMeans聚类模型\n", "score_list = list()  # 用来存储每个K下模型的平局轮廓系数\n", "silhouette_int = -1  # 初始化的平均轮廓系数阀值\n", "for n_clusters in range(2, 8):  # 遍历从2到5几个有限组\n", "    model_kmeans = KMeans(n_clusters=n_clusters)  # 建立聚类模型对象\n", "    labels_tmp = model_kmeans.fit_predict(X)  # 训练聚类模型\n", "    silhouette_tmp = silhouette_score(X, labels_tmp)  # 得到每个K下的平均轮廓系数\n", "    if silhouette_tmp > silhouette_int:  # 如果平均轮廓系数更高\n", "        best_k = n_clusters  # 保存K将最好的K存储下来\n", "        silhouette_int = silhouette_tmp  # 保存平均轮廓得分\n", "        best_kmeans = model_kmeans  # 保存模型实例对象\n", "        cluster_labels_k = labels_tmp  # 保存聚类标签\n", "    score_list.append([n_clusters, silhouette_tmp])  # 将每次K及其得分追加到列表\n", "print('{:*^60}'.format('K值对应的轮廓系数:'))\n", "print(np.array(score_list))  # 打印输出所有K下的详细得分\n", "print('最优的K值是:{0} \\n对应的轮廓系数是:{1}'.format(best_k, silhouette_int))"]}, {"cell_type": "markdown", "metadata": {"id": "5AA084B2714040D381E1BBA365B3E8C0", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["总体思想（评价指标）还是怎么聚才能使得簇内距离足够小，簇与簇之间平均距离足够大来评判。"]}, {"cell_type": "markdown", "metadata": {"id": "B85519A6585547DA8CF6E5F063FA7DCD", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 聚类结果特征分析与展示"]}, {"cell_type": "markdown", "metadata": {"id": "B1569570C61F4BA789DC1CAF07B3AA82", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["通过上面模型，我们其实给每个观测（样本）打了个标签clusters，即他属于4类中的哪一类："]}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "25C6DC36EFFF4B7CB1D0327A49D91388", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>渠道代号</th>\n", "      <th>日均UV</th>\n", "      <th>平均注册率</th>\n", "      <th>平均搜索量</th>\n", "      <th>访问深度</th>\n", "      <th>订单转化率</th>\n", "      <th>投放总时间</th>\n", "      <th>素材类型</th>\n", "      <th>广告类型</th>\n", "      <th>合作方式</th>\n", "      <th>广告尺寸</th>\n", "      <th>广告卖点</th>\n", "      <th>clusters</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A203</td>\n", "      <td>3.69</td>\n", "      <td>0.0071</td>\n", "      <td>0.0214</td>\n", "      <td>2.3071</td>\n", "      <td>0.0258</td>\n", "      <td>20</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>roi</td>\n", "      <td>140*40</td>\n", "      <td>打折</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A387</td>\n", "      <td>178.70</td>\n", "      <td>0.0040</td>\n", "      <td>0.0324</td>\n", "      <td>2.0489</td>\n", "      <td>0.0030</td>\n", "      <td>19</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>cpc</td>\n", "      <td>140*40</td>\n", "      <td>满减</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A388</td>\n", "      <td>91.77</td>\n", "      <td>0.0022</td>\n", "      <td>0.0530</td>\n", "      <td>1.8771</td>\n", "      <td>0.0026</td>\n", "      <td>4</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>cpc</td>\n", "      <td>140*40</td>\n", "      <td>满减</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A389</td>\n", "      <td>1.09</td>\n", "      <td>0.0074</td>\n", "      <td>0.3382</td>\n", "      <td>4.2426</td>\n", "      <td>0.0153</td>\n", "      <td>10</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>cpc</td>\n", "      <td>140*40</td>\n", "      <td>满减</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A390</td>\n", "      <td>3.37</td>\n", "      <td>0.0028</td>\n", "      <td>0.1740</td>\n", "      <td>2.1934</td>\n", "      <td>0.0007</td>\n", "      <td>30</td>\n", "      <td>jpg</td>\n", "      <td>banner</td>\n", "      <td>cpc</td>\n", "      <td>140*40</td>\n", "      <td>满减</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   渠道代号    日均UV   平均注册率   平均搜索量    访问深度   订单转化率  投放总时间 素材类型    广告类型 合作方式  \\\n", "0  A203    3.69  0.0071  0.0214  2.3071  0.0258     20  jpg  banner  roi   \n", "1  A387  178.70  0.0040  0.0324  2.0489  0.0030     19  jpg  banner  cpc   \n", "2  A388   91.77  0.0022  0.0530  1.8771  0.0026      4  jpg  banner  cpc   \n", "3  A389    1.09  0.0074  0.3382  4.2426  0.0153     10  jpg  banner  cpc   \n", "4  A390    3.37  0.0028  0.1740  2.1934  0.0007     30  jpg  banner  cpc   \n", "\n", "     广告尺寸 广告卖点  clusters  \n", "0  140*40   打折         1  \n", "1  140*40   满减         1  \n", "2  140*40   满减         1  \n", "3  140*40   满减         1  \n", "4  140*40   满减         1  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将原始数据与聚类标签整合\n", "cluster_labels = pd.DataFrame(cluster_labels_k, columns=['clusters'])  # 获得训练集下的标签信息\n", "merge_data = pd.concat((raw_data2, cluster_labels), axis=1)  # 将原始处理过的数据跟聚类标签整合\n", "merge_data.head()"]}, {"cell_type": "markdown", "metadata": {"id": "A1D272EFF9B0442E87A0537BB2767D7B", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["然后看看，每个类别下的样本数量和占比情况："]}, {"cell_type": "code", "execution_count": 27, "metadata": {"id": "E7C81773030844DB8536CBE67A136EE7", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["clusters   0    1    2    3\n", "counts    73  154  313  349\n", "##############################\n", "clusters       0     1     2     3\n", "percentage  0.08  0.17  0.35  0.39\n"]}], "source": ["# 计算每个聚类类别下的样本量和样本占比\n", "clustering_count = pd.DataFrame(merge_data['渠道代号'].groupby(merge_data['clusters']).count()).T.rename({'渠道代号': 'counts'})  # 计算每个聚类类别的样本量\n", "clustering_ratio = (clustering_count / len(merge_data)).round(2).rename({'counts': 'percentage'})  # 计算每个聚类类别的样本量占比\n", "print(clustering_count)\n", "print(\"#\"*30)\n", "print(clustering_ratio)"]}, {"cell_type": "markdown", "metadata": {"id": "E7B6AAEBDAB14CAF8CBC5217FC216E27", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["每个类别内部最显著的特征："]}, {"cell_type": "code", "execution_count": 28, "metadata": {"id": "7C8CA32E59D64E8387F7AB6E6689E2B3", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*************************每个类别主要的特征:*************************\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>counts</th>\n", "      <td>73</td>\n", "      <td>154</td>\n", "      <td>313</td>\n", "      <td>349</td>\n", "    </tr>\n", "    <tr>\n", "      <th>percentage</th>\n", "      <td>0.08</td>\n", "      <td>0.17</td>\n", "      <td>0.35</td>\n", "      <td>0.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>日均UV</th>\n", "      <td>1904.371</td>\n", "      <td>2717.419</td>\n", "      <td>1390.013</td>\n", "      <td>933.015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>平均注册率</th>\n", "      <td>0.003</td>\n", "      <td>0.005</td>\n", "      <td>0.003</td>\n", "      <td>0.003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>平均搜索量</th>\n", "      <td>0.106</td>\n", "      <td>0.051</td>\n", "      <td>0.152</td>\n", "      <td>0.064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>访问深度</th>\n", "      <td>0.943</td>\n", "      <td>0.947</td>\n", "      <td>1.168</td>\n", "      <td>5.916</td>\n", "    </tr>\n", "    <tr>\n", "      <th>订单转化率</th>\n", "      <td>0.009</td>\n", "      <td>0.007</td>\n", "      <td>0.017</td>\n", "      <td>0.006</td>\n", "    </tr>\n", "    <tr>\n", "      <th>投放总时间</th>\n", "      <td>8.217</td>\n", "      <td>8.529</td>\n", "      <td>8.199</td>\n", "      <td>8.77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>素材类型</th>\n", "      <td>swf</td>\n", "      <td>jpg</td>\n", "      <td>swf</td>\n", "      <td>jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>广告类型</th>\n", "      <td>tips</td>\n", "      <td>banner</td>\n", "      <td>不确定</td>\n", "      <td>横幅</td>\n", "    </tr>\n", "    <tr>\n", "      <th>合作方式</th>\n", "      <td>cpm</td>\n", "      <td>cpc</td>\n", "      <td>roi</td>\n", "      <td>cpc</td>\n", "    </tr>\n", "    <tr>\n", "      <th>广告尺寸</th>\n", "      <td>450*300</td>\n", "      <td>308*388</td>\n", "      <td>600*90</td>\n", "      <td>600*90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>广告卖点</th>\n", "      <td>打折</td>\n", "      <td>满减</td>\n", "      <td>打折</td>\n", "      <td>直降</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   0         1         2        3\n", "counts            73       154       313      349\n", "percentage      0.08      0.17      0.35     0.39\n", "日均UV        1904.371  2717.419  1390.013  933.015\n", "平均注册率          0.003     0.005     0.003    0.003\n", "平均搜索量          0.106     0.051     0.152    0.064\n", "访问深度           0.943     0.947     1.168    5.916\n", "订单转化率          0.009     0.007     0.017    0.006\n", "投放总时间          8.217     8.529     8.199     8.77\n", "素材类型             swf       jpg       swf      jpg\n", "广告类型            tips    banner       不确定       横幅\n", "合作方式             cpm       cpc       roi      cpc\n", "广告尺寸         450*300   308*388    600*90   600*90\n", "广告卖点              打折        满减        打折       直降"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# 计算各个聚类类别内部最显著特征值\n", "cluster_features = []  # 空列表，用于存储最终合并后的所有特征信息\n", "for line in range(best_k):  # 读取每个类索引\n", "    label_data = merge_data[merge_data['clusters'] == line]  # 获得特定类的数据\n", "\n", "    part1_data = label_data.iloc[:, 1:7]  # 获得数值型数据特征\n", "    part1_desc = part1_data.describe().round(3)  # 得到数值型特征的描述性统计信息\n", "    merge_data1 = part1_desc.iloc[2, :]  # 得到数值型特征的均值\n", "\n", "    part2_data = label_data.iloc[:, 7:-1]  # 获得字符串型数据特征\n", "    part2_desc = part2_data.describe(include='all')  # 获得字符串型数据特征的描述性统计信息\n", "    merge_data2 = part2_desc.iloc[2, :]  # 获得字符串型数据特征的最频繁值\n", "\n", "    merge_line = pd.concat((merge_data1, merge_data2), axis=0)  # 将数值型和字符串型典型特征沿行合并\n", "    cluster_features.append(merge_line)  # 将每个类别下的数据特征追加到列表\n", "\n", "#  输出完整的类别特征信息\n", "cluster_pd = pd.DataFrame(cluster_features).T  # 将列表转化为矩阵\n", "print('{:*^60}'.format('每个类别主要的特征:'))\n", "all_cluster_set = pd.concat((clustering_count, clustering_ratio, cluster_pd),axis=0)  # 将每个聚类类别的所有信息合并\n", "all_cluster_set"]}, {"cell_type": "markdown", "metadata": {"id": "36769323294F47608973E04B56398E10", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["图形化输出："]}, {"cell_type": "code", "execution_count": 29, "metadata": {"id": "B7DE831A00104E5EBA7542291E5AE0B5", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       日均UV  平均注册率  平均搜索量   访问深度  订单转化率  投放总时间\n", "0  1904.371  0.003  0.106  0.943  0.009  8.217\n", "1  2717.419  0.005  0.051  0.947  0.007  8.529\n", "2  1390.013  0.003  0.152  1.168  0.017  8.199\n", "3   933.015  0.003  0.064  5.916  0.006  8.770\n", "--------------------\n", "[[5.44358789e-01 0.00000000e+00 5.44554455e-01 0.00000000e+00\n", "  2.72727273e-01 3.15236427e-02]\n", " [1.00000000e+00 1.00000000e+00 0.00000000e+00 8.04343455e-04\n", "  9.09090909e-02 5.77933450e-01]\n", " [2.56106801e-01 0.00000000e+00 1.00000000e+00 4.52443193e-02\n", "  1.00000000e+00 0.00000000e+00]\n", " [0.00000000e+00 0.00000000e+00 1.28712871e-01 1.00000000e+00\n", "  0.00000000e+00 1.00000000e+00]]\n"]}], "source": ["#各类别数据预处理\n", "num_sets = cluster_pd.iloc[:6, :].T.astype(np.float64)  # 获取要展示的数据\n", "num_sets_max_min = model_scaler.fit_transform(num_sets)  # 获得标准化后的数据\n", "print(num_sets)\n", "print('-'*20)\n", "print(num_sets_max_min)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"id": "7E07FF3B46E646758C97FBB3D9949D39", "jupyter": {}, "scrolled": true, "slideshow": {"slide_type": "slide"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.         1.04719755 2.0943951  3.14159265 4.1887902  5.23598776\n", " 0.        ]\n", "['日均UV' '平均注册率' '平均搜索量' '访问深度' '订单转化率' '投放总时间']\n"]}, {"data": {"text/plain": ["<matplotlib.legend.Legend at 0x292a10759d0>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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********************************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\n", "text/plain": ["<Figure size 700x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 画图\n", "fig = plt.figure(figsize=(7,7))  # 建立画布\n", "ax = fig.add_subplot(111, polar=True)  # 增加子网格，注意polar参数\n", "labels = np.array(merge_data1.index)  # 设置要展示的数据标签\n", "cor_list = ['g', 'r', 'y', 'b']  # 定义不同类别的颜色\n", "angles = np.linspace(0, 2 * np.pi, len(labels), endpoint=False)  # 计算各个区间的角度\n", "angles = np.concatenate((angles, [angles[0]]))  # 建立相同首尾字段以便于闭合\n", "# 画雷达图\n", "for i in range(len(num_sets)):  # 循环每个类别\n", "    data_tmp = num_sets_max_min[i, :]  # 获得对应类数据\n", "    data = np.concatenate((data_tmp, [data_tmp[0]]))  # 建立相同首尾字段以便于闭合\n", "    ax.plot(angles, data, 'o-', c=cor_list[i], label=\"第%d类渠道\"%(i))  # 画线\n", "    ax.fill(angles, data,alpha=0.8)\n", "    \n", "# 设置图像显示格式\n", "print(angles)\n", "print(labels)\n", "\n", "ax.set_thetagrids(angles[0:-1] * 180 / np.pi, labels, fontproperties=\"SimHei\")  # 设置极坐标轴\n", "ax.set_title(\"各聚类类别显著特征对比\", fontproperties=\"SimHei\")  # 设置标题放置\n", "ax.set_rlim(-0.2, 1.2)  # 设置坐标轴尺度范围\n", "plt.legend(loc=\"upper right\" ,bbox_to_anchor=(1.2,1.0))  # 设置图例位置"]}, {"cell_type": "markdown", "metadata": {"id": "B1F49840A6F649E785005569578D8E98", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["## 数据结论\n"]}, {"cell_type": "markdown", "metadata": {"id": "D351B13258F4423EA4841A55F0EC2750", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["从案例结果来看，所有的渠道被分为4各类别，每个类别的样本量分别为：154、313、349 、73，对应占比分别为：17%、35%、39%、8%。"]}, {"cell_type": "markdown", "metadata": {"id": "A1AB29556611409A875E4DB7256985A8", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["通过雷达图可以清楚的知道："]}, {"cell_type": "markdown", "metadata": {"id": "2636D2DB4758498B82F45A5807C5174E", "jupyter": {}, "slideshow": {"slide_type": "slide"}, "tags": []}, "source": ["类别1（索引为2类的渠道）\n", "这类广告媒体除了访问深度和投放时间较高，其他属性较低，因此这类广告媒体效果质量较差，并且占到39%，因此这类是主题渠道之一。\n", "业务部门要考虑他的实际投放价值。\n", "\n", "\n", "\n", "类别2（索引为1类的渠道）\n", "这类广告媒体除了访问深度略差，在平均搜索量、日均UV、订单转化率等广告效果指标上表现良好，是一类综合效果较好的渠道。\n", "但是日均UV是短板，较低。无法给企业带来大量的流量以及新用户，这类广告的特质适合用户转化，尤其是有关订单的转化提升。\n", "\n", "\n", "\n", "类别3（索引为0类的渠道）\n", "这类广告媒体的显著特征是日均UV和注册率较高，其“引流”和“拉新”效果好，可以在广告媒体中定位为引流角色。\n", "符合“广而告之”的诉求，适合“拉新”使用。\n", "\n", "\n", "\n", "类别4（索引为3类的渠道）\n", "这类渠道各方面特征都不明显，各个流量质量和流量数量的指标均处于“中等”层次。不突出但是均衡，考虑在各场景下可以考虑在这个渠道投放广告。"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "384px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 4}
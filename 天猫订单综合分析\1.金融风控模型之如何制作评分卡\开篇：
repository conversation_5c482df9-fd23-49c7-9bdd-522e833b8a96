开篇：
A卡/申请评分卡：在客户申请处理期，预测客户开户后一定时期内违约拖欠的风险概率，有效排除了信用不良客户和非目标客户的申请

B卡/行为评分卡：在帐户管理期，根据账户历史上所表现出来的各种行为特征来预测该账户未来的信贷表现

C卡/催收评分卡：在帐户管理期，对逾期帐户预测催收策略反应的概率，从而采取相应的催收措施


------------------------------------------------

4.1.1

在银行系统中，这个数据通常使来自于其他部门的同事的收集，因此千万别忘记抓住给你数据的人，问问她/他各个项都是什么含义。通常来说，当特征非常多的时候（比如几百个），都会有一个附带的excel或pdf文档给到你， 备注了各个特征都是什么含义。

#index_col=0:第一列为index值
data = pd.read_csv(r"Acard.csv",index_col=0) 


------------------------------------------------

4.1.2 
现实数据，尤其是银行业数据，可能会存在的一个问题就是样本重复，即有超过一行的样本所显示的所有特征都一 样。有时候可能是人为输入重复，有时候可能是系统录入重复，总而言之我们必须对数据进行去重处理。可能有人会说，难道不可能出现说两个样本的特征就是一模一样，但他们是两个样本吗？比如，两个人，一模一样的名字， 年龄，性别，学历，工资……当特征量很少的时候，这的确是有可能的，但一些指标，比如说家属人数，月收入，已借有的房地产贷款数量等等，几乎不可能都出现一样。尤其是银行业数据经常是几百个特征，所有特征都一样的 可能性是微乎其微的。即便真的出现了如此极端的情况，我们也可以当作是少量信息损失，将这条记录当作重复值除去。

# 对data的数据的重复值进行删除 
data.drop_duplicates(inplace=True) 

------------------------------------------------

4.1.3 

那字段"收入"怎么办呢？对于银行数据来说，我们甚至可以有这样的推断：一个来借钱的人应该是会知道，“高收 
入”或者“稳定收入”的对于他自己而言会是申请贷款过程中的一个助力，因此如果收入稳定良好的人，肯定会倾向于写上自己的收入情况，那么这些“收入”栏缺失的人，更可能是收入状况不稳定或收入比较低的人。基于这种判断， 
把所有收入为空的客户都当成是低收入人群。当然了，也有可能这些 缺失是银行数据收集过程中的失误，我们并无法判断为什么收入栏会有缺失，所以我们的推断也有可能是不正确 
的。具体采用什么样的手段填补缺失值，要和业务人员去沟通，观察缺失值是如何产生的。在这里，我们使用随机 森林填补“收入”。

*首先建立新的特征和标签矩阵
df = X.copy() # df是原始特征矩阵，145563行 * 10列 
fill = df.loc[:, to_fill] # 要填充的列/income
df = pd.concat([df.loc[:, df.columns != to_fill], pd.DataFrame(y)], axis=1)
#把原特征矩阵df除了”income“的剩下9列特征和标签y，一共10列组成新的df特征和标签的矩阵



*找出训练集和测试集
Ytrain = fill[fill.notnull()] # 在income中没有缺失值的属性作为训练集Ytrain
Ytest = fill[fill.isnull()] # 在income中有缺失值的空格作为测试集Ytest
Xtrain = df.iloc[Ytrain.index, :] # 在新的10列特征和标签中，Ytrain对于索引的所有列/9个特征当做训练集Xtrain
Xtest = df.iloc[Ytest.index, :] # 在新的10列特征和标签中，Ytest对于索引的所有列/9个特征当做测试集Xtest






{"cells": [{"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Table of Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><ul class=\"toc-item\"><li><span><a href=\"#分析背景\" data-toc-modified-id=\"分析背景-0.1\">分析背景</a></span></li><li><span><a href=\"#分析目标\" data-toc-modified-id=\"分析目标-0.2\">分析目标</a></span></li></ul></li><li><span><a href=\"#1-数据概况分析\" data-toc-modified-id=\"1-数据概况分析-1\">1 数据概况分析</a></span><ul class=\"toc-item\"><li><span><a href=\"#1.1-数据预览\" data-toc-modified-id=\"1.1-数据预览-1.1\">1.1 数据预览</a></span><ul class=\"toc-item\"><li><span><a href=\"#指标解释\" data-toc-modified-id=\"指标解释-1.1.1\">指标解释</a></span></li></ul></li><li><span><a href=\"#1.2-数据清洗\" data-toc-modified-id=\"1.2-数据清洗-1.2\">1.2 数据清洗</a></span><ul class=\"toc-item\"><li><span><a href=\"#1.2.1-类别型变量\" data-toc-modified-id=\"1.2.1-类别型变量-1.2.1\">1.2.1 类别型变量</a></span></li></ul></li></ul></li><li><span><a href=\"#2-单变量分析\" data-toc-modified-id=\"2-单变量分析-2\">2 单变量分析</a></span><ul class=\"toc-item\"><li><span><a href=\"#2.1-观察样本0、1的平衡性\" data-toc-modified-id=\"2.1-观察样本0、1的平衡性-2.1\">2.1 观察样本0、1的平衡性</a></span></li><li><span><a href=\"#2.2-观察均值大小\" data-toc-modified-id=\"2.2-观察均值大小-2.2\">2.2 观察均值大小</a></span><ul class=\"toc-item\"><li><span><a href=\"#对于数据类型为0和1的变量，观察均值大小可以帮助我们分析这个变量在flag上的分布：\" data-toc-modified-id=\"对于数据类型为0和1的变量，观察均值大小可以帮助我们分析这个变量在flag上的分布：-2.2.1\">对于数据类型为0和1的变量，观察均值大小可以帮助我们分析这个变量在flag上的分布：</a></span></li></ul></li><li><span><a href=\"#2.3-可视化\" data-toc-modified-id=\"2.3-可视化-2.3\">2.3 可视化</a></span></li></ul></li><li><span><a href=\"#3-相关和可视化\" data-toc-modified-id=\"3-相关和可视化-3\">3 相关和可视化</a></span></li><li><span><a href=\"#4-逻辑回归模型的建立和评估\" data-toc-modified-id=\"4-逻辑回归模型的建立和评估-4\">4 逻辑回归模型的建立和评估</a></span><ul class=\"toc-item\"><li><span><a href=\"#4.1-模型建立\" data-toc-modified-id=\"4.1-模型建立-4.1\">4.1 模型建立</a></span><ul class=\"toc-item\"><li><span><a href=\"#4.1.1-抽取训练集和测试集并进行拟合\" data-toc-modified-id=\"4.1.1-抽取训练集和测试集并进行拟合-4.1.1\">4.1.1 抽取训练集和测试集并进行拟合</a></span></li><li><span><a href=\"#4.1.2-查看模型结果\" data-toc-modified-id=\"4.1.2-查看模型结果-4.1.2\">4.1.2 查看模型结果</a></span></li></ul></li><li><span><a href=\"#4.2-模型评估\" data-toc-modified-id=\"4.2-模型评估-4.2\">4.2 模型评估</a></span><ul class=\"toc-item\"><li><span><a href=\"#4.2.1-评估方法一：计算准确度\" data-toc-modified-id=\"4.2.1-评估方法一：计算准确度-4.2.1\">4.2.1 评估方法一：计算准确度</a></span><ul class=\"toc-item\"><li><span><a href=\"#比较训练集和测试集的准确率，保证内在信息一致性:\" data-toc-modified-id=\"比较训练集和测试集的准确率，保证内在信息一致性:-4.2.1.1\">比较训练集和测试集的准确率，保证内在信息一致性:</a></span></li></ul></li><li><span><a href=\"#4.2.2-评估方法二：ROC和AUC\" data-toc-modified-id=\"4.2.2-评估方法二：ROC和AUC-4.2.2\">4.2.2 评估方法二：ROC和AUC</a></span></li></ul></li><li><span><a href=\"#4.3-模型优化\" data-toc-modified-id=\"4.3-模型优化-4.3\">4.3 模型优化</a></span></li></ul></li><li><span><a href=\"#5-业务建议\" data-toc-modified-id=\"5-业务建议-5\">5 业务建议</a></span><ul class=\"toc-item\"><li><span><a href=\"#5.1-用户分析\" data-toc-modified-id=\"5.1-用户分析-5.1\">5.1 用户分析</a></span></li><li><span><a href=\"#5.2-提高优惠券使用率分析---高价值用户\" data-toc-modified-id=\"5.2-提高优惠券使用率分析---高价值用户-5.2\">5.2 提高优惠券使用率分析 - 高价值用户</a></span></li><li><span><a href=\"#5.3-结论\" data-toc-modified-id=\"5.3-结论-5.3\">5.3 结论</a></span></li></ul></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 分析背景\n", "“天猫”（英文：Tmail，亦称淘宝商城，天猫商城）原名淘宝商城，是一个综合性购物网站，也是马云淘宝网打造的B2C(Business-to-Consumer, 商业零售)品牌。其整合数千家品牌商、生产商，为商家和消费者之间提供一站式解决方案，提供100%品质保证的商品，7天无理由退货的售后服务，以及购物积分返现等优质服务"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 分析目标\n", "根据用户数据以及消费行为数据\n", "- 使用Python建立分类模型，进行逻辑回归\n", "- 预测使用优惠券概率较高的客群"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1 数据概况分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.1 数据预览"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 指标解释\n", "- ID\t记录编码\n", "\n", "- age\t年龄\n", "\n", "- job\t职业\n", "\n", "- marital\t婚姻状态\n", "\n", "- default\t花呗是否有违约\n", "\n", "- returned\t是否有过退货\n", "\n", "- loan\t是否使用花呗结账\n", "\n", "- coupon_used_in_last6_month\t过去六个月使用的优惠券数量\n", "\n", "- coupon_used_in_last_month\t过去一个月使用的优惠券数量\n", "\n", "- coupon_ind\t该次活动中是否有使用优惠券"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th>age</th>\n", "      <th>job</th>\n", "      <th>marital</th>\n", "      <th>default</th>\n", "      <th>returned</th>\n", "      <th>loan</th>\n", "      <th>coupon_used_in_last6_month</th>\n", "      <th>coupon_used_in_last_month</th>\n", "      <th>coupon_ind</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>43</td>\n", "      <td>management</td>\n", "      <td>married</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>42</td>\n", "      <td>technician</td>\n", "      <td>divorced</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>47</td>\n", "      <td>admin.</td>\n", "      <td>married</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>yes</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>28</td>\n", "      <td>management</td>\n", "      <td>single</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>yes</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>42</td>\n", "      <td>technician</td>\n", "      <td>divorced</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   ID  age         job   marital default returned loan  \\\n", "0   1   43  management   married      no      yes   no   \n", "1   2   42  technician  divorced      no      yes   no   \n", "2   3   47      admin.   married      no      yes  yes   \n", "3   4   28  management    single      no      yes  yes   \n", "4   5   42  technician  divorced      no      yes   no   \n", "\n", "   coupon_used_in_last6_month  coupon_used_in_last_month  coupon_ind  \n", "0                           2                          0           0  \n", "1                           1                          1           0  \n", "2                           2                          0           0  \n", "3                           2                          0           0  \n", "4                           5                          0           0  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["#导入模块和数据\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "%matplotlib inline\n", "\n", "coupon = pd.read_csv('tianmao.csv')\n", "coupon.head()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["(25317, 10)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["#查看数据行列总数\n", "coupon.shape"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 25317 entries, 0 to 25316\n", "Data columns (total 10 columns):\n", " #   Column                      Non-Null Count  Dtype \n", "---  ------                      --------------  ----- \n", " 0   ID                          25317 non-null  int64 \n", " 1   age                         25317 non-null  int64 \n", " 2   job                         25317 non-null  object\n", " 3   marital                     25317 non-null  object\n", " 4   default                     25317 non-null  object\n", " 5   returned                    25317 non-null  object\n", " 6   loan                        25317 non-null  object\n", " 7   coupon_used_in_last6_month  25317 non-null  int64 \n", " 8   coupon_used_in_last_month   25317 non-null  int64 \n", " 9   coupon_ind                  25317 non-null  int64 \n", "dtypes: int64(5), object(5)\n", "memory usage: 1.9+ MB\n"]}], "source": ["#查看数据是否有缺失值\n", "coupon.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["查看完数据整体后，发现无缺失值"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.2 数据清洗"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2.1 类别型变量"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>default_no</th>\n", "      <th>default_yes</th>\n", "      <th>returned_no</th>\n", "      <th>returned_yes</th>\n", "      <th>loan_no</th>\n", "      <th>loan_yes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   default_no  default_yes  returned_no  returned_yes  loan_no  loan_yes\n", "0           1            0            0             1        1         0\n", "1           1            0            0             1        1         0\n", "2           1            0            0             1        0         1\n", "3           1            0            0             1        0         1\n", "4           1            0            0             1        1         0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["#将类别型变量转换为数字型变量，便于之后分析\n", "#但在本案例中，为了后续分析方便，只处理default, returned, loan这三个变量，保留job, marital \n", "#把default、returned、loan三个变量单独取出来进行哑变量处理get_dummies()。\n", "coupon1 = coupon[['default','returned','loan']]\n", "coupon1 = pd.get_dummies(coupon1)\n", "coupon1.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th>age</th>\n", "      <th>job</th>\n", "      <th>marital</th>\n", "      <th>default</th>\n", "      <th>returned</th>\n", "      <th>loan</th>\n", "      <th>coupon_used_in_last6_month</th>\n", "      <th>coupon_used_in_last_month</th>\n", "      <th>coupon_ind</th>\n", "      <th>default_no</th>\n", "      <th>default_yes</th>\n", "      <th>returned_no</th>\n", "      <th>returned_yes</th>\n", "      <th>loan_no</th>\n", "      <th>loan_yes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>43</td>\n", "      <td>management</td>\n", "      <td>married</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>42</td>\n", "      <td>technician</td>\n", "      <td>divorced</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>47</td>\n", "      <td>admin.</td>\n", "      <td>married</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>yes</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>28</td>\n", "      <td>management</td>\n", "      <td>single</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>yes</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>42</td>\n", "      <td>technician</td>\n", "      <td>divorced</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   ID  age         job   marital default returned loan  \\\n", "0   1   43  management   married      no      yes   no   \n", "1   2   42  technician  divorced      no      yes   no   \n", "2   3   47      admin.   married      no      yes  yes   \n", "3   4   28  management    single      no      yes  yes   \n", "4   5   42  technician  divorced      no      yes   no   \n", "\n", "   coupon_used_in_last6_month  coupon_used_in_last_month  coupon_ind  \\\n", "0                           2                          0           0   \n", "1                           1                          1           0   \n", "2                           2                          0           0   \n", "3                           2                          0           0   \n", "4                           5                          0           0   \n", "\n", "   default_no  default_yes  returned_no  returned_yes  loan_no  loan_yes  \n", "0           1            0            0             1        1         0  \n", "1           1            0            0             1        1         0  \n", "2           1            0            0             1        0         1  \n", "3           1            0            0             1        0         1  \n", "4           1            0            0             1        1         0  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["#把处理后的表格和原表格进行拼接\n", "coupon = pd.concat([coupon, coupon1], axis = 1)\n", "coupon.head()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 25317 entries, 0 to 25316\n", "Data columns (total 9 columns):\n", " #   Column                      Non-Null Count  Dtype \n", "---  ------                      --------------  ----- \n", " 0   age                         25317 non-null  int64 \n", " 1   job                         25317 non-null  object\n", " 2   marital                     25317 non-null  object\n", " 3   coupon_used_in_last6_month  25317 non-null  int64 \n", " 4   coupon_used_in_last_month   25317 non-null  int64 \n", " 5   flag                        25317 non-null  int64 \n", " 6   default_yes                 25317 non-null  uint8 \n", " 7   returned_yes                25317 non-null  uint8 \n", " 8   loan_yes                    25317 non-null  uint8 \n", "dtypes: int64(4), object(2), uint8(3)\n", "memory usage: 1.2+ MB\n"]}], "source": ["#删除包含重复信息和无意义信息的数据\n", "coupon.drop(['ID', 'default', 'default_no', 'returned', 'returned_no', 'loan', 'loan_no'], axis = 1, inplace = True)\n", "\n", "#将coupon_ind重新命名为flag，便于之后分析\n", "coupon = coupon.rename(columns = {'coupon_ind' : 'flag'})\n", "coupon.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2 单变量分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.1 观察样本0、1的平衡性"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    0.883043\n", "1    0.116957\n", "Name: flag, dtype: float64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["#二分类模型，观察样本(flag)0,1的平衡性\n", "coupon['flag'].value_counts(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 在二分类问题中，0、1的占比要保持平衡型，实际情况中不低于0.05，否则会影响模型的预测\n", "- 该数据集0、1占比均高于0.05，因此其分布是合理的"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.2 观察均值大小"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_12876\\2235428395.py:4: FutureWarning: The default value of numeric_only in DataFrameGroupBy.mean is deprecated. In a future version, numeric_only will default to False. Either specify numeric_only or select only columns which should be valid for the function.\n", "  summary.mean()\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>coupon_used_in_last6_month</th>\n", "      <th>coupon_used_in_last_month</th>\n", "      <th>default_yes</th>\n", "      <th>returned_yes</th>\n", "      <th>loan_yes</th>\n", "    </tr>\n", "    <tr>\n", "      <th>flag</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>40.819601</td>\n", "      <td>2.857846</td>\n", "      <td>0.260378</td>\n", "      <td>0.018876</td>\n", "      <td>0.579755</td>\n", "      <td>0.169037</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>41.809524</td>\n", "      <td>2.124282</td>\n", "      <td>0.537994</td>\n", "      <td>0.008781</td>\n", "      <td>0.357649</td>\n", "      <td>0.094563</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            age  coupon_used_in_last6_month  coupon_used_in_last_month  \\\n", "flag                                                                     \n", "0     40.819601                    2.857846                   0.260378   \n", "1     41.809524                    2.124282                   0.537994   \n", "\n", "      default_yes  returned_yes  loan_yes  \n", "flag                                       \n", "0        0.018876      0.579755  0.169037  \n", "1        0.008781      0.357649  0.094563  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["#先按客户是否使用coupon进行分类聚合\n", "summary = coupon.groupby(['flag'])\n", "#求出各种情况均值的占比情况\n", "summary.mean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 对于数据类型为0和1的变量，观察均值大小可以帮助我们分析这个变量在flag上的分布：\n", "- coupon_used_in_last_month在0的均值为0.26，在1的均值为0.53，说明越是在上个月使用了coupon的客户，接下来再使用coupon的概率会越高\n", "- default_yes和loan_yes在0时的均值均大于在1时的均值，说明花呗违约和用花呗结账的客户在接下来的时间里使用coupon的概率较小\n", "- age在0和1的均值分别为40.8和41.8，差别不大，说明年龄无太大的区分关系"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.3 可视化"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot: xlabel='count', ylabel='returned_yes'>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#观察returned_yes在flag上的分布\n", "sns.countplot(y = 'returned_yes', hue = 'flag', data = coupon)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 相比起没有退货的客户，退货的客户使用coupon的概率较小"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot: xlabel='count', ylabel='marital'>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAmYAAAGwCAYAAAADo6klAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjYuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8o6BhiAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAvbklEQVR4nO3deXQUZb7G8aezJ4QkECCLJBAWI2jYZR9FiUb0osAoDOZCQB0VQYgwgIwKooMiyI77uQ7iMIo4gFwXECMoMuybRNkXw0hCFMzGlpB+7x8c+tomIAkJ/UK+n3P6nHTV21W/95eTznOqq6odxhgjAAAAeJyXpwsAAADAWQQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACzh4+kCcJbT6dThw4dVvXp1ORwOT5cDAAAugjFG+fn5io6OlpfXpR/vIphZ4vDhw4qJifF0GQAAoBwOHTqkunXrXvJ2CGaWqF69uqSzv9iQkBAPVwMAAC5GXl6eYmJiXP/HLxXBzBLnPr4MCQkhmAEAcIWpqNOQOPkfAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMAS3PnfMjc9/Z68/QM9XQYAAFeVTZP7e7qEi8IRMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgSz35gzZ47CwsIueTsOh0OLFy++5O0AAICqg2D2G3369NHu3bs9XQYAAKiCfDxdgCcUFhbKz8+vxPKioiIFBgYqMDDQA1UBAICqzrojZl26dNHjjz+u1NRU1ahRQxEREXrrrbd0/PhxDRw4UNWrV1ejRo302WefSZKKi4v14IMPKi4uToGBgYqPj9eMGTPctjlgwAD16NFDEyZMUHR0tOLj43Xw4EE5HA7Nnz9fN998swICAjRv3rxSP8r86KOP1KpVKwUEBKhBgwYaP368zpw541q/Z88e3XTTTQoICFDTpk21fPnySu8TAAC4+lh5xOydd97RqFGjtH79es2fP1+DBg3SokWL1LNnT/31r3/VtGnT1K9fP2VkZMjX11d169bVggULFB4ern//+996+OGHFRUVpd69e7u2mZaWppCQkBKh6cknn9SUKVPUsmVLBQQEaNmyZW7rV61apf79+2vmzJn6wx/+oH379unhhx+WJI0bN05Op1O9evVSRESE1q1bp9zcXKWmpv7uHE+fPq3Tp0+7nufl5V1CxwAAwNXAYYwxni7i17p06aLi4mKtWrVK0tkjYqGhoerVq5fmzp0rScrKylJUVJTWrFmj9u3bl9jGkCFDlJWVpQ8//FDS2SNmS5cuVUZGhusjzIMHDyouLk7Tp0/XsGHDXK+dM2eOUlNTlZOTI0lKTExU165dNWbMGNeYf/zjHxo1apQOHz6szz//XHfddZd++OEHRUdHS5KWLl2qbt26adGiRerRo0ep83z22Wc1fvz4EsvTxzRR9QDvMnbNbrFjt3u6BAAAKkVeXp5CQ0OVm5urkJCQS96elUfMmjVr5vrZ29tb4eHhSkhIcC2LiIiQJGVnZ0uSXnnlFb399tvKyMjQyZMnVVhYqBYtWrhtMyEhodTzytq0aXPBWrZt26bVq1drwoQJrmXFxcU6deqUTpw4oR07digmJsYVyiSpQ4cOvzvHMWPGaPjw4a7neXl5iomJ+d3XAQCAq5eVwczX19ftucPhcFvmcDgkSU6nU++//77+8pe/aMqUKerQoYOqV6+uyZMna926dW7bqFatWqn7Ot/ycwoKCjR+/Hj16tWrxLqAgICLmk9p/P395e/vX+7XAwCAq4+VwawsVq9erY4dO+qxxx5zLdu3b1+Fbb9Vq1batWuXGjVqVOr6Jk2a6NChQ8rMzFRUVJQkae3atRW2fwAAUHVc8cGscePGmjt3rpYtW6a4uDi9++672rBhg+Li4ipk+2PHjtV//dd/KTY2Vvfee6+8vLy0bds2paen629/+5sSExN17bXXKiUlRZMnT1ZeXp6eeuqpCtk3AACoWqy7XUZZPfLII+rVq5f69Omjdu3a6ejRo25Hzy5VUlKSPv74Y33++ee68cYb1b59e02bNk316tWTJHl5eWnRokU6efKk2rZtq4ceesjtfDQAAICLZd1VmVXVuas6uCoTAIArR0VflXnFHzEDAAC4WhDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACzh4+kC4C7mybUKCQnxdBkAAMADOGIGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYwsfTBcDdTU+/J2//QE+XcdXaNLm/p0sAAOC8OGIGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWMLnYgfOnDnzojc6dOjQchUDAABQlV10MJs2bdpFjXM4HAQzAACAcrjoYHbgwIHKrAMAAKDK4xwzAAAAS1z0EbPf+s9//qMlS5YoIyNDhYWFbuumTp16yYUBAABUNeUKZmlpabr77rvVoEED7dy5UzfccIMOHjwoY4xatWpV0TUCAABUCeX6KHPMmDH6y1/+ou3btysgIED/+te/dOjQId1888267777KrpGAACAKqFcwWzHjh3q37+/JMnHx0cnT55UcHCwnnvuOb300ksVWiAAAEBVUa5gVq1aNdd5ZVFRUdq3b59r3c8//1wxlQEAAFQx5TrHrH379vrmm2/UpEkT3XnnnRoxYoS2b9+uhQsXqn379hVdIwAAQJVQrmA2depUFRQUSJLGjx+vgoICzZ8/X40bN+aKTAAAgHIqVzBr0KCB6+dq1arp9ddfr7CCAAAAqqpynWPWoEEDHT16tMTynJwct9AGAACAi1euYHbw4EEVFxeXWH769Gn9+OOPl1wUAABAVVSmjzKXLFni+nnZsmUKDQ11PS8uLlZaWprq169fYcUBAABUJWUKZj169JAkORwOpaSkuK3z9fVV/fr1NWXKlEsqqEuXLmrRooWmT5+u+vXrKzU1VampqZe0TU8YMGCAcnJytHjxYk+XAgAArhBlCmZOp1OSFBcXpw0bNqhWrVqVUtQ5GzZsULVq1Sp1HwAAALYo11WZBw4cqOg6SlW7du1K3b4xRsXFxfLxKfd3uQMAAFSYiz75f+bMmTp16pTr5ws9Ltbx48fVv39/BQcHKyoqqsTHoPXr19f06dMlSffff7/69Onjtr6oqEi1atXS3LlzJZ29+GDo0KGqU6eOAgIC1LlzZ23YsME1fuXKlXI4HPrss8/UunVr+fv765tvvpHT6dSkSZPUqFEj+fv7KzY2VhMmTHC97tChQ+rdu7fCwsJUs2ZN3XPPPTp48KBrfXFxsYYPH66wsDCFh4dr1KhRMsZcdB8AAACkMhwxmzZtmpKTkxUQEKCpU6fK4XCUOs7hcGjo0KEXtc2RI0fqq6++0kcffaQ6deror3/9qzZv3qwWLVqUGJucnKz77rtPBQUFCg4OlnT2AoQTJ06oZ8+ekqRRo0bpX//6l9555x3Vq1dPkyZNUlJSkvbu3auaNWu6tvXkk0/q5ZdfVoMGDVSjRg2NGTNGb731lqZNm6bOnTsrMzNTO3fulHQ2/CUlJalDhw5atWqVfHx89Le//U133HGHvv32W/n5+WnKlCmaM2eO3n77bTVp0kRTpkzRokWLdOutt5537qdPn9bp06ddz/Py8i6qZwAA4CpmPCQ/P9/4+fmZDz74wLXs6NGjJjAw0AwbNswYY0y9evXMtGnTjDHGFBUVmVq1apm5c+e6xvft29f06dPHGGNMQUGB8fX1NfPmzXOtLywsNNHR0WbSpEnGGGNWrFhhJJnFixe7xuTl5Rl/f3/z1ltvlVrnu+++a+Lj443T6XQtO336tAkMDDTLli0zxhgTFRXl2se5WuvWrWvuueee885/3LhxRlKJR/qYJuaH8Tdc1AMAAHhWbm6ukWRyc3MrZHtlvo9ZUVGRGjZsqB07dlxSINy3b58KCwvVrl0717KaNWsqPj6+1PE+Pj7q3bu35s2bJ+nsx6AfffSRkpOTXdsrKipSp06dXK/x9fVV27ZtS9Tapk0b1887duzQ6dOn1bVr11L3u23bNu3du1fVq1dXcHCwgoODVbNmTZ06dUr79u1Tbm6uMjMz3ebh4+Pjto/SjBkzRrm5ua7HoUOHLjgeAABc/cp81ruvr6/rXLPLLTk5WTfffLOys7O1fPlyBQYG6o477ijzdn59pWdgYOAFxxYUFKh169auQPhrl3Jxgr+/v/z9/cv9egAAcPUp153/Bw8erJdeeklnzpwp944bNmwoX19frVu3zrXsl19+0e7du8/7mo4dOyomJkbz58/XvHnzdN9998nX19e1PT8/P61evdo1vqioSBs2bFDTpk3Pu83GjRsrMDBQaWlppa5v1aqV9uzZozp16qhRo0Zuj9DQUIWGhioqKsptHmfOnNGmTZsuuhcAAABSOW+XsWHDBqWlpenzzz9XQkJCiXuNLVy48He3ERwcrAcffFAjR45UeHi46tSpo6eeekpeXhfOivfff79ef/117d69WytWrHAtr1atmgYNGqSRI0eqZs2aio2N1aRJk3TixAk9+OCD591eQECARo8erVGjRsnPz0+dOnXSTz/9pO+++04PPvigkpOTNXnyZN1zzz167rnnVLduXf3www9auHChRo0apbp162rYsGGaOHGiGjdurOuuu05Tp05VTk7O7/YAAADg18oVzMLCwvTHP/7xknc+efJkFRQUqHv37qpevbpGjBih3NzcC74mOTlZEyZMUL169dzOJ5OkiRMnyul0ql+/fsrPz1ebNm20bNky1ahR44LbfOaZZ+Tj46OxY8fq8OHDioqK0qOPPipJCgoK0tdff63Ro0erV69eys/P1zXXXKOuXbsqJCREkjRixAhlZmYqJSVFXl5eeuCBB9SzZ8/fnQsAAMCvOYzhhls2yMvLU2hoqNLHNFH1AO+Lek3s2O2VXBUAALiQc/+/c3NzXQdsLkW5zjEDAABAxSv3dxF9+OGH+uCDD5SRkaHCwkK3dZs3b77kwgAAAKqach0xmzlzpgYOHKiIiAht2bJFbdu2VXh4uPbv369u3bpVdI0AAABVQrmC2auvvqo333xTs2bNkp+fn0aNGqXly5dr6NChnPAOAABQTuUKZhkZGerYsaOkszdozc/PlyT169dP7733XsVVBwAAUIWUK5hFRkbq2LFjkqTY2FitXbtWknTgwAFxkScAAED5lCuY3XrrrVqyZIkkaeDAgXriiSd02223qU+fPurZs2eFFggAAFBVlOuqzDfffFNOp1PS2a9nqlWrllavXq27777bdWNWAAAAlE25gpmXl5cKCwu1efNmZWdnKzAwUImJiZKkpUuXqnv37hVaJAAAQFVQrmC2dOlS9evXT0ePHi2xzuFwqLi4+JILAwAAqGrKdY7Z448/rt69eyszM1NOp9PtQSgDAAAon3IFsyNHjmj48OGKiIio6HoAAACqrHIFs3vvvVcrV66s4FIAAACqtnKdYzZ79mzdd999WrVqlRISEuTr6+u2fujQoRVSHAAAQFVSrmD23nvv6fPPP1dAQIBWrlwph8PhWudwOAhmAAAA5VCuYPbUU09p/PjxevLJJ+XlVa5PQwEAAPAb5UpVhYWF6tOnD6EMAACgApUrWaWkpGj+/PkVXQsAAECVVq6PMouLizVp0iQtW7ZMzZo1K3Hy/9SpUyukOAAAgKqkXMFs+/btatmypSQpPT3dbd2vLwQAAADAxStXMFuxYkVF1wEAAFDlcfY+AACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlfDxdANzFPLlWISEhni4DAAB4AEfMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAAS/h4ugC4u+np9+TtH+jpMoAr2qbJ/T1dAgCUC0fMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEj6eLgAAAFzZiouLVVRU5OkyKo2fn5+8vC7PsSyCGQAAKBdjjLKyspSTk+PpUiqVl5eX4uLi5OfnV+n7IpgBAIByORfK6tSpo6CgIDkcDk+XVOGcTqcOHz6szMxMxcbGVvocCWYAAKDMiouLXaEsPDzc0+VUqtq1a+vw4cM6c+aMfH19K3VfnPwPAADK7Nw5ZUFBQR6upPKd+wizuLi40vdFMAMAAOV2NX58+VuXc44EMwAAAEsQzAAAgEcZY/Twww+rZs2acjgcCgsLU2pqqqfL8giCGQAA8KilS5dqzpw5+vjjj5WZmakbbrjB0yV5zFUfzAYMGKAePXpU6DYPHjwoh8OhrVu3Vuh2AQCoivbt26eoqCh17NhRkZGR8vGpujeNuOqD2YwZMzRnzhxPlwEAAEoxYMAAPf7448rIyJDD4VD9+vVLjHn33XfVpk0bVa9eXZGRkbr//vuVnZ3tNmbJkiVq3LixAgICdMstt+idd96Rw+G44m5+e9UHs9DQUIWFhXm6DAAAUIoZM2boueeeU926dZWZmakNGzaUGFNUVKTnn39e27Zt0+LFi3Xw4EENGDDAtf7AgQO699571aNHD23btk2PPPKInnrqqcs4i4pz1QSzDz/8UAkJCQoMDFR4eLgSExN1/PjxEh9ldunSRUOHDtWoUaNUs2ZNRUZG6tlnn3Xb1s6dO9W5c2cFBASoadOm+uKLL+RwOLR48eLz7j89PV3dunVTcHCwIiIi1K9fP/3888/nHX/69Gnl5eW5PQAAqGpCQ0NVvXp1eXt7KzIyUrVr1y4x5oEHHlC3bt3UoEEDtW/fXjNnztRnn32mgoICSdIbb7yh+Ph4TZ48WfHx8frTn/7kFtyuJFfFh7iZmZnq27evJk2apJ49eyo/P1+rVq2SMabU8e+8846GDx+udevWac2aNRowYIA6deqk2267TcXFxerRo4diY2O1bt065efna8SIERfcf05Ojm699VY99NBDmjZtmk6ePKnRo0erd+/e+vLLL0t9zYsvvqjx48eXWD4veIaqB3iXvQmVIHbsdk+XAACANm3apGeffVbbtm3TL7/8IqfTKUnKyMhQ06ZNtWvXLt14441ur2nbtq0nSr1kV00wO3PmjHr16qV69epJkhISEs47vlmzZho3bpwkqXHjxpo9e7bS0tJ02223afny5dq3b59WrlypyMhISdKECRN02223nXd7s2fPVsuWLfXCCy+4lr399tuKiYnR7t27de2115Z4zZgxYzR8+HDX87y8PMXExJRt4gAAXOWOHz+upKQkJSUlad68eapdu7YyMjKUlJSkwsJCT5dX4a6KYNa8eXN17dpVCQkJSkpK0u233657771XNWrUKHV8s2bN3J5HRUW5TiLctWuXYmJiXKFM+v3UvW3bNq1YsULBwcEl1u3bt6/UYObv7y9/f//fnRsAAFXZzp07dfToUU2cONF1AGPjxo1uY+Lj4/Xpp5+6LSvtXLUrwVVxjpm3t7eWL1+uzz77TE2bNtWsWbMUHx+vAwcOlDr+t19A6nA4XIdFy6OgoEDdu3fX1q1b3R579uzRTTfdVO7tAgBQ1cXGxsrPz0+zZs3S/v37tWTJEj3//PNuYx555BHt3LlTo0eP1u7du/XBBx+47shwpX1l1FURzKSzje/UqZPGjx+vLVu2yM/PT4sWLSrzduLj43Xo0CEdOXLEtez3UnerVq303XffqX79+mrUqJHbo1q1amWuAQAAnFW7dm3NmTNHCxYsUNOmTTVx4kS9/PLLbmPi4uL04YcfauHChWrWrJlee+0111WZV9qnU1dFMFu3bp1eeOEFbdy4URkZGVq4cKF++uknNWnSpMzbuu2229SwYUOlpKTo22+/1erVq/X0009LOn/qHjx4sI4dO6a+fftqw4YN2rdvn5YtW6aBAwdelm+iBwDgSpaamqqDBw+6nq9cuVLTp093Pe/bt68OHDigU6dO6d///re6d+8uY4xatGjhGnP33Xdrz549OnXqlFasWKGjR4+qbt26CggIuHwTqQBXRTALCQnR119/rTvvvFPXXnutnn76aU2ZMkXdunUr87a8vb21ePFiFRQU6MYbb9RDDz3kSt3n++VGR0dr9erVKi4u1u23366EhASlpqYqLCxMXl5XRYsBALDaq6++qg0bNmj//v169913NXnyZKWkpHi6rDJzmPPdUwIuq1evVufOnbV37141bNiwUvaRl5en0NBQpY9pwu0yAADWO3XqlA4cOKC4uDgrjko98cQTmj9/vo4dO6bY2Fj169dPY8aMqZCvd7rQXM/9/87NzVVISMgl7+uquCqzoi1atEjBwcFq3Lix9u7dq2HDhqlTp06VFsoAAMClmTZtmqZNm+bpMi4ZwawU+fn5Gj16tDIyMlSrVi0lJiZqypQpni4LAABc5Qhmpejfv7/69+/v6TIAAEAVw5npAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJbgqkwAAFChWo+ce1n3t2ly+e6k8Morr2jy5MnKyspS8+bNNWvWLLVt27aCqysbjpgBAIAqZ/78+Ro+fLjGjRunzZs3q3nz5kpKSlJ2drZH6yKYAQCAKmfq1Kn685//rIEDB6pp06Z6/fXXFRQUpLffftujdRHMAABAlVJYWKhNmzYpMTHRtczLy0uJiYlas2aNBysjmAEAgCrm559/VnFxsSIiItyWR0REKCsry0NVnUUwAwAAsATBDAAAVCm1atWSt7e3jhw54rb8yJEjioyM9FBVZxHMAABAleLn56fWrVsrLS3NtczpdCotLU0dOnTwYGXcxwwAAFRBw4cPV0pKitq0aaO2bdtq+vTpOn78uAYOHOjRughmAACgQpX3hq+XU58+ffTTTz9p7NixysrKUosWLbR06dISFwRcbgQzAABQJQ0ZMkRDhgzxdBluOMcMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABL8JVMAACgQmU8l3BZ9xc7dnuZxn/99deaPHmyNm3apMzMTC1atEg9evSonOLKiCNmAACgSjl+/LiaN2+uV155xdOllMARMwAAUKV069ZN3bp183QZpeKIGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAluCoTAABUKQUFBdq7d6/r+YEDB7R161bVrFlTsbGxHqyMYAYAACpYWW/4erlt3LhRt9xyi+v58OHDJUkpKSmaM2eOh6o6i2AGAACqlC5dusgY4+kySsU5ZgAAAJbgiJllYp5cq5CQEE+XAQAAPIAjZgAAAJYgmAEAAFiCYAYAAMrN1pPoK9LlnCPBDAAAlJmvr68k6cSJEx6upPIVFhZKkry9vSt9X5z8DwAAyszb21thYWHKzs6WJAUFBcnhcHi4qorndDr1008/KSgoSD4+lR+bCGYAAKBcIiMjJckVzq5WXl5eio2NvSzBk2AGAADKxeFwKCoqSnXq1FFRUZGny6k0fn5+8vK6PGd/EcwAAMAl8fb2viznX1UFnPwPAABgCYIZAACAJQhmAAAAluAcM0ucu3ldXl6ehysBAAAX69z/7Yq6CS3BzBJHjx6VJMXExHi4EgAAUFb5+fkKDQ295O0QzCxRs2ZNSVJGRkaF/GKvBnl5eYqJidGhQ4cUEhLi6XKsQE9Koieloy8l0ZOS6ElJZe2JMUb5+fmKjo6ukP0TzCxx7v4ooaGh/HH8RkhICD35DXpSEj0pHX0piZ6URE9KKktPKvKACif/AwAAWIJgBgAAYAmCmSX8/f01btw4+fv7e7oUa9CTkuhJSfSkdPSlJHpSEj0pydM9cZiKur4TAAAAl4QjZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGaWeOWVV1S/fn0FBASoXbt2Wr9+vadLqhAvvviibrzxRlWvXl116tRRjx49tGvXLrcxp06d0uDBgxUeHq7g4GD98Y9/1JEjR9zGZGRk6K677lJQUJDq1KmjkSNH6syZM25jVq5cqVatWsnf31+NGjXSnDlzKnt6FWLixIlyOBxKTU11LauKPfnxxx/13//93woPD1dgYKASEhK0ceNG13pjjMaOHauoqCgFBgYqMTFRe/bscdvGsWPHlJycrJCQEIWFhenBBx9UQUGB25hvv/1Wf/jDHxQQEKCYmBhNmjTpssyvrIqLi/XMM88oLi5OgYGBatiwoZ5//nm37+O72nvy9ddfq3v37oqOjpbD4dDixYvd1l/O+S9YsEDXXXedAgIClJCQoE8//bTC53sxLtSToqIijR49WgkJCapWrZqio6PVv39/HT582G0bVaknv/Xoo4/K4XBo+vTpbsut6omBx73//vvGz8/PvP322+a7774zf/7zn01YWJg5cuSIp0u7ZElJSebvf/+7SU9PN1u3bjV33nmniY2NNQUFBa4xjz76qImJiTFpaWlm48aNpn379qZjx46u9WfOnDE33HCDSUxMNFu2bDGffvqpqVWrlhkzZoxrzP79+01QUJAZPny4+f77782sWbOMt7e3Wbp06WWdb1mtX7/e1K9f3zRr1swMGzbMtbyq9eTYsWOmXr16ZsCAAWbdunVm//79ZtmyZWbv3r2uMRMnTjShoaFm8eLFZtu2bebuu+82cXFx5uTJk64xd9xxh2nevLlZu3atWbVqlWnUqJHp27eva31ubq6JiIgwycnJJj093bz33nsmMDDQvPHGG5d1vhdjwoQJJjw83Hz88cfmwIEDZsGCBSY4ONjMmDHDNeZq78mnn35qnnrqKbNw4UIjySxatMht/eWa/+rVq423t7eZNGmS+f77783TTz9tfH19zfbt2yu9B791oZ7k5OSYxMREM3/+fLNz506zZs0a07ZtW9O6dWu3bVSlnvzawoULTfPmzU10dLSZNm2a2zqbekIws0Dbtm3N4MGDXc+Li4tNdHS0efHFFz1YVeXIzs42ksxXX31ljDn7RuLr62sWLFjgGrNjxw4jyaxZs8YYc/aPzsvLy2RlZbnGvPbaayYkJMScPn3aGGPMqFGjzPXXX++2rz59+pikpKTKnlK55efnm8aNG5vly5ebm2++2RXMqmJPRo8ebTp37nze9U6n00RGRprJkye7luXk5Bh/f3/z3nvvGWOM+f77740ks2HDBteYzz77zDgcDvPjjz8aY4x59dVXTY0aNVw9Orfv+Pj4ip7SJbvrrrvMAw884LasV69eJjk52RhT9Xry23+4l3P+vXv3NnfddZdbPe3atTOPPPJIhc6xrC4UQs5Zv369kWR++OEHY0zV7cl//vMfc80115j09HRTr149t2BmW0/4KNPDCgsLtWnTJiUmJrqWeXl5KTExUWvWrPFgZZUjNzdX0v9/afumTZtUVFTkNv/rrrtOsbGxrvmvWbNGCQkJioiIcI1JSkpSXl6evvvuO9eYX2/j3Bibezh48GDdddddJequij1ZsmSJ2rRpo/vuu0916tRRy5Yt9dZbb7nWHzhwQFlZWW7zCQ0NVbt27dx6EhYWpjZt2rjGJCYmysvLS+vWrXONuemmm+Tn5+cak5SUpF27dumXX36p7GmWSceOHZWWlqbdu3dLkrZt26ZvvvlG3bp1k1Q1e/Jrl3P+V9Lf0m/l5ubK4XAoLCxMUtXsidPpVL9+/TRy5Ehdf/31Jdbb1hOCmYf9/PPPKi4udvsHK0kRERHKysryUFWVw+l0KjU1VZ06ddINN9wgScrKypKfn5/rTeOcX88/Kyur1P6cW3ehMXl5eTp58mRlTOeSvP/++9q8ebNefPHFEuuqYk/279+v1157TY0bN9ayZcs0aNAgDR06VO+8846k/5/Thf5OsrKyVKdOHbf1Pj4+qlmzZpn6Zosnn3xSf/rTn3TdddfJ19dXLVu2VGpqqpKTkyVVzZ782uWc//nG2Nwf6ey5qqNHj1bfvn1dX8ZdFXvy0ksvycfHR0OHDi11vW098SnTaOASDB48WOnp6frmm288XYpHHTp0SMOGDdPy5csVEBDg6XKs4HQ61aZNG73wwguSpJYtWyo9PV2vv/66UlJSPFydZ3zwwQeaN2+e/vnPf+r666/X1q1blZqaqujo6CrbE1y8oqIi9e7dW8YYvfbaa54ux2M2bdqkGTNmaPPmzXI4HJ4u56JwxMzDatWqJW9v7xJX3B05ckSRkZEeqqriDRkyRB9//LFWrFihunXrupZHRkaqsLBQOTk5buN/Pf/IyMhS+3Nu3YXGhISEKDAwsKKnc0k2bdqk7OxstWrVSj4+PvLx8dFXX32lmTNnysfHRxEREVWuJ1FRUWratKnbsiZNmigjI0PS/8/pQn8nkZGRys7Odlt/5swZHTt2rEx9s8XIkSNdR80SEhLUr18/PfHEE66jrFWxJ792Oed/vjG29udcKPvhhx+0fPly19Eyqer1ZNWqVcrOzlZsbKzr/faHH37QiBEjVL9+fUn29YRg5mF+fn5q3bq10tLSXMucTqfS0tLUoUMHD1ZWMYwxGjJkiBYtWqQvv/xScXFxbutbt24tX19ft/nv2rVLGRkZrvl36NBB27dvd/vDOfdmc+6feYcOHdy2cW6MjT3s2rWrtm/frq1bt7oebdq0UXJysuvnqtaTTp06lbiNyu7du1WvXj1JUlxcnCIjI93mk5eXp3Xr1rn1JCcnR5s2bXKN+fLLL+V0OtWuXTvXmK+//lpFRUWuMcuXL1d8fLxq1KhRafMrjxMnTsjLy/0t2tvbW06nU1LV7MmvXc75X0l/S+dC2Z49e/TFF18oPDzcbX1V60m/fv307bffur3fRkdHa+TIkVq2bJkkC3tSpksFUCnef/994+/vb+bMmWO+//578/DDD5uwsDC3K+6uVIMGDTKhoaFm5cqVJjMz0/U4ceKEa8yjjz5qYmNjzZdffmk2btxoOnToYDp06OBaf+7WELfffrvZunWrWbp0qaldu3apt4YYOXKk2bFjh3nllVesvTVEaX59VaYxVa8n69evNz4+PmbChAlmz549Zt68eSYoKMj84x//cI2ZOHGiCQsLMx999JH59ttvzT333FPqrRFatmxp1q1bZ7755hvTuHFjt0vec3JyTEREhOnXr59JT08377//vgkKCrLi1hC/lZKSYq655hrX7TIWLlxoatWqZUaNGuUac7X3JD8/32zZssVs2bLFSDJTp041W7ZscV1heLnmv3r1auPj42Nefvlls2PHDjNu3DiP3RriQj0pLCw0d999t6lbt67ZunWr23vur68mrEo9Kc1vr8o0xq6eEMwsMWvWLBMbG2v8/PxM27Ztzdq1az1dUoWQVOrj73//u2vMyZMnzWOPPWZq1KhhgoKCTM+ePU1mZqbbdg4ePGi6detmAgMDTa1atcyIESNMUVGR25gVK1aYFi1aGD8/P9OgQQO3fdjut8GsKvbkf//3f80NN9xg/P39zXXXXWfefPNNt/VOp9M888wzJiIiwvj7+5uuXbuaXbt2uY05evSo6du3rwkODjYhISFm4MCBJj8/323Mtm3bTOfOnY2/v7+55pprzMSJEyt9buWRl5dnhg0bZmJjY01AQIBp0KCBeeqpp9z+wV7tPVmxYkWp7x8pKSnGmMs7/w8++MBce+21xs/Pz1x//fXmk08+qbR5X8iFenLgwIHzvueuWLHCtY2q1JPSlBbMbOqJw5hf3UYaAAAAHsM5ZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAYDFDh48KIfDoa1bt3q6FACXAcEMAADAEgQzALgAp9OpSZMmqVGjRvL391dsbKwmTJggSdq+fbtuvfVWBQYGKjw8XA8//LAKCgpcr+3SpYtSU1PdttejRw8NGDDA9bx+/fp64YUX9MADD6h69eqKjY3Vm2++6VofFxcnSWrZsqUcDoe6dOlSaXMF4HkEMwC4gDFjxmjixIl65pln9P333+uf//ynIiIidPz4cSUlJalGjRrasGGDFixYoC+++EJDhgwp8z6mTJmiNm3aaMuWLXrsscc0aNAg7dq1S5K0fv16SdIXX3yhzMxMLVy4sELnB8AuPp4uAABslZ+frxkzZmj27NlKSUmRJDVs2FCdO3fWW2+9pVOnTmnu3LmqVq2aJGn27Nnq3r27XnrpJUVERFz0fu6880499thjkqTRo0dr2rRpWrFiheLj41W7dm1JUnh4uCIjIyt4hgBswxEzADiPHTt26PTp0+ratWup65o3b+4KZZLUqVMnOZ1O19Gui9WsWTPXzw6HQ5GRkcrOzi5/4QCuWAQzADiPwMDAS3q9l5eXjDFuy4qKikqM8/X1dXvucDjkdDovad8ArkwEMwA4j8aNGyswMFBpaWkl1jVp0kTbtm3T8ePHXctWr14tLy8vxcfHS5Jq166tzMxM1/ri4mKlp6eXqQY/Pz/XawFc/QhmAHAeAQEBGj16tEaNGqW5c+dq3759Wrt2rf7nf/5HycnJCggIUEpKitLT07VixQo9/vjj6tevn+v8sltvvVWffPKJPvnkE+3cuVODBg1STk5OmWqoU6eOAgMDtXTpUh05ckS5ubmVMFMAtiCYAcAFPPPMMxoxYoTGjh2rJk2aqE+fPsrOzlZQUJCWLVumY8eO6cYbb9S9996rrl27avbs2a7XPvDAA0pJSVH//v118803q0GDBrrlllvKtH8fHx/NnDlTb7zxhqKjo3XPPfdU9BQBWMRhfnsCBAAAADyCI2YAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGCJ/wO/Q9+v8HaOtgAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#观察marital在flag上的分布\n", "sns.countplot(y = 'marital', hue = 'flag', data = coupon)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 已婚客户使用coupon的概率比未婚和离婚客户使用coupon的概率略高\n", "- 已婚人士未使用coupon的概率比未婚人士未使用coupon的概率也要高\n", "- 但是三者均未使用coupon的概率比使用coupon的概率要高得多"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot: xlabel='count', ylabel='job'>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#观察job在flag上的分布\n", "sns.countplot(y = 'job', hue = 'flag', data = coupon, order = coupon['job'].value_counts().index)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- job title为management, technician, blue-collar的客户越有可能使用coupon"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_12876\\966048336.py:2: UserWarning: \n", "\n", "`distplot` is a deprecated function and will be removed in seaborn v0.14.0.\n", "\n", "Please adapt your code to use either `displot` (a figure-level function with\n", "similar flexibility) or `histplot` (an axes-level function for histograms).\n", "\n", "For a guide to updating your code to use the new functions, please see\n", "https://gist.github.com/mwaskom/de44147ed2974457ad6372750bbe5751\n", "\n", "  sns.distplot(coupon['age'])\n"]}, {"data": {"text/plain": ["<AxesSubplot: xlabel='age', ylabel='Density'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#观察age在flag上的分布\n", "sns.distplot(coupon['age'])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["count    25317.000000\n", "mean        40.935379\n", "std         10.634289\n", "min         18.000000\n", "25%         33.000000\n", "50%         39.000000\n", "75%         48.000000\n", "max         95.000000\n", "Name: age, dtype: float64"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["#查看age在整体数据的分布情况\n", "coupon['age'].describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 数据显示18 - 95岁的客户使用coupon可能性较高，使用coupon概率较高的客群集中在40岁\n", "- 发现age > 60岁的极端值较少，但它们影响了整体数据分布，需要把这部分数据剔除在分析范围外"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_12876\\1778155165.py:5: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  age60['age_new'] = pd.cut(age60.age, bins, right=False,labels = labels)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "    <tr>\n", "      <th>age_new</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>&lt;20</th>\n", "      <td>25.0</td>\n", "      <td>18.760000</td>\n", "      <td>0.435890</td>\n", "      <td>18.0</td>\n", "      <td>19.0</td>\n", "      <td>19.0</td>\n", "      <td>19.0</td>\n", "      <td>19.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>&lt;40</th>\n", "      <td>13063.0</td>\n", "      <td>32.569624</td>\n", "      <td>4.130196</td>\n", "      <td>20.0</td>\n", "      <td>30.0</td>\n", "      <td>33.0</td>\n", "      <td>36.0</td>\n", "      <td>39.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>&lt;60</th>\n", "      <td>11215.0</td>\n", "      <td>48.416942</td>\n", "      <td>5.720111</td>\n", "      <td>40.0</td>\n", "      <td>43.0</td>\n", "      <td>48.0</td>\n", "      <td>53.0</td>\n", "      <td>59.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           count       mean       std   min   25%   50%   75%   max\n", "age_new                                                            \n", "<20         25.0  18.760000  0.435890  18.0  19.0  19.0  19.0  19.0\n", "<40      13063.0  32.569624  4.130196  20.0  30.0  33.0  36.0  39.0\n", "<60      11215.0  48.416942  5.720111  40.0  43.0  48.0  53.0  59.0"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["#对于年龄进行快速分组，探究各个年龄段对于是否使用coupon的影响\n", "age60 = coupon[coupon['age'] < 60]\n", "bins = [0, 20, 40, 60]\n", "labels = ['<20','<40','<60']\n", "age60['age_new'] = pd.cut(age60.age, bins, right=False,labels = labels)\n", "age60.groupby(['age_new'])['age'].describe()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["count     24303\n", "unique        3\n", "top         <40\n", "freq      13063\n", "Name: age_new, dtype: object"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["age60['age_new'].describe()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 900x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#绘制age60['age_new']的饼图，使数据更加直观\n", "plt.figure(figsize=[9,7])\n", "age60['age_new'].value_counts().plot.pie()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 20 - 40岁的客群使用coupon概率最高\n", "- 18，32，48岁分别为三个年龄段中使用coupon概率较高的年龄平均值"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3 相关和可视化"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_12876\\3374003348.py:2: FutureWarning: The default value of numeric_only in DataFrame.corr is deprecated. In a future version, it will default to False. Select only valid columns or specify the value of numeric_only to silence this warning.\n", "  coupon.corr()[['flag']].sort_values('flag', ascending = False)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>flag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>flag</th>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coupon_used_in_last_month</th>\n", "      <td>0.116550</td>\n", "    </tr>\n", "    <tr>\n", "      <th>age</th>\n", "      <td>0.029916</td>\n", "    </tr>\n", "    <tr>\n", "      <th>default_yes</th>\n", "      <td>-0.024608</td>\n", "    </tr>\n", "    <tr>\n", "      <th>loan_yes</th>\n", "      <td>-0.065231</td>\n", "    </tr>\n", "    <tr>\n", "      <th>coupon_used_in_last6_month</th>\n", "      <td>-0.075173</td>\n", "    </tr>\n", "    <tr>\n", "      <th>returned_yes</th>\n", "      <td>-0.143589</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                flag\n", "flag                        1.000000\n", "coupon_used_in_last_month   0.116550\n", "age                         0.029916\n", "default_yes                -0.024608\n", "loan_yes                   -0.065231\n", "coupon_used_in_last6_month -0.075173\n", "returned_yes               -0.143589"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["#围绕flag变量，观察它与其他变量的关系\n", "coupon.corr()[['flag']].sort_values('flag', ascending = False)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_12876\\2228719517.py:1: FutureWarning: The default value of numeric_only in DataFrame.corr is deprecated. In a future version, it will default to False. Select only valid columns or specify the value of numeric_only to silence this warning.\n", "  sns.heatmap(coupon.corr(), cmap = 'Blues')\n"]}, {"data": {"text/plain": ["<AxesSubplot: >"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.heatmap(coupon.corr(), cmap = 'Blues')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- flag与coupon_used_in_last_month, age成强正相关关系\n", "- flag与coupon_used_in_last6_month, returned_yes成强负相关关系\n", "- 其他变量与flag的相关不明显，为了分析准确性，因此不做过度解读"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 4 逻辑回归模型的建立和评估"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.1 模型建立\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.1.1 抽取训练集和测试集并进行拟合"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["#先设定自变量x和因变量y\n", "x = coupon[['coupon_used_in_last_month', 'returned_yes', 'loan_yes']]\n", "y = coupon['flag']"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {color: black;background-color: white;}#sk-container-id-1 pre{padding: 0;}#sk-container-id-1 div.sk-toggleable {background-color: white;}#sk-container-id-1 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-1 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-1 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-1 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-1 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-1 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-1 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-1 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-1 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-1 div.sk-item {position: relative;z-index: 1;}#sk-container-id-1 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-1 div.sk-item::before, #sk-container-id-1 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-1 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-1 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-1 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-1 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-1 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-1 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-1 div.sk-label-container {text-align: center;}#sk-container-id-1 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-1 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LogisticRegression</label><div class=\"sk-toggleable__content\"><pre>LogisticRegression()</pre></div></div></div></div></div>"], "text/plain": ["LogisticRegression()"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["#调用sklearn模块，随机抽取训练集和测试集\n", "from sklearn.model_selection import train_test_split\n", "x_train, x_test, y_train, y_test = train_test_split(x, y, test_size = 0.3, random_state = 100) #训练集和测试集抽取比例为70/30\n", "\n", "#调用sklearn中逻辑回归模块\n", "from sklearn import linear_model\n", "lr = linear_model.LogisticRegression()\n", "\n", "#拟合\n", "lr.fit(x_train, y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.1.2 查看模型结果"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.38674154, -0.95906584, -0.56226592]])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["#查看系数\n", "lr.coef_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 当coupon_used_in_last_month从0到1时，不使用coupon到使用coupon的概率提升是e的0.38，即是其他组客户的1.46倍\n", "- 当returned_yes从1到0时，使用coupon到不使用coupon的概率提升是其他组客户的0.38倍\n", "- 当loan_yes从1到0时，使用coupon到不使用coupon的概率提升是其他组客户的0.57倍\n", "- 故从概率来看，上月使用过coupon的客户、未退货客户和未用花呗付款的客群再使用coupon的概率会更高。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-1.63641623])"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["#查看截距\n", "lr.intercept_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.2 模型评估"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2.1 评估方法一：计算准确度"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["#通过训练集和测试集的自变量x，分别计算出对应的预测值\n", "y_pred_train = lr.predict(x_train)\n", "y_pred_test = lr.predict(x_test)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[15596,    27],\n", "       [ 2092,     6]], dtype=int64)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["#搭建训练集混淆矩阵\n", "from sklearn import metrics\n", "metrics.confusion_matrix(y_train, y_pred_train)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.8804243552846904"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["#查看训练集准确率\n", "metrics.accuracy_score(y_train, y_pred_train)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[6722,   11],\n", "       [ 862,    1]], dtype=int64)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["#搭建测试集混淆矩阵\n", "metrics.confusion_matrix(y_test, y_pred_test)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.8850710900473934"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["#查看测试集准确率\n", "metrics.accuracy_score(y_test, y_pred_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 比较训练集和测试集的准确率，保证内在信息一致性:\n", "- 模型在train和test的表现中不能相差过大。案例中训练集与测试集的准确率相差很小，故该模型是合理的\n", "- 但是未来客群和特点都可能会发生变化，需要做出自己的平衡。train和test只是帮助建立一个概念，不要为了单独的模型太过追求准确率"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2.2 评估方法二：ROC和AUC"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.5005658226636232\n"]}], "source": ["#使用auc评估模型\n", "from sklearn.metrics import roc_curve, auc\n", "fpr,tpr,threshold = roc_curve(y_train, y_pred_train)\n", "roc_auc = auc(fpr,tpr)\n", "\n", "print(roc_auc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["一般好的模型评分在0.7 - 0.8之间，故此模型需要被调整"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.3 模型优化"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["#新增变量returned_yes\n", "x = coupon[['coupon_used_in_last_month', 'returned_yes', 'loan_yes', 'coupon_used_in_last6_month', 'default_yes', 'age']]\n", "y = coupon['flag']"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-2 {color: black;background-color: white;}#sk-container-id-2 pre{padding: 0;}#sk-container-id-2 div.sk-toggleable {background-color: white;}#sk-container-id-2 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-2 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-2 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-2 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-2 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-2 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-2 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-2 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-2 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-2 div.sk-item {position: relative;z-index: 1;}#sk-container-id-2 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-2 div.sk-item::before, #sk-container-id-2 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-2 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-2 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-2 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-2 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-2 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-2 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-2 div.sk-label-container {text-align: center;}#sk-container-id-2 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-2 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LogisticRegression</label><div class=\"sk-toggleable__content\"><pre>LogisticRegression()</pre></div></div></div></div></div>"], "text/plain": ["LogisticRegression()"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["#调用sklearn模块，随机抽取训练集和测试集\n", "from sklearn.model_selection import train_test_split\n", "x_train, x_test, y_train, y_test = train_test_split(x, y, test_size = 0.3, random_state = 100) #训练集和测试集抽取比例为70/30\n", "\n", "#调用sklearn中逻辑回归模块\n", "from sklearn import linear_model\n", "lr = linear_model.LogisticRegression()\n", "\n", "#拟合\n", "lr.fit(x_train, y_train)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.433019  , -0.98378658, -0.53258398, -0.16775221, -0.53713798,\n", "         0.00114021]])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["lr.coef_"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.5005658226636232\n"]}], "source": ["#使用auc评估模型\n", "from sklearn.metrics import roc_curve, auc\n", "fpr,tpr,threshold = roc_curve(y_train, y_pred_train)\n", "roc_auc = auc(fpr,tpr)\n", "\n", "print(roc_auc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 只有coupon_used_in_last_month, age和flag的成正相关关系，其他变量与flag均为反相关关系\n", "- 模型迭代后AUC评分变化不大，说明coupon的使用率明显较低"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 5 业务建议"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5.1 用户分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 数据显示18 - 95岁的客户使用coupon可能性较高，使用coupon概率较高的客群集中在40岁\n", "- 20 - 40岁的客群使用coupon概率最高\n", "- 18，32，48岁分别为三个年龄段中使用coupon概率较高的年龄平均值"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5.2 提高优惠券使用率分析 - 高价值用户"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- coupon_used_in_last_month在0的均值为0.26，在1的均值为0.53，说明越是在上个月使用了coupon的客户，接下来再使用coupon的概率会越高\n", "- default_yes和loan_yes在0时的均值均大于在1时的均值，说明花呗违约和用花呗结账的客户在接下来的时间里使用coupon的概率较小\n", "\n", "\n", "- 相比起没有退货的客户，退货的客户使用coupon的概率较小\n", "- 已婚客户使用coupon的概率比未婚和离婚客户使用coupon的概率略高\n", "- job title为management, technician, blue-collar的客户越有可能使用coupon\n", "\n", "\n", "- flag与coupon_used_in_last_month, age成强正相关关系\n", "- flag与coupon_used_in_last6_month, returned_yes成强负相关关系\n", "- 其他变量与flag的相关性都不明显"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5.3 结论"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 天猫优惠券的使用率较低\n", "- 重点留意20 - 60客户的留存情况，对有购买潜力或者购买种类比较单一的客户，制定向上销售模型或交叉销售模型，提升现有客户价值\n", "- 鼓励上月用过优惠券的客户再次使用优惠券，制定相应的产品响应模型或活动响应模型，最大化收益\n", "- 对无退货客户、已婚客户、无花呗违约和未用花呗结账的用户制定客户流失预警模型或者客户赢回模型，其次是管理、技术员和蓝领阶层，尽量挽回客户\n", "- 加大APP内优惠券的宣传力度，加强banner、广告推送等营销措施；在APP外进行额外推送、第三方优惠券推送，让客户了解并提升使用优惠券的概率"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "384px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 2}
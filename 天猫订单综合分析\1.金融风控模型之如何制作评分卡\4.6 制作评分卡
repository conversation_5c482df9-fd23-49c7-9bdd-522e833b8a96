4.6 制作评分卡

其中A与B是常数，A叫做“补偿”，B叫做“刻度”，log(odds)代表了一个人违约的可能性。其实逻辑回归的结果取对数几率形式会得到θx，因此用LR的系数等价样本中的10个特征，即我们的参数*特征矩阵，两个常数可以通过两个银行产品假设的分值/Score和开发组计算出来的对数纪律，2个带入公式求出A和B，最后回带到评分卡的公式中得到Score


------------------------------------------------


银行的产品经理规定
*某个特定的违约概率下的预期分值 
*指定的违约概率翻倍的分数（PDO）
这2个值，作为初始化值


------------------------------------------------

例如，假设对数几率为1/60时,设定的特定分数为600，PDO=20，那么对数几率为1/30时的分数就是620。带入以上线性表达式，可以得到：

 600 = A - B*log(1/60)
 620 = A - B*log(1/30) 
 
 对于一个二元方程组一合并，就会得到A和B的表达式


------------------------------------------------


B = 20/np.log(2) 
A = 600 + B*np.log(1/60)

------------------------------------------------
A的值               B的值
28.85390081777927, 481.8621880878296


------------------------------------------------

有了A和B，分数就很容易得到了。其中不受评分卡中各特征影响的基础分，就是将LR的截距作为log(odds)	的值带入公式进行计算，因为LR的截距项不受特征的影响，因此要得到一个基础分 ， 而其他10个特征各个分档的分数，我们将LR的系数带入进行计算


lr.intercept_ # 逻辑回归的截距项

Out[122]:
array([0.00425793]

------------------------------------------------

#得到基础分
base_score = A - B*lr.intercept_ 
base_score 

--------------------




array([481.73933034])

--------------------



woeall
--------------------
woeall是每个特征对应的每个箱子的WOE值
RevolvingUtilizationOfUnsecuredLines': RevolvingUtilizationOfUnsecuredLines
 (-inf, 0.297]     1.458785
 (0.297, 0.551]   -0.256584
 (0.551, 0.983]   -1.182678
 (0.983, 1.0]     -0.481060
 (1.0, inf]       -2.040016
 dtype: float64, 'age': age

--------------------


lr.coef_
Out[131]:
array([[-0.73063772, -0.33649123, -0.7887396 , -0.43458978, -0.22397899,
        -0.56786122, -0.56912206, -0.93238052, -0.28063673, -0.80945779]])

#输出的是LR建模后，各个特征前面的系数


--------------------
lr.coef

# 它是二维的
lr.coef_.shape 
Out[132]:
(1, 10)



--------------------
第一个0是它本身，第二个1是取第二个数，就是年龄对应的截距
lr.coef_[0][1]

Out[136]:
-0.7306377194096024


--------------------

lr.coef_[0][0] * B # 等价于log(odds)
-21.08174828957311

就是评分卡中后面的公式


————————————————————————————

score_age = woeall["age"] * (-B*lr.coef_[0][0]) 
score_age 
age
(-inf, 36.0]   -11.098114
(36.0, 52.0]    -6.358115
(52.0, 56.0]     0.347484

得到的是一个特征下面每个箱子的分数，比如说一个客户的年龄是29岁，就在基础分数是481上减去11；
或者客户的年龄是55岁，那就再基础分481加上0.347

那么多个特征的评分卡用for循环写到本地文件

















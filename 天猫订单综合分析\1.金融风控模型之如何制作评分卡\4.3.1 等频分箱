4.3.1 等频分箱

qcut是根据这些值的频率来选择箱子的均匀间隔
""" 
pd.qcut，基于分位数的分箱函数，本质是将连续型变量离散化 
只能够处理一维数据。返回箱子的上限和下限 
参数q：要分箱的个数 
参数retbins=True来要求同时返回结构为索引为样本索引，元素为分到的箱子的Series 
现在返回两个值：每个样本属于哪个箱子，以及所有箱子的上限和下限 
""" 

#retbins=True：bool，是否返回bins，默认为False。当传入True时，额外返回bins，即每个边界值。

model_data["qcut"], updown = pd.qcut(model_data["age"], retbins=True, q=20)
model_data["qcut"]
左开右闭，一共分为20组，比如说在年龄(36.0, 39.0]有12790人
---------------------------------------------------
(36.0, 39.0]      12790
(20.999, 28.0]    11796
(58.0, 61.0]      11523
(46.0, 48.0]      11222

---------------------------------------------------

updown
---------------------------------------------------
返回的是21个数，每相邻的2个数为一组/一个箱子，共20个箱子
21., 28., 31., 34., 36., 39., 41., 43., 45., 46., 48., 50., 52.,
       54., 56., 58., 61., 64., 68., 74., 99.])



---------------------------------------------------



coount_y0 = model_data[model_data["SeriousDlqin2yrs"] == 0].groupby(by="qcut").count() ["SeriousDlqin2yrs"]
coount_y0
---------------------------------------------------
按照年龄分箱的标签中属于0的个数有多少个，在年龄20.999~28.0中，属于0的有4232
20.999, 28.0]    4232
(28.0, 31.0]      3534
(31.0, 34.0]      4045
(34.0, 36.0]      2925
(36.0, 39.0]      5269


---------------------------------------------------

# zip的使用,生成的是“惰性对象”，前面要加*号，封装的列表，返回的是对于索引的元祖
[*zip([7,8,9],["a","s","d"])] 


---------------------------------------------------

#再把箱子里面的0和1放在一块儿
[*zip(updown,updown[1:],coount_y0,coount_y1)]
# 在第一个箱子里面，最小的年龄是21岁，最大的年龄是28岁 ，并且属于0/坏客户有4232个，属于1/好客户有7621

# 按照年龄分箱的上界、下界、属于0的个数、属于1的个数有20组
updown
---------------------------------------------------
(21.0, 28.0, 4232, 7592),
 (28.0, 31.0, 3534, 5838),
 (31.0, 34.0, 4045, 6813),
 (34.0, 36.0, 2925, 4710),
 (36.0, 39.0, 5269, 7570),
















































{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["- 客户来贷款的数据，用户来的时候需要填写信息表，产品和数据分析师，就整理好数据，打好规则，算法这边根据历史数据就需要建立一个模型，新的客户一进来应不应该给客户贷款"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 拿到手后的数据是4万多行，而且每列每个特征数据量不小，样本个数越多，对咱们很有利的，从列中选择哪个当做特征是致关重要的，这个很耗时，产品经理需要打规则和打标签，建立“精准”的“用户画像。"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] File b'LoanStats3a.csv' does not exist: b'LoanStats3a.csv'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-19-e85f98330845>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0mwarnings\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfilterwarnings\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'ignore'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 5\u001b[0;31m \u001b[0mloans_2007\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_csv\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'LoanStats3a.csv'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mskiprows\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      6\u001b[0m \u001b[0mhalf_count\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mloans_2007\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m/\u001b[0m \u001b[0;36m2\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m \u001b[0mloans_2007\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mloans_2007\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdropna\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mthresh\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mhalf_count\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0maxis\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.local/lib/python3.7/site-packages/pandas/io/parsers.py\u001b[0m in \u001b[0;36mparser_f\u001b[0;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, squeeze, prefix, mangle_dupe_cols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, dialect, error_bad_lines, warn_bad_lines, delim_whitespace, low_memory, memory_map, float_precision)\u001b[0m\n\u001b[1;32m    683\u001b[0m         )\n\u001b[1;32m    684\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 685\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0m_read\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilepath_or_buffer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwds\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    686\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    687\u001b[0m     \u001b[0mparser_f\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mname\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.local/lib/python3.7/site-packages/pandas/io/parsers.py\u001b[0m in \u001b[0;36m_read\u001b[0;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[1;32m    455\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    456\u001b[0m     \u001b[0;31m# Create the parser.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 457\u001b[0;31m     \u001b[0mparser\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mTextFileReader\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfp_or_buf\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwds\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    458\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    459\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mchunksize\u001b[0m \u001b[0;32mor\u001b[0m \u001b[0miterator\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.local/lib/python3.7/site-packages/pandas/io/parsers.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[1;32m    893\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0moptions\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"has_index_names\"\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mkwds\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"has_index_names\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    894\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 895\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_make_engine\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mengine\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    896\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    897\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mclose\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.local/lib/python3.7/site-packages/pandas/io/parsers.py\u001b[0m in \u001b[0;36m_make_engine\u001b[0;34m(self, engine)\u001b[0m\n\u001b[1;32m   1133\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_make_engine\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mengine\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"c\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1134\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mengine\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0;34m\"c\"\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1135\u001b[0;31m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mCParserWrapper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mf\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0moptions\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1136\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1137\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mengine\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0;34m\"python\"\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.local/lib/python3.7/site-packages/pandas/io/parsers.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, src, **kwds)\u001b[0m\n\u001b[1;32m   1915\u001b[0m         \u001b[0mkwds\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"usecols\"\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0musecols\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1916\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1917\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_reader\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mparsers\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mTextReader\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msrc\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwds\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1918\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0munnamed_cols\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_reader\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0munnamed_cols\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1919\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32mpandas/_libs/parsers.pyx\u001b[0m in \u001b[0;36mpandas._libs.parsers.TextReader.__cinit__\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/parsers.pyx\u001b[0m in \u001b[0;36mpandas._libs.parsers.TextReader._setup_parser_source\u001b[0;34m()\u001b[0m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] File b'LoanStats3a.csv' does not exist: b'LoanStats3a.csv'"]}], "source": ["import pandas as pd\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "loans_2007 = pd.read_csv('LoanStats3a.csv', skiprows=1)\n", "half_count = len(loans_2007) / 2\n", "loans_2007 = loans_2007.dropna(thresh=half_count, axis=1)\n", "loans_2007 = loans_2007.drop(['desc', 'url'],axis=1)\n", "loans_2007.to_csv('loans_2007.csv', index=False)"]}, {"cell_type": "code", "execution_count": 67, "metadata": {"scrolled": true}, "outputs": [{"ename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evalue": "Error tokenizing data. C error: Expected 2 fields in line 4, saw 3\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                               <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-67-66b0669dcf4b>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mpandas\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mloans_2019\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_csv\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"2019.csv\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      3\u001b[0m \u001b[0;31m#loans_2007.drop_duplicates()\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mloans_2019\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0miloc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;31m#第一行的数据\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"--------------------\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.local/lib/python3.7/site-packages/pandas/io/parsers.py\u001b[0m in \u001b[0;36mparser_f\u001b[0;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, squeeze, prefix, mangle_dupe_cols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, dialect, error_bad_lines, warn_bad_lines, delim_whitespace, low_memory, memory_map, float_precision)\u001b[0m\n\u001b[1;32m    683\u001b[0m         )\n\u001b[1;32m    684\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 685\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0m_read\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilepath_or_buffer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwds\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    686\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    687\u001b[0m     \u001b[0mparser_f\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mname\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.local/lib/python3.7/site-packages/pandas/io/parsers.py\u001b[0m in \u001b[0;36m_read\u001b[0;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[1;32m    461\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    462\u001b[0m     \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 463\u001b[0;31m         \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mparser\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnrows\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    464\u001b[0m     \u001b[0;32mfinally\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    465\u001b[0m         \u001b[0mparser\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mclose\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.local/lib/python3.7/site-packages/pandas/io/parsers.py\u001b[0m in \u001b[0;36mread\u001b[0;34m(self, nrows)\u001b[0m\n\u001b[1;32m   1152\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mread\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnrows\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mNone\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1153\u001b[0m         \u001b[0mnrows\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0m_validate_integer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"nrows\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnrows\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1154\u001b[0;31m         \u001b[0mret\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnrows\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1155\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1156\u001b[0m         \u001b[0;31m# May alter columns / col_dict\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.local/lib/python3.7/site-packages/pandas/io/parsers.py\u001b[0m in \u001b[0;36mread\u001b[0;34m(self, nrows)\u001b[0m\n\u001b[1;32m   2057\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mread\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnrows\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mNone\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2058\u001b[0m         \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2059\u001b[0;31m             \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_reader\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnrows\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2060\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mStopIteration\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2061\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_first_chunk\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32mpandas/_libs/parsers.pyx\u001b[0m in \u001b[0;36mpandas._libs.parsers.TextReader.read\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/parsers.pyx\u001b[0m in \u001b[0;36mpandas._libs.parsers.TextReader._read_low_memory\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/parsers.pyx\u001b[0m in \u001b[0;36mpandas._libs.parsers.TextReader._read_rows\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/parsers.pyx\u001b[0m in \u001b[0;36mpandas._libs.parsers.TextReader._tokenize_rows\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/parsers.pyx\u001b[0m in \u001b[0;36mpandas._libs.parsers.raise_parser_error\u001b[0;34m()\u001b[0m\n", "\u001b[0;31mParserError\u001b[0m: Error tokenizing data. C error: Expected 2 fields in line 4, saw 3\n"]}], "source": ["import pandas as pd\n", "loans_2019 = pd.read_csv(\"2019.csv\")\n", "#loans_2007.drop_duplicates()\n", "print(loans_2019.iloc[0])#第一行的数据\n", "print(\"--------------------\")\n", "print(loans_2019.shape[1]) #一共是52列特征\n", "\n", "\"\"\"\n", "id:是用户唯一ID，是谁其实不重要\n", "loan_amnt：期望贷款的值 ，比如说期望贷款5000\n", "funded_amnt_inv：实际上发放了多少钱，是模型建立后的预测值吧，对于咱们马上建立的模型是没有用的，把当时填的信息为准，而不是模型预测后的数据为准\n", "term：还款日期，分36个月 ，这个是可以用的\n", "int_rate：利息，10.65% ，利息越高，还款越困难，反之，越容易，这个可以用\n", "grade ：B ， 打分\n", "sub_grade ：B2 ，打分  ，其实打分都是说的是一个事，去掉一个就行了，比如有两列都是给客户打分\n", "①   ②\n", "1    A\n", "3    C\n", "5    E\n", "2    B\n", "4    D\n", "两者表述的“高度”是相关，取1列就行，否则造成“冗余”\n", "emp_title：客户所在公司名称，越有名气越好，贷款越容易，可以把公司分成A B C D的档次，耗时和成本太高，把成千上万家公司分成，难度很大，除非已经分好了\n", "           所以这个特征就不要了\n", "home_ownership(房屋的使用权) ：RENT ，房屋是租的、还是你自己的的还是贷款买的，这个可以当做特征\n", "\n", "\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 接下来就是筛选特征：跟“id”相关的index值筛掉(客户是谁我不关心)，预测完后的数据也不要，比如说放款了多少钱\n", "- 就说一个特征，你留还是不留，有时一个项目组讨论一个星期都讨论不完"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["loans_2019 = loans_2019.drop([\"id\", \"member_id\", \"funded_amnt\", \"funded_amnt_inv\", \"grade\", \"sub_grade\", \"emp_title\", \"issue_d\"], axis=1)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["loans_2019 = loans_2019.drop([\"zip_code\", \"out_prncp\", \"out_prncp_inv\", \"total_pymnt\", \"total_pymnt_inv\", \"total_rec_prncp\"], axis=1)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loan_amnt                            5000\n", "term                            36 months\n", "int_rate                           10.65%\n", "installment                        162.87\n", "emp_length                      10+ years\n", "home_ownership                       RENT\n", "annual_inc                          24000\n", "verification_status              Verified\n", "loan_status                    Fully Paid\n", "pymnt_plan                              n\n", "purpose                       credit_card\n", "title                            Computer\n", "addr_state                             AZ\n", "dti                                 27.65\n", "delinq_2yrs                             0\n", "earliest_cr_line                 Jan-1985\n", "inq_last_6mths                          1\n", "open_acc                                3\n", "pub_rec                                 0\n", "revol_bal                           13648\n", "revol_util                          83.7%\n", "total_acc                               9\n", "initial_list_status                     f\n", "last_credit_pull_d               Nov-2016\n", "collections_12_mths_ex_med              0\n", "policy_code                             1\n", "application_type               INDIVIDUAL\n", "acc_now_delinq                          0\n", "chargeoff_within_12_mths                0\n", "delinq_amnt                             0\n", "pub_rec_bankruptcies                    0\n", "tax_liens                               0\n", "Name: 0, dtype: object\n", "-----------------------\n", "32\n"]}], "source": ["#保留候选特征\n", "loans_2019 = loans_2019.drop([\"total_rec_int\", \"total_rec_late_fee\", \"recoveries\", \"collection_recovery_fee\", \"last_pymnt_d\", \"last_pymnt_amnt\"], axis=1)\n", "print(loans_2019.iloc[0])#第一行数据\n", "print(\"-----------------------\")\n", "print(loans_2019.shape[1]) #原始是52列，现在是32列的候选特征，还是有点“冗余”，但是还少了一个东西呀，是啥样.....是不是“标签label”啊"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 在原始数据中,没有0或者1的还不还款的特征，但是有“loan status”这个特征，意思是当前“贷款的状态”"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fully Paid                                             33902\n", "Charged Off                                             5658\n", "Does not meet the credit policy. Status:<PERSON>y Paid      1988\n", "Does not meet the credit policy. Status:Charged Off      761\n", "Current                                                  201\n", "Late (31-120 days)                                        10\n", "In Grace Period                                            9\n", "Late (16-30 days)                                          5\n", "Default                                                    1\n", "Name: loan_status, dtype: int64\n"]}], "source": ["print(loans_2019['loan_status'].value_counts())#计算该列特征的属性的个数\n", "#Fully Paid：批准了客户的贷款，后面给他打个“1”\n", "#Charged Off：没有批准了客户的贷款，后面给他打个“0”\n", "#Does not meet the credit policy. Status:Fully Paid：，没有满足要求的有1988个，也不要说清楚不贷款，就不要这个属性了\n", "#后面的属性不确定比较强\n", "#Late (16-30 days)  ：延期了16-30 days\n", "#Late (31-120 days)：延期了31-120 days ， 所以这些都不确定的属性，相当于“取保候审”"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["#要做一个二分类，用0 1 表示 \n", "loans_2019 = loans_2019[(loans_2019['loan_status'] == \"Fully Paid\") | (loans_2019['loan_status'] == \"Charged Off\")]\n", "\n", "status_replace = {\n", "    #特征当做key，value里还有一个字典\n", "    \"loan_status\" : {\n", "        #第一个键值改为1 ，第二个键值改为0\n", "        \"Fully Paid\": 1,\n", "        \"Charged Off\": 0,\n", "    }\n", "}\n", "#可以用pandas的DataFrame的格式，做成字典\n", "\n", "loans_2019 = loans_2019.replace(status_replace)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['initial_list_status', 'collections_12_mths_ex_med', 'policy_code', 'application_type', 'acc_now_delinq', 'chargeoff_within_12_mths', 'delinq_amnt', 'tax_liens']\n", "(42538, 46)\n"]}], "source": ["#在原始数据中的特征值或者属性里都是一样的，对于分类模型的预测是没有用的\n", "#某列特征都是n n n  NaN  n n ,有缺失的，唯一的属性就有2个，用pandas算法空值给去掉\n", "\n", "orig_columns = loans_2019.columns#展现出所有的列\n", "drop_columns = [] #初始化空值\n", "for col in orig_columns:\n", "    #   dropna()先删除空值，再去重算唯一的属性                \n", "    col_series = loans_2019[col].dropna().unique()#去重唯一的属性\n", "    if len(col_series) == 1: #如果该特征的属性只有一个属性，就给过滤掉该特征\n", "        drop_columns.append(col)\n", "loans_2019 = loans_2019.drop(drop_columns, axis=1)\n", "print(drop_columns)\n", "print(loans_2007.shape)\n", "loans_2019.to_csv('filtered_loans_2019.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 当你筛选出特征和标签了，就可以丢给scikit-learn吗？不行吧 ， 还要做缺失值、字符值、标点符号、%号、str值等处理\n", "- 把24个列的缺失值情况，统计一下每列的缺失值"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loan_amnt                  0\n", "term                       0\n", "int_rate                   0\n", "installment                0\n", "emp_length              1073\n", "home_ownership             0\n", "annual_inc                 0\n", "verification_status        0\n", "loan_status                0\n", "pymnt_plan                 0\n", "purpose                    0\n", "title                     11\n", "addr_state                 0\n", "dti                        0\n", "delinq_2yrs                0\n", "earliest_cr_line           0\n", "inq_last_6mths             0\n", "open_acc                   0\n", "pub_rec                    0\n", "revol_bal                  0\n", "revol_util                50\n", "total_acc                  0\n", "last_credit_pull_d         2\n", "pub_rec_bankruptcies     697\n", "dtype: int64\n"]}], "source": ["import pandas as pd\n", "loans = pd.read_csv('filtered_loans_2019.csv')\n", "null_counts = loans.isnull().sum()#用pandas的isnull统计一下每列的缺失值，给累加起来\n", "print(null_counts)\n", "#对于每列中缺失的情况不是很大，大多数是很好的 ，那就删掉几个列也无可厚非(对于样本大)，或者是只删除缺失值，或者用均值、中位数和众数补充"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["object     12\n", "float64    10\n", "int64       1\n", "dtype: int64\n"]}], "source": ["loans = loans.drop(\"pub_rec_bankruptcies\", axis=1)\n", "loans = loans.dropna(axis=0)\n", "#用dtypes类型统计有多少个是object、int、float类型的特征\n", "print(loans.dtypes.value_counts())"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["term                     36 months\n", "int_rate                    10.65%\n", "emp_length               10+ years\n", "home_ownership                RENT\n", "verification_status       Verified\n", "pymnt_plan                       n\n", "purpose                credit_card\n", "title                     Computer\n", "addr_state                      AZ\n", "earliest_cr_line          Jan-1985\n", "revol_util                   83.7%\n", "last_credit_pull_d        Nov-2016\n", "Name: 0, dtype: object\n"]}], "source": ["#Pandas里select_dtypes只选定“ohbject”的类型str，只选定字符型的数据\n", "object_columns_df = loans.select_dtypes(include=[\"object\"])\n", "print(object_columns_df.iloc[0])\n", "#term：分期多少个月啊\n", "#int_rate：利息，10.65%，后面还要把%去掉\n", "#emp_length：10年的映射成10，9年的映射成9\n", "#home_ownership：房屋所有权，是租的、还是自己的、还是低压出去了，那就用0 1 2来代替"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RENT        18371\n", "MORTGAGE    17131\n", "OWN          2827\n", "OTHER          96\n", "NONE            3\n", "Name: home_ownership, dtype: int64\n", "Not Verified       16436\n", "Verified           12251\n", "Source Verified     9741\n", "Name: verification_status, dtype: int64\n", "10+ years    8821\n", "< 1 year     4563\n", "2 years      4371\n", "3 years      4074\n", "4 years      3409\n", "5 years      3270\n", "1 year       3227\n", "6 years      2212\n", "7 years      1756\n", "8 years      1472\n", "9 years      1253\n", "Name: emp_length, dtype: int64\n", " 36 months    28234\n", " 60 months    10194\n", "Name: term, dtype: int64\n", "CA    6882\n", "NY    3684\n", "FL    2766\n", "TX    2659\n", "NJ    1814\n", "IL    1480\n", "PA    1470\n", "VA    1371\n", "GA    1352\n", "MA    1306\n", "OH    1177\n", "MD    1030\n", "AZ     828\n", "WA     800\n", "CO     764\n", "NC     754\n", "CT     728\n", "MI     688\n", "MO     658\n", "MN     589\n", "NV     477\n", "SC     462\n", "WI     439\n", "OR     431\n", "AL     429\n", "LA     425\n", "KY     321\n", "OK     292\n", "KS     258\n", "UT     251\n", "AR     233\n", "DC     211\n", "RI     196\n", "NM     182\n", "HI     168\n", "WV     167\n", "NH     162\n", "DE     110\n", "WY      79\n", "MT      78\n", "AK      77\n", "SD      62\n", "VT      54\n", "MS      19\n", "TN      17\n", "IN       9\n", "ID       6\n", "NE       5\n", "IA       5\n", "ME       3\n", "Name: addr_state, dtype: int64\n"]}], "source": ["cols = ['home_ownership', 'verification_status', 'emp_length', 'term', 'addr_state']\n", "for c in cols:\n", "    print(loans[c].value_counts())\n", "    \n", "#把这些可选项映射成数值型的吧， verification_status，客户的信息是否被确认了，没被确认了为0，确认了为1，用数值型的替换字符型的\n", "#对于年份，是否可以用10 9 8 7 6来代替10年 9年 8年"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["debt_consolidation    18137\n", "credit_card            4970\n", "other                  3803\n", "home_improvement       2869\n", "major_purchase         2108\n", "small_business         1771\n", "car                    1492\n", "wedding                 932\n", "medical                 667\n", "moving                  557\n", "house                   365\n", "vacation                350\n", "educational             312\n", "renewable_energy         95\n", "Name: purpose, dtype: int64\n", "------------------------------------------------\n", "Debt Consolidation            2128\n", "Debt Consolidation Loan       1671\n", "Personal Loan                  640\n", "Consolidation                  503\n", "debt consolidation             483\n", "                              ... \n", "Lower my car rate at Wells       1\n", "Ranger <PERSON>                      1\n", "Payoff high interest loan        1\n", "Freedom55                        1\n", "RAMDPM                           1\n", "Name: title, Length: 19094, dtype: int64\n"]}], "source": ["print(loans[\"purpose\"].value_counts())#purpose：你贷款时的目的是什么，买房还是买车，还是其他消费\n", "print(\"------------------------------------------------\")\n", "print(loans[\"title\"].value_counts())#title：跟purpose一样，贷款的目的，选一个就行了"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["# jemp_length做成字典，emp_length当做key ，value里还是字典 ，\"10+ years\": 10...\n", "# 再在后面调用replace函数，刚才利息这列特征，是不是有%啊，再用astype()处理一下\n", "mapping_dict = {\n", "    \"emp_length\": {\n", "        \"10+ years\": 10,\n", "        \"9 years\": 9,\n", "        \"8 years\": 8,\n", "        \"7 years\": 7,\n", "        \"6 years\": 6,\n", "        \"5 years\": 5,\n", "        \"4 years\": 4,\n", "        \"3 years\": 3,\n", "        \"2 years\": 2,\n", "        \"1 year\": 1,\n", "        \"< 1 year\": 0,\n", "        \"n/a\": 0\n", "    }\n", "}\n", "loans = loans.drop([\"last_credit_pull_d\", \"earliest_cr_line\", \"addr_state\", \"title\"], axis=1)\n", "loans[\"int_rate\"] = loans[\"int_rate\"].str.rstrip(\"%\").astype(\"float\")\n", "loans[\"revol_util\"] = loans[\"revol_util\"].str.rstrip(\"%\").astype(\"float\")\n", "loans = loans.replace(mapping_dict)"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["cat_columns = [\"home_ownership\", \"verification_status\", \"emp_length\", \"purpose\", \"term\"]\n", "dummy_df = pd.get_dummies(loans[cat_columns])\n", "loans = pd.concat([loans, dummy_df], axis=1)\n", "loans = loans.drop(cat_columns, axis=1)\n", "loans = loans.drop(\"pymnt_plan\", axis=1)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["loans.to_csv('cleaned_loans2019.csv', index=False)"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 38428 entries, 0 to 38427\n", "Data columns (total 48 columns):\n", "loan_amnt                              38428 non-null float64\n", "int_rate                               38428 non-null float64\n", "installment                            38428 non-null float64\n", "annual_inc                             38428 non-null float64\n", "loan_status                            38428 non-null int64\n", "dti                                    38428 non-null float64\n", "delinq_2yrs                            38428 non-null float64\n", "inq_last_6mths                         38428 non-null float64\n", "open_acc                               38428 non-null float64\n", "pub_rec                                38428 non-null float64\n", "revol_bal                              38428 non-null float64\n", "revol_util                             38428 non-null float64\n", "total_acc                              38428 non-null float64\n", "home_ownership_MORTGAGE                38428 non-null int64\n", "home_ownership_NONE                    38428 non-null int64\n", "home_ownership_OTHER                   38428 non-null int64\n", "home_ownership_OWN                     38428 non-null int64\n", "home_ownership_RENT                    38428 non-null int64\n", "verification_status_Not Verified       38428 non-null int64\n", "verification_status_Source Verified    38428 non-null int64\n", "verification_status_Verified           38428 non-null int64\n", "emp_length_0                           38428 non-null int64\n", "emp_length_1                           38428 non-null int64\n", "emp_length_2                           38428 non-null int64\n", "emp_length_3                           38428 non-null int64\n", "emp_length_4                           38428 non-null int64\n", "emp_length_5                           38428 non-null int64\n", "emp_length_6                           38428 non-null int64\n", "emp_length_7                           38428 non-null int64\n", "emp_length_8                           38428 non-null int64\n", "emp_length_9                           38428 non-null int64\n", "emp_length_10                          38428 non-null int64\n", "purpose_car                            38428 non-null int64\n", "purpose_credit_card                    38428 non-null int64\n", "purpose_debt_consolidation             38428 non-null int64\n", "purpose_educational                    38428 non-null int64\n", "purpose_home_improvement               38428 non-null int64\n", "purpose_house                          38428 non-null int64\n", "purpose_major_purchase                 38428 non-null int64\n", "purpose_medical                        38428 non-null int64\n", "purpose_moving                         38428 non-null int64\n", "purpose_other                          38428 non-null int64\n", "purpose_renewable_energy               38428 non-null int64\n", "purpose_small_business                 38428 non-null int64\n", "purpose_vacation                       38428 non-null int64\n", "purpose_wedding                        38428 non-null int64\n", "term_ 36 months                        38428 non-null int64\n", "term_ 60 months                        38428 non-null int64\n", "dtypes: float64(12), int64(36)\n", "memory usage: 14.1 MB\n", "None\n"]}], "source": ["import pandas as pd\n", "loans = pd.read_csv(\"cleaned_loans2019.csv\") # 清洗完的数据拿过来，现在的数据要么是float类型和int类型\n", "print(loans.info())\n"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["import pandas as pd \n", "#接下来就是如何算4个指标 fp tp fn tn\n", "# False positives.\n", "fp_filter = (predictions == 1) & (loans[\"loan_status\"] == 0)\n", "fp = len(predictions[fp_filter])\n", "\n", "# True positives.\n", "tp_filter = (predictions == 1) & (loans[\"loan_status\"] == 1)\n", "tp = len(predictions[tp_filter])\n", "\n", "# False negatives.\n", "fn_filter = (predictions == 0) & (loans[\"loan_status\"] == 1)\n", "fn = len(predictions[fn_filter])\n", "\n", "# True negatives\n", "tn_filter = (predictions == 0) & (loans[\"loan_status\"] == 0)\n", "tn = len(predictions[tn_filter])"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["#LR不是回归而是分类，用它进行训练了\n", "from sklearn.linear_model import LogisticRegression\n", "\n", "lr = LogisticRegression()\n", "cols = loans.columns\n", "train_cols = cols.drop(\"loan_status\")\n", "features = loans[train_cols]\n", "target = loans[\"loan_status\"]\n", "lr.fit(features, target)\n", "predictions = lr.predict(features)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.linear_model import LogisticRegression\n", "from sklearn.model_selection import cross_val_predict,KFold\n", "\"\"\"\n", "class_weight：可以调整正反样本的权重\n", "balanced:希望正负样本平衡一些的\n", "\"\"\"\n", "lr = LogisticRegression(class_weight=\"balanced\")\n", "kf = KFold(features.shape[0], random_state=1)\n", "predictions = cross_val_predict(lr, features, target, cv=kf)\n", "predictions = pd.Series(predictions)\n", "\n", "# False positives.\n", "fp_filter = (predictions == 1) & (loans[\"loan_status\"] == 0)\n", "fp = len(predictions[fp_filter])\n", "\n", "# True positives.\n", "tp_filter = (predictions == 1) & (loans[\"loan_status\"] == 1)\n", "tp = len(predictions[tp_filter])\n", "\n", "# False negatives.\n", "fn_filter = (predictions == 0) & (loans[\"loan_status\"] == 1)\n", "fn = len(predictions[fn_filter])\n", "\n", "# True negatives\n", "tn_filter = (predictions == 0) & (loans[\"loan_status\"] == 0)\n", "tn = len(predictions[tn_filter])\n", "\n", "# Rates，就可以用刚才的指标进行衡量了呀\n", "tpr = tp / float((tp + fn))\n", "fpr = fp / float((fp + tn))\n", "\"\"\"\n", "tpr:比较高，我们非常喜欢，给他贷款了，而且这些人能还钱了\n", "fpr：比较高，这些人不会还钱，但还是贷给他了吧\n", "为什么这个2个值都那么高呢？把所有人来了，都借给他钱呀，打印出前20行都为1，为什么会出现这种情况？\n", "绝对是前面的数据出现问题了，比如说数据是6：1，绝大多数是1，小部分是0，样本不均衡的情况下，导致分类器错误的认为把所有的样本预测为1，因为负样本少，咱们就“数据增强”，\n", "把负样本1增强到4份儿，是不是可以啊，要么收集数据 ，数据已经定值了，没办法收集，要么是造数据，你知道什么样的人会还钱吗？也不好造吧，怎么解决样本不均衡的问题呢？\n", "接下来要考虑权重的东西了，一部分是6份，另一部分是1份，把6份的权重设置为1，把1份的权重设置为6，设置权重项来进行衡量，把不均衡的样本变得均衡，加了权重项，让正样本对结果的影响小一些，\n", "让负样本对结果的影响大一些，通过加入权重项，模型对结果变得均衡一下，有一个参数很重要\n", "\"\"\"\n", "print(tpr)\n", "print(fpr)\n", "print(predictions[:20])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.linear_model import LogisticRegression\n", "from sklearn.cross_validation import cross_val_predict\n", "\"\"\"\n", "权重项可以自己定义的\n", "0代表5倍的\n", "1代表10倍的\n", "\"\"\"\n", "penalty = {\n", "    0: 5,\n", "    1: 1\n", "}\n", "\n", "lr = LogisticRegression(class_weight=penalty)\n", "kf = KFold(features.shape[0], random_state=1)\n", "predictions = cross_val_predict(lr, features, target, cv=kf)\n", "predictions = pd.Series(predictions)\n", "\n", "# False positives.\n", "fp_filter = (predictions == 1) & (loans[\"loan_status\"] == 0)\n", "fp = len(predictions[fp_filter])\n", "\n", "# True positives.\n", "tp_filter = (predictions == 1) & (loans[\"loan_status\"] == 1)\n", "tp = len(predictions[tp_filter])\n", "\n", "# False negatives.\n", "fn_filter = (predictions == 0) & (loans[\"loan_status\"] == 1)\n", "fn = len(predictions[fn_filter])\n", "\n", "# True negatives\n", "tn_filter = (predictions == 0) & (loans[\"loan_status\"] == 0)\n", "tn = len(predictions[tn_filter])\n", "\n", "# Rates\n", "tpr = tp / float((tp + fn))\n", "fpr = fp / float((fp + tn))\n", "\n", "print(tpr)\n", "print(fpr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.cross_validation import cross_val_predict\n", "rf = RandomForestClassifier(n_estimators=10,class_weight=\"balanced\", random_state=1)\n", "#print help(RandomForestClassifier)\n", "kf = KFold(features.shape[0], random_state=1)\n", "predictions = cross_val_predict(rf, features, target, cv=kf)\n", "predictions = pd.Series(predictions)\n", "\n", "# False positives.\n", "fp_filter = (predictions == 1) & (loans[\"loan_status\"] == 0)\n", "fp = len(predictions[fp_filter])\n", "\n", "# True positives.\n", "tp_filter = (predictions == 1) & (loans[\"loan_status\"] == 1)\n", "tp = len(predictions[tp_filter])\n", "\n", "# False negatives.\n", "fn_filter = (predictions == 0) & (loans[\"loan_status\"] == 1)\n", "fn = len(predictions[fn_filter])\n", "\n", "# True negatives\n", "tn_filter = (predictions == 0) & (loans[\"loan_status\"] == 0)\n", "tn = len(predictions[tn_filter])\n", "\n", "# Rates\n", "tpr = tp / float((tp + fn))\n", "fpr = fp / float((fp + tn))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 1}
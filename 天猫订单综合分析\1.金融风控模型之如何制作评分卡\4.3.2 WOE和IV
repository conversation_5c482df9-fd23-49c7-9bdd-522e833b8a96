4.3.2 WOE和IV


- bad rate :一个箱子中，坏客户在count_0+count_1所占的比例
- good%：是指一个箱的好客户占整个样本中好客户的比重
- bad%:是指一个箱的坏客户占整个样本中坏客户的比重


-------------------------------------------







# 按照年龄分箱的上界、下界、属于0的个数、属于1的个数有20组
columns = ["min","max","count_0","count_1"] 
df = pd.DataFrame(num_bins,columns=columns )
df.head()
-------------------------------------------
min	max	count_0	count_1
0	21.0	28.0	4232	7616
1	28.0	31.0	3534	5850
2	31.0	34.0	4045	6796
3	34.0	36.0	2925	4766
4	36.0	39.0	5269	7477





df["total"] = df.count_0 + df.count_1  # 一个箱子的标签数0或1
df["percentage"] = df.total / df.total.sum() # 一个箱子的标签数0或1，占总样本的比例
df["bad_rate"] = df.count_1 / df.total  # 一个箱子中，坏客户客户在count_0+count_1所占的比例
df["good%"] = df.count_0/df.count_0.sum()  # 一个箱的好客户占整个样本中好客户的比重
df["bad%"] = df.count_1/df.count_1.sum()  # 个箱的坏客户占整个样本中坏客户的比重
df["woe"] = np.log(df["good%"] / df["bad%"]) # 证据权重


-------------------------------------------




df["total"] = df.count_0 + df.count_1  # 一个箱子的标签数0或1
df["percentage"] = df.total / df.total.sum() # 一个箱子的标签数0或1，占总样本的比例
df["bad_rate"] = df.count_1 / df.total  # 坏客户占总样本的比例
df["good%"] = df.count_0/df.count_0.sum()  # 好客户占总好客户的比例
df["bad%"] = df.count_1/df.count_1.sum()  # 坏客户占总坏客户的比例
df["woe"] = np.log(df["good%"] / df["bad%"]) # 证据权重
df.head()

-------------------------------------------
min	max	count_0	count_1	total	percentage	bad_rate	good%	bad%	woe
0	21.0	28.0	4232	7616	11848	0.060761	0.642809	0.043375	0.078173	-0.589033
1	28.0	31.0	3534	5850	9384	0.048125	0.623402	0.036221	0.060046	-0.505468
2	31.0	34.0	4045	6796	10841	0.055597	0.626879	0.041459	0.069756	-0.520309
3	34.0	36.0	2925	4766	7691	0.039443	0.619685	0.029979	0.048920	-0.489669
4	36.0	39.0	5269	7477	12746	0.065367	0.586615	0.054004	0.076746	-0.351448



rate = df["good%"] - df["bad%"] 
iv_age = np.sum(rate * df.woe)
iv_age
-------------------------------------------
0.3556206022321849 # 年龄的IV值在0.3 ~ 0.49之间 ，有效信息较多，对模型的贡献度较高

然后把WOE和IV值封装成函数




{"cells": [{"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Table of Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#查看源数据类型\" data-toc-modified-id=\"查看源数据类型-1\">查看源数据类型</a></span></li><li><span><a href=\"#计算本月的相关的指标\" data-toc-modified-id=\"计算本月的相关的指标-2\">计算本月的相关的指标</a></span></li><li><span><a href=\"#计算上月相关指标\" data-toc-modified-id=\"计算上月相关指标-3\">计算上月相关指标</a></span></li><li><span><a href=\"#计算去年同期相关指标\" data-toc-modified-id=\"计算去年同期相关指标-4\">计算去年同期相关指标</a></span></li><li><span><a href=\"#创建DataFrame-添加同比和环比字段\" data-toc-modified-id=\"创建DataFrame-添加同比和环比字段-5\">创建DataFrame 添加同比和环比字段</a></span></li><li><span><a href=\"#查看报表\" data-toc-modified-id=\"查看报表-6\">查看报表</a></span></li><li><span><a href=\"#将结果导出本地\" data-toc-modified-id=\"将结果导出本地-7\">将结果导出本地</a></span></li></ul></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 查看源数据类型"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3744 entries, 0 to 3743\n", "Data columns (total 7 columns):\n", " #   Column  Non-Null Count  Dtype         \n", "---  ------  --------------  -----         \n", " 0   商品ID    3478 non-null   float64       \n", " 1   类别ID    3478 non-null   float64       \n", " 2   门店编号    3478 non-null   object        \n", " 3   单价      3478 non-null   float64       \n", " 4   销量      3478 non-null   float64       \n", " 5   成交时间    3478 non-null   datetime64[ns]\n", " 6   订单ID    3478 non-null   object        \n", "dtypes: datetime64[ns](1), float64(4), object(2)\n", "memory usage: 204.9+ KB\n"]}], "source": ["import pandas as pd\n", "from datetime import datetime\n", "\n", "data=pd.read_csv(\"order-14.1.csv\",parse_dates=[\"成交时间\"],encoding='gbk')\n", "data.head()\n", "# print(data.head(5))\n", "# 查看源数据类型\n", "data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 计算本月的相关的指标"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["本月销售额为：10412.78,客流量为：343,客单价为：30.36\n"]}], "source": ["# 计算本月的相关的指标\n", "This_month=data[(data[\"成交时间\"]>=datetime(2018,2,1))&(data[\"成交时间\"]<=datetime(2018,2,28))]\n", "# 销售额计算\n", "sales_1=(This_month[\"销量\"]*This_month['单价']).sum()\n", "# 客流量计算\n", "traffic_1=This_month[\"订单ID\"].drop_duplicates().count()\n", "# 客单价计算\n", "s_t_1=sales_1/traffic_1\n", "print(\"本月销售额为：{:.2f},客流量为：{},客单价为：{:.2f}\".format(sales_1,traffic_1,s_t_1))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 计算上月相关指标"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["本月销售额为：9940.97,客流量为：315,客单价为：31.56\n"]}], "source": ["# 计算上月相关指标\n", "last_month=data[(data[\"成交时间\"]>=datetime(2018,1,1))&(data[\"成交时间\"]<=datetime(2018,1,31))]\n", "\n", "# 销售额计算\n", "sales_2=(last_month[\"销量\"]*last_month['单价']).sum()\n", "# 客流量计算\n", "traffic_2=last_month[\"订单ID\"].drop_duplicates().count()\n", "# 客单价计算\n", "s_t_2=sales_2/traffic_2\n", "print(\"本月销售额为：{:.2f},客流量为：{},客单价为：{:.2f}\".format(sales_2,traffic_2,s_t_2))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 计算去年同期相关指标"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["本月销售额为：8596.31,客流量为：262,客单价为：32.81\n"]}], "source": ["# 计算去年同期相关指标\n", "same_month=data[(data[\"成交时间\"]>=datetime(2017,2,1))&(data[\"成交时间\"]<=datetime(2017,2,28))]\n", "\n", "sales_3=(same_month[\"销量\"]*same_month[\"单价\"]).sum()\n", "\n", "traffic_3=same_month[\"订单ID\"].drop_duplicates().count()\n", "s_t_3=sales_3/traffic_3\n", "print(\"本月销售额为：{:.2f},客流量为：{},客单价为：{:.2f}\".format(sales_3,traffic_3,s_t_3))\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10412.78007 343 30.357959387755105\n", "9940.97291 315 31.55864415873016\n", "8596.313470000001 262 32.810356755725195\n"]}], "source": ["# 利用函数提高编码效率\n", "def get_month_data(data):\n", "    sale=(data[\"销量\"]*data[\"单价\"]).sum()\n", "    traffic=data[\"订单ID\"].drop_duplicates().count()\n", "    s_t=sale/traffic\n", "    return (sale,traffic,s_t)\n", "\n", "# 本月相关指数\n", "sales_1,traffic_1,s_t_1=get_month_data(This_month)\n", "print(sales_1,traffic_1,s_t_1)\n", "\n", "# 上月相关指数\n", "sales_2,traffic_2,s_t_2=get_month_data(last_month)\n", "print(sales_2,traffic_2,s_t_2)\n", "\n", "# 去年同期相关指数\n", "sales_3,traffic_3,s_t_3=get_month_data(same_month)\n", "print(sales_3,traffic_3,s_t_3)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 创建DataFrame 添加同比和环比字段"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# 创建DataFrame\n", "report=pd.DataFrame([[sales_1,sales_2,sales_3],[traffic_1,traffic_2,traffic_3],[s_t_1,s_t_2,s_t_3]],\n", "                    columns=[\"本月累计\",\"上月同期\",\"去年同期\"],index=[\"销售额\",\"客流量\",\"客单价\"])\n", "# print(report)\n", "# 添加同比和环比字段\n", "report[\"环比\"]=report[\"本月累计\"]/report[\"上月同期\"]-1\n", "\n", "report[\"同比\"]=report[\"本月累计\"]/report[\"去年同期\"]-1\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 查看报表"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>本月累计</th>\n", "      <th>上月同期</th>\n", "      <th>去年同期</th>\n", "      <th>环比</th>\n", "      <th>同比</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>销售额</th>\n", "      <td>10412.780070</td>\n", "      <td>9940.972910</td>\n", "      <td>8596.313470</td>\n", "      <td>0.047461</td>\n", "      <td>0.211308</td>\n", "    </tr>\n", "    <tr>\n", "      <th>客流量</th>\n", "      <td>343.000000</td>\n", "      <td>315.000000</td>\n", "      <td>262.000000</td>\n", "      <td>0.088889</td>\n", "      <td>0.309160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>客单价</th>\n", "      <td>30.357959</td>\n", "      <td>31.558644</td>\n", "      <td>32.810357</td>\n", "      <td>-0.038046</td>\n", "      <td>-0.074745</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             本月累计         上月同期         去年同期        环比        同比\n", "销售额  10412.780070  9940.972910  8596.313470  0.047461  0.211308\n", "客流量    343.000000   315.000000   262.000000  0.088889  0.309160\n", "客单价     30.357959    31.558644    32.810357 -0.038046 -0.074745"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看报表\n", "report"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 将结果导出本地"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# 将结果导出本地\n", "report.to_csv(\"order.csv\",encoding=\"utf-8-sig\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 2}
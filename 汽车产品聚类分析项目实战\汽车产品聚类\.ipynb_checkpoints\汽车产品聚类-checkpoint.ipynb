{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 导入模块"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# 使用KMeans进行聚类,导入库\n", "from sklearn.cluster import KMeans\n", "\n", "from sklearn.metrics import silhouette_score\n", "\n", "import matplotlib.pyplot as plt\n", "# 预处理\n", "from sklearn import preprocessing\n", "from sklearn.preprocessing import LabelEncoder\n", "import pandas as pd\n", "# 矩阵运算\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 数据加载"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>car_ID</th>\n", "      <th>symboling</th>\n", "      <th>CarName</th>\n", "      <th>fueltype</th>\n", "      <th>aspiration</th>\n", "      <th>doornumber</th>\n", "      <th>carbody</th>\n", "      <th>drivewheel</th>\n", "      <th>enginelocation</th>\n", "      <th>wheelbase</th>\n", "      <th>...</th>\n", "      <th>enginesize</th>\n", "      <th>fuelsystem</th>\n", "      <th>boreratio</th>\n", "      <th>stroke</th>\n", "      <th>compressionratio</th>\n", "      <th>horsepower</th>\n", "      <th>peakrpm</th>\n", "      <th>citympg</th>\n", "      <th>highwaympg</th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>alfa-romero giulia</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>two</td>\n", "      <td>convertible</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>88.6</td>\n", "      <td>...</td>\n", "      <td>130</td>\n", "      <td>mpfi</td>\n", "      <td>3.47</td>\n", "      <td>2.68</td>\n", "      <td>9.0</td>\n", "      <td>111</td>\n", "      <td>5000</td>\n", "      <td>21</td>\n", "      <td>27</td>\n", "      <td>13495.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>alfa-romero stelvio</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>two</td>\n", "      <td>convertible</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>88.6</td>\n", "      <td>...</td>\n", "      <td>130</td>\n", "      <td>mpfi</td>\n", "      <td>3.47</td>\n", "      <td>2.68</td>\n", "      <td>9.0</td>\n", "      <td>111</td>\n", "      <td>5000</td>\n", "      <td>21</td>\n", "      <td>27</td>\n", "      <td>16500.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>alfa-r<PERSON>o Quadrifoglio</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>two</td>\n", "      <td>hatchback</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>94.5</td>\n", "      <td>...</td>\n", "      <td>152</td>\n", "      <td>mpfi</td>\n", "      <td>2.68</td>\n", "      <td>3.47</td>\n", "      <td>9.0</td>\n", "      <td>154</td>\n", "      <td>5000</td>\n", "      <td>19</td>\n", "      <td>26</td>\n", "      <td>16500.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>audi 100 ls</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>fwd</td>\n", "      <td>front</td>\n", "      <td>99.8</td>\n", "      <td>...</td>\n", "      <td>109</td>\n", "      <td>mpfi</td>\n", "      <td>3.19</td>\n", "      <td>3.40</td>\n", "      <td>10.0</td>\n", "      <td>102</td>\n", "      <td>5500</td>\n", "      <td>24</td>\n", "      <td>30</td>\n", "      <td>13950.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>audi 100ls</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>4wd</td>\n", "      <td>front</td>\n", "      <td>99.4</td>\n", "      <td>...</td>\n", "      <td>136</td>\n", "      <td>mpfi</td>\n", "      <td>3.19</td>\n", "      <td>3.40</td>\n", "      <td>8.0</td>\n", "      <td>115</td>\n", "      <td>5500</td>\n", "      <td>18</td>\n", "      <td>22</td>\n", "      <td>17450.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>201</td>\n", "      <td>-1</td>\n", "      <td>volvo 145e (sw)</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>mpfi</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>9.5</td>\n", "      <td>114</td>\n", "      <td>5400</td>\n", "      <td>23</td>\n", "      <td>28</td>\n", "      <td>16845.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>202</td>\n", "      <td>-1</td>\n", "      <td>volvo 144ea</td>\n", "      <td>gas</td>\n", "      <td>turbo</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>mpfi</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>8.7</td>\n", "      <td>160</td>\n", "      <td>5300</td>\n", "      <td>19</td>\n", "      <td>25</td>\n", "      <td>19045.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>203</td>\n", "      <td>-1</td>\n", "      <td>volvo 244dl</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>...</td>\n", "      <td>173</td>\n", "      <td>mpfi</td>\n", "      <td>3.58</td>\n", "      <td>2.87</td>\n", "      <td>8.8</td>\n", "      <td>134</td>\n", "      <td>5500</td>\n", "      <td>18</td>\n", "      <td>23</td>\n", "      <td>21485.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>204</td>\n", "      <td>-1</td>\n", "      <td>volvo 246</td>\n", "      <td>diesel</td>\n", "      <td>turbo</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>...</td>\n", "      <td>145</td>\n", "      <td>idi</td>\n", "      <td>3.01</td>\n", "      <td>3.40</td>\n", "      <td>23.0</td>\n", "      <td>106</td>\n", "      <td>4800</td>\n", "      <td>26</td>\n", "      <td>27</td>\n", "      <td>22470.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>205</td>\n", "      <td>-1</td>\n", "      <td>volvo 264gl</td>\n", "      <td>gas</td>\n", "      <td>turbo</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>mpfi</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>9.5</td>\n", "      <td>114</td>\n", "      <td>5400</td>\n", "      <td>19</td>\n", "      <td>25</td>\n", "      <td>22625.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>205 rows × 26 columns</p>\n", "</div>"], "text/plain": ["     car_ID  symboling                   CarName fueltype aspiration  \\\n", "0         1          3        alfa-romero giulia      gas        std   \n", "1         2          3       alfa-romero stelvio      gas        std   \n", "2         3          1  alfa-romero Quadrifoglio      gas        std   \n", "3         4          2               audi 100 ls      gas        std   \n", "4         5          2                audi 100ls      gas        std   \n", "..      ...        ...                       ...      ...        ...   \n", "200     201         -1           volvo 145e (sw)      gas        std   \n", "201     202         -1               volvo 144ea      gas      turbo   \n", "202     203         -1               volvo 244dl      gas        std   \n", "203     204         -1                 volvo 246   diesel      turbo   \n", "204     205         -1               volvo 264gl      gas      turbo   \n", "\n", "    doornumber      carbody drivewheel enginelocation  wheelbase  ...  \\\n", "0          two  convertible        rwd          front       88.6  ...   \n", "1          two  convertible        rwd          front       88.6  ...   \n", "2          two    hatchback        rwd          front       94.5  ...   \n", "3         four        sedan        fwd          front       99.8  ...   \n", "4         four        sedan        4wd          front       99.4  ...   \n", "..         ...          ...        ...            ...        ...  ...   \n", "200       four        sedan        rwd          front      109.1  ...   \n", "201       four        sedan        rwd          front      109.1  ...   \n", "202       four        sedan        rwd          front      109.1  ...   \n", "203       four        sedan        rwd          front      109.1  ...   \n", "204       four        sedan        rwd          front      109.1  ...   \n", "\n", "     enginesize  fuelsystem  boreratio  stroke compressionratio horsepower  \\\n", "0           130        mpfi       3.47    2.68              9.0        111   \n", "1           130        mpfi       3.47    2.68              9.0        111   \n", "2           152        mpfi       2.68    3.47              9.0        154   \n", "3           109        mpfi       3.19    3.40             10.0        102   \n", "4           136        mpfi       3.19    3.40              8.0        115   \n", "..          ...         ...        ...     ...              ...        ...   \n", "200         141        mpfi       3.78    3.15              9.5        114   \n", "201         141        mpfi       3.78    3.15              8.7        160   \n", "202         173        mpfi       3.58    2.87              8.8        134   \n", "203         145         idi       3.01    3.40             23.0        106   \n", "204         141        mpfi       3.78    3.15              9.5        114   \n", "\n", "     peakrpm citympg  highwaympg    price  \n", "0       5000      21          27  13495.0  \n", "1       5000      21          27  16500.0  \n", "2       5000      19          26  16500.0  \n", "3       5500      24          30  13950.0  \n", "4       5500      18          22  17450.0  \n", "..       ...     ...         ...      ...  \n", "200     5400      23          28  16845.0  \n", "201     5300      19          25  19045.0  \n", "202     5500      18          23  21485.0  \n", "203     4800      26          27  22470.0  \n", "204     5400      19          25  22625.0  \n", "\n", "[205 rows x 26 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# 数据加载\n", "data = pd.read_csv('./car_price.csv')\n", "data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 字段说明"]}, {"cell_type": "markdown", "metadata": {}, "source": ["| 字段 | 说明 |\n", "| :------ | ---------- |\n", "| Car_ID                    | 汽车编号   |\n", "| Symboling                 | 车险等级   |\n", "| carCompany                | 汽车厂商   |\n", "| fueltype                  | 燃料类型   |\n", "| aspiration                | 吸气类型   |\n", "| doornumber                | 车门数     |\n", "| carbody                   | 车身类型   |\n", "| drivewheel                | 驱动类型   |\n", "| enginelocation            | 引擎位置   |\n", "| wheelbase                 | 轴距       |\n", "| carlength                 | 车长       |\n", "| carwidth                  | 车宽       |\n", "| carheight                 | 车高       |\n", "| curbweight                | 整备重量   |\n", "| enginetype                | 发动机类型 |\n", "| cylindernumber            | 气缸号     |\n", "| enginesize                | 发动机尺寸 |\n", "| fuelsystem                | 燃料系统   |\n", "| boreratio                 | 汽车传动比 |\n", "| stroke                    | 发动机冲程 |\n", "| compressionratio          | 汽车压缩比 |\n", "| horsepower                | 马力       |\n", "| peakrpm                   | 峰值转速   |\n", "| citympg                   | 城市里程   |\n", "| highwaympg                | 高速里程   |\n", "| price(Dependent variable) | 车辆价格 |"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symboling</th>\n", "      <th>fueltype</th>\n", "      <th>aspiration</th>\n", "      <th>doornumber</th>\n", "      <th>carbody</th>\n", "      <th>drivewheel</th>\n", "      <th>enginelocation</th>\n", "      <th>wheelbase</th>\n", "      <th>carlength</th>\n", "      <th>carwidth</th>\n", "      <th>...</th>\n", "      <th>enginesize</th>\n", "      <th>fuelsystem</th>\n", "      <th>boreratio</th>\n", "      <th>stroke</th>\n", "      <th>compressionratio</th>\n", "      <th>horsepower</th>\n", "      <th>peakrpm</th>\n", "      <th>citympg</th>\n", "      <th>highwaympg</th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>two</td>\n", "      <td>convertible</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>88.6</td>\n", "      <td>168.8</td>\n", "      <td>64.1</td>\n", "      <td>...</td>\n", "      <td>130</td>\n", "      <td>mpfi</td>\n", "      <td>3.47</td>\n", "      <td>2.68</td>\n", "      <td>9.0</td>\n", "      <td>111</td>\n", "      <td>5000</td>\n", "      <td>21</td>\n", "      <td>27</td>\n", "      <td>13495.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>two</td>\n", "      <td>convertible</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>88.6</td>\n", "      <td>168.8</td>\n", "      <td>64.1</td>\n", "      <td>...</td>\n", "      <td>130</td>\n", "      <td>mpfi</td>\n", "      <td>3.47</td>\n", "      <td>2.68</td>\n", "      <td>9.0</td>\n", "      <td>111</td>\n", "      <td>5000</td>\n", "      <td>21</td>\n", "      <td>27</td>\n", "      <td>16500.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>two</td>\n", "      <td>hatchback</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>94.5</td>\n", "      <td>171.2</td>\n", "      <td>65.5</td>\n", "      <td>...</td>\n", "      <td>152</td>\n", "      <td>mpfi</td>\n", "      <td>2.68</td>\n", "      <td>3.47</td>\n", "      <td>9.0</td>\n", "      <td>154</td>\n", "      <td>5000</td>\n", "      <td>19</td>\n", "      <td>26</td>\n", "      <td>16500.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>fwd</td>\n", "      <td>front</td>\n", "      <td>99.8</td>\n", "      <td>176.6</td>\n", "      <td>66.2</td>\n", "      <td>...</td>\n", "      <td>109</td>\n", "      <td>mpfi</td>\n", "      <td>3.19</td>\n", "      <td>3.40</td>\n", "      <td>10.0</td>\n", "      <td>102</td>\n", "      <td>5500</td>\n", "      <td>24</td>\n", "      <td>30</td>\n", "      <td>13950.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>4wd</td>\n", "      <td>front</td>\n", "      <td>99.4</td>\n", "      <td>176.6</td>\n", "      <td>66.4</td>\n", "      <td>...</td>\n", "      <td>136</td>\n", "      <td>mpfi</td>\n", "      <td>3.19</td>\n", "      <td>3.40</td>\n", "      <td>8.0</td>\n", "      <td>115</td>\n", "      <td>5500</td>\n", "      <td>18</td>\n", "      <td>22</td>\n", "      <td>17450.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>-1</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>188.8</td>\n", "      <td>68.9</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>mpfi</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>9.5</td>\n", "      <td>114</td>\n", "      <td>5400</td>\n", "      <td>23</td>\n", "      <td>28</td>\n", "      <td>16845.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>-1</td>\n", "      <td>gas</td>\n", "      <td>turbo</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>188.8</td>\n", "      <td>68.8</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>mpfi</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>8.7</td>\n", "      <td>160</td>\n", "      <td>5300</td>\n", "      <td>19</td>\n", "      <td>25</td>\n", "      <td>19045.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>-1</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>188.8</td>\n", "      <td>68.9</td>\n", "      <td>...</td>\n", "      <td>173</td>\n", "      <td>mpfi</td>\n", "      <td>3.58</td>\n", "      <td>2.87</td>\n", "      <td>8.8</td>\n", "      <td>134</td>\n", "      <td>5500</td>\n", "      <td>18</td>\n", "      <td>23</td>\n", "      <td>21485.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>-1</td>\n", "      <td>diesel</td>\n", "      <td>turbo</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>188.8</td>\n", "      <td>68.9</td>\n", "      <td>...</td>\n", "      <td>145</td>\n", "      <td>idi</td>\n", "      <td>3.01</td>\n", "      <td>3.40</td>\n", "      <td>23.0</td>\n", "      <td>106</td>\n", "      <td>4800</td>\n", "      <td>26</td>\n", "      <td>27</td>\n", "      <td>22470.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>-1</td>\n", "      <td>gas</td>\n", "      <td>turbo</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>188.8</td>\n", "      <td>68.9</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>mpfi</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>9.5</td>\n", "      <td>114</td>\n", "      <td>5400</td>\n", "      <td>19</td>\n", "      <td>25</td>\n", "      <td>22625.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>205 rows × 24 columns</p>\n", "</div>"], "text/plain": ["     symboling fueltype aspiration doornumber      carbody drivewheel  \\\n", "0            3      gas        std        two  convertible        rwd   \n", "1            3      gas        std        two  convertible        rwd   \n", "2            1      gas        std        two    hatchback        rwd   \n", "3            2      gas        std       four        sedan        fwd   \n", "4            2      gas        std       four        sedan        4wd   \n", "..         ...      ...        ...        ...          ...        ...   \n", "200         -1      gas        std       four        sedan        rwd   \n", "201         -1      gas      turbo       four        sedan        rwd   \n", "202         -1      gas        std       four        sedan        rwd   \n", "203         -1   diesel      turbo       four        sedan        rwd   \n", "204         -1      gas      turbo       four        sedan        rwd   \n", "\n", "    enginelocation  wheelbase  carlength  carwidth  ...  enginesize  \\\n", "0            front       88.6      168.8      64.1  ...         130   \n", "1            front       88.6      168.8      64.1  ...         130   \n", "2            front       94.5      171.2      65.5  ...         152   \n", "3            front       99.8      176.6      66.2  ...         109   \n", "4            front       99.4      176.6      66.4  ...         136   \n", "..             ...        ...        ...       ...  ...         ...   \n", "200          front      109.1      188.8      68.9  ...         141   \n", "201          front      109.1      188.8      68.8  ...         141   \n", "202          front      109.1      188.8      68.9  ...         173   \n", "203          front      109.1      188.8      68.9  ...         145   \n", "204          front      109.1      188.8      68.9  ...         141   \n", "\n", "     fuelsystem boreratio stroke  compressionratio horsepower  peakrpm  \\\n", "0          mpfi      3.47   2.68               9.0        111     5000   \n", "1          mpfi      3.47   2.68               9.0        111     5000   \n", "2          mpfi      2.68   3.47               9.0        154     5000   \n", "3          mpfi      3.19   3.40              10.0        102     5500   \n", "4          mpfi      3.19   3.40               8.0        115     5500   \n", "..          ...       ...    ...               ...        ...      ...   \n", "200        mpfi      3.78   3.15               9.5        114     5400   \n", "201        mpfi      3.78   3.15               8.7        160     5300   \n", "202        mpfi      3.58   2.87               8.8        134     5500   \n", "203         idi      3.01   3.40              23.0        106     4800   \n", "204        mpfi      3.78   3.15               9.5        114     5400   \n", "\n", "     citympg  highwaympg    price  \n", "0         21          27  13495.0  \n", "1         21          27  16500.0  \n", "2         19          26  16500.0  \n", "3         24          30  13950.0  \n", "4         18          22  17450.0  \n", "..       ...         ...      ...  \n", "200       23          28  16845.0  \n", "201       19          25  19045.0  \n", "202       18          23  21485.0  \n", "203       26          27  22470.0  \n", "204       19          25  22625.0  \n", "\n", "[205 rows x 24 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 删除，无价值的特征\n", "train_x = data.drop(['car_ID','CarName'],axis = 1)\n", "train_x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 特征工程"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symboling</th>\n", "      <th>fueltype</th>\n", "      <th>aspiration</th>\n", "      <th>doornumber</th>\n", "      <th>carbody</th>\n", "      <th>drivewheel</th>\n", "      <th>enginelocation</th>\n", "      <th>wheelbase</th>\n", "      <th>carlength</th>\n", "      <th>carwidth</th>\n", "      <th>...</th>\n", "      <th>enginesize</th>\n", "      <th>fuelsystem</th>\n", "      <th>boreratio</th>\n", "      <th>stroke</th>\n", "      <th>compressionratio</th>\n", "      <th>horsepower</th>\n", "      <th>peakrpm</th>\n", "      <th>citympg</th>\n", "      <th>highwaympg</th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>88.6</td>\n", "      <td>168.8</td>\n", "      <td>64.1</td>\n", "      <td>...</td>\n", "      <td>130</td>\n", "      <td>5</td>\n", "      <td>3.47</td>\n", "      <td>2.68</td>\n", "      <td>9.0</td>\n", "      <td>111</td>\n", "      <td>5000</td>\n", "      <td>21</td>\n", "      <td>27</td>\n", "      <td>13495.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>88.6</td>\n", "      <td>168.8</td>\n", "      <td>64.1</td>\n", "      <td>...</td>\n", "      <td>130</td>\n", "      <td>5</td>\n", "      <td>3.47</td>\n", "      <td>2.68</td>\n", "      <td>9.0</td>\n", "      <td>111</td>\n", "      <td>5000</td>\n", "      <td>21</td>\n", "      <td>27</td>\n", "      <td>16500.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>94.5</td>\n", "      <td>171.2</td>\n", "      <td>65.5</td>\n", "      <td>...</td>\n", "      <td>152</td>\n", "      <td>5</td>\n", "      <td>2.68</td>\n", "      <td>3.47</td>\n", "      <td>9.0</td>\n", "      <td>154</td>\n", "      <td>5000</td>\n", "      <td>19</td>\n", "      <td>26</td>\n", "      <td>16500.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>99.8</td>\n", "      <td>176.6</td>\n", "      <td>66.2</td>\n", "      <td>...</td>\n", "      <td>109</td>\n", "      <td>5</td>\n", "      <td>3.19</td>\n", "      <td>3.40</td>\n", "      <td>10.0</td>\n", "      <td>102</td>\n", "      <td>5500</td>\n", "      <td>24</td>\n", "      <td>30</td>\n", "      <td>13950.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>99.4</td>\n", "      <td>176.6</td>\n", "      <td>66.4</td>\n", "      <td>...</td>\n", "      <td>136</td>\n", "      <td>5</td>\n", "      <td>3.19</td>\n", "      <td>3.40</td>\n", "      <td>8.0</td>\n", "      <td>115</td>\n", "      <td>5500</td>\n", "      <td>18</td>\n", "      <td>22</td>\n", "      <td>17450.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>109.1</td>\n", "      <td>188.8</td>\n", "      <td>68.9</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>5</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>9.5</td>\n", "      <td>114</td>\n", "      <td>5400</td>\n", "      <td>23</td>\n", "      <td>28</td>\n", "      <td>16845.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>109.1</td>\n", "      <td>188.8</td>\n", "      <td>68.8</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>5</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>8.7</td>\n", "      <td>160</td>\n", "      <td>5300</td>\n", "      <td>19</td>\n", "      <td>25</td>\n", "      <td>19045.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>109.1</td>\n", "      <td>188.8</td>\n", "      <td>68.9</td>\n", "      <td>...</td>\n", "      <td>173</td>\n", "      <td>5</td>\n", "      <td>3.58</td>\n", "      <td>2.87</td>\n", "      <td>8.8</td>\n", "      <td>134</td>\n", "      <td>5500</td>\n", "      <td>18</td>\n", "      <td>23</td>\n", "      <td>21485.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>109.1</td>\n", "      <td>188.8</td>\n", "      <td>68.9</td>\n", "      <td>...</td>\n", "      <td>145</td>\n", "      <td>3</td>\n", "      <td>3.01</td>\n", "      <td>3.40</td>\n", "      <td>23.0</td>\n", "      <td>106</td>\n", "      <td>4800</td>\n", "      <td>26</td>\n", "      <td>27</td>\n", "      <td>22470.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>109.1</td>\n", "      <td>188.8</td>\n", "      <td>68.9</td>\n", "      <td>...</td>\n", "      <td>141</td>\n", "      <td>5</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>9.5</td>\n", "      <td>114</td>\n", "      <td>5400</td>\n", "      <td>19</td>\n", "      <td>25</td>\n", "      <td>22625.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>205 rows × 24 columns</p>\n", "</div>"], "text/plain": ["     symboling  fueltype  aspiration  doornumber  carbody  drivewheel  \\\n", "0            3         1           0           1        0           2   \n", "1            3         1           0           1        0           2   \n", "2            1         1           0           1        2           2   \n", "3            2         1           0           0        3           1   \n", "4            2         1           0           0        3           0   \n", "..         ...       ...         ...         ...      ...         ...   \n", "200         -1         1           0           0        3           2   \n", "201         -1         1           1           0        3           2   \n", "202         -1         1           0           0        3           2   \n", "203         -1         0           1           0        3           2   \n", "204         -1         1           1           0        3           2   \n", "\n", "     enginelocation  wheelbase  carlength  carwidth  ...  enginesize  \\\n", "0                 0       88.6      168.8      64.1  ...         130   \n", "1                 0       88.6      168.8      64.1  ...         130   \n", "2                 0       94.5      171.2      65.5  ...         152   \n", "3                 0       99.8      176.6      66.2  ...         109   \n", "4                 0       99.4      176.6      66.4  ...         136   \n", "..              ...        ...        ...       ...  ...         ...   \n", "200               0      109.1      188.8      68.9  ...         141   \n", "201               0      109.1      188.8      68.8  ...         141   \n", "202               0      109.1      188.8      68.9  ...         173   \n", "203               0      109.1      188.8      68.9  ...         145   \n", "204               0      109.1      188.8      68.9  ...         141   \n", "\n", "     fuelsystem  boreratio  stroke  compressionratio  horsepower  peakrpm  \\\n", "0             5       3.47    2.68               9.0         111     5000   \n", "1             5       3.47    2.68               9.0         111     5000   \n", "2             5       2.68    3.47               9.0         154     5000   \n", "3             5       3.19    3.40              10.0         102     5500   \n", "4             5       3.19    3.40               8.0         115     5500   \n", "..          ...        ...     ...               ...         ...      ...   \n", "200           5       3.78    3.15               9.5         114     5400   \n", "201           5       3.78    3.15               8.7         160     5300   \n", "202           5       3.58    2.87               8.8         134     5500   \n", "203           3       3.01    3.40              23.0         106     4800   \n", "204           5       3.78    3.15               9.5         114     5400   \n", "\n", "     citympg  highwaympg    price  \n", "0         21          27  13495.0  \n", "1         21          27  16500.0  \n", "2         19          26  16500.0  \n", "3         24          30  13950.0  \n", "4         18          22  17450.0  \n", "..       ...         ...      ...  \n", "200       23          28  16845.0  \n", "201       19          25  19045.0  \n", "202       18          23  21485.0  \n", "203       26          27  22470.0  \n", "204       19          25  22625.0  \n", "\n", "[205 rows x 24 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将非数值字段转化为数值\n", "le = LabelEncoder()\n", "columns = ['fueltype','aspiration','doornumber','carbody','drivewheel','enginelocation',\n", "           'enginetype','cylindernumber','fuelsystem']\n", "for column in columns:\n", "    # 训练并将标签转换为归一化的编码。\n", "    train_x[column] = le.fit_transform(train_x[column])\n", "train_x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 归一化处理"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1.        , 1.        , 0.        , ..., 0.22222222, 0.28947368,\n", "        0.20795889],\n", "       [1.        , 1.        , 0.        , ..., 0.22222222, 0.28947368,\n", "        0.28255797],\n", "       [0.6       , 1.        , 0.        , ..., 0.16666667, 0.26315789,\n", "        0.28255797],\n", "       ...,\n", "       [0.2       , 1.        , 0.        , ..., 0.13888889, 0.18421053,\n", "        0.40631051],\n", "       [0.2       , 0.        , 1.        , ..., 0.36111111, 0.28947368,\n", "        0.43076312],\n", "       [0.2       , 1.        , 1.        , ..., 0.16666667, 0.23684211,\n", "        0.43461099]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 规范化到 [0,1] 空间\n", "min_max_scaler=preprocessing.MinMaxScaler()\n", "# MinMaxScaler()将每个要素缩放到给定范围，拟合数据，然后对其进行转换。\n", "train_x = min_max_scaler.fit_transform(train_x)\n", "train_x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 聚类参数选择"]}, {"cell_type": "markdown", "metadata": {}, "source": ["字体选择"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["['DejaVu Sans Mono',\n", " 'STIXGeneral',\n", " 'STIXSizeOneSym',\n", " 'STIXNonUnicode',\n", " 'cmb10',\n", " 'STIXSizeTwoSym',\n", " 'STIXNonUnicode',\n", " 'STIXNonUnicode',\n", " 'cmmi10',\n", " 'DejaVu Sans Mono',\n", " 'cmr10',\n", " 'cmsy10',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " 'STIXSizeFourSym',\n", " 'STIXSizeFiveSym',\n", " 'STIXSizeThreeSym',\n", " 'STIXSizeTwoSym',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " 'STIXSizeOneSym',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " 'DejaVu Sans Display',\n", " 'STIXGeneral',\n", " 'cmtt10',\n", " 'DejaVu Sans',\n", " 'DejaVu Sans Mono',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " 'cmex10',\n", " 'STIXGeneral',\n", " 'STIXGeneral',\n", " 'DejaVu Sans Mono',\n", " 'DejaVu Sans',\n", " 'cmss10',\n", " 'STIXNonUnicode',\n", " 'STIXSizeThreeSym',\n", " 'DejaVu Sans',\n", " 'DejaVu Sans',\n", " 'STIXSizeFourSym',\n", " 'Lucida Sans Unicode',\n", " 'Perpetua',\n", " 'Consolas',\n", " 'Sitka',\n", " '<PERSON>',\n", " 'Arial',\n", " 'Tw Cen MT',\n", " 'Tw Cen MT Condensed Extra Bold',\n", " 'Berlin Sans FB Demi',\n", " 'Eras Light ITC',\n", " 'Myanmar Text',\n", " 'MS Outlook',\n", " 'Dubai',\n", " 'MS Gothic',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Nirmala UI',\n", " 'DengXian',\n", " 'Rockwell',\n", " 'Segoe UI Variable',\n", " 'Segoe UI',\n", " 'Lucida Fax',\n", " 'Bookman Old Style',\n", " 'Nirmala UI',\n", " 'Segoe UI',\n", " 'Wingdings',\n", " 'Bell MT',\n", " 'Footlight MT Light',\n", " '<PERSON>',\n", " 'Agency FB',\n", " '<PERSON><PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Baskerville Old Face',\n", " 'FZShuTi',\n", " 'Bell MT',\n", " 'Lucida Sans',\n", " 'Freestyle Script',\n", " 'Berlin Sans FB',\n", " 'Bodoni MT',\n", " 'Times New Roman',\n", " 'Book Antiqua',\n", " 'Century',\n", " 'STLiti',\n", " 'Showcard Gothic',\n", " 'Lucida Fax',\n", " 'STXingkai',\n", " '<PERSON> Condensed',\n", " 'Elephant',\n", " 'DengXian',\n", " 'Franklin Gothic Demi',\n", " 'Microsoft YaHei',\n", " 'Consolas',\n", " 'Gill Sans MT',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Kristen ITC',\n", " 'Trebuchet MS',\n", " 'Matura MT Script Capitals',\n", " 'Broadway',\n", " 'Segoe UI',\n", " '<PERSON><PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Segoe UI',\n", " 'Symbol',\n", " 'Bell MT',\n", " 'Magne<PERSON>',\n", " 'Segoe MDL2 Assets',\n", " 'Goudy Old Style',\n", " 'Yu Gothic',\n", " 'High Tower Text',\n", " 'Calisto MT',\n", " 'KaiTi',\n", " 'Juice ITC',\n", " 'Britannic Bold',\n", " 'French Script MT',\n", " 'AniMe Matrix - MB_EN',\n", " '<PERSON><PERSON><PERSON>',\n", " 'STSong',\n", " 'FangSong',\n", " 'Bodoni MT',\n", " 'Cooper Black',\n", " 'Century Schoolbook',\n", " 'Lucida Console',\n", " 'Microsoft New Tai Lue',\n", " 'High Tower Text',\n", " 'Courier New',\n", " 'Javanese Text',\n", " 'MT Extra',\n", " 'Century Schoolbook',\n", " 'Rockwell Condensed',\n", " 'Franklin Gothic Book',\n", " 'Gill Sans Ultra Bold Condensed',\n", " '<PERSON><PERSON>',\n", " 'Californian FB',\n", " '<PERSON><PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Arial',\n", " 'Sans Serif Collection',\n", " 'Rage Italic',\n", " 'SimSun',\n", " 'Rockwell Extra Bold',\n", " 'Monotype Corsiva',\n", " '<PERSON><PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Gill Sans MT Ext Condensed Bold',\n", " 'Constantia',\n", " 'STHupo',\n", " 'Script MT Bold',\n", " 'Arial',\n", " 'Lucida Sans Typewriter',\n", " 'Segoe UI Historic',\n", " 'Trebuchet MS',\n", " 'Gill Sans MT',\n", " 'Eras Medium ITC',\n", " 'Nirmala UI',\n", " 'Century Gothic',\n", " 'Courier New',\n", " 'Segoe UI',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Dubai',\n", " '<PERSON><PERSON><PERSON> Script',\n", " '<PERSON>dar<PERSON>',\n", " 'Microsoft Himalaya',\n", " 'Lucida Sans Typewriter',\n", " 'Poor Richard',\n", " '<PERSON>dar<PERSON>',\n", " '<PERSON>dar<PERSON>',\n", " 'Segoe UI',\n", " 'Segoe UI',\n", " 'Comic Sans MS',\n", " 'Lucida Sans Typewriter',\n", " '<PERSON>dar<PERSON>',\n", " 'Sylfaen',\n", " 'Niagara Solid',\n", " 'Berlin Sans FB',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Century Gothic',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Segoe UI',\n", " 'Dubai',\n", " 'Bodoni MT',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Bodoni MT',\n", " 'Bookshelf Symbol 7',\n", " 'Yu Gothic',\n", " 'DejaVu Math TeX Gyre',\n", " '<PERSON>dar<PERSON>',\n", " 'Franklin Gothic Heavy',\n", " 'Castellar',\n", " 'Forte',\n", " 'Tw Cen MT',\n", " 'Arial Rounded MT Bold',\n", " 'Algerian',\n", " 'Playbill',\n", " 'Microsoft PhagsPa',\n", " 'Sitka',\n", " 'Niagara Engraved',\n", " 'Georgia',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Malgun Gothic',\n", " 'Microsoft PhagsPa',\n", " 'Haettenschweiler',\n", " '<PERSON><PERSON><PERSON>',\n", " 'HoloLens MDL2 Assets',\n", " 'Snap ITC',\n", " 'Bodoni MT',\n", " 'Bauhaus 93',\n", " 'Times New Roman',\n", " 'Century Gothic',\n", " 'Times New Roman',\n", " 'Gill Sans MT',\n", " 'Old English Text MT',\n", " 'Malgun Gothic',\n", " 'Wide Latin',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Harlow Solid Italic',\n", " 'DengXian',\n", " 'Modern No. 20',\n", " 'Segoe UI',\n", " 'Wingdings 3',\n", " 'Calisto MT',\n", " 'Bookman Old Style',\n", " 'Microsoft YaHei',\n", " '<PERSON><PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Tempus Sans ITC',\n", " '<PERSON><PERSON><PERSON>',\n", " 'STXinwei',\n", " 'Cascadia Code',\n", " 'Perpetua',\n", " 'Elephant',\n", " 'Franklin Gothic Medium',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Centaur',\n", " 'Microsoft JhengHei',\n", " 'Informal Roman',\n", " 'Trebuchet MS',\n", " 'St<PERSON>cil',\n", " 'Bookman Old Style',\n", " 'Myanmar Text',\n", " 'Arial',\n", " 'Perpetua',\n", " 'Lucida Sans',\n", " 'Lucida Fax',\n", " 'Goudy Old Style',\n", " 'I<PERSON>rint MT Shadow',\n", " 'Cambria',\n", " 'Comic Sans MS',\n", " 'Courier New',\n", " 'Luc<PERSON>',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " 'Curlz MT',\n", " 'Book Antiqua',\n", " '<PERSON>',\n", " 'Segoe UI',\n", " 'Leelawadee UI',\n", " 'Lucida Sans',\n", " 'Webdings',\n", " 'Jokerman',\n", " 'Blackadder ITC',\n", " 'Franklin Gothic Medium Cond',\n", " 'Eras Bold ITC',\n", " 'Viner Hand ITC',\n", " 'Ink Free',\n", " '<PERSON><PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Microsoft New Tai Lue',\n", " 'SimSun-ExtB',\n", " 'Cascadia Mono',\n", " '<PERSON><PERSON> ITC',\n", " 'HYZhongHei',\n", " 'Arial',\n", " 'Microsoft Sans Serif',\n", " 'Franklin Gothic Heavy',\n", " 'Tw Cen MT Condensed',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Luc<PERSON>',\n", " 'Malgun Gothic',\n", " 'Copperplate Gothic Bold',\n", " 'Onyx',\n", " 'Eras Demi ITC',\n", " 'Cambria',\n", " 'Comic Sans MS',\n", " '<PERSON><PERSON><PERSON>',\n", " 'STZhongsong',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Book Antiqua',\n", " 'Bodoni MT',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Microsoft JhengHei',\n", " 'Mistral',\n", " 'Georgia',\n", " 'Gab<PERSON>la',\n", " 'Segoe UI Symbol',\n", " 'Segoe UI',\n", " '<PERSON><PERSON>',\n", " 'Arial',\n", " 'Courier New',\n", " 'Microsoft JhengHei',\n", " 'Gill Sans MT',\n", " 'Georgia',\n", " 'Rockwell',\n", " 'Tw Cen MT',\n", " 'Consolas',\n", " 'Palace Script MT',\n", " 'Calisto MT',\n", " 'Consolas',\n", " '<PERSON><PERSON><PERSON>',\n", " 'SimHei',\n", " 'Engravers MT',\n", " 'Microsoft Tai Le',\n", " 'Bahnschrift',\n", " 'Segoe Fluent Icons',\n", " 'Comic Sans MS',\n", " 'Brush Script MT',\n", " 'Bodoni MT',\n", " 'Yu Gothic',\n", " 'Bodoni MT',\n", " 'Leelawadee UI',\n", " 'Rockwell',\n", " 'Rockwell Condensed',\n", " 'Bradley Hand ITC',\n", " 'Microsoft Yi Baiti',\n", " 'Cambria',\n", " 'Arial',\n", " 'Impact',\n", " 'Franklin Gothic Medium',\n", " 'Lucida Handwriting',\n", " 'Agency FB',\n", " 'Bookman Old Style',\n", " 'STXihei',\n", " 'Segoe Print',\n", " 'LiSu',\n", " 'Rockwell',\n", " 'Luc<PERSON>',\n", " 'Luc<PERSON>',\n", " 'Franklin Gothic Demi',\n", " 'Cambria',\n", " 'Century Schoolbook',\n", " 'Segoe UI',\n", " 'Bodoni MT',\n", " 'Century Gothic',\n", " 'Mongolian Baiti',\n", " 'Century Schoolbook',\n", " '<PERSON>dar<PERSON>',\n", " '<PERSON><PERSON><PERSON>',\n", " 'MV Boli',\n", " 'Copperplate Gothic Light',\n", " 'Californian FB',\n", " 'Gloucester MT Extra Condensed',\n", " 'Book Antiqua',\n", " 'Segoe Print',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Bodoni MT',\n", " 'Arial',\n", " 'Constantia',\n", " 'Segoe UI Emoji',\n", " 'Yu Gothic',\n", " 'Ravie',\n", " 'Lucida Fax',\n", " 'OCR A Extended',\n", " 'MingLiU-ExtB',\n", " 'Times New Roman',\n", " 'Kingsoft Symbol',\n", " 'Lucida Sans Typewriter',\n", " 'Wingdings 2',\n", " 'STKait<PERSON>',\n", " 'Gill Sans Ultra Bold',\n", " 'Lucida Sans',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Franklin Gothic Book',\n", " 'Constantia',\n", " 'Gill Sans MT Condensed',\n", " 'Constantia',\n", " 'Calisto MT',\n", " 'STFangsong',\n", " 'Arial',\n", " 'Trebuchet MS',\n", " 'Perpetua Titling MT',\n", " 'Perpetua Titling MT',\n", " 'Tw Cen MT Condensed',\n", " 'Goudy Old Style',\n", " 'Chiller',\n", " 'YouYuan',\n", " 'Vivald<PERSON>',\n", " 'Microsoft YaHei',\n", " 'Georgia',\n", " 'MS Reference Sans Serif',\n", " '<PERSON><PERSON><PERSON>',\n", " 'Franklin Gothic Demi Cond',\n", " 'Californian FB',\n", " 'Perpetua',\n", " 'Leelawadee UI',\n", " 'Microsoft Tai Le',\n", " 'FZYaoTi',\n", " 'Dubai',\n", " 'STCaiyun',\n", " 'Parchment',\n", " 'Lucida Calligraphy',\n", " 'MS Reference Specialty',\n", " 'Papy<PERSON>',\n", " 'Bodoni MT',\n", " 'Tw Cen MT']"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查找自己电脑上的中文字体，从打印输出中找到对应中文字体\n", "# 本电脑上，我选择：STKaiti（注意，电脑不同，字体可能不同，需要各位自己修改）\n", "from matplotlib.font_manager import FontManager\n", "fm = FontManager()\n", "[font.name for font in fm.ttflist]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["字体设置"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["plt.rcParams['font.family'] = 'STKaiti'\n", "plt.rcParams['font.size'] = 20"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### SSE（簇惯性）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<font size = 12 color = 'red'>$SSE = \\sum\\limits_{i = 0}^k\\sum\\limits_{j = 0}^{m_i}||x_j^{(i)} - \\mu^{(i)}||_2^2 \\quad $</font>\n", "\n", "k 表示簇数量\n", "\n", "$m_i$ 表示第 i 簇中样本数量\n", "\n", "$x_j^{(i)}$ 表示第 i 簇中第 j 个样本\n", "\n", "$\\mu^{(i)}$ 表示第 i 簇中心"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 轮廓系数"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* 第 $i$ 个样本与簇内的其他点之间的平均距离作为簇内的**内聚度** $a^{(i)}$\n", "\n", "* 第 $i$ 样本与最近簇中所有点之间的平均距离看作是与最近簇的**分离度** $b^{(i)}$\n", "\n", "* 将簇的分离度与簇内聚度之差除以二者中比较大的数得到**轮廓系数**，计算公式如下：\n", "\n", "\n", "\n", "<font size = 12 color = 'green'>$S^{(i)} = \\frac{|b^{(i)} - a^{(i)}|}{max \\{b^{(i)},\\ a^{(i)}\\}}$</font>\n", "\n", "\n", "\n", "将所有点的轮廓系数求平均，就是该聚类结果总的**轮廓系数**！"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 聚类筛选"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-09-09 12:49:20,729 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:20,801 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1382: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:20,967 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:20,980 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1382: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:21,138 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:21,153 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1382: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:21,311 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:21,324 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1382: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:21,484 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:21,498 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1382: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:21,657 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:21,671 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1382: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:21,833 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:21,846 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1382: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:22,006 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:22,020 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1382: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:22,181 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:22,195 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1382: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1600x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 选择聚类组数\n", "# 每簇样本到其聚类中心的距离的平方和\n", "# 各个簇的inertia相加的和越小，即簇内越相似\n", "# 但是k越大inertia越小，追求k越大对应用无益处\n", "sse = [] # 簇惯性\n", "ss = [] # 轮廓系数\n", "for k in range(2, 11):\n", "    kmeans = KMeans(n_clusters=k)\n", "    kmeans.fit(train_x)\n", "    # 计算inertia簇内误差平方和\n", "    sse.append(kmeans.inertia_)\n", "    ss.append(silhouette_score(train_x,kmeans.predict(train_x)))\n", "\n", "\n", "plt.figure(figsize=(16,6))\n", "x = range(2, 11)\n", "plt.subplot(1,2,1)\n", "plt.plot(x, sse, 'o-')\n", "plt.xlabel('K')\n", "plt.ylabel('SSE簇惯性')\n", "\n", "plt.subplot(1,2,2)\n", "plt.plot(x,ss,'r*-')\n", "plt.xlabel('K')\n", "plt.ylabel('轮廓系数')\n", "plt.savefig('./1-聚类簇数.png',dpi = 200)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 聚类运算"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-09-09 12:49:22,698 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:870: FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4. Set the value of `n_init` explicitly to suppress the warning\n", "  warnings.warn(\n", "\n", "2024-09-09 12:49:22,711 [42168] WARNING  py.warnings:109: [JupyterRequire] D:\\Anaconda3\\Lib\\site-packages\\sklearn\\cluster\\_kmeans.py:1382: UserWarning: KMeans is known to have a memory leak on Windows with MKL, when there are less chunks than available threads. You can avoid it by setting the environment variable OMP_NUM_THREADS=1.\n", "  warnings.warn(\n", "\n"]}, {"data": {"text/plain": ["array([1, 1, 1, 2, 2, 1, 2, 2, 0, 6, 1, 2, 1, 0, 0, 0, 1, 0, 3, 3, 7, 3,\n", "       3, 6, 7, 7, 7, 6, 7, 6, 3, 3, 3, 3, 3, 7, 7, 3, 3, 7, 7, 2, 3, 7,\n", "       3, 7, 1, 0, 0, 1, 3, 3, 3, 7, 7, 1, 1, 1, 1, 3, 7, 3, 7, 4, 7, 0,\n", "       4, 5, 5, 5, 5, 0, 1, 0, 1, 6, 3, 3, 3, 6, 6, 3, 6, 6, 6, 7, 7, 6,\n", "       2, 3, 4, 3, 7, 7, 3, 3, 7, 7, 3, 7, 7, 2, 2, 2, 1, 6, 1, 0, 5, 0,\n", "       5, 0, 5, 0, 5, 0, 5, 0, 3, 6, 7, 7, 7, 7, 6, 1, 1, 1, 1, 1, 2, 1,\n", "       1, 2, 1, 2, 6, 0, 3, 3, 3, 7, 7, 2, 7, 2, 7, 2, 7, 2, 3, 3, 7, 7,\n", "       7, 7, 7, 7, 4, 4, 7, 7, 7, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1, 2, 5, 2,\n", "       2, 2, 1, 1, 0, 0, 4, 3, 4, 2, 2, 4, 2, 1, 1, 2, 5, 2, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 5, 0])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 使用KMeans聚类,分成8类\n", "kmeans = KMeans(n_clusters=8)\n", "kmeans.fit(train_x)  # 建模\n", "# 预测\n", "predict_y = kmeans.predict(train_x)\n", "predict_y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 结果分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 结果合并"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>car_ID</th>\n", "      <th>symboling</th>\n", "      <th>CarName</th>\n", "      <th>fueltype</th>\n", "      <th>aspiration</th>\n", "      <th>doornumber</th>\n", "      <th>carbody</th>\n", "      <th>drivewheel</th>\n", "      <th>enginelocation</th>\n", "      <th>wheelbase</th>\n", "      <th>...</th>\n", "      <th>fuelsystem</th>\n", "      <th>boreratio</th>\n", "      <th>stroke</th>\n", "      <th>compressionratio</th>\n", "      <th>horsepower</th>\n", "      <th>peakrpm</th>\n", "      <th>citympg</th>\n", "      <th>highwaympg</th>\n", "      <th>price</th>\n", "      <th>聚类结果</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>alfa-romero giulia</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>two</td>\n", "      <td>convertible</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>88.6</td>\n", "      <td>...</td>\n", "      <td>mpfi</td>\n", "      <td>3.47</td>\n", "      <td>2.68</td>\n", "      <td>9.0</td>\n", "      <td>111</td>\n", "      <td>5000</td>\n", "      <td>21</td>\n", "      <td>27</td>\n", "      <td>13495.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>alfa-romero stelvio</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>two</td>\n", "      <td>convertible</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>88.6</td>\n", "      <td>...</td>\n", "      <td>mpfi</td>\n", "      <td>3.47</td>\n", "      <td>2.68</td>\n", "      <td>9.0</td>\n", "      <td>111</td>\n", "      <td>5000</td>\n", "      <td>21</td>\n", "      <td>27</td>\n", "      <td>16500.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>alfa-r<PERSON>o Quadrifoglio</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>two</td>\n", "      <td>hatchback</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>94.5</td>\n", "      <td>...</td>\n", "      <td>mpfi</td>\n", "      <td>2.68</td>\n", "      <td>3.47</td>\n", "      <td>9.0</td>\n", "      <td>154</td>\n", "      <td>5000</td>\n", "      <td>19</td>\n", "      <td>26</td>\n", "      <td>16500.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>audi 100 ls</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>fwd</td>\n", "      <td>front</td>\n", "      <td>99.8</td>\n", "      <td>...</td>\n", "      <td>mpfi</td>\n", "      <td>3.19</td>\n", "      <td>3.40</td>\n", "      <td>10.0</td>\n", "      <td>102</td>\n", "      <td>5500</td>\n", "      <td>24</td>\n", "      <td>30</td>\n", "      <td>13950.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>audi 100ls</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>4wd</td>\n", "      <td>front</td>\n", "      <td>99.4</td>\n", "      <td>...</td>\n", "      <td>mpfi</td>\n", "      <td>3.19</td>\n", "      <td>3.40</td>\n", "      <td>8.0</td>\n", "      <td>115</td>\n", "      <td>5500</td>\n", "      <td>18</td>\n", "      <td>22</td>\n", "      <td>17450.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>201</td>\n", "      <td>-1</td>\n", "      <td>volvo 145e (sw)</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>...</td>\n", "      <td>mpfi</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>9.5</td>\n", "      <td>114</td>\n", "      <td>5400</td>\n", "      <td>23</td>\n", "      <td>28</td>\n", "      <td>16845.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>202</td>\n", "      <td>-1</td>\n", "      <td>volvo 144ea</td>\n", "      <td>gas</td>\n", "      <td>turbo</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>...</td>\n", "      <td>mpfi</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>8.7</td>\n", "      <td>160</td>\n", "      <td>5300</td>\n", "      <td>19</td>\n", "      <td>25</td>\n", "      <td>19045.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>203</td>\n", "      <td>-1</td>\n", "      <td>volvo 244dl</td>\n", "      <td>gas</td>\n", "      <td>std</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>...</td>\n", "      <td>mpfi</td>\n", "      <td>3.58</td>\n", "      <td>2.87</td>\n", "      <td>8.8</td>\n", "      <td>134</td>\n", "      <td>5500</td>\n", "      <td>18</td>\n", "      <td>23</td>\n", "      <td>21485.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>204</td>\n", "      <td>-1</td>\n", "      <td>volvo 246</td>\n", "      <td>diesel</td>\n", "      <td>turbo</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>...</td>\n", "      <td>idi</td>\n", "      <td>3.01</td>\n", "      <td>3.40</td>\n", "      <td>23.0</td>\n", "      <td>106</td>\n", "      <td>4800</td>\n", "      <td>26</td>\n", "      <td>27</td>\n", "      <td>22470.0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>205</td>\n", "      <td>-1</td>\n", "      <td>volvo 264gl</td>\n", "      <td>gas</td>\n", "      <td>turbo</td>\n", "      <td>four</td>\n", "      <td>sedan</td>\n", "      <td>rwd</td>\n", "      <td>front</td>\n", "      <td>109.1</td>\n", "      <td>...</td>\n", "      <td>mpfi</td>\n", "      <td>3.78</td>\n", "      <td>3.15</td>\n", "      <td>9.5</td>\n", "      <td>114</td>\n", "      <td>5400</td>\n", "      <td>19</td>\n", "      <td>25</td>\n", "      <td>22625.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>205 rows × 27 columns</p>\n", "</div>"], "text/plain": ["     car_ID  symboling                   CarName fueltype aspiration  \\\n", "0         1          3        alfa-romero giulia      gas        std   \n", "1         2          3       alfa-romero stelvio      gas        std   \n", "2         3          1  alfa-romero Quadrifoglio      gas        std   \n", "3         4          2               audi 100 ls      gas        std   \n", "4         5          2                audi 100ls      gas        std   \n", "..      ...        ...                       ...      ...        ...   \n", "200     201         -1           volvo 145e (sw)      gas        std   \n", "201     202         -1               volvo 144ea      gas      turbo   \n", "202     203         -1               volvo 244dl      gas        std   \n", "203     204         -1                 volvo 246   diesel      turbo   \n", "204     205         -1               volvo 264gl      gas      turbo   \n", "\n", "    doornumber      carbody drivewheel enginelocation  wheelbase  ...  \\\n", "0          two  convertible        rwd          front       88.6  ...   \n", "1          two  convertible        rwd          front       88.6  ...   \n", "2          two    hatchback        rwd          front       94.5  ...   \n", "3         four        sedan        fwd          front       99.8  ...   \n", "4         four        sedan        4wd          front       99.4  ...   \n", "..         ...          ...        ...            ...        ...  ...   \n", "200       four        sedan        rwd          front      109.1  ...   \n", "201       four        sedan        rwd          front      109.1  ...   \n", "202       four        sedan        rwd          front      109.1  ...   \n", "203       four        sedan        rwd          front      109.1  ...   \n", "204       four        sedan        rwd          front      109.1  ...   \n", "\n", "     fuelsystem  boreratio  stroke  compressionratio horsepower peakrpm  \\\n", "0          mpfi       3.47    2.68               9.0        111    5000   \n", "1          mpfi       3.47    2.68               9.0        111    5000   \n", "2          mpfi       2.68    3.47               9.0        154    5000   \n", "3          mpfi       3.19    3.40              10.0        102    5500   \n", "4          mpfi       3.19    3.40               8.0        115    5500   \n", "..          ...        ...     ...               ...        ...     ...   \n", "200        mpfi       3.78    3.15               9.5        114    5400   \n", "201        mpfi       3.78    3.15               8.7        160    5300   \n", "202        mpfi       3.58    2.87               8.8        134    5500   \n", "203         idi       3.01    3.40              23.0        106    4800   \n", "204        mpfi       3.78    3.15               9.5        114    5400   \n", "\n", "     citympg highwaympg    price  聚类结果  \n", "0         21         27  13495.0     1  \n", "1         21         27  16500.0     1  \n", "2         19         26  16500.0     1  \n", "3         24         30  13950.0     2  \n", "4         18         22  17450.0     2  \n", "..       ...        ...      ...   ...  \n", "200       23         28  16845.0     0  \n", "201       19         25  19045.0     0  \n", "202       18         23  21485.0     0  \n", "203       26         27  22470.0     5  \n", "204       19         25  22625.0     0  \n", "\n", "[205 rows x 27 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 合并聚类结果，插入到原数据中,axis： 需要合并链接的轴，0是行，1是列 \n", "result = pd.concat((data,pd.DataFrame(predict_y)),axis=1)\n", "# 将结果列重命名为'聚类结果'\n", "result.rename({0:u'聚类结果'},axis=1,inplace=True)\n", "result"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>聚类结果</th>\n", "      <th>carbody</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">0</th>\n", "      <th>sedan</th>\n", "      <td>22709.739130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>wagon</th>\n", "      <td>15627.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">1</th>\n", "      <th>convertible</th>\n", "      <td>21890.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>hardtop</th>\n", "      <td>23540.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>hatchback</th>\n", "      <td>14686.394737</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sedan</th>\n", "      <td>23210.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">2</th>\n", "      <th>hatchback</th>\n", "      <td>10618.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sedan</th>\n", "      <td>12443.444444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>wagon</th>\n", "      <td>12799.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">3</th>\n", "      <th>hardtop</th>\n", "      <td>8249.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>hatchback</th>\n", "      <td>6789.857143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sedan</th>\n", "      <td>7820.214286</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">4</th>\n", "      <th>hatchback</th>\n", "      <td>7788.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sedan</th>\n", "      <td>9914.428571</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">5</th>\n", "      <th>hardtop</th>\n", "      <td>28176.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sedan</th>\n", "      <td>19026.875000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>wagon</th>\n", "      <td>19727.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">6</th>\n", "      <th>hatchback</th>\n", "      <td>13345.243615</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sedan</th>\n", "      <td>8918.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">7</th>\n", "      <th>hatchback</th>\n", "      <td>7813.714286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sedan</th>\n", "      <td>7891.460000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>wagon</th>\n", "      <td>7955.500000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         price\n", "聚类结果 carbody                  \n", "0    sedan        22709.739130\n", "     wagon        15627.500000\n", "1    convertible  21890.500000\n", "     hardtop      23540.500000\n", "     hatchback    14686.394737\n", "     sedan        23210.500000\n", "2    hatchback    10618.000000\n", "     sedan        12443.444444\n", "     wagon        12799.333333\n", "3    hardtop       8249.000000\n", "     hatchback     6789.857143\n", "     sedan         7820.214286\n", "4    hatchback     7788.000000\n", "     sedan         9914.428571\n", "5    hardtop      28176.000000\n", "     sedan        19026.875000\n", "     wagon        19727.666667\n", "6    hatchback    13345.243615\n", "     sedan         8918.500000\n", "7    hatchback     7813.714286\n", "     sedan         7891.460000\n", "     wagon         7955.500000"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 分组运算\n", "g1 = result.groupby(by = ['聚类结果','carbody'])[['price']].mean()\n", "g1"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"5\" halign=\"left\">price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>carbody</th>\n", "      <th>convertible</th>\n", "      <th>hardtop</th>\n", "      <th>hatchback</th>\n", "      <th>sedan</th>\n", "      <th>wagon</th>\n", "    </tr>\n", "    <tr>\n", "      <th>聚类结果</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>22709.739130</td>\n", "      <td>15627.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21890.5</td>\n", "      <td>23540.5</td>\n", "      <td>14686.394737</td>\n", "      <td>23210.500000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>10618.000000</td>\n", "      <td>12443.444444</td>\n", "      <td>12799.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NaN</td>\n", "      <td>8249.0</td>\n", "      <td>6789.857143</td>\n", "      <td>7820.214286</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>7788.000000</td>\n", "      <td>9914.428571</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>NaN</td>\n", "      <td>28176.0</td>\n", "      <td>NaN</td>\n", "      <td>19026.875000</td>\n", "      <td>19727.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>13345.243615</td>\n", "      <td>8918.500000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>7813.714286</td>\n", "      <td>7891.460000</td>\n", "      <td>7955.500000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              price                                                   \n", "carbody convertible  hardtop     hatchback         sedan         wagon\n", "聚类结果                                                                  \n", "0               NaN      NaN           NaN  22709.739130  15627.500000\n", "1           21890.5  23540.5  14686.394737  23210.500000           NaN\n", "2               NaN      NaN  10618.000000  12443.444444  12799.333333\n", "3               NaN   8249.0   6789.857143   7820.214286           NaN\n", "4               NaN      NaN   7788.000000   9914.428571           NaN\n", "5               NaN  28176.0           NaN  19026.875000  19727.666667\n", "6               NaN      NaN  13345.243615   8918.500000           NaN\n", "7               NaN      NaN   7813.714286   7891.460000   7955.500000"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["g2 = g1.unstack() # 重塑，行变成了列\n", "g2"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"5\" halign=\"left\">price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>carbody</th>\n", "      <th>convertible</th>\n", "      <th>hardtop</th>\n", "      <th>hatchback</th>\n", "      <th>sedan</th>\n", "      <th>wagon</th>\n", "    </tr>\n", "    <tr>\n", "      <th>聚类结果</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NaN</td>\n", "      <td>8249.0</td>\n", "      <td>6789.857143</td>\n", "      <td>7820.214286</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>7813.714286</td>\n", "      <td>7891.460000</td>\n", "      <td>7955.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>13345.243615</td>\n", "      <td>8918.500000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>7788.000000</td>\n", "      <td>9914.428571</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>10618.000000</td>\n", "      <td>12443.444444</td>\n", "      <td>12799.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>NaN</td>\n", "      <td>28176.0</td>\n", "      <td>NaN</td>\n", "      <td>19026.875000</td>\n", "      <td>19727.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>22709.739130</td>\n", "      <td>15627.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21890.5</td>\n", "      <td>23540.5</td>\n", "      <td>14686.394737</td>\n", "      <td>23210.500000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              price                                                   \n", "carbody convertible  hardtop     hatchback         sedan         wagon\n", "聚类结果                                                                  \n", "3               NaN   8249.0   6789.857143   7820.214286           NaN\n", "7               NaN      NaN   7813.714286   7891.460000   7955.500000\n", "6               NaN      NaN  13345.243615   8918.500000           NaN\n", "4               NaN      NaN   7788.000000   9914.428571           NaN\n", "2               NaN      NaN  10618.000000  12443.444444  12799.333333\n", "5               NaN  28176.0           NaN  19026.875000  19727.666667\n", "0               NaN      NaN           NaN  22709.739130  15627.500000\n", "1           21890.5  23540.5  14686.394737  23210.500000           NaN"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["g2.sort_values(by = ('price','sedan'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 低端轿车聚类结果"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CarName</th>\n", "      <th>wheelbase</th>\n", "      <th>price</th>\n", "      <th>horsepower</th>\n", "      <th>carbody</th>\n", "      <th>fueltype</th>\n", "      <th>聚类结果</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>buick century special</td>\n", "      <td>120.9</td>\n", "      <td>40960.0</td>\n", "      <td>184</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>bmw x3</td>\n", "      <td>110.0</td>\n", "      <td>36880.0</td>\n", "      <td>182</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>jaguar xf</td>\n", "      <td>113.0</td>\n", "      <td>35550.0</td>\n", "      <td>176</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>buick opel isuzu deluxe</td>\n", "      <td>115.6</td>\n", "      <td>34184.0</td>\n", "      <td>155</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>jaguar xj</td>\n", "      <td>113.0</td>\n", "      <td>32250.0</td>\n", "      <td>176</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>bmw x4</td>\n", "      <td>103.5</td>\n", "      <td>30760.0</td>\n", "      <td>182</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>bmw z4</td>\n", "      <td>103.5</td>\n", "      <td>24565.0</td>\n", "      <td>121</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>audi 4000</td>\n", "      <td>105.8</td>\n", "      <td>23875.0</td>\n", "      <td>140</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>volvo 264gl</td>\n", "      <td>109.1</td>\n", "      <td>22625.0</td>\n", "      <td>114</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>volvo 244dl</td>\n", "      <td>109.1</td>\n", "      <td>21485.0</td>\n", "      <td>134</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>bmw x3</td>\n", "      <td>101.2</td>\n", "      <td>21105.0</td>\n", "      <td>121</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>volvo 144ea</td>\n", "      <td>109.1</td>\n", "      <td>19045.0</td>\n", "      <td>160</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>saab 99e</td>\n", "      <td>99.1</td>\n", "      <td>18620.0</td>\n", "      <td>160</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>198</th>\n", "      <td>volvo 264gl</td>\n", "      <td>104.3</td>\n", "      <td>18420.0</td>\n", "      <td>162</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>mazda glc</td>\n", "      <td>104.9</td>\n", "      <td>18280.0</td>\n", "      <td>120</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117</th>\n", "      <td>peugeot 604sl</td>\n", "      <td>108.0</td>\n", "      <td>18150.0</td>\n", "      <td>142</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>volvo 145e (sw)</td>\n", "      <td>109.1</td>\n", "      <td>16845.0</td>\n", "      <td>114</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>peugeot 504</td>\n", "      <td>107.9</td>\n", "      <td>16630.0</td>\n", "      <td>97</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>volvo 244dl</td>\n", "      <td>104.3</td>\n", "      <td>15985.0</td>\n", "      <td>114</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>toyota starlet</td>\n", "      <td>104.5</td>\n", "      <td>15690.0</td>\n", "      <td>156</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>peugeot 504</td>\n", "      <td>107.9</td>\n", "      <td>15580.0</td>\n", "      <td>95</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>volvo 145e (sw)</td>\n", "      <td>104.3</td>\n", "      <td>12940.0</td>\n", "      <td>114</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>peugeot 504</td>\n", "      <td>107.9</td>\n", "      <td>11900.0</td>\n", "      <td>97</td>\n", "      <td>sedan</td>\n", "      <td>gas</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     CarName  wheelbase    price  horsepower carbody fueltype  \\\n", "73     buick century special      120.9  40960.0         184   sedan      gas   \n", "17                    bmw x3      110.0  36880.0         182   sedan      gas   \n", "48                 jaguar xf      113.0  35550.0         176   sedan      gas   \n", "71   buick opel isuzu deluxe      115.6  34184.0         155   sedan      gas   \n", "47                 jaguar xj      113.0  32250.0         176   sedan      gas   \n", "15                    bmw x4      103.5  30760.0         182   sedan      gas   \n", "14                    bmw z4      103.5  24565.0         121   sedan      gas   \n", "8                  audi 4000      105.8  23875.0         140   sedan      gas   \n", "204              volvo 264gl      109.1  22625.0         114   sedan      gas   \n", "202              volvo 244dl      109.1  21485.0         134   sedan      gas   \n", "13                    bmw x3      101.2  21105.0         121   sedan      gas   \n", "201              volvo 144ea      109.1  19045.0         160   sedan      gas   \n", "137                 saab 99e       99.1  18620.0         160   sedan      gas   \n", "198              volvo 264gl      104.3  18420.0         162   sedan      gas   \n", "65                 mazda glc      104.9  18280.0         120   sedan      gas   \n", "117            peugeot 604sl      108.0  18150.0         142   sedan      gas   \n", "200          volvo 145e (sw)      109.1  16845.0         114   sedan      gas   \n", "115              peugeot 504      107.9  16630.0          97   sedan      gas   \n", "196              volvo 244dl      104.3  15985.0         114   sedan      gas   \n", "180           toyota starlet      104.5  15690.0         156   sedan      gas   \n", "111              peugeot 504      107.9  15580.0          95   sedan      gas   \n", "194          volvo 145e (sw)      104.3  12940.0         114   sedan      gas   \n", "107              peugeot 504      107.9  11900.0          97   sedan      gas   \n", "\n", "     聚类结果  \n", "73      0  \n", "17      0  \n", "48      0  \n", "71      0  \n", "47      0  \n", "15      0  \n", "14      0  \n", "8       0  \n", "204     0  \n", "202     0  \n", "13      0  \n", "201     0  \n", "137     0  \n", "198     0  \n", "65      0  \n", "117     0  \n", "200     0  \n", "115     0  \n", "196     0  \n", "180     0  \n", "111     0  \n", "194     0  \n", "107     0  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看：类别是1的标准三厢车（具体根据分组运算结果确定）\n", "cond = result.apply(lambda x : x['聚类结果'] == 0 and 'sedan' in x['carbody'],axis = 1)\n", "\n", "columns = ['CarName',\"wheelbase\", \"price\",'horsepower','carbody','fueltype','聚类结果']\n", "# 价格降序排名\n", "result[cond][columns].sort_values('price',ascending = False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 高端轿车聚类结果"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CarName</th>\n", "      <th>wheelbase</th>\n", "      <th>price</th>\n", "      <th>horsepower</th>\n", "      <th>carbody</th>\n", "      <th>fueltype</th>\n", "      <th>聚类结果</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>mazda rx-7 gs</td>\n", "      <td>104.9</td>\n", "      <td>18344.0</td>\n", "      <td>72</td>\n", "      <td>sedan</td>\n", "      <td>diesel</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>mazda glc deluxe</td>\n", "      <td>98.8</td>\n", "      <td>10795.0</td>\n", "      <td>64</td>\n", "      <td>sedan</td>\n", "      <td>diesel</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>volkswagen super beetle</td>\n", "      <td>97.3</td>\n", "      <td>9495.0</td>\n", "      <td>68</td>\n", "      <td>sedan</td>\n", "      <td>diesel</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>184</th>\n", "      <td>volkswagen model 111</td>\n", "      <td>97.3</td>\n", "      <td>7995.0</td>\n", "      <td>52</td>\n", "      <td>sedan</td>\n", "      <td>diesel</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>toyota corona</td>\n", "      <td>95.7</td>\n", "      <td>7898.0</td>\n", "      <td>56</td>\n", "      <td>sedan</td>\n", "      <td>diesel</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>vokswagen rabbit</td>\n", "      <td>97.3</td>\n", "      <td>7775.0</td>\n", "      <td>52</td>\n", "      <td>sedan</td>\n", "      <td>diesel</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>nissan gt-r</td>\n", "      <td>94.5</td>\n", "      <td>7099.0</td>\n", "      <td>55</td>\n", "      <td>sedan</td>\n", "      <td>diesel</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     CarName  wheelbase    price  horsepower carbody fueltype  \\\n", "66             mazda rx-7 gs      104.9  18344.0          72   sedan   diesel   \n", "63          mazda glc deluxe       98.8  10795.0          64   sedan   diesel   \n", "187  volkswagen super beetle       97.3   9495.0          68   sedan   diesel   \n", "184     volkswagen model 111       97.3   7995.0          52   sedan   diesel   \n", "158            toyota corona       95.7   7898.0          56   sedan   diesel   \n", "182         vokswagen rabbit       97.3   7775.0          52   sedan   diesel   \n", "90               nissan gt-r       94.5   7099.0          55   sedan   diesel   \n", "\n", "     聚类结果  \n", "66      4  \n", "63      4  \n", "187     4  \n", "184     4  \n", "158     4  \n", "182     4  \n", "90      4  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 根据条件（售价）筛选高端轿车（三厢车）\n", "cond = result.apply(lambda x : x['聚类结果'] == 4  and 'sedan' in x['carbody'],axis =1)\n", "\n", "columns = ['CarName',\"wheelbase\", \"price\",'horsepower','carbody','fueltype','聚类结果']\n", "result[cond][columns].sort_values('price',ascending = False)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["0      False\n", "1      False\n", "2      False\n", "3      False\n", "4      False\n", "       ...  \n", "200    False\n", "201    False\n", "202    False\n", "203    False\n", "204    False\n", "Length: 205, dtype: bool"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["cond"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 中端SUV聚类结果"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CarName</th>\n", "      <th>wheelbase</th>\n", "      <th>price</th>\n", "      <th>horsepower</th>\n", "      <th>carbody</th>\n", "      <th>fueltype</th>\n", "      <th>聚类结果</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [CarName, wheelbase, price, horsepower, carbody, fueltype, 聚类结果]\n", "Index: []"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 根据条件（售价）筛选中端SUV轿车\n", "cond = result.apply(lambda x : x['聚类结果'] == 1 and 'wagon' in x['carbody'],axis =1)\n", "columns = ['CarName',\"wheelbase\", \"price\",'horsepower','carbody','fueltype','聚类结果']\n", "result[cond][columns].sort_values('price',ascending = False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "273.167px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 2}
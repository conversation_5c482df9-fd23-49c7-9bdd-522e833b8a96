<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>大屏展示</title>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/maps/shanghai.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/jquery.min.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/jquery-ui.min.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/ResizeSensor.js"></script>

            <link rel="stylesheet"  href="https://assets.pyecharts.org/assets/v5/jquery-ui.css">

</head>
<body >
    <style>.box {  } </style>
        
    <div class="box">
                <div id="********************************" class="chart-container" style="position: absolute; width: 900px; height: 500px; top: 31px; left: 8px;"></div>
    <script>
        var chart_******************************** = echarts.init(
            document.getElementById('********************************'), 'white', {renderer: 'canvas'});
        var option_******************************** = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "effectScatter",
            "coordinateSystem": "geo",
            "showEffectOn": "render",
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            },
            "symbolSize": 9,
            "data": [
                {
                    "name": "\u4f18\u8863\u5e93\u4e03\u5b9d\u51ef\u5fb7\u5e97",
                    "value": [
                        121.348318,
                        31.17132,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u4e0a\u6d77\u957f\u6cf0\u5e7f\u573a\u5e97",
                    "value": [
                        121.607512,
                        31.210366,
                        3
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u4e94\u89d2\u573a\u53c8\u4e00\u57ce\u5e97",
                    "value": [
                        121.521831,
                        31.307534000000004,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u4e94\u89d2\u573a\u5e97",
                    "value": [
                        121.52031399999998,
                        31.306632,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5149\u542f\u57ce\u5e97",
                    "value": [
                        121.434446,
                        31.190694,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5357\u4eac\u4e1c\u8def\u4e2d\u8054\u5e97",
                    "value": [
                        121.489976,
                        31.242919,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5357\u4eac\u4e1c\u8def\u7b2c\u4e00\u767e\u8d27\u5e97",
                    "value": [
                        121.481317,
                        31.240769,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5357\u4eac\u897f\u8def\u5e97",
                    "value": [
                        121.465285,
                        31.235748,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5858\u6865\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.526688,
                        31.214227,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5927\u5b81\u56fd\u9645\u5e97",
                    "value": [
                        121.459213,
                        31.281012,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5927\u60a6\u57ce\u5e97",
                    "value": [
                        121.478725,
                        31.248893,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5927\u62c7\u6307\u5e7f\u573a\u5e97",
                    "value": [
                        121.566506,
                        31.233415,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u592a\u5e73\u6d0b\u4e0d\u591c\u57ce\u5e97",
                    "value": [
                        121.46328,
                        31.251464,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5b89\u4ead\u5609\u4ead\u835f\u5e97",
                    "value": [
                        121.16916100000002,
                        31.293966,
                        3
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5b9d\u5c71\u4e07\u8fbe\u5e7f\u573a\u5e97",
                    "value": [
                        121.452854,
                        31.330385,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5b9d\u5c71\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.417753,
                        31.276786,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5f00\u5143\u5730\u4e2d\u6d77\u5e97",
                    "value": [
                        121.225779,
                        31.043855,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6210\u5c71\u8def\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.542118,
                        31.184962,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6211\u683c\u5e7f\u573a\u5e97",
                    "value": [
                        121.429781,
                        31.244328000000003,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u65b0\u6885\u8054\u5408\u5e7f\u573a\u5e97",
                    "value": [
                        121.52288,
                        31.235271,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u65e5\u6708\u5149\u5e97",
                    "value": [
                        121.47510800000002,
                        31.21155,
                        4
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u66f9\u5b89\u516c\u8def\u5e97",
                    "value": [
                        121.370842,
                        31.259728000000003,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6b63\u5927\u4e50\u57ce\u5e97",
                    "value": [
                        121.464675,
                        31.192402,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6b63\u5927\u5e7f\u573a\u5e97",
                    "value": [
                        121.505676,
                        31.242789,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6c38\u65b0\u57ce\u5e97",
                    "value": [
                        121.44902,
                        31.198699,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6c5f\u6865\u4e07\u8fbe\u5e97",
                    "value": [
                        121.33073,
                        31.247020000000003,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6d66\u4e1c\u4e2d\u623f\u91d1\u8c0a\u5e7f\u573a",
                    "value": [
                        121.519196,
                        31.147252,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6d66\u4e1c\u5546\u573a\u6210\u5c71\u8def\u5e97",
                    "value": [
                        121.513909,
                        31.177723,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6dee\u6d77\u8def\u592a\u5e73\u6d0b\u5e97",
                    "value": [
                        121.467549,
                        31.223576,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6e2f\u6c47\u5e7f\u573a\u5e97",
                    "value": [
                        121.44325,
                        31.201186,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u767e\u8054\u4e2d\u73af\u5e97",
                    "value": [
                        121.389893,
                        31.250845,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u767e\u8054\u5357\u6865\u5e97",
                    "value": [
                        121.490514,
                        30.92182,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u8398\u5e84\u4ef2\u76db\u5e97",
                    "value": [
                        121.39373700000002,
                        31.112857,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u83b2\u82b1\u56fd\u9645\u5e7f\u573a\u5e97",
                    "value": [
                        121.409081,
                        31.139156,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u897f\u90ca\u767e\u8054\u5e97",
                    "value": [
                        121.377053,
                        31.214539,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u91d1\u5c71\u767e\u8054\u5e97",
                    "value": [
                        121.354894,
                        30.737391,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u91d1\u6865\u5e97",
                    "value": [
                        121.585552,
                        31.261473,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u95f5\u884c\u6d66\u6c5f\u9547\u5e97",
                    "value": [
                        121.510835,
                        31.100538,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u9655\u897f\u8def\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.447881,
                        31.24915,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u9f99\u4e4b\u68a6\u5e97",
                    "value": [
                        121.484808,
                        31.276663,
                        1
                    ]
                }
            ],
            "label": {
                "show": false,
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {
                "": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": function (params) {        return params.name + ' : ' + params.value[2];    },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u4e0a\u6d77\u5e02\u4f18\u8863\u5e93\u95e8\u5e97\u53ef\u89c6\u5316",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "visualMap": {
        "show": true,
        "type": "piecewise",
        "min": 0,
        "max": 100,
        "inRange": {
            "color": [
                "#50a3ba",
                "#eac763",
                "#d94e5d"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "hoverLink": true,
        "orient": "vertical",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 14,
        "borderWidth": 0,
        "pieces": [
            {
                "min": 0,
                "max": 1,
                "label": "1",
                "color": "#50A3BA"
            },
            {
                "min": 1,
                "max": 2,
                "label": "2",
                "color": "#DD675E"
            },
            {
                "min": 2,
                "max": 3,
                "label": "3",
                "color": "#E2C568"
            },
            {
                "min": 3,
                "label": "4",
                "color": "#3700A4"
            }
        ]
    },
    "geo": {
        "map": "\u4e0a\u6d77",
        "roam": true,
        "aspectScale": 0.75,
        "nameProperty": "name",
        "selectedMode": false,
        "emphasis": {}
    }
};
        chart_********************************.setOption(option_********************************);
    </script>
<br/>                <div id="8c01bd8603524aa5a0ad6df2339f25da" class="chart-container" style="position: absolute; width: 900px; height: 500px; top: 33px; left: 919px;"></div>
    <script>
        var chart_8c01bd8603524aa5a0ad6df2339f25da = echarts.init(
            document.getElementById('8c01bd8603524aa5a0ad6df2339f25da'), 'white', {renderer: 'canvas'});
        var option_8c01bd8603524aa5a0ad6df2339f25da = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "heatmap",
            "coordinateSystem": "geo",
            "data": [
                {
                    "name": "\u4f18\u8863\u5e93\u4e03\u5b9d\u51ef\u5fb7\u5e97",
                    "value": [
                        121.348318,
                        31.17132,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u4e0a\u6d77\u957f\u6cf0\u5e7f\u573a\u5e97",
                    "value": [
                        121.607512,
                        31.210366,
                        3
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u4e94\u89d2\u573a\u53c8\u4e00\u57ce\u5e97",
                    "value": [
                        121.521831,
                        31.307534000000004,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u4e94\u89d2\u573a\u5e97",
                    "value": [
                        121.52031399999998,
                        31.306632,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5149\u542f\u57ce\u5e97",
                    "value": [
                        121.434446,
                        31.190694,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5357\u4eac\u4e1c\u8def\u4e2d\u8054\u5e97",
                    "value": [
                        121.489976,
                        31.242919,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5357\u4eac\u4e1c\u8def\u7b2c\u4e00\u767e\u8d27\u5e97",
                    "value": [
                        121.481317,
                        31.240769,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5357\u4eac\u897f\u8def\u5e97",
                    "value": [
                        121.465285,
                        31.235748,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5858\u6865\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.526688,
                        31.214227,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5927\u5b81\u56fd\u9645\u5e97",
                    "value": [
                        121.459213,
                        31.281012,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5927\u60a6\u57ce\u5e97",
                    "value": [
                        121.478725,
                        31.248893,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5927\u62c7\u6307\u5e7f\u573a\u5e97",
                    "value": [
                        121.566506,
                        31.233415,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u592a\u5e73\u6d0b\u4e0d\u591c\u57ce\u5e97",
                    "value": [
                        121.46328,
                        31.251464,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5b89\u4ead\u5609\u4ead\u835f\u5e97",
                    "value": [
                        121.16916100000002,
                        31.293966,
                        3
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5b9d\u5c71\u4e07\u8fbe\u5e7f\u573a\u5e97",
                    "value": [
                        121.452854,
                        31.330385,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5b9d\u5c71\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.417753,
                        31.276786,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5f00\u5143\u5730\u4e2d\u6d77\u5e97",
                    "value": [
                        121.225779,
                        31.043855,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6210\u5c71\u8def\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.542118,
                        31.184962,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6211\u683c\u5e7f\u573a\u5e97",
                    "value": [
                        121.429781,
                        31.244328000000003,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u65b0\u6885\u8054\u5408\u5e7f\u573a\u5e97",
                    "value": [
                        121.52288,
                        31.235271,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u65e5\u6708\u5149\u5e97",
                    "value": [
                        121.47510800000002,
                        31.21155,
                        4
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u66f9\u5b89\u516c\u8def\u5e97",
                    "value": [
                        121.370842,
                        31.259728000000003,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6b63\u5927\u4e50\u57ce\u5e97",
                    "value": [
                        121.464675,
                        31.192402,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6b63\u5927\u5e7f\u573a\u5e97",
                    "value": [
                        121.505676,
                        31.242789,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6c38\u65b0\u57ce\u5e97",
                    "value": [
                        121.44902,
                        31.198699,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6c5f\u6865\u4e07\u8fbe\u5e97",
                    "value": [
                        121.33073,
                        31.247020000000003,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6d66\u4e1c\u4e2d\u623f\u91d1\u8c0a\u5e7f\u573a",
                    "value": [
                        121.519196,
                        31.147252,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6d66\u4e1c\u5546\u573a\u6210\u5c71\u8def\u5e97",
                    "value": [
                        121.513909,
                        31.177723,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6dee\u6d77\u8def\u592a\u5e73\u6d0b\u5e97",
                    "value": [
                        121.467549,
                        31.223576,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6e2f\u6c47\u5e7f\u573a\u5e97",
                    "value": [
                        121.44325,
                        31.201186,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u767e\u8054\u4e2d\u73af\u5e97",
                    "value": [
                        121.389893,
                        31.250845,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u767e\u8054\u5357\u6865\u5e97",
                    "value": [
                        121.490514,
                        30.92182,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u8398\u5e84\u4ef2\u76db\u5e97",
                    "value": [
                        121.39373700000002,
                        31.112857,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u83b2\u82b1\u56fd\u9645\u5e7f\u573a\u5e97",
                    "value": [
                        121.409081,
                        31.139156,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u897f\u90ca\u767e\u8054\u5e97",
                    "value": [
                        121.377053,
                        31.214539,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u91d1\u5c71\u767e\u8054\u5e97",
                    "value": [
                        121.354894,
                        30.737391,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u91d1\u6865\u5e97",
                    "value": [
                        121.585552,
                        31.261473,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u95f5\u884c\u6d66\u6c5f\u9547\u5e97",
                    "value": [
                        121.510835,
                        31.100538,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u9655\u897f\u8def\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.447881,
                        31.24915,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u9f99\u4e4b\u68a6\u5e97",
                    "value": [
                        121.484808,
                        31.276663,
                        1
                    ]
                }
            ],
            "pointSize": 20,
            "blurSize": 20,
            "label": {
                "show": false,
                "margin": 8
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {
                "": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": function (params) {        return params.name + ' : ' + params.value[2];    },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u4e0a\u6d77\u5e02\u4f18\u8863\u5e93\u95e8\u5e97\u53ef\u89c6\u5316",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "visualMap": {
        "show": true,
        "type": "piecewise",
        "min": 0,
        "max": 100,
        "inRange": {
            "color": [
                "#50a3ba",
                "#eac763",
                "#d94e5d"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "hoverLink": true,
        "orient": "vertical",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 14,
        "borderWidth": 0,
        "pieces": [
            {
                "min": 0,
                "max": 1,
                "label": "1",
                "color": "#50A3BA"
            },
            {
                "min": 1,
                "max": 2,
                "label": "2",
                "color": "#E2C568"
            },
            {
                "min": 2,
                "max": 3,
                "label": "3",
                "color": "#DD675E"
            },
            {
                "min": 3,
                "label": "4",
                "color": "#DD0200"
            }
        ]
    },
    "geo": {
        "map": "\u4e0a\u6d77",
        "roam": true,
        "aspectScale": 0.75,
        "nameProperty": "name",
        "selectedMode": false,
        "emphasis": {}
    }
};
        chart_8c01bd8603524aa5a0ad6df2339f25da.setOption(option_8c01bd8603524aa5a0ad6df2339f25da);
    </script>
<br/>                <div id="5449904cfd1e42fc9ebf829e27828592" class="chart-container" style="position: absolute; width: 900px; height: 500px; top: 557px; left: 9px;"></div>
    <script>
        var chart_5449904cfd1e42fc9ebf829e27828592 = echarts.init(
            document.getElementById('5449904cfd1e42fc9ebf829e27828592'), 'white', {renderer: 'canvas'});
        var option_5449904cfd1e42fc9ebf829e27828592 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "effectScatter",
            "coordinateSystem": "geo",
            "showEffectOn": "render",
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            },
            "symbolSize": 9,
            "data": [
                {
                    "name": "\u4f18\u8863\u5e93\u5357\u4eac\u897f\u8def\u5e97",
                    "value": [
                        121.465285,
                        31.235748,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6d66\u4e1c\u5546\u573a\u6210\u5c71\u8def\u5e97",
                    "value": [
                        121.513909,
                        31.177723,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5927\u62c7\u6307\u5e7f\u573a\u5e97",
                    "value": [
                        121.566506,
                        31.233415,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5b9d\u5c71\u4e07\u8fbe\u5e7f\u573a\u5e97",
                    "value": [
                        121.452854,
                        31.330385,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5149\u542f\u57ce\u5e97",
                    "value": [
                        121.434446,
                        31.190694,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u897f\u90ca\u767e\u8054\u5e97",
                    "value": [
                        121.377053,
                        31.214539,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u65e5\u6708\u5149\u5e97",
                    "value": [
                        121.475108,
                        31.21155,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u66f9\u5b89\u516c\u8def\u5e97",
                    "value": [
                        121.370842,
                        31.259728000000003,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5b89\u4ead\u5609\u4ead\u835f\u5e97",
                    "value": [
                        121.16916100000002,
                        31.293966,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u592a\u5e73\u6d0b\u4e0d\u591c\u57ce\u5e97",
                    "value": [
                        121.46328,
                        31.251464,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6d66\u4e1c\u4e2d\u623f\u91d1\u8c0a\u5e7f\u573a",
                    "value": [
                        121.519196,
                        31.147252,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5357\u4eac\u4e1c\u8def\u7b2c\u4e00\u767e\u8d27\u5e97",
                    "value": [
                        121.481317,
                        31.240769,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6dee\u6d77\u8def\u592a\u5e73\u6d0b\u5e97",
                    "value": [
                        121.467549,
                        31.223576,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u767e\u8054\u5357\u6865\u5e97",
                    "value": [
                        121.490514,
                        30.92182,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5f00\u5143\u5730\u4e2d\u6d77\u5e97",
                    "value": [
                        121.225779,
                        31.043855,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u91d1\u5c71\u767e\u8054\u5e97",
                    "value": [
                        121.354894,
                        30.737391,
                        2
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6211\u683c\u5e7f\u573a\u5e97",
                    "value": [
                        121.429781,
                        31.244328000000003,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6c5f\u6865\u4e07\u8fbe\u5e97",
                    "value": [
                        121.33073,
                        31.247020000000003,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5927\u60a6\u57ce\u5e97",
                    "value": [
                        121.478725,
                        31.248893,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6210\u5c71\u8def\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.542118,
                        31.184962,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5b9d\u5c71\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.417753,
                        31.276786,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u83b2\u82b1\u56fd\u9645\u5e7f\u573a\u5e97",
                    "value": [
                        121.409081,
                        31.139156,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u4e03\u5b9d\u51ef\u5fb7\u5e97",
                    "value": [
                        121.348318,
                        31.17132,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u91d1\u6865\u5e97",
                    "value": [
                        121.585552,
                        31.261473,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5858\u6865\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.526688,
                        31.214227,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6c38\u65b0\u57ce\u5e97",
                    "value": [
                        121.44902,
                        31.198699,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u767e\u8054\u4e2d\u73af\u5e97",
                    "value": [
                        121.389893,
                        31.250845,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u9f99\u4e4b\u68a6\u5e97",
                    "value": [
                        121.484808,
                        31.276663,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u9655\u897f\u8def\u5df4\u9ece\u6625\u5929\u5e97",
                    "value": [
                        121.447881,
                        31.24915,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5927\u5b81\u56fd\u9645\u5e97",
                    "value": [
                        121.459213,
                        31.281012,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u65b0\u6885\u8054\u5408\u5e7f\u573a\u5e97",
                    "value": [
                        121.52288,
                        31.235271,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u4e94\u89d2\u573a\u5e97",
                    "value": [
                        121.52031399999998,
                        31.306632,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6b63\u5927\u5e7f\u573a\u5e97",
                    "value": [
                        121.505676,
                        31.242789,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6e2f\u6c47\u5e7f\u573a\u5e97",
                    "value": [
                        121.44325,
                        31.201186,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u5357\u4eac\u4e1c\u8def\u4e2d\u8054\u5e97",
                    "value": [
                        121.489976,
                        31.242919,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u8398\u5e84\u4ef2\u76db\u5e97",
                    "value": [
                        121.39373700000002,
                        31.112857,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u4e94\u89d2\u573a\u53c8\u4e00\u57ce\u5e97",
                    "value": [
                        121.521831,
                        31.307534000000004,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u4e0a\u6d77\u957f\u6cf0\u5e7f\u573a\u5e97",
                    "value": [
                        121.607512,
                        31.210366,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u6b63\u5927\u4e50\u57ce\u5e97",
                    "value": [
                        121.464675,
                        31.192402,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u95f5\u884c\u6d66\u6c5f\u9547\u5e97",
                    "value": [
                        121.510835,
                        31.100538,
                        0
                    ]
                },
                {
                    "name": "\u5c71\u4e1c\u4e2d\u8def145\u53f7",
                    "value": [
                        121.491114,
                        31.239373,
                        0
                    ]
                },
                {
                    "name": "\u4e1c\u65b9\u8def1630\u53f7",
                    "value": [
                        121.531632,
                        31.213081,
                        0
                    ]
                },
                {
                    "name": "\u5236\u9020\u5c40\u8def639\u53f7",
                    "value": [
                        121.492472,
                        31.208031,
                        0
                    ]
                },
                {
                    "name": "\u745e\u91d1\u4e8c\u8def197\u53f7",
                    "value": [
                        121.472605,
                        31.217703000000004,
                        0
                    ]
                },
                {
                    "name": "\u63a7\u6c5f\u8def1665\u53f7",
                    "value": [
                        121.523675,
                        31.280067,
                        0
                    ]
                },
                {
                    "name": "\u4e1c\u65b9\u8def1678\u53f7",
                    "value": [
                        121.530729,
                        31.211445,
                        0
                    ]
                },
                {
                    "name": "\u6dee\u6d77\u897f\u8def241\u53f7",
                    "value": [
                        121.43348600000002,
                        31.204657,
                        0
                    ]
                },
                {
                    "name": "\u6b66\u8fdb\u8def85\u53f7",
                    "value": [
                        121.49498,
                        31.259005,
                        0
                    ]
                },
                {
                    "name": "\u5b9c\u5c71\u8def600\u53f7",
                    "value": [
                        121.430603,
                        31.184901,
                        0
                    ]
                },
                {
                    "name": "\u533b\u5b66\u9662\u8def138\u53f7",
                    "value": [
                        121.458553,
                        31.204393,
                        0
                    ]
                },
                {
                    "name": "\u4e4c\u9c81\u6728\u9f50\u4e2d\u8def12\u53f7",
                    "value": [
                        121.45042,
                        31.222696000000003,
                        0
                    ]
                },
                {
                    "name": "\u67ab\u6797\u8def138\u53f7",
                    "value": [
                        121.461124,
                        31.20416,
                        0
                    ]
                },
                {
                    "name": "\u6c7e\u9633\u8def83\u53f7",
                    "value": [
                        121.459801,
                        31.216084,
                        0
                    ]
                },
                {
                    "name": "\u65b9\u659c\u8def419\u53f7",
                    "value": [
                        121.489711,
                        31.220091,
                        0
                    ]
                },
                {
                    "name": "\u96f6\u9675\u8def399\u53f7",
                    "value": [
                        121.459427,
                        31.196451,
                        0
                    ]
                },
                {
                    "name": "\u5ef6\u957f\u4e2d\u8def301\u53f7",
                    "value": [
                        121.460247,
                        31.277936,
                        0
                    ]
                },
                {
                    "name": "\u65b0\u6751\u8def389\u53f7",
                    "value": [
                        121.200217,
                        30.797994,
                        2
                    ]
                },
                {
                    "name": "\u653f\u6c11\u8def507\u53f7",
                    "value": [
                        121.50893899999998,
                        31.306621000000003,
                        0
                    ]
                },
                {
                    "name": "\u51e4\u9633\u8def415\u53f7",
                    "value": [
                        121.47413799999998,
                        31.23909,
                        0
                    ]
                },
                {
                    "name": "\u957f\u6d77\u8def174\u53f7",
                    "value": [
                        121.53438,
                        31.314777000000003,
                        0
                    ]
                },
                {
                    "name": "\u957f\u6d77\u8def225\u53f7",
                    "value": [
                        121.535568,
                        31.315785,
                        0
                    ]
                },
                {
                    "name": "\u7518\u6cb3\u8def110\u53f7",
                    "value": [
                        121.494791,
                        31.294773,
                        0
                    ]
                },
                {
                    "name": "\u9752\u6d77\u8def44\u53f7",
                    "value": [
                        121.47057,
                        31.236615000000004,
                        0
                    ]
                },
                {
                    "name": "\u5b9b\u5e73\u5357\u8def725\u53f7",
                    "value": [
                        121.456107,
                        31.193764,
                        0
                    ]
                },
                {
                    "name": "\u666e\u5b89\u8def185\u53f7",
                    "value": [
                        121.484847,
                        31.228823,
                        0
                    ]
                },
                {
                    "name": "\u82b7\u6c5f\u4e2d\u8def274\u53f7",
                    "value": [
                        121.477667,
                        31.265602,
                        0
                    ]
                },
                {
                    "name": "\u5ef6\u5b89\u897f\u8def221\u53f7",
                    "value": [
                        121.448122,
                        31.225464,
                        0
                    ]
                },
                {
                    "name": "\u957f\u4e50\u8def536\u53f7",
                    "value": [
                        121.46268700000002,
                        31.226189,
                        0
                    ]
                },
                {
                    "name": "\u6c34\u7535\u8def56\u53f7",
                    "value": [
                        121.477515,
                        31.279301,
                        0
                    ]
                },
                {
                    "name": "\u5317\u4eac\u897f\u8def1400\u5f0424\u53f7",
                    "value": [
                        121.454787,
                        31.235549,
                        0
                    ]
                },
                {
                    "name": "\u6b66\u5937\u8def196\u53f7",
                    "value": [
                        121.43331,
                        31.219685,
                        0
                    ]
                },
                {
                    "name": "\u5eb7\u5b9a\u8def380\u53f73\u697c",
                    "value": [
                        121.455435,
                        31.240979,
                        0
                    ]
                },
                {
                    "name": "\u8861\u5c71\u8def910\u53f7",
                    "value": [
                        121.445945,
                        31.201999,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u65e5\u6708\u5149\u5e97",
                    "value": [
                        121.475108,
                        31.21155,
                        0
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u4e03\u5b9d\u51ef\u5fb7\u5e97",
                    "value": [
                        121.348318,
                        31.17132,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u8398\u5e84\u4ef2\u76db\u5e97",
                    "value": [
                        121.39373700000002,
                        31.112857,
                        1
                    ]
                },
                {
                    "name": "\u4f18\u8863\u5e93\u65e5\u6708\u5149\u5e97",
                    "value": [
                        121.475108,
                        31.21155,
                        0
                    ]
                },
                {
                    "name": "\u653f\u6c11\u8def507\u53f7",
                    "value": [
                        121.50893899999998,
                        31.306621000000003,
                        0
                    ]
                },
                {
                    "name": "\u5eb7\u5b9a\u8def380\u53f73\u697c",
                    "value": [
                        121.455435,
                        31.240979,
                        0
                    ]
                },
                {
                    "name": "\u666e\u5b89\u8def185\u53f7",
                    "value": [
                        121.484847,
                        31.228823,
                        0
                    ]
                }
            ],
            "label": {
                "show": false,
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {
                "": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": function (params) {        return params.name + ' : ' + params.value[2];    },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u4e0a\u6d77\u5e02\u70ed\u95e8\u5730\u70b9\u5206\u7ec4\u53ef\u89c6\u5316",
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "visualMap": {
        "show": true,
        "type": "piecewise",
        "min": 0,
        "max": 100,
        "inRange": {
            "color": [
                "#50a3ba",
                "#eac763",
                "#d94e5d"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "hoverLink": true,
        "orient": "vertical",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 14,
        "borderWidth": 0,
        "pieces": [
            {
                "min": 0,
                "max": 0,
                "label": "1",
                "color": "#50A3BA"
            },
            {
                "min": 0,
                "max": 1,
                "label": "2",
                "color": "#DD0200"
            },
            {
                "min": 1,
                "max": 2,
                "label": "3",
                "color": "#E2C568"
            }
        ]
    },
    "geo": {
        "map": "\u4e0a\u6d77",
        "roam": true,
        "aspectScale": 0.75,
        "nameProperty": "name",
        "selectedMode": false,
        "emphasis": {}
    }
};
        chart_5449904cfd1e42fc9ebf829e27828592.setOption(option_5449904cfd1e42fc9ebf829e27828592);
    </script>
<br/>    </div>
    <script>
            $('#********************************').css('border-style', 'dashed').css('border-width', '0px');$("#********************************>div:nth-child(1)").width("100%").height("100%");
            new ResizeSensor(jQuery('#********************************'), function() { chart_********************************.resize()});
            $('#8c01bd8603524aa5a0ad6df2339f25da').css('border-style', 'dashed').css('border-width', '0px');$("#8c01bd8603524aa5a0ad6df2339f25da>div:nth-child(1)").width("100%").height("100%");
            new ResizeSensor(jQuery('#8c01bd8603524aa5a0ad6df2339f25da'), function() { chart_8c01bd8603524aa5a0ad6df2339f25da.resize()});
            $('#5449904cfd1e42fc9ebf829e27828592').css('border-style', 'dashed').css('border-width', '0px');$("#5449904cfd1e42fc9ebf829e27828592>div:nth-child(1)").width("100%").height("100%");
            new ResizeSensor(jQuery('#5449904cfd1e42fc9ebf829e27828592'), function() { chart_5449904cfd1e42fc9ebf829e27828592.resize()});
            var charts_id = ['********************************','8c01bd8603524aa5a0ad6df2339f25da','5449904cfd1e42fc9ebf829e27828592'];
function downloadCfg () {
    const fileName = 'chart_config.json'
    let downLink = document.createElement('a')
    downLink.download = fileName

    let result = []
    for(let i=0; i<charts_id.length; i++) {
        chart = $('#'+charts_id[i])
        result.push({
            cid: charts_id[i],
            width: chart.css("width"),
            height: chart.css("height"),
            top: chart.offset().top + "px",
            left: chart.offset().left + "px"
        })
    }

    let blob = new Blob([JSON.stringify(result)])
    downLink.href = URL.createObjectURL(blob)
    document.body.appendChild(downLink)
    downLink.click()
    document.body.removeChild(downLink)
}
    </script>
</body>
</html>
